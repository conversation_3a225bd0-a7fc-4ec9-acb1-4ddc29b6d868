{"mappings": "AAAA,iBAAiB;IAAG,iBAAiB,CAAC,OAAS,CAAC,aAAO,EAAE,KAAK,QAAQ,CAAC,sCAAuB,CAAC;IAC7F,iBAAiB,CAAC,qFAAiD,CAAC;IACpE,kBAAkB,CAAC,OAAS,CAAC,aAAO,EAAE,KAAK,QAAQ,CAAC,0CAAwB,CAAC;IAC7E,mBAAmB,CAAC,4CAA6B,CAAC;AACpD", "sources": ["packages/@react-stately/datepicker/intl/tr-TR.json"], "sourcesContent": ["{\n  \"rangeOverflow\": \"<PERSON><PERSON><PERSON>, {maxValue} veya öncesi olmalıdır.\",\n  \"rangeReversed\": \"Başlangıç tarihi bitiş tarihinden önce olmalıdır.\",\n  \"rangeUnderflow\": \"<PERSON><PERSON><PERSON>, {minValue} veya sonrası olmalıdır.\",\n  \"unavailableDate\": \"Seçilen tarih kullanılamıyor.\"\n}\n"], "names": [], "version": 3, "file": "tr-TR.main.js.map"}