@keyframes fui-fade-in {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes fui-fade-out {
  from {
    opacity: 1;
  }
  to {
    opacity: 0;
  }
}

@keyframes fui-slide-from-top {
  from {
    transform: translateY(4px) scale(0.97);
  }
  to {
    transform: translateY(0) scale(1);
  }
}

@keyframes fui-slide-to-top {
  from {
    transform: translateY(0) scale(1);
  }
  to {
    transform: translateY(4px) scale(0.97);
  }
}

@keyframes fui-slide-from-bottom {
  from {
    transform: translateY(-4px) scale(0.97);
  }
  to {
    transform: translateY(0) scale(1);
  }
}

@keyframes fui-slide-to-bottom {
  from {
    transform: translateY(0) scale(1);
  }
  to {
    transform: translateY(-4px) scale(0.97);
  }
}

@keyframes fui-slide-from-left {
  from {
    transform: translateX(4px) scale(0.97);
  }
  to {
    transform: translateX(0) scale(1);
  }
}

@keyframes fui-slide-to-left {
  from {
    transform: translateX(0) scale(1);
  }
  to {
    transform: translateX(4px) scale(0.97);
  }
}

@keyframes fui-slide-from-right {
  from {
    transform: translateX(-4px) scale(0.97);
  }
  to {
    transform: translateX(0) scale(1);
  }
}

@keyframes fui-slide-to-right {
  from {
    transform: translateX(0) scale(1);
  }
  to {
    transform: translateX(-4px) scale(0.97);
  }
}

@media (prefers-reduced-motion: no-preference) {
  .fui-PopperContent {
    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
  }

    .fui-PopperContent:where([data-state='open']) {
      animation-duration: 300ms;
    }

      .fui-PopperContent:where([data-state='open']):where([data-side='top']) {
        animation-name: fui-slide-from-top, fui-fade-in;
      }
      .fui-PopperContent:where([data-state='open']):where([data-side='bottom']) {
        animation-name: fui-slide-from-bottom, fui-fade-in;
      }
      .fui-PopperContent:where([data-state='open']):where([data-side='left']) {
        animation-name: fui-slide-from-left, fui-fade-in;
      }
      .fui-PopperContent:where([data-state='open']):where([data-side='right']) {
        animation-name: fui-slide-from-right, fui-fade-in;
      }

    .fui-PopperContent:where([data-state='closed']) {
      animation-duration: 150ms;
    }

      .fui-PopperContent:where([data-state='closed']):where([data-side='top']) {
        animation-name: fui-slide-to-top, fui-fade-out;
      }
      .fui-PopperContent:where([data-state='closed']):where([data-side='bottom']) {
        animation-name: fui-slide-to-bottom, fui-fade-out;
      }
      .fui-PopperContent:where([data-state='closed']):where([data-side='left']) {
        animation-name: fui-slide-to-left, fui-fade-out;
      }
      .fui-PopperContent:where([data-state='closed']):where([data-side='right']) {
        animation-name: fui-slide-to-right, fui-fade-out;
      }
}

.frosted-ui:where([data-is-root-theme='true']) {
  position: relative;
  z-index: 0;
}

.fui-reset:where(a) {
  cursor: var(--cursor-link);
  text-decoration: none;
  color: inherit;
  outline: none;
}

.fui-reset:where(button) {
  -webkit-appearance: none;
          appearance: none;
  cursor: var(--cursor-button);
  background-color: transparent;
  border: none;
  font-size: inherit;
  font-family: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  outline: none;
  color: inherit;
  padding: 0;
  margin: 0;
  text-align: initial;
  -webkit-tap-highlight-color: transparent;
}

.fui-reset:where(h1, h2, h3, h4, h5, h6) {
  font-size: inherit;
  font-weight: inherit;
  margin: 0;
}

.fui-reset:where(ol, ul) {
  list-style: none;
  margin: 0;
  padding: 0;
}

.fui-reset:where(p) {
  margin: 0;
}

.fui-reset:where(pre) {
  font-family: inherit;
  margin: 0;
}

*,
::before,
::after {
  box-sizing: border-box;
  border-width: 0;
  border-style: solid;
}

blockquote,
dl,
dd,
h1,
h2,
h3,
h4,
h5,
h6,
hr,
figure,
p,
pre {
  margin: 0;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  font-size: inherit;
  font-weight: inherit;
}

ol,
ul {
  list-style: none;
  margin: 0;
  padding: 0;
}

img,
svg,
video,
canvas,
audio,
iframe,
embed,
object {
  display: block;
  vertical-align: middle;
}

img,
video {
  max-width: 100%;
  height: auto;
}

*::-webkit-scrollbar {
  width: var(--space-1);
  height: var(--space-1);
}

*::-webkit-scrollbar-track {
  background-color: var(--gray-a3);
}

*::-webkit-scrollbar-corner {
  background-color: var(--gray-a3);
}

*::-webkit-scrollbar-thumb {
  border-radius: var(--space-1);
  background-color: var(--gray-a8);
}

@media (hover: hover) {
  :hover::-webkit-scrollbar-thumb:hover {
    background-color: var(--gray-a9);
  }
}

:root, .light, .light-theme {
  --tomato-1: #fffcfc;
  --tomato-2: #fff8f7;
  --tomato-3: #feebe7;
  --tomato-4: #ffdcd3;
  --tomato-5: #ffcdc2;
  --tomato-6: #fdbdaf;
  --tomato-7: #f5a898;
  --tomato-8: #ec8e7b;
  --tomato-9: #e54d2e;
  --tomato-10: #dd4425;
  --tomato-11: #d13415;
  --tomato-12: #5c271f;
  --tomato-a1: #ff000003;
  --tomato-a2: #ff200008;
  --tomato-a3: #f52b0018;
  --tomato-a4: #ff35002c;
  --tomato-a5: #ff2e003d;
  --tomato-a6: #f92d0050;
  --tomato-a7: #e7280067;
  --tomato-a8: #db250084;
  --tomato-a9: #df2600d1;
  --tomato-a10: #d72400da;
  --tomato-a11: #cd2200ea;
  --tomato-a12: #460900e0;
  --red-1: #fffcfc;
  --red-2: #fff7f7;
  --red-3: #feebec;
  --red-4: #ffdbdc;
  --red-5: #ffcdce;
  --red-6: #fdbdbe;
  --red-7: #f4a9aa;
  --red-8: #eb8e90;
  --red-9: #e5484d;
  --red-10: #dc3e42;
  --red-11: #ce2c31;
  --red-12: #641723;
  --red-a1: #ff000003;
  --red-a2: #ff000008;
  --red-a3: #f3000d14;
  --red-a4: #ff000824;
  --red-a5: #ff000632;
  --red-a6: #f8000442;
  --red-a7: #df000356;
  --red-a8: #d2000571;
  --red-a9: #db0007b7;
  --red-a10: #d10005c1;
  --red-a11: #c40006d3;
  --red-a12: #55000de8;
  --ruby-1: #fffcfd;
  --ruby-2: #fff7f8;
  --ruby-3: #feeaed;
  --ruby-4: #ffdce1;
  --ruby-5: #ffced6;
  --ruby-6: #f8bfc8;
  --ruby-7: #efacb8;
  --ruby-8: #e592a3;
  --ruby-9: #e54666;
  --ruby-10: #dc3b5d;
  --ruby-11: #ca244d;
  --ruby-12: #64172b;
  --ruby-a1: #ff005503;
  --ruby-a2: #ff002008;
  --ruby-a3: #f3002515;
  --ruby-a4: #ff002523;
  --ruby-a5: #ff002a31;
  --ruby-a6: #e4002440;
  --ruby-a7: #ce002553;
  --ruby-a8: #c300286d;
  --ruby-a9: #db002cb9;
  --ruby-a10: #d2002cc4;
  --ruby-a11: #c10030db;
  --ruby-a12: #550016e8;
  --crimson-1: #fffcfd;
  --crimson-2: #fef7f9;
  --crimson-3: #ffe9f0;
  --crimson-4: #fedce7;
  --crimson-5: #facedd;
  --crimson-6: #f3bed1;
  --crimson-7: #eaacc3;
  --crimson-8: #e093b2;
  --crimson-9: #e93d82;
  --crimson-10: #df3478;
  --crimson-11: #cb1d63;
  --crimson-12: #621639;
  --crimson-a1: #ff005503;
  --crimson-a2: #e0004008;
  --crimson-a3: #ff005216;
  --crimson-a4: #f8005123;
  --crimson-a5: #e5004f31;
  --crimson-a6: #d0004b41;
  --crimson-a7: #bf004753;
  --crimson-a8: #b6004a6c;
  --crimson-a9: #e2005bc2;
  --crimson-a10: #d70056cb;
  --crimson-a11: #c4004fe2;
  --crimson-a12: #530026e9;
  --pink-1: #fffcfe;
  --pink-2: #fef7fb;
  --pink-3: #fee9f5;
  --pink-4: #fbdcef;
  --pink-5: #f6cee7;
  --pink-6: #efbfdd;
  --pink-7: #e7acd0;
  --pink-8: #dd93c2;
  --pink-9: #d6409f;
  --pink-10: #cf3897;
  --pink-11: #c2298a;
  --pink-12: #651249;
  --pink-a1: #ff00aa03;
  --pink-a2: #e0008008;
  --pink-a3: #f4008c16;
  --pink-a4: #e2008b23;
  --pink-a5: #d1008331;
  --pink-a6: #c0007840;
  --pink-a7: #b6006f53;
  --pink-a8: #af006f6c;
  --pink-a9: #c8007fbf;
  --pink-a10: #c2007ac7;
  --pink-a11: #b60074d6;
  --pink-a12: #59003bed;
  --plum-1: #fefcff;
  --plum-2: #fdf7fd;
  --plum-3: #fbebfb;
  --plum-4: #f7def8;
  --plum-5: #f2d1f3;
  --plum-6: #e9c2ec;
  --plum-7: #deade3;
  --plum-8: #cf91d8;
  --plum-9: #ab4aba;
  --plum-10: #a144af;
  --plum-11: #953ea3;
  --plum-12: #53195d;
  --plum-a1: #aa00ff03;
  --plum-a2: #c000c008;
  --plum-a3: #cc00cc14;
  --plum-a4: #c200c921;
  --plum-a5: #b700bd2e;
  --plum-a6: #a400b03d;
  --plum-a7: #9900a852;
  --plum-a8: #9000a56e;
  --plum-a9: #89009eb5;
  --plum-a10: #7f0092bb;
  --plum-a11: #730086c1;
  --plum-a12: #40004be6;
  --purple-1: #fefcfe;
  --purple-2: #fbf7fe;
  --purple-3: #f7edfe;
  --purple-4: #f2e2fc;
  --purple-5: #ead5f9;
  --purple-6: #e0c4f4;
  --purple-7: #d1afec;
  --purple-8: #be93e4;
  --purple-9: #8e4ec6;
  --purple-10: #8347b9;
  --purple-11: #8145b5;
  --purple-12: #402060;
  --purple-a1: #aa00aa03;
  --purple-a2: #8000e008;
  --purple-a3: #8e00f112;
  --purple-a4: #8d00e51d;
  --purple-a5: #8000db2a;
  --purple-a6: #7a01d03b;
  --purple-a7: #6d00c350;
  --purple-a8: #6600c06c;
  --purple-a9: #5c00adb1;
  --purple-a10: #53009eb8;
  --purple-a11: #52009aba;
  --purple-a12: #250049df;
  --violet-1: #fdfcfe;
  --violet-2: #faf8ff;
  --violet-3: #f4f0fe;
  --violet-4: #ebe4ff;
  --violet-5: #e1d9ff;
  --violet-6: #d4cafe;
  --violet-7: #c2b5f5;
  --violet-8: #aa99ec;
  --violet-9: #6e56cf;
  --violet-10: #654dc4;
  --violet-11: #6550b9;
  --violet-12: #2f265f;
  --violet-a1: #5500aa03;
  --violet-a2: #4900ff07;
  --violet-a3: #4400ee0f;
  --violet-a4: #4300ff1b;
  --violet-a5: #3600ff26;
  --violet-a6: #3100fb35;
  --violet-a7: #2d01dd4a;
  --violet-a8: #2b00d066;
  --violet-a9: #2400b7a9;
  --violet-a10: #2300abb2;
  --violet-a11: #1f0099af;
  --violet-a12: #0b0043d9;
  --iris-1: #fdfdff;
  --iris-2: #f8f8ff;
  --iris-3: #f0f1fe;
  --iris-4: #e6e7ff;
  --iris-5: #dadcff;
  --iris-6: #cbcdff;
  --iris-7: #b8baf8;
  --iris-8: #9b9ef0;
  --iris-9: #5b5bd6;
  --iris-10: #5151cd;
  --iris-11: #5753c6;
  --iris-12: #272962;
  --iris-a1: #0000ff02;
  --iris-a2: #0000ff07;
  --iris-a3: #0011ee0f;
  --iris-a4: #000bff19;
  --iris-a5: #000eff25;
  --iris-a6: #000aff34;
  --iris-a7: #0008e647;
  --iris-a8: #0008d964;
  --iris-a9: #0000c0a4;
  --iris-a10: #0000b6ae;
  --iris-a11: #0600abac;
  --iris-a12: #000246d8;
  --cyan-1: #fafdfe;
  --cyan-2: #f2fafb;
  --cyan-3: #def7f9;
  --cyan-4: #caf1f6;
  --cyan-5: #b5e9f0;
  --cyan-6: #9ddde7;
  --cyan-7: #7dcedc;
  --cyan-8: #3db9cf;
  --cyan-9: #00a2c7;
  --cyan-10: #0797b9;
  --cyan-11: #107d98;
  --cyan-12: #0d3c48;
  --cyan-a1: #0099cc05;
  --cyan-a2: #009db10d;
  --cyan-a3: #00c2d121;
  --cyan-a4: #00bcd435;
  --cyan-a5: #01b4cc4a;
  --cyan-a6: #00a7c162;
  --cyan-a7: #009fbb82;
  --cyan-a8: #00a3c0c2;
  --cyan-a9: #00a2c7;
  --cyan-a10: #0094b7f8;
  --cyan-a11: #007491ef;
  --cyan-a12: #00323ef2;
  --teal-1: #fafefd;
  --teal-2: #f3fbf9;
  --teal-3: #e0f8f3;
  --teal-4: #ccf3ea;
  --teal-5: #b8eae0;
  --teal-6: #a1ded2;
  --teal-7: #83cdc1;
  --teal-8: #53b9ab;
  --teal-9: #12a594;
  --teal-10: #0d9b8a;
  --teal-11: #008573;
  --teal-12: #0d3d38;
  --teal-a1: #00cc9905;
  --teal-a2: #00aa800c;
  --teal-a3: #00c69d1f;
  --teal-a4: #00c39633;
  --teal-a5: #00b49047;
  --teal-a6: #00a6855e;
  --teal-a7: #0099807c;
  --teal-a8: #009783ac;
  --teal-a9: #009e8ced;
  --teal-a10: #009684f2;
  --teal-a11: #008573;
  --teal-a12: #00332df2;
  --jade-1: #fbfefd;
  --jade-2: #f4fbf7;
  --jade-3: #e6f7ed;
  --jade-4: #d6f1e3;
  --jade-5: #c3e9d7;
  --jade-6: #acdec8;
  --jade-7: #8bceb6;
  --jade-8: #56ba9f;
  --jade-9: #29a383;
  --jade-10: #26997b;
  --jade-11: #208368;
  --jade-12: #1d3b31;
  --jade-a1: #00c08004;
  --jade-a2: #00a3460b;
  --jade-a3: #00ae4819;
  --jade-a4: #00a85129;
  --jade-a5: #00a2553c;
  --jade-a6: #009a5753;
  --jade-a7: #00945f74;
  --jade-a8: #00976ea9;
  --jade-a9: #00916bd6;
  --jade-a10: #008764d9;
  --jade-a11: #007152df;
  --jade-a12: #002217e2;
  --green-1: #fbfefc;
  --green-2: #f4fbf6;
  --green-3: #e6f6eb;
  --green-4: #d6f1df;
  --green-5: #c4e8d1;
  --green-6: #adddc0;
  --green-7: #8eceaa;
  --green-8: #5bb98b;
  --green-9: #30a46c;
  --green-10: #2b9a66;
  --green-11: #218358;
  --green-12: #193b2d;
  --green-a1: #00c04004;
  --green-a2: #00a32f0b;
  --green-a3: #00a43319;
  --green-a4: #00a83829;
  --green-a5: #019c393b;
  --green-a6: #00963c52;
  --green-a7: #00914071;
  --green-a8: #00924ba4;
  --green-a9: #008f4acf;
  --green-a10: #008647d4;
  --green-a11: #00713fde;
  --green-a12: #002616e6;
  --grass-1: #fbfefb;
  --grass-2: #f5fbf5;
  --grass-3: #e9f6e9;
  --grass-4: #daf1db;
  --grass-5: #c9e8ca;
  --grass-6: #b2ddb5;
  --grass-7: #94ce9a;
  --grass-8: #65ba74;
  --grass-9: #46a758;
  --grass-10: #3e9b4f;
  --grass-11: #2a7e3b;
  --grass-12: #203c25;
  --grass-a1: #00c00004;
  --grass-a2: #0099000a;
  --grass-a3: #00970016;
  --grass-a4: #009f0725;
  --grass-a5: #00930536;
  --grass-a6: #008f0a4d;
  --grass-a7: #018b0f6b;
  --grass-a8: #008d199a;
  --grass-a9: #008619b9;
  --grass-a10: #007b17c1;
  --grass-a11: #006514d5;
  --grass-a12: #002006df;
  --brown-1: #fefdfc;
  --brown-2: #fcf9f6;
  --brown-3: #f6eee7;
  --brown-4: #f0e4d9;
  --brown-5: #ebdaca;
  --brown-6: #e4cdb7;
  --brown-7: #dcbc9f;
  --brown-8: #cea37e;
  --brown-9: #ad7f58;
  --brown-10: #a07553;
  --brown-11: #815e46;
  --brown-12: #3e332e;
  --brown-a1: #aa550003;
  --brown-a2: #aa550009;
  --brown-a3: #a04b0018;
  --brown-a4: #9b4a0026;
  --brown-a5: #9f4d0035;
  --brown-a6: #a04e0048;
  --brown-a7: #a34e0060;
  --brown-a8: #9f4a0081;
  --brown-a9: #823c00a7;
  --brown-a10: #723300ac;
  --brown-a11: #522100b9;
  --brown-a12: #140600d1;
  --sky-1: #f9feff;
  --sky-2: #f1fafd;
  --sky-3: #e1f6fd;
  --sky-4: #d1f0fa;
  --sky-5: #bee7f5;
  --sky-6: #a9daed;
  --sky-7: #8dcae3;
  --sky-8: #60b3d7;
  --sky-9: #7ce2fe;
  --sky-10: #74daf8;
  --sky-11: #00749e;
  --sky-12: #1d3e56;
  --sky-a1: #00d5ff06;
  --sky-a2: #00a4db0e;
  --sky-a3: #00b3ee1e;
  --sky-a4: #00ace42e;
  --sky-a5: #00a1d841;
  --sky-a6: #0092ca56;
  --sky-a7: #0089c172;
  --sky-a8: #0085bf9f;
  --sky-a9: #00c7fe83;
  --sky-a10: #00bcf38b;
  --sky-a11: #00749e;
  --sky-a12: #002540e2;
  --mint-1: #f9fefd;
  --mint-2: #f2fbf9;
  --mint-3: #ddf9f2;
  --mint-4: #c8f4e9;
  --mint-5: #b3ecde;
  --mint-6: #9ce0d0;
  --mint-7: #7ecfbd;
  --mint-8: #4cbba5;
  --mint-9: #86ead4;
  --mint-10: #7de0cb;
  --mint-11: #027864;
  --mint-12: #16433c;
  --mint-a1: #00d5aa06;
  --mint-a2: #00b18a0d;
  --mint-a3: #00d29e22;
  --mint-a4: #00cc9937;
  --mint-a5: #00c0914c;
  --mint-a6: #00b08663;
  --mint-a7: #00a17d81;
  --mint-a8: #009e7fb3;
  --mint-a9: #00d3a579;
  --mint-a10: #00c39982;
  --mint-a11: #007763fd;
  --mint-a12: #00312ae9;
  --yellow-1: #fdfdf9;
  --yellow-2: #fefce9;
  --yellow-3: #fffab8;
  --yellow-4: #fff394;
  --yellow-5: #ffe770;
  --yellow-6: #f3d768;
  --yellow-7: #e4c767;
  --yellow-8: #d5ae39;
  --yellow-9: #ffe629;
  --yellow-10: #ffdc00;
  --yellow-11: #9e6c00;
  --yellow-12: #473b1f;
  --yellow-a1: #aaaa0006;
  --yellow-a2: #f4dd0016;
  --yellow-a3: #ffee0047;
  --yellow-a4: #ffe3016b;
  --yellow-a5: #ffd5008f;
  --yellow-a6: #ebbc0097;
  --yellow-a7: #d2a10098;
  --yellow-a8: #c99700c6;
  --yellow-a9: #ffe100d6;
  --yellow-a10: #ffdc00;
  --yellow-a11: #9e6c00;
  --yellow-a12: #2e2000e0;
  --amber-1: #fefdfb;
  --amber-2: #fefbe9;
  --amber-3: #fff7c2;
  --amber-4: #ffee9c;
  --amber-5: #fbe577;
  --amber-6: #f3d673;
  --amber-7: #e9c162;
  --amber-8: #e2a336;
  --amber-9: #ffc53d;
  --amber-10: #ffba18;
  --amber-11: #ab6400;
  --amber-12: #4f3422;
  --amber-a1: #c0800004;
  --amber-a2: #f4d10016;
  --amber-a3: #ffde003d;
  --amber-a4: #ffd40063;
  --amber-a5: #f8cf0088;
  --amber-a6: #eab5008c;
  --amber-a7: #dc9b009d;
  --amber-a8: #da8a00c9;
  --amber-a9: #ffb300c2;
  --amber-a10: #ffb300e7;
  --amber-a11: #ab6400;
  --amber-a12: #341500dd;
  --gold-1: #fdfdfc;
  --gold-2: #faf9f2;
  --gold-3: #f2f0e7;
  --gold-4: #eae6db;
  --gold-5: #e1dccf;
  --gold-6: #d8d0bf;
  --gold-7: #cbc0aa;
  --gold-8: #b9a88d;
  --gold-9: #978365;
  --gold-10: #8c7a5e;
  --gold-11: #71624b;
  --gold-12: #3b352b;
  --gold-a1: #55550003;
  --gold-a2: #9d8a000d;
  --gold-a3: #75600018;
  --gold-a4: #6b4e0024;
  --gold-a5: #60460030;
  --gold-a6: #64440040;
  --gold-a7: #63420055;
  --gold-a8: #633d0072;
  --gold-a9: #5332009a;
  --gold-a10: #492d00a1;
  --gold-a11: #362100b4;
  --gold-a12: #130c00d4;
  --bronze-1: #fdfcfc;
  --bronze-2: #fdf7f5;
  --bronze-3: #f6edea;
  --bronze-4: #efe4df;
  --bronze-5: #e7d9d3;
  --bronze-6: #dfcdc5;
  --bronze-7: #d3bcb3;
  --bronze-8: #c2a499;
  --bronze-9: #a18072;
  --bronze-10: #957468;
  --bronze-11: #7d5e54;
  --bronze-12: #43302b;
  --bronze-a1: #55000003;
  --bronze-a2: #cc33000a;
  --bronze-a3: #92250015;
  --bronze-a4: #80280020;
  --bronze-a5: #7423002c;
  --bronze-a6: #7324003a;
  --bronze-a7: #6c1f004c;
  --bronze-a8: #671c0066;
  --bronze-a9: #551a008d;
  --bronze-a10: #4c150097;
  --bronze-a11: #3d0f00ab;
  --bronze-a12: #1d0600d4;
  --gray-1: #fcfcfc;
  --gray-2: #f9f9f9;
  --gray-3: #f0f0f0;
  --gray-4: #e8e8e8;
  --gray-5: #e0e0e0;
  --gray-6: #d9d9d9;
  --gray-7: #cecece;
  --gray-8: #bbbbbb;
  --gray-9: #8d8d8d;
  --gray-10: #838383;
  --gray-11: #646464;
  --gray-12: #202020;
  --gray-a1: #00000003;
  --gray-a2: #00000006;
  --gray-a3: #0000000f;
  --gray-a4: #00000017;
  --gray-a5: #0000001f;
  --gray-a6: #00000026;
  --gray-a7: #00000031;
  --gray-a8: #00000044;
  --gray-a9: #00000072;
  --gray-a10: #0000007c;
  --gray-a11: #0000009b;
  --gray-a12: #000000df;
  --mauve-1: #fdfcfd;
  --mauve-2: #faf9fb;
  --mauve-3: #f2eff3;
  --mauve-4: #eae7ec;
  --mauve-5: #e3dfe6;
  --mauve-6: #dbd8e0;
  --mauve-7: #d0cdd7;
  --mauve-8: #bcbac7;
  --mauve-9: #8e8c99;
  --mauve-10: #84828e;
  --mauve-11: #65636d;
  --mauve-12: #211f26;
  --mauve-a1: #55005503;
  --mauve-a2: #2b005506;
  --mauve-a3: #30004010;
  --mauve-a4: #20003618;
  --mauve-a5: #20003820;
  --mauve-a6: #14003527;
  --mauve-a7: #10003332;
  --mauve-a8: #08003145;
  --mauve-a9: #05001d73;
  --mauve-a10: #0500197d;
  --mauve-a11: #0400119c;
  --mauve-a12: #020008e0;
  --slate-1: #fcfcfd;
  --slate-2: #f9f9fb;
  --slate-3: #f0f0f3;
  --slate-4: #e8e8ec;
  --slate-5: #e0e1e6;
  --slate-6: #d9d9e0;
  --slate-7: #cdced6;
  --slate-8: #b9bbc6;
  --slate-9: #8b8d98;
  --slate-10: #80838d;
  --slate-11: #60646c;
  --slate-12: #1c2024;
  --slate-a1: #00005503;
  --slate-a2: #00005506;
  --slate-a3: #0000330f;
  --slate-a4: #00002d17;
  --slate-a5: #0009321f;
  --slate-a6: #00002f26;
  --slate-a7: #00062e32;
  --slate-a8: #00083046;
  --slate-a9: #00051d74;
  --slate-a10: #00071b7f;
  --slate-a11: #0007149f;
  --slate-a12: #000509e3;
  --sage-1: #fbfdfc;
  --sage-2: #f7f9f8;
  --sage-3: #eef1f0;
  --sage-4: #e6e9e8;
  --sage-5: #dfe2e0;
  --sage-6: #d7dad9;
  --sage-7: #cbcfcd;
  --sage-8: #b8bcba;
  --sage-9: #868e8b;
  --sage-10: #7c8481;
  --sage-11: #5f6563;
  --sage-12: #1a211e;
  --sage-a1: #00804004;
  --sage-a2: #00402008;
  --sage-a3: #002d1e11;
  --sage-a4: #001f1519;
  --sage-a5: #00180820;
  --sage-a6: #00140d28;
  --sage-a7: #00140a34;
  --sage-a8: #000f0847;
  --sage-a9: #00110b79;
  --sage-a10: #00100a83;
  --sage-a11: #000a07a0;
  --sage-a12: #000805e5;
  --olive-1: #fcfdfc;
  --olive-2: #f8faf8;
  --olive-3: #eff1ef;
  --olive-4: #e7e9e7;
  --olive-5: #dfe2df;
  --olive-6: #d7dad7;
  --olive-7: #cccfcc;
  --olive-8: #b9bcb8;
  --olive-9: #898e87;
  --olive-10: #7f847d;
  --olive-11: #60655f;
  --olive-12: #1d211c;
  --olive-a1: #00550003;
  --olive-a2: #00490007;
  --olive-a3: #00200010;
  --olive-a4: #00160018;
  --olive-a5: #00180020;
  --olive-a6: #00140028;
  --olive-a7: #000f0033;
  --olive-a8: #040f0047;
  --olive-a9: #050f0078;
  --olive-a10: #040e0082;
  --olive-a11: #020a00a0;
  --olive-a12: #010600e3;
  --sand-1: #fdfdfc;
  --sand-2: #f9f9f8;
  --sand-3: #f1f0ef;
  --sand-4: #e9e8e6;
  --sand-5: #e2e1de;
  --sand-6: #dad9d6;
  --sand-7: #cfceca;
  --sand-8: #bcbbb5;
  --sand-9: #8d8d86;
  --sand-10: #82827c;
  --sand-11: #63635e;
  --sand-12: #21201c;
  --sand-a1: #55550003;
  --sand-a2: #25250007;
  --sand-a3: #20100010;
  --sand-a4: #1f150019;
  --sand-a5: #1f180021;
  --sand-a6: #19130029;
  --sand-a7: #19140035;
  --sand-a8: #1915014a;
  --sand-a9: #0f0f0079;
  --sand-a10: #0c0c0083;
  --sand-a11: #080800a1;
  --sand-a12: #060500e3;
  --blue-1: #fdfdfe;
  --blue-2: #f6faff;
  --blue-3: #ebf2ff;
  --blue-4: #ddeaff;
  --blue-5: #cce0ff;
  --blue-6: #b7d3ff;
  --blue-7: #a0c0fd;
  --blue-8: #7ea7f5;
  --blue-9: #1754d8;
  --blue-10: #0543c7;
  --blue-11: #265ccf;
  --blue-12: #162e5f;
  --blue-a1: #00008002;
  --blue-a2: #0072ff09;
  --blue-a3: #005aff14;
  --blue-a4: #0062ff22;
  --blue-a5: #0064ff33;
  --blue-a6: #0064ff48;
  --blue-a7: #0056fa5f;
  --blue-a8: #0052ec81;
  --blue-a9: #0043d4e8;
  --blue-a10: #003fc6fa;
  --blue-a11: #0040c7d9;
  --blue-a12: #001a50e9;
  --orange-1: #fffcfb;
  --orange-2: #fff7f4;
  --orange-3: #ffeae4;
  --orange-4: #ffd8cb;
  --orange-5: #ffc9b8;
  --orange-6: #ffb8a3;
  --orange-7: #ffa38d;
  --orange-8: #f7886e;
  --orange-9: #fa4616;
  --orange-10: #ec3400;
  --orange-11: #dd2400;
  --orange-12: #5f2518;
  --orange-a1: #ff400004;
  --orange-a2: #ff46000b;
  --orange-a3: #ff39001b;
  --orange-a4: #ff400034;
  --orange-a5: #ff3e0047;
  --orange-a6: #ff3b005c;
  --orange-a7: #ff320072;
  --orange-a8: #f12e0091;
  --orange-a9: #fa3500e9;
  --orange-a10: #ec3400;
  --orange-a11: #dd2400;
  --orange-a12: #4e0e00e7;
  --lemon-1: #fcfdf9;
  --lemon-2: #f9fcee;
  --lemon-3: #f0fbc5;
  --lemon-4: #e6f6a6;
  --lemon-5: #dbed8a;
  --lemon-6: #cdde7b;
  --lemon-7: #bdcd6d;
  --lemon-8: #a6b842;
  --lemon-9: #d7f100;
  --lemon-10: #cee610;
  --lemon-11: #6f7d00;
  --lemon-12: #3a401d;
  --lemon-a1: #80aa0006;
  --lemon-a2: #a5d20011;
  --lemon-a3: #beee003a;
  --lemon-a4: #b8e60059;
  --lemon-a5: #b1d80075;
  --lemon-a6: #9fc00084;
  --lemon-a7: #8ca80092;
  --lemon-a8: #879f00bd;
  --lemon-a9: #d7f100;
  --lemon-a10: #cbe400ef;
  --lemon-a11: #6f7d00;
  --lemon-a12: #212800e2;
  --indigo-1: #fdfdff;
  --indigo-2: #f7f8ff;
  --indigo-3: #f0f0ff;
  --indigo-4: #e5e5ff;
  --indigo-5: #dadaff;
  --indigo-6: #cbcbff;
  --indigo-7: #b8b5ff;
  --indigo-8: #9e95ff;
  --indigo-9: #6318f8;
  --indigo-10: #5800e6;
  --indigo-11: #642ef1;
  --indigo-12: #2f1978;
  --indigo-a1: #0000ff02;
  --indigo-a2: #0020ff08;
  --indigo-a3: #0000ff0f;
  --indigo-a4: #0000ff1a;
  --indigo-a5: #0000ff25;
  --indigo-a6: #0000ff34;
  --indigo-a7: #0b01ff4a;
  --indigo-a8: #1601ff6a;
  --indigo-a9: #5300f7e7;
  --indigo-a10: #5800e6;
  --indigo-a11: #4200eed1;
  --indigo-a12: #180069e6;
  --lime-1: #fafefa;
  --lime-2: #f4fcf3;
  --lime-3: #dffbdc;
  --lime-4: #caf8c6;
  --lime-5: #b4f1af;
  --lime-6: #9be696;
  --lime-7: #7bd676;
  --lime-8: #42c340;
  --lime-9: #06d718;
  --lime-10: #00cb00;
  --lime-11: #008600;
  --lime-12: #194318;
  --lime-a1: #00cc0005;
  --lime-a2: #16c0000c;
  --lime-a3: #16e20023;
  --lime-a4: #12e00039;
  --lime-a5: #10d30050;
  --lime-a6: #0dc30069;
  --lime-a7: #0ab30089;
  --lime-a8: #03af00bf;
  --lime-a9: #00d612f9;
  --lime-a10: #00cb00;
  --lime-a11: #008600;
  --lime-a12: #013000e7;
  --magenta-1: #fffcfd;
  --magenta-2: #fff6f9;
  --magenta-3: #ffe7ef;
  --magenta-4: #ffd9e6;
  --magenta-5: #ffcadb;
  --magenta-6: #fcbacf;
  --magenta-7: #f4a6bf;
  --magenta-8: #ec8cad;
  --magenta-9: #ff008d;
  --magenta-10: #ef0081;
  --magenta-11: #d40070;
  --magenta-12: #6b0037;
  --magenta-a1: #ff005503;
  --magenta-a2: #ff005509;
  --magenta-a3: #ff005518;
  --magenta-a4: #ff005826;
  --magenta-a5: #ff005235;
  --magenta-a6: #f4004e45;
  --magenta-a7: #e0004859;
  --magenta-a8: #d5004a73;
  --magenta-a9: #ff008d;
  --magenta-a10: #ef0081;
  --magenta-a11: #d40070;
  --magenta-a12: #6b0037;
  --gray-surface: #ffffffcc;
  --mauve-surface: #ffffffcc;
  --slate-surface: #ffffffcc;
  --sage-surface: #ffffffcc;
  --olive-surface: #ffffffcc;
  --sand-surface: #ffffffcc;
  --tomato-surface: #fff6f5cc;
  --red-surface: #fff5f5cc;
  --ruby-surface: #fff5f6cc;
  --crimson-surface: #fef5f8cc;
  --pink-surface: #fef5facc;
  --plum-surface: #fdf5fdcc;
  --purple-surface: #faf5fecc;
  --violet-surface: #f9f6ffcc;
  --iris-surface: #f6f6ffcc;
  --cyan-surface: #eff9facc;
  --teal-surface: #f0faf8cc;
  --jade-surface: #f1faf5cc;
  --green-surface: #f1faf4cc;
  --grass-surface: #f3faf3cc;
  --brown-surface: #fbf8f4cc;
  --bronze-surface: #fdf5f3cc;
  --gold-surface: #f9f8efcc;
  --sky-surface: #eef9fdcc;
  --mint-surface: #effaf8cc;
  --yellow-surface: #fefbe4cc;
  --amber-surface: #fefae4cc;
  --blue-surface: #f4f9ffcc;
  --orange-surface: #fff5f1cc;
  --indigo-surface: #f5f6ffcc;
  --magenta-surface: #fff4f8cc;

  --lemon-surface: #f8fbeacc;
  --lime-surface: #f1fbf0cc;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root, .light, .light-theme {
      --tomato-1: color(display-p3 0.998 0.989 0.988);
      --tomato-2: color(display-p3 0.994 0.974 0.969);
      --tomato-3: color(display-p3 0.985 0.924 0.909);
      --tomato-4: color(display-p3 0.996 0.868 0.835);
      --tomato-5: color(display-p3 0.98 0.812 0.77);
      --tomato-6: color(display-p3 0.953 0.75 0.698);
      --tomato-7: color(display-p3 0.917 0.673 0.611);
      --tomato-8: color(display-p3 0.875 0.575 0.502);
      --tomato-9: color(display-p3 0.831 0.345 0.231);
      --tomato-10: color(display-p3 0.802 0.313 0.2);
      --tomato-11: color(display-p3 0.755 0.259 0.152);
      --tomato-12: color(display-p3 0.335 0.165 0.132);
      --tomato-a1: color(display-p3 0.675 0.024 0.024 / 0.012);
      --tomato-a2: color(display-p3 0.757 0.145 0.02 / 0.032);
      --tomato-a3: color(display-p3 0.831 0.184 0.012 / 0.091);
      --tomato-a4: color(display-p3 0.976 0.192 0.004 / 0.165);
      --tomato-a5: color(display-p3 0.918 0.192 0.004 / 0.232);
      --tomato-a6: color(display-p3 0.847 0.173 0.004 / 0.302);
      --tomato-a7: color(display-p3 0.788 0.165 0.004 / 0.389);
      --tomato-a8: color(display-p3 0.749 0.153 0.004 / 0.499);
      --tomato-a9: color(display-p3 0.78 0.149 0 / 0.769);
      --tomato-a10: color(display-p3 0.757 0.141 0 / 0.8);
      --tomato-a11: color(display-p3 0.755 0.259 0.152);
      --tomato-a12: color(display-p3 0.335 0.165 0.132);
      --red-1: color(display-p3 0.998 0.989 0.988);
      --red-2: color(display-p3 0.995 0.971 0.971);
      --red-3: color(display-p3 0.985 0.925 0.925);
      --red-4: color(display-p3 0.999 0.866 0.866);
      --red-5: color(display-p3 0.984 0.812 0.811);
      --red-6: color(display-p3 0.955 0.751 0.749);
      --red-7: color(display-p3 0.915 0.675 0.672);
      --red-8: color(display-p3 0.872 0.575 0.572);
      --red-9: color(display-p3 0.83 0.329 0.324);
      --red-10: color(display-p3 0.798 0.294 0.285);
      --red-11: color(display-p3 0.744 0.234 0.222);
      --red-12: color(display-p3 0.36 0.115 0.143);
      --red-a1: color(display-p3 0.675 0.024 0.024 / 0.012);
      --red-a2: color(display-p3 0.863 0.024 0.024 / 0.028);
      --red-a3: color(display-p3 0.792 0.008 0.008 / 0.075);
      --red-a4: color(display-p3 1 0.008 0.008 / 0.134);
      --red-a5: color(display-p3 0.918 0.008 0.008 / 0.189);
      --red-a6: color(display-p3 0.831 0.02 0.004 / 0.251);
      --red-a7: color(display-p3 0.741 0.016 0.004 / 0.33);
      --red-a8: color(display-p3 0.698 0.012 0.004 / 0.428);
      --red-a9: color(display-p3 0.749 0.008 0 / 0.675);
      --red-a10: color(display-p3 0.714 0.012 0 / 0.714);
      --red-a11: color(display-p3 0.744 0.234 0.222);
      --red-a12: color(display-p3 0.36 0.115 0.143);
      --ruby-1: color(display-p3 0.998 0.989 0.992);
      --ruby-2: color(display-p3 0.995 0.971 0.974);
      --ruby-3: color(display-p3 0.983 0.92 0.928);
      --ruby-4: color(display-p3 0.987 0.869 0.885);
      --ruby-5: color(display-p3 0.968 0.817 0.839);
      --ruby-6: color(display-p3 0.937 0.758 0.786);
      --ruby-7: color(display-p3 0.897 0.685 0.721);
      --ruby-8: color(display-p3 0.851 0.588 0.639);
      --ruby-9: color(display-p3 0.83 0.323 0.408);
      --ruby-10: color(display-p3 0.795 0.286 0.375);
      --ruby-11: color(display-p3 0.728 0.211 0.311);
      --ruby-12: color(display-p3 0.36 0.115 0.171);
      --ruby-a1: color(display-p3 0.675 0.024 0.349 / 0.012);
      --ruby-a2: color(display-p3 0.863 0.024 0.024 / 0.028);
      --ruby-a3: color(display-p3 0.804 0.008 0.11 / 0.079);
      --ruby-a4: color(display-p3 0.91 0.008 0.125 / 0.13);
      --ruby-a5: color(display-p3 0.831 0.004 0.133 / 0.185);
      --ruby-a6: color(display-p3 0.745 0.004 0.118 / 0.244);
      --ruby-a7: color(display-p3 0.678 0.004 0.114 / 0.314);
      --ruby-a8: color(display-p3 0.639 0.004 0.125 / 0.412);
      --ruby-a9: color(display-p3 0.753 0 0.129 / 0.679);
      --ruby-a10: color(display-p3 0.714 0 0.125 / 0.714);
      --ruby-a11: color(display-p3 0.728 0.211 0.311);
      --ruby-a12: color(display-p3 0.36 0.115 0.171);
      --crimson-1: color(display-p3 0.998 0.989 0.992);
      --crimson-2: color(display-p3 0.991 0.969 0.976);
      --crimson-3: color(display-p3 0.987 0.917 0.941);
      --crimson-4: color(display-p3 0.975 0.866 0.904);
      --crimson-5: color(display-p3 0.953 0.813 0.864);
      --crimson-6: color(display-p3 0.921 0.755 0.817);
      --crimson-7: color(display-p3 0.88 0.683 0.761);
      --crimson-8: color(display-p3 0.834 0.592 0.694);
      --crimson-9: color(display-p3 0.843 0.298 0.507);
      --crimson-10: color(display-p3 0.807 0.266 0.468);
      --crimson-11: color(display-p3 0.731 0.195 0.388);
      --crimson-12: color(display-p3 0.352 0.111 0.221);
      --crimson-a1: color(display-p3 0.675 0.024 0.349 / 0.012);
      --crimson-a2: color(display-p3 0.757 0.02 0.267 / 0.032);
      --crimson-a3: color(display-p3 0.859 0.008 0.294 / 0.083);
      --crimson-a4: color(display-p3 0.827 0.008 0.298 / 0.134);
      --crimson-a5: color(display-p3 0.753 0.008 0.275 / 0.189);
      --crimson-a6: color(display-p3 0.682 0.004 0.247 / 0.244);
      --crimson-a7: color(display-p3 0.62 0.004 0.251 / 0.318);
      --crimson-a8: color(display-p3 0.6 0.004 0.251 / 0.408);
      --crimson-a9: color(display-p3 0.776 0 0.298 / 0.702);
      --crimson-a10: color(display-p3 0.737 0 0.275 / 0.734);
      --crimson-a11: color(display-p3 0.731 0.195 0.388);
      --crimson-a12: color(display-p3 0.352 0.111 0.221);
      --pink-1: color(display-p3 0.998 0.989 0.996);
      --pink-2: color(display-p3 0.992 0.97 0.985);
      --pink-3: color(display-p3 0.981 0.917 0.96);
      --pink-4: color(display-p3 0.963 0.867 0.932);
      --pink-5: color(display-p3 0.939 0.815 0.899);
      --pink-6: color(display-p3 0.907 0.756 0.859);
      --pink-7: color(display-p3 0.869 0.683 0.81);
      --pink-8: color(display-p3 0.825 0.59 0.751);
      --pink-9: color(display-p3 0.775 0.297 0.61);
      --pink-10: color(display-p3 0.748 0.27 0.581);
      --pink-11: color(display-p3 0.698 0.219 0.528);
      --pink-12: color(display-p3 0.363 0.101 0.279);
      --pink-a1: color(display-p3 0.675 0.024 0.675 / 0.012);
      --pink-a2: color(display-p3 0.757 0.02 0.51 / 0.032);
      --pink-a3: color(display-p3 0.765 0.008 0.529 / 0.083);
      --pink-a4: color(display-p3 0.737 0.008 0.506 / 0.134);
      --pink-a5: color(display-p3 0.663 0.004 0.451 / 0.185);
      --pink-a6: color(display-p3 0.616 0.004 0.424 / 0.244);
      --pink-a7: color(display-p3 0.596 0.004 0.412 / 0.318);
      --pink-a8: color(display-p3 0.573 0.004 0.404 / 0.412);
      --pink-a9: color(display-p3 0.682 0 0.447 / 0.702);
      --pink-a10: color(display-p3 0.655 0 0.424 / 0.73);
      --pink-a11: color(display-p3 0.698 0.219 0.528);
      --pink-a12: color(display-p3 0.363 0.101 0.279);
      --plum-1: color(display-p3 0.995 0.988 0.999);
      --plum-2: color(display-p3 0.988 0.971 0.99);
      --plum-3: color(display-p3 0.973 0.923 0.98);
      --plum-4: color(display-p3 0.953 0.875 0.966);
      --plum-5: color(display-p3 0.926 0.825 0.945);
      --plum-6: color(display-p3 0.89 0.765 0.916);
      --plum-7: color(display-p3 0.84 0.686 0.877);
      --plum-8: color(display-p3 0.775 0.58 0.832);
      --plum-9: color(display-p3 0.624 0.313 0.708);
      --plum-10: color(display-p3 0.587 0.29 0.667);
      --plum-11: color(display-p3 0.543 0.263 0.619);
      --plum-12: color(display-p3 0.299 0.114 0.352);
      --plum-a1: color(display-p3 0.675 0.024 1 / 0.012);
      --plum-a2: color(display-p3 0.58 0.024 0.58 / 0.028);
      --plum-a3: color(display-p3 0.655 0.008 0.753 / 0.079);
      --plum-a4: color(display-p3 0.627 0.008 0.722 / 0.126);
      --plum-a5: color(display-p3 0.58 0.004 0.69 / 0.177);
      --plum-a6: color(display-p3 0.537 0.004 0.655 / 0.236);
      --plum-a7: color(display-p3 0.49 0.004 0.616 / 0.314);
      --plum-a8: color(display-p3 0.471 0.004 0.6 / 0.42);
      --plum-a9: color(display-p3 0.451 0 0.576 / 0.687);
      --plum-a10: color(display-p3 0.42 0 0.529 / 0.71);
      --plum-a11: color(display-p3 0.543 0.263 0.619);
      --plum-a12: color(display-p3 0.299 0.114 0.352);
      --purple-1: color(display-p3 0.995 0.988 0.996);
      --purple-2: color(display-p3 0.983 0.971 0.993);
      --purple-3: color(display-p3 0.963 0.931 0.989);
      --purple-4: color(display-p3 0.937 0.888 0.981);
      --purple-5: color(display-p3 0.904 0.837 0.966);
      --purple-6: color(display-p3 0.86 0.774 0.942);
      --purple-7: color(display-p3 0.799 0.69 0.91);
      --purple-8: color(display-p3 0.719 0.583 0.874);
      --purple-9: color(display-p3 0.523 0.318 0.751);
      --purple-10: color(display-p3 0.483 0.289 0.7);
      --purple-11: color(display-p3 0.473 0.281 0.687);
      --purple-12: color(display-p3 0.234 0.132 0.363);
      --purple-a1: color(display-p3 0.675 0.024 0.675 / 0.012);
      --purple-a2: color(display-p3 0.443 0.024 0.722 / 0.028);
      --purple-a3: color(display-p3 0.506 0.008 0.835 / 0.071);
      --purple-a4: color(display-p3 0.451 0.004 0.831 / 0.114);
      --purple-a5: color(display-p3 0.431 0.004 0.788 / 0.165);
      --purple-a6: color(display-p3 0.384 0.004 0.745 / 0.228);
      --purple-a7: color(display-p3 0.357 0.004 0.71 / 0.31);
      --purple-a8: color(display-p3 0.322 0.004 0.702 / 0.416);
      --purple-a9: color(display-p3 0.298 0 0.639 / 0.683);
      --purple-a10: color(display-p3 0.271 0 0.58 / 0.71);
      --purple-a11: color(display-p3 0.473 0.281 0.687);
      --purple-a12: color(display-p3 0.234 0.132 0.363);
      --violet-1: color(display-p3 0.991 0.988 0.995);
      --violet-2: color(display-p3 0.978 0.974 0.998);
      --violet-3: color(display-p3 0.953 0.943 0.993);
      --violet-4: color(display-p3 0.916 0.897 1);
      --violet-5: color(display-p3 0.876 0.851 1);
      --violet-6: color(display-p3 0.825 0.793 0.981);
      --violet-7: color(display-p3 0.752 0.712 0.943);
      --violet-8: color(display-p3 0.654 0.602 0.902);
      --violet-9: color(display-p3 0.417 0.341 0.784);
      --violet-10: color(display-p3 0.381 0.306 0.741);
      --violet-11: color(display-p3 0.383 0.317 0.702);
      --violet-12: color(display-p3 0.179 0.15 0.359);
      --violet-a1: color(display-p3 0.349 0.024 0.675 / 0.012);
      --violet-a2: color(display-p3 0.161 0.024 0.863 / 0.028);
      --violet-a3: color(display-p3 0.204 0.004 0.871 / 0.059);
      --violet-a4: color(display-p3 0.196 0.004 1 / 0.102);
      --violet-a5: color(display-p3 0.165 0.008 1 / 0.15);
      --violet-a6: color(display-p3 0.153 0.004 0.906 / 0.208);
      --violet-a7: color(display-p3 0.141 0.004 0.796 / 0.287);
      --violet-a8: color(display-p3 0.133 0.004 0.753 / 0.397);
      --violet-a9: color(display-p3 0.114 0 0.675 / 0.659);
      --violet-a10: color(display-p3 0.11 0 0.627 / 0.695);
      --violet-a11: color(display-p3 0.383 0.317 0.702);
      --violet-a12: color(display-p3 0.179 0.15 0.359);
      --iris-1: color(display-p3 0.992 0.992 0.999);
      --iris-2: color(display-p3 0.972 0.973 0.998);
      --iris-3: color(display-p3 0.943 0.945 0.992);
      --iris-4: color(display-p3 0.902 0.906 1);
      --iris-5: color(display-p3 0.857 0.861 1);
      --iris-6: color(display-p3 0.799 0.805 0.987);
      --iris-7: color(display-p3 0.721 0.727 0.955);
      --iris-8: color(display-p3 0.61 0.619 0.918);
      --iris-9: color(display-p3 0.357 0.357 0.81);
      --iris-10: color(display-p3 0.318 0.318 0.774);
      --iris-11: color(display-p3 0.337 0.326 0.748);
      --iris-12: color(display-p3 0.154 0.161 0.371);
      --iris-a1: color(display-p3 0.02 0.02 1 / 0.008);
      --iris-a2: color(display-p3 0.024 0.024 0.863 / 0.028);
      --iris-a3: color(display-p3 0.004 0.071 0.871 / 0.059);
      --iris-a4: color(display-p3 0.012 0.051 1 / 0.099);
      --iris-a5: color(display-p3 0.008 0.035 1 / 0.142);
      --iris-a6: color(display-p3 0 0.02 0.941 / 0.2);
      --iris-a7: color(display-p3 0.004 0.02 0.847 / 0.279);
      --iris-a8: color(display-p3 0.004 0.024 0.788 / 0.389);
      --iris-a9: color(display-p3 0 0 0.706 / 0.644);
      --iris-a10: color(display-p3 0 0 0.667 / 0.683);
      --iris-a11: color(display-p3 0.337 0.326 0.748);
      --iris-a12: color(display-p3 0.154 0.161 0.371);
      --cyan-1: color(display-p3 0.982 0.992 0.996);
      --cyan-2: color(display-p3 0.955 0.981 0.984);
      --cyan-3: color(display-p3 0.888 0.965 0.975);
      --cyan-4: color(display-p3 0.821 0.941 0.959);
      --cyan-5: color(display-p3 0.751 0.907 0.935);
      --cyan-6: color(display-p3 0.671 0.862 0.9);
      --cyan-7: color(display-p3 0.564 0.8 0.854);
      --cyan-8: color(display-p3 0.388 0.715 0.798);
      --cyan-9: color(display-p3 0.282 0.627 0.765);
      --cyan-10: color(display-p3 0.264 0.583 0.71);
      --cyan-11: color(display-p3 0.08 0.48 0.63);
      --cyan-12: color(display-p3 0.108 0.232 0.277);
      --cyan-a1: color(display-p3 0.02 0.608 0.804 / 0.02);
      --cyan-a2: color(display-p3 0.02 0.557 0.647 / 0.044);
      --cyan-a3: color(display-p3 0.004 0.694 0.796 / 0.114);
      --cyan-a4: color(display-p3 0.004 0.678 0.784 / 0.181);
      --cyan-a5: color(display-p3 0.004 0.624 0.733 / 0.248);
      --cyan-a6: color(display-p3 0.004 0.584 0.706 / 0.33);
      --cyan-a7: color(display-p3 0.004 0.541 0.667 / 0.436);
      --cyan-a8: color(display-p3 0 0.533 0.667 / 0.612);
      --cyan-a9: color(display-p3 0 0.482 0.675 / 0.718);
      --cyan-a10: color(display-p3 0 0.435 0.608 / 0.738);
      --cyan-a11: color(display-p3 0.08 0.48 0.63);
      --cyan-a12: color(display-p3 0.108 0.232 0.277);
      --teal-1: color(display-p3 0.983 0.996 0.992);
      --teal-2: color(display-p3 0.958 0.983 0.976);
      --teal-3: color(display-p3 0.895 0.971 0.952);
      --teal-4: color(display-p3 0.831 0.949 0.92);
      --teal-5: color(display-p3 0.761 0.914 0.878);
      --teal-6: color(display-p3 0.682 0.864 0.825);
      --teal-7: color(display-p3 0.581 0.798 0.756);
      --teal-8: color(display-p3 0.433 0.716 0.671);
      --teal-9: color(display-p3 0.297 0.637 0.581);
      --teal-10: color(display-p3 0.275 0.599 0.542);
      --teal-11: color(display-p3 0.08 0.5 0.43);
      --teal-12: color(display-p3 0.11 0.235 0.219);
      --teal-a1: color(display-p3 0.024 0.757 0.514 / 0.016);
      --teal-a2: color(display-p3 0.02 0.647 0.467 / 0.044);
      --teal-a3: color(display-p3 0.004 0.741 0.557 / 0.106);
      --teal-a4: color(display-p3 0.004 0.702 0.537 / 0.169);
      --teal-a5: color(display-p3 0.004 0.643 0.494 / 0.24);
      --teal-a6: color(display-p3 0.004 0.569 0.447 / 0.318);
      --teal-a7: color(display-p3 0.004 0.518 0.424 / 0.42);
      --teal-a8: color(display-p3 0 0.506 0.424 / 0.569);
      --teal-a9: color(display-p3 0 0.482 0.404 / 0.702);
      --teal-a10: color(display-p3 0 0.451 0.369 / 0.726);
      --teal-a11: color(display-p3 0.08 0.5 0.43);
      --teal-a12: color(display-p3 0.11 0.235 0.219);
      --jade-1: color(display-p3 0.986 0.996 0.992);
      --jade-2: color(display-p3 0.962 0.983 0.969);
      --jade-3: color(display-p3 0.912 0.965 0.932);
      --jade-4: color(display-p3 0.858 0.941 0.893);
      --jade-5: color(display-p3 0.795 0.909 0.847);
      --jade-6: color(display-p3 0.715 0.864 0.791);
      --jade-7: color(display-p3 0.603 0.802 0.718);
      --jade-8: color(display-p3 0.44 0.72 0.629);
      --jade-9: color(display-p3 0.319 0.63 0.521);
      --jade-10: color(display-p3 0.299 0.592 0.488);
      --jade-11: color(display-p3 0.15 0.5 0.37);
      --jade-12: color(display-p3 0.142 0.229 0.194);
      --jade-a1: color(display-p3 0.024 0.757 0.514 / 0.016);
      --jade-a2: color(display-p3 0.024 0.612 0.22 / 0.04);
      --jade-a3: color(display-p3 0.012 0.596 0.235 / 0.087);
      --jade-a4: color(display-p3 0.008 0.588 0.255 / 0.142);
      --jade-a5: color(display-p3 0.004 0.561 0.251 / 0.204);
      --jade-a6: color(display-p3 0.004 0.525 0.278 / 0.287);
      --jade-a7: color(display-p3 0.004 0.506 0.29 / 0.397);
      --jade-a8: color(display-p3 0 0.506 0.337 / 0.561);
      --jade-a9: color(display-p3 0 0.459 0.298 / 0.683);
      --jade-a10: color(display-p3 0 0.42 0.271 / 0.702);
      --jade-a11: color(display-p3 0.15 0.5 0.37);
      --jade-a12: color(display-p3 0.142 0.229 0.194);
      --green-1: color(display-p3 0.986 0.996 0.989);
      --green-2: color(display-p3 0.963 0.983 0.967);
      --green-3: color(display-p3 0.913 0.964 0.925);
      --green-4: color(display-p3 0.859 0.94 0.879);
      --green-5: color(display-p3 0.796 0.907 0.826);
      --green-6: color(display-p3 0.718 0.863 0.761);
      --green-7: color(display-p3 0.61 0.801 0.675);
      --green-8: color(display-p3 0.451 0.715 0.559);
      --green-9: color(display-p3 0.332 0.634 0.442);
      --green-10: color(display-p3 0.308 0.595 0.417);
      --green-11: color(display-p3 0.19 0.5 0.32);
      --green-12: color(display-p3 0.132 0.228 0.18);
      --green-a1: color(display-p3 0.024 0.757 0.267 / 0.016);
      --green-a2: color(display-p3 0.024 0.565 0.129 / 0.036);
      --green-a3: color(display-p3 0.012 0.596 0.145 / 0.087);
      --green-a4: color(display-p3 0.008 0.588 0.145 / 0.142);
      --green-a5: color(display-p3 0.004 0.541 0.157 / 0.204);
      --green-a6: color(display-p3 0.004 0.518 0.157 / 0.283);
      --green-a7: color(display-p3 0.004 0.486 0.165 / 0.389);
      --green-a8: color(display-p3 0 0.478 0.2 / 0.55);
      --green-a9: color(display-p3 0 0.455 0.165 / 0.667);
      --green-a10: color(display-p3 0 0.416 0.153 / 0.691);
      --green-a11: color(display-p3 0.19 0.5 0.32);
      --green-a12: color(display-p3 0.132 0.228 0.18);
      --grass-1: color(display-p3 0.986 0.996 0.985);
      --grass-2: color(display-p3 0.966 0.983 0.964);
      --grass-3: color(display-p3 0.923 0.965 0.917);
      --grass-4: color(display-p3 0.872 0.94 0.865);
      --grass-5: color(display-p3 0.811 0.908 0.802);
      --grass-6: color(display-p3 0.733 0.864 0.724);
      --grass-7: color(display-p3 0.628 0.803 0.622);
      --grass-8: color(display-p3 0.477 0.72 0.482);
      --grass-9: color(display-p3 0.38 0.647 0.378);
      --grass-10: color(display-p3 0.344 0.598 0.342);
      --grass-11: color(display-p3 0.263 0.488 0.261);
      --grass-12: color(display-p3 0.151 0.233 0.153);
      --grass-a1: color(display-p3 0.024 0.757 0.024 / 0.016);
      --grass-a2: color(display-p3 0.024 0.565 0.024 / 0.036);
      --grass-a3: color(display-p3 0.059 0.576 0.008 / 0.083);
      --grass-a4: color(display-p3 0.035 0.565 0.008 / 0.134);
      --grass-a5: color(display-p3 0.047 0.545 0.008 / 0.197);
      --grass-a6: color(display-p3 0.031 0.502 0.004 / 0.275);
      --grass-a7: color(display-p3 0.012 0.482 0.004 / 0.377);
      --grass-a8: color(display-p3 0 0.467 0.008 / 0.522);
      --grass-a9: color(display-p3 0.008 0.435 0 / 0.624);
      --grass-a10: color(display-p3 0.008 0.388 0 / 0.659);
      --grass-a11: color(display-p3 0.263 0.488 0.261);
      --grass-a12: color(display-p3 0.151 0.233 0.153);
      --brown-1: color(display-p3 0.995 0.992 0.989);
      --brown-2: color(display-p3 0.987 0.976 0.964);
      --brown-3: color(display-p3 0.959 0.936 0.909);
      --brown-4: color(display-p3 0.934 0.897 0.855);
      --brown-5: color(display-p3 0.909 0.856 0.798);
      --brown-6: color(display-p3 0.88 0.808 0.73);
      --brown-7: color(display-p3 0.841 0.742 0.639);
      --brown-8: color(display-p3 0.782 0.647 0.514);
      --brown-9: color(display-p3 0.651 0.505 0.368);
      --brown-10: color(display-p3 0.601 0.465 0.344);
      --brown-11: color(display-p3 0.485 0.374 0.288);
      --brown-12: color(display-p3 0.236 0.202 0.183);
      --brown-a1: color(display-p3 0.675 0.349 0.024 / 0.012);
      --brown-a2: color(display-p3 0.675 0.349 0.024 / 0.036);
      --brown-a3: color(display-p3 0.573 0.314 0.012 / 0.091);
      --brown-a4: color(display-p3 0.545 0.302 0.008 / 0.146);
      --brown-a5: color(display-p3 0.561 0.29 0.004 / 0.204);
      --brown-a6: color(display-p3 0.553 0.294 0.004 / 0.271);
      --brown-a7: color(display-p3 0.557 0.286 0.004 / 0.361);
      --brown-a8: color(display-p3 0.549 0.275 0.004 / 0.487);
      --brown-a9: color(display-p3 0.447 0.22 0 / 0.632);
      --brown-a10: color(display-p3 0.388 0.188 0 / 0.655);
      --brown-a11: color(display-p3 0.485 0.374 0.288);
      --brown-a12: color(display-p3 0.236 0.202 0.183);
      --sky-1: color(display-p3 0.98 0.995 0.999);
      --sky-2: color(display-p3 0.953 0.98 0.99);
      --sky-3: color(display-p3 0.899 0.963 0.989);
      --sky-4: color(display-p3 0.842 0.937 0.977);
      --sky-5: color(display-p3 0.777 0.9 0.954);
      --sky-6: color(display-p3 0.701 0.851 0.921);
      --sky-7: color(display-p3 0.604 0.785 0.879);
      --sky-8: color(display-p3 0.457 0.696 0.829);
      --sky-9: color(display-p3 0.585 0.877 0.983);
      --sky-10: color(display-p3 0.555 0.845 0.959);
      --sky-11: color(display-p3 0.193 0.448 0.605);
      --sky-12: color(display-p3 0.145 0.241 0.329);
      --sky-a1: color(display-p3 0.02 0.804 1 / 0.02);
      --sky-a2: color(display-p3 0.024 0.592 0.757 / 0.048);
      --sky-a3: color(display-p3 0.004 0.655 0.886 / 0.102);
      --sky-a4: color(display-p3 0.004 0.604 0.851 / 0.157);
      --sky-a5: color(display-p3 0.004 0.565 0.792 / 0.224);
      --sky-a6: color(display-p3 0.004 0.502 0.737 / 0.299);
      --sky-a7: color(display-p3 0.004 0.459 0.694 / 0.397);
      --sky-a8: color(display-p3 0 0.435 0.682 / 0.542);
      --sky-a9: color(display-p3 0.004 0.71 0.965 / 0.416);
      --sky-a10: color(display-p3 0.004 0.647 0.914 / 0.444);
      --sky-a11: color(display-p3 0.193 0.448 0.605);
      --sky-a12: color(display-p3 0.145 0.241 0.329);
      --mint-1: color(display-p3 0.98 0.995 0.992);
      --mint-2: color(display-p3 0.957 0.985 0.977);
      --mint-3: color(display-p3 0.888 0.972 0.95);
      --mint-4: color(display-p3 0.819 0.951 0.916);
      --mint-5: color(display-p3 0.747 0.918 0.873);
      --mint-6: color(display-p3 0.668 0.87 0.818);
      --mint-7: color(display-p3 0.567 0.805 0.744);
      --mint-8: color(display-p3 0.42 0.724 0.649);
      --mint-9: color(display-p3 0.62 0.908 0.834);
      --mint-10: color(display-p3 0.585 0.871 0.797);
      --mint-11: color(display-p3 0.203 0.463 0.397);
      --mint-12: color(display-p3 0.136 0.259 0.236);
      --mint-a1: color(display-p3 0.02 0.804 0.608 / 0.02);
      --mint-a2: color(display-p3 0.02 0.647 0.467 / 0.044);
      --mint-a3: color(display-p3 0.004 0.761 0.553 / 0.114);
      --mint-a4: color(display-p3 0.004 0.741 0.545 / 0.181);
      --mint-a5: color(display-p3 0.004 0.678 0.51 / 0.255);
      --mint-a6: color(display-p3 0.004 0.616 0.463 / 0.334);
      --mint-a7: color(display-p3 0.004 0.549 0.412 / 0.432);
      --mint-a8: color(display-p3 0 0.529 0.392 / 0.581);
      --mint-a9: color(display-p3 0.004 0.765 0.569 / 0.381);
      --mint-a10: color(display-p3 0.004 0.69 0.51 / 0.416);
      --mint-a11: color(display-p3 0.203 0.463 0.397);
      --mint-a12: color(display-p3 0.136 0.259 0.236);
      --yellow-1: color(display-p3 0.992 0.992 0.978);
      --yellow-2: color(display-p3 0.995 0.99 0.922);
      --yellow-3: color(display-p3 0.997 0.982 0.749);
      --yellow-4: color(display-p3 0.992 0.953 0.627);
      --yellow-5: color(display-p3 0.984 0.91 0.51);
      --yellow-6: color(display-p3 0.934 0.847 0.474);
      --yellow-7: color(display-p3 0.876 0.785 0.46);
      --yellow-8: color(display-p3 0.811 0.689 0.313);
      --yellow-9: color(display-p3 1 0.92 0.22);
      --yellow-10: color(display-p3 0.977 0.868 0.291);
      --yellow-11: color(display-p3 0.6 0.44 0);
      --yellow-12: color(display-p3 0.271 0.233 0.137);
      --yellow-a1: color(display-p3 0.675 0.675 0.024 / 0.024);
      --yellow-a2: color(display-p3 0.953 0.855 0.008 / 0.079);
      --yellow-a3: color(display-p3 0.988 0.925 0.004 / 0.251);
      --yellow-a4: color(display-p3 0.98 0.875 0.004 / 0.373);
      --yellow-a5: color(display-p3 0.969 0.816 0.004 / 0.491);
      --yellow-a6: color(display-p3 0.875 0.71 0 / 0.526);
      --yellow-a7: color(display-p3 0.769 0.604 0 / 0.542);
      --yellow-a8: color(display-p3 0.725 0.549 0 / 0.687);
      --yellow-a9: color(display-p3 1 0.898 0 / 0.781);
      --yellow-a10: color(display-p3 0.969 0.812 0 / 0.71);
      --yellow-a11: color(display-p3 0.6 0.44 0);
      --yellow-a12: color(display-p3 0.271 0.233 0.137);
      --amber-1: color(display-p3 0.995 0.992 0.985);
      --amber-2: color(display-p3 0.994 0.986 0.921);
      --amber-3: color(display-p3 0.994 0.969 0.782);
      --amber-4: color(display-p3 0.989 0.937 0.65);
      --amber-5: color(display-p3 0.97 0.902 0.527);
      --amber-6: color(display-p3 0.936 0.844 0.506);
      --amber-7: color(display-p3 0.89 0.762 0.443);
      --amber-8: color(display-p3 0.85 0.65 0.3);
      --amber-9: color(display-p3 1 0.77 0.26);
      --amber-10: color(display-p3 0.959 0.741 0.274);
      --amber-11: color(display-p3 0.64 0.4 0);
      --amber-12: color(display-p3 0.294 0.208 0.145);
      --amber-a1: color(display-p3 0.757 0.514 0.024 / 0.016);
      --amber-a2: color(display-p3 0.902 0.804 0.008 / 0.079);
      --amber-a3: color(display-p3 0.965 0.859 0.004 / 0.22);
      --amber-a4: color(display-p3 0.969 0.82 0.004 / 0.35);
      --amber-a5: color(display-p3 0.933 0.796 0.004 / 0.475);
      --amber-a6: color(display-p3 0.875 0.682 0.004 / 0.495);
      --amber-a7: color(display-p3 0.804 0.573 0 / 0.557);
      --amber-a8: color(display-p3 0.788 0.502 0 / 0.699);
      --amber-a9: color(display-p3 1 0.686 0 / 0.742);
      --amber-a10: color(display-p3 0.945 0.643 0 / 0.726);
      --amber-a11: color(display-p3 0.64 0.4 0);
      --amber-a12: color(display-p3 0.294 0.208 0.145);
      --gold-1: color(display-p3 0.992 0.992 0.989);
      --gold-2: color(display-p3 0.98 0.976 0.953);
      --gold-3: color(display-p3 0.947 0.94 0.909);
      --gold-4: color(display-p3 0.914 0.904 0.865);
      --gold-5: color(display-p3 0.88 0.865 0.816);
      --gold-6: color(display-p3 0.84 0.818 0.756);
      --gold-7: color(display-p3 0.788 0.753 0.677);
      --gold-8: color(display-p3 0.715 0.66 0.565);
      --gold-9: color(display-p3 0.579 0.517 0.41);
      --gold-10: color(display-p3 0.538 0.479 0.38);
      --gold-11: color(display-p3 0.433 0.386 0.305);
      --gold-12: color(display-p3 0.227 0.209 0.173);
      --gold-a1: color(display-p3 0.349 0.349 0.024 / 0.012);
      --gold-a2: color(display-p3 0.592 0.514 0.024 / 0.048);
      --gold-a3: color(display-p3 0.4 0.357 0.012 / 0.091);
      --gold-a4: color(display-p3 0.357 0.298 0.008 / 0.134);
      --gold-a5: color(display-p3 0.345 0.282 0.004 / 0.185);
      --gold-a6: color(display-p3 0.341 0.263 0.004 / 0.244);
      --gold-a7: color(display-p3 0.345 0.235 0.004 / 0.322);
      --gold-a8: color(display-p3 0.345 0.22 0.004 / 0.436);
      --gold-a9: color(display-p3 0.286 0.18 0 / 0.589);
      --gold-a10: color(display-p3 0.255 0.161 0 / 0.62);
      --gold-a11: color(display-p3 0.433 0.386 0.305);
      --gold-a12: color(display-p3 0.227 0.209 0.173);
      --bronze-1: color(display-p3 0.991 0.988 0.988);
      --bronze-2: color(display-p3 0.989 0.97 0.961);
      --bronze-3: color(display-p3 0.958 0.932 0.919);
      --bronze-4: color(display-p3 0.929 0.894 0.877);
      --bronze-5: color(display-p3 0.898 0.853 0.832);
      --bronze-6: color(display-p3 0.861 0.805 0.778);
      --bronze-7: color(display-p3 0.812 0.739 0.706);
      --bronze-8: color(display-p3 0.741 0.647 0.606);
      --bronze-9: color(display-p3 0.611 0.507 0.455);
      --bronze-10: color(display-p3 0.563 0.461 0.414);
      --bronze-11: color(display-p3 0.471 0.373 0.336);
      --bronze-12: color(display-p3 0.251 0.191 0.172);
      --bronze-a1: color(display-p3 0.349 0.024 0.024 / 0.012);
      --bronze-a2: color(display-p3 0.71 0.22 0.024 / 0.04);
      --bronze-a3: color(display-p3 0.482 0.2 0.008 / 0.083);
      --bronze-a4: color(display-p3 0.424 0.133 0.004 / 0.122);
      --bronze-a5: color(display-p3 0.4 0.145 0.004 / 0.169);
      --bronze-a6: color(display-p3 0.388 0.125 0.004 / 0.224);
      --bronze-a7: color(display-p3 0.365 0.11 0.004 / 0.295);
      --bronze-a8: color(display-p3 0.341 0.102 0.004 / 0.393);
      --bronze-a9: color(display-p3 0.29 0.094 0 / 0.546);
      --bronze-a10: color(display-p3 0.255 0.082 0 / 0.585);
      --bronze-a11: color(display-p3 0.471 0.373 0.336);
      --bronze-a12: color(display-p3 0.251 0.191 0.172);
      --gray-1: color(display-p3 0.988 0.988 0.988);
      --gray-2: color(display-p3 0.975 0.975 0.975);
      --gray-3: color(display-p3 0.939 0.939 0.939);
      --gray-4: color(display-p3 0.908 0.908 0.908);
      --gray-5: color(display-p3 0.88 0.88 0.88);
      --gray-6: color(display-p3 0.849 0.849 0.849);
      --gray-7: color(display-p3 0.807 0.807 0.807);
      --gray-8: color(display-p3 0.732 0.732 0.732);
      --gray-9: color(display-p3 0.553 0.553 0.553);
      --gray-10: color(display-p3 0.512 0.512 0.512);
      --gray-11: color(display-p3 0.392 0.392 0.392);
      --gray-12: color(display-p3 0.125 0.125 0.125);
      --gray-a1: color(display-p3 0 0 0 / 0.012);
      --gray-a2: color(display-p3 0 0 0 / 0.024);
      --gray-a3: color(display-p3 0 0 0 / 0.063);
      --gray-a4: color(display-p3 0 0 0 / 0.09);
      --gray-a5: color(display-p3 0 0 0 / 0.122);
      --gray-a6: color(display-p3 0 0 0 / 0.153);
      --gray-a7: color(display-p3 0 0 0 / 0.192);
      --gray-a8: color(display-p3 0 0 0 / 0.267);
      --gray-a9: color(display-p3 0 0 0 / 0.447);
      --gray-a10: color(display-p3 0 0 0 / 0.486);
      --gray-a11: color(display-p3 0 0 0 / 0.608);
      --gray-a12: color(display-p3 0 0 0 / 0.875);
      --mauve-1: color(display-p3 0.991 0.988 0.992);
      --mauve-2: color(display-p3 0.98 0.976 0.984);
      --mauve-3: color(display-p3 0.946 0.938 0.952);
      --mauve-4: color(display-p3 0.915 0.906 0.925);
      --mauve-5: color(display-p3 0.886 0.876 0.901);
      --mauve-6: color(display-p3 0.856 0.846 0.875);
      --mauve-7: color(display-p3 0.814 0.804 0.84);
      --mauve-8: color(display-p3 0.735 0.728 0.777);
      --mauve-9: color(display-p3 0.555 0.549 0.596);
      --mauve-10: color(display-p3 0.514 0.508 0.552);
      --mauve-11: color(display-p3 0.395 0.388 0.424);
      --mauve-12: color(display-p3 0.128 0.122 0.147);
      --mauve-a1: color(display-p3 0.349 0.024 0.349 / 0.012);
      --mauve-a2: color(display-p3 0.184 0.024 0.349 / 0.024);
      --mauve-a3: color(display-p3 0.129 0.008 0.255 / 0.063);
      --mauve-a4: color(display-p3 0.094 0.012 0.216 / 0.095);
      --mauve-a5: color(display-p3 0.098 0.008 0.224 / 0.126);
      --mauve-a6: color(display-p3 0.055 0.004 0.18 / 0.153);
      --mauve-a7: color(display-p3 0.067 0.008 0.184 / 0.197);
      --mauve-a8: color(display-p3 0.02 0.004 0.176 / 0.271);
      --mauve-a9: color(display-p3 0.02 0.004 0.106 / 0.451);
      --mauve-a10: color(display-p3 0.012 0.004 0.09 / 0.491);
      --mauve-a11: color(display-p3 0.016 0 0.059 / 0.612);
      --mauve-a12: color(display-p3 0.008 0 0.027 / 0.879);
      --slate-1: color(display-p3 0.988 0.988 0.992);
      --slate-2: color(display-p3 0.976 0.976 0.984);
      --slate-3: color(display-p3 0.94 0.941 0.953);
      --slate-4: color(display-p3 0.908 0.909 0.925);
      --slate-5: color(display-p3 0.88 0.881 0.901);
      --slate-6: color(display-p3 0.85 0.852 0.876);
      --slate-7: color(display-p3 0.805 0.808 0.838);
      --slate-8: color(display-p3 0.727 0.733 0.773);
      --slate-9: color(display-p3 0.547 0.553 0.592);
      --slate-10: color(display-p3 0.503 0.512 0.549);
      --slate-11: color(display-p3 0.379 0.392 0.421);
      --slate-12: color(display-p3 0.113 0.125 0.14);
      --slate-a1: color(display-p3 0.024 0.024 0.349 / 0.012);
      --slate-a2: color(display-p3 0.024 0.024 0.349 / 0.024);
      --slate-a3: color(display-p3 0.004 0.004 0.204 / 0.059);
      --slate-a4: color(display-p3 0.012 0.012 0.184 / 0.091);
      --slate-a5: color(display-p3 0.004 0.039 0.2 / 0.122);
      --slate-a6: color(display-p3 0.008 0.008 0.165 / 0.15);
      --slate-a7: color(display-p3 0.008 0.027 0.184 / 0.197);
      --slate-a8: color(display-p3 0.004 0.031 0.176 / 0.275);
      --slate-a9: color(display-p3 0.004 0.02 0.106 / 0.455);
      --slate-a10: color(display-p3 0.004 0.027 0.098 / 0.499);
      --slate-a11: color(display-p3 0 0.02 0.063 / 0.62);
      --slate-a12: color(display-p3 0 0.012 0.031 / 0.887);
      --sage-1: color(display-p3 0.986 0.992 0.988);
      --sage-2: color(display-p3 0.97 0.977 0.974);
      --sage-3: color(display-p3 0.935 0.944 0.94);
      --sage-4: color(display-p3 0.904 0.913 0.909);
      --sage-5: color(display-p3 0.875 0.885 0.88);
      --sage-6: color(display-p3 0.844 0.854 0.849);
      --sage-7: color(display-p3 0.8 0.811 0.806);
      --sage-8: color(display-p3 0.725 0.738 0.732);
      --sage-9: color(display-p3 0.531 0.556 0.546);
      --sage-10: color(display-p3 0.492 0.515 0.506);
      --sage-11: color(display-p3 0.377 0.395 0.389);
      --sage-12: color(display-p3 0.107 0.129 0.118);
      --sage-a1: color(display-p3 0.024 0.514 0.267 / 0.016);
      --sage-a2: color(display-p3 0.02 0.267 0.145 / 0.032);
      --sage-a3: color(display-p3 0.008 0.184 0.125 / 0.067);
      --sage-a4: color(display-p3 0.012 0.094 0.051 / 0.095);
      --sage-a5: color(display-p3 0.008 0.098 0.035 / 0.126);
      --sage-a6: color(display-p3 0.004 0.078 0.027 / 0.157);
      --sage-a7: color(display-p3 0 0.059 0.039 / 0.2);
      --sage-a8: color(display-p3 0.004 0.047 0.031 / 0.275);
      --sage-a9: color(display-p3 0.004 0.059 0.035 / 0.471);
      --sage-a10: color(display-p3 0 0.047 0.031 / 0.51);
      --sage-a11: color(display-p3 0 0.031 0.02 / 0.624);
      --sage-a12: color(display-p3 0 0.027 0.012 / 0.895);
      --olive-1: color(display-p3 0.989 0.992 0.989);
      --olive-2: color(display-p3 0.974 0.98 0.973);
      --olive-3: color(display-p3 0.939 0.945 0.937);
      --olive-4: color(display-p3 0.907 0.914 0.905);
      --olive-5: color(display-p3 0.878 0.885 0.875);
      --olive-6: color(display-p3 0.846 0.855 0.843);
      --olive-7: color(display-p3 0.803 0.812 0.8);
      --olive-8: color(display-p3 0.727 0.738 0.723);
      --olive-9: color(display-p3 0.541 0.556 0.532);
      --olive-10: color(display-p3 0.5 0.515 0.491);
      --olive-11: color(display-p3 0.38 0.395 0.374);
      --olive-12: color(display-p3 0.117 0.129 0.111);
      --olive-a1: color(display-p3 0.024 0.349 0.024 / 0.012);
      --olive-a2: color(display-p3 0.024 0.302 0.024 / 0.028);
      --olive-a3: color(display-p3 0.008 0.129 0.008 / 0.063);
      --olive-a4: color(display-p3 0.012 0.094 0.012 / 0.095);
      --olive-a5: color(display-p3 0.035 0.098 0.008 / 0.126);
      --olive-a6: color(display-p3 0.027 0.078 0.004 / 0.157);
      --olive-a7: color(display-p3 0.02 0.059 0 / 0.2);
      --olive-a8: color(display-p3 0.02 0.059 0.004 / 0.279);
      --olive-a9: color(display-p3 0.02 0.051 0.004 / 0.467);
      --olive-a10: color(display-p3 0.024 0.047 0 / 0.51);
      --olive-a11: color(display-p3 0.012 0.039 0 / 0.628);
      --olive-a12: color(display-p3 0.008 0.024 0 / 0.891);
      --sand-1: color(display-p3 0.992 0.992 0.989);
      --sand-2: color(display-p3 0.977 0.977 0.973);
      --sand-3: color(display-p3 0.943 0.942 0.936);
      --sand-4: color(display-p3 0.913 0.912 0.903);
      --sand-5: color(display-p3 0.885 0.883 0.873);
      --sand-6: color(display-p3 0.854 0.852 0.839);
      --sand-7: color(display-p3 0.813 0.81 0.794);
      --sand-8: color(display-p3 0.738 0.734 0.713);
      --sand-9: color(display-p3 0.553 0.553 0.528);
      --sand-10: color(display-p3 0.511 0.511 0.488);
      --sand-11: color(display-p3 0.388 0.388 0.37);
      --sand-12: color(display-p3 0.129 0.126 0.111);
      --sand-a1: color(display-p3 0.349 0.349 0.024 / 0.012);
      --sand-a2: color(display-p3 0.161 0.161 0.024 / 0.028);
      --sand-a3: color(display-p3 0.067 0.067 0.008 / 0.063);
      --sand-a4: color(display-p3 0.129 0.129 0.012 / 0.099);
      --sand-a5: color(display-p3 0.098 0.067 0.008 / 0.126);
      --sand-a6: color(display-p3 0.102 0.075 0.004 / 0.161);
      --sand-a7: color(display-p3 0.098 0.098 0.004 / 0.208);
      --sand-a8: color(display-p3 0.086 0.075 0.004 / 0.287);
      --sand-a9: color(display-p3 0.051 0.051 0.004 / 0.471);
      --sand-a10: color(display-p3 0.047 0.047 0 / 0.514);
      --sand-a11: color(display-p3 0.031 0.031 0 / 0.632);
      --sand-a12: color(display-p3 0.024 0.02 0 / 0.891);
      --blue-1: color(display-p3 0.9907 0.9925 0.9965);
      --blue-2: color(display-p3 0.9679 0.9781 0.9997);
      --blue-3: color(display-p3 0.9281 0.9496 0.9956);
      --blue-4: color(display-p3 0.8754 0.9169 1);
      --blue-5: color(display-p3 0.8137 0.8753 1);
      --blue-6: color(display-p3 0.7387 0.8226 1);
      --blue-7: color(display-p3 0.6508 0.7498 0.9743);
      --blue-8: color(display-p3 0.5268 0.6493 0.9375);
      --blue-9: color(display-p3 0.1632 0.3246 0.8163);
      --blue-10: color(display-p3 0.108 0.2592 0.7498);
      --blue-11: color(display-p3 0.2053 0.3558 0.7842);
      --blue-12: color(display-p3 0.1088 0.1781 0.3608);
      --blue-a1: color(display-p3 0.0196 0.0196 0.5098 / 0.008);
      --blue-a2: color(display-p3 0.0196 0.3882 0.8784 / 0.032);
      --blue-a3: color(display-p3 0.0078 0.3216 0.949 / 0.075);
      --blue-a4: color(display-p3 0.0078 0.349 0.9412 / 0.126);
      --blue-a5: color(display-p3 0.0039 0.3255 0.9373 / 0.185);
      --blue-a6: color(display-p3 0.0039 0.3294 0.9412 / 0.263);
      --blue-a7: color(display-p3 0.0039 0.2824 0.9216 / 0.35);
      --blue-a8: color(display-p3 0.0039 0.2667 0.8706 / 0.475);
      --blue-a9: color(display-p3 0 0.1922 0.7804 / 0.836);
      --blue-a10: color(display-p3 0 0.1725 0.7216 / 0.895);
      --blue-a11: color(display-p3 0 0.1922 0.7294 / 0.797);
      --blue-a12: color(display-p3 0 0.0745 0.2824 / 0.891);
      --orange-1: color(display-p3 0.9978 0.9885 0.9856);
      --orange-2: color(display-p3 0.9992 0.9689 0.9596);
      --orange-3: color(display-p3 1 0.9209 0.8965);
      --orange-4: color(display-p3 1 0.8524 0.8);
      --orange-5: color(display-p3 1 0.7925 0.724);
      --orange-6: color(display-p3 0.9936 0.7338 0.6567);
      --orange-7: color(display-p3 0.9501 0.6574 0.572);
      --orange-8: color(display-p3 0.9107 0.5559 0.4555);
      --orange-9: color(display-p3 0.9049 0.3335 0.1831);
      --orange-10: color(display-p3 0.8535 0.2731 0.1143);
      --orange-11: color(display-p3 0.7978 0.2236 0.0507);
      --orange-12: color(display-p3 0.3453 0.1605 0.1105);
      --orange-a1: color(display-p3 0.7569 0.2667 0.0235 / 0.016);
      --orange-a2: color(display-p3 0.9137 0.2902 0.0196 / 0.044);
      --orange-a3: color(display-p3 0.8471 0.2314 0.0039 / 0.102);
      --orange-a4: color(display-p3 0.8824 0.2471 0.0078 / 0.197);
      --orange-a5: color(display-p3 0.8863 0.2392 0.0039 / 0.267);
      --orange-a6: color(display-p3 0.8745 0.2314 0.0039 / 0.346);
      --orange-a7: color(display-p3 0.8824 0.1961 0.0039 / 0.428);
      --orange-a8: color(display-p3 0.8353 0.1882 0 / 0.546);
      --orange-a9: color(display-p3 0.8863 0.1843 0 / 0.816);
      --orange-a10: color(display-p3 0.8275 0.1608 0 / 0.863);
      --orange-a11: color(display-p3 0.7686 0.1176 0 / 0.879);
      --orange-a12: color(display-p3 0.2627 0.0549 0 / 0.891);
      --lemon-1: color(display-p3 0.9897 0.9925 0.9788);
      --lemon-2: color(display-p3 0.9782 0.9885 0.9371);
      --lemon-3: color(display-p3 0.9489 0.9835 0.7941);
      --lemon-4: color(display-p3 0.9146 0.9617 0.6859);
      --lemon-5: color(display-p3 0.8728 0.9274 0.5873);
      --lemon-6: color(display-p3 0.8164 0.8701 0.5296);
      --lemon-7: color(display-p3 0.7518 0.8027 0.4756);
      --lemon-8: color(display-p3 0.6642 0.7184 0.3325);
      --lemon-9: color(display-p3 0.8632 0.9421 0.2941);
      --lemon-10: color(display-p3 0.8258 0.9005 0.2994);
      --lemon-11: color(display-p3 0.4463 0.4898 0.1262);
      --lemon-12: color(display-p3 0.231 0.2493 0.1314);
      --lemon-a1: color(display-p3 0.5137 0.6745 0.0235 / 0.024);
      --lemon-a2: color(display-p3 0.6902 0.8157 0.0078 / 0.063);
      --lemon-a3: color(display-p3 0.7569 0.9255 0.0039 / 0.208);
      --lemon-a4: color(display-p3 0.7255 0.8784 0.0039 / 0.314);
      --lemon-a5: color(display-p3 0.6863 0.8196 0.0039 / 0.412);
      --lemon-a6: color(display-p3 0.6118 0.7176 0.0039 / 0.471);
      --lemon-a7: color(display-p3 0.5294 0.6196 0 / 0.526);
      --lemon-a8: color(display-p3 0.4941 0.5765 0 / 0.667);
      --lemon-a9: color(display-p3 0.8039 0.9137 0 / 0.695);
      --lemon-a10: color(display-p3 0.7569 0.8549 0 / 0.702);
      --lemon-a11: color(display-p3 0.3569 0.4078 0 / 0.859);
      --lemon-a12: color(display-p3 0.1137 0.1373 0 / 0.867);
      --indigo-1: color(display-p3 0.9914 0.9917 1);
      --indigo-2: color(display-p3 0.9707 0.9716 1);
      --indigo-3: color(display-p3 0.9408 0.9422 1);
      --indigo-4: color(display-p3 0.8983 0.8995 1);
      --indigo-5: color(display-p3 0.8524 0.8522 1);
      --indigo-6: color(display-p3 0.7953 0.7919 1);
      --indigo-7: color(display-p3 0.7161 0.7042 1);
      --indigo-8: color(display-p3 0.6117 0.5789 1);
      --indigo-9: color(display-p3 0.3562 0.1176 0.9345);
      --indigo-10: color(display-p3 0.309 0 0.8658);
      --indigo-11: color(display-p3 0.3639 0.1927 0.9095);
      --indigo-12: color(display-p3 0.1733 0.1009 0.451);
      --indigo-a1: color(display-p3 0.0196 0.0196 1 / 0.008);
      --indigo-a2: color(display-p3 0.0196 0.1451 0.8784 / 0.032);
      --indigo-a3: color(display-p3 0.0039 0.0039 0.9373 / 0.059);
      --indigo-a4: color(display-p3 0.0039 0.0039 0.9255 / 0.102);
      --indigo-a5: color(display-p3 0.0078 0.0078 0.9216 / 0.146);
      --indigo-a6: color(display-p3 0.0039 0.0039 0.9255 / 0.204);
      --indigo-a7: color(display-p3 0.0314 0.0039 0.9333 / 0.291);
      --indigo-a8: color(display-p3 0.0706 0.0039 0.9373 / 0.416);
      --indigo-a9: color(display-p3 0.2706 0 0.9255 / 0.883);
      --indigo-a10: color(display-p3 0.2824 0 0.8588 / 0.957);
      --indigo-a11: color(display-p3 0.2157 0 0.8902 / 0.808);
      --indigo-a12: color(display-p3 0.0784 0 0.3882 / 0.899);
      --lime-1: color(display-p3 0.9836 0.9961 0.9805);
      --lime-2: color(display-p3 0.9614 0.9862 0.9553);
      --lime-3: color(display-p3 0.8945 0.9821 0.8729);
      --lime-4: color(display-p3 0.8269 0.9657 0.7927);
      --lime-5: color(display-p3 0.7545 0.9369 0.7099);
      --lime-6: color(display-p3 0.6723 0.8927 0.6185);
      --lime-7: color(display-p3 0.5673 0.8305 0.5032);
      --lime-8: color(display-p3 0.4126 0.7539 0.3277);
      --lime-9: color(display-p3 0.3843 0.8306 0.2661);
      --lime-10: color(display-p3 0.3386 0.7852 0.2155);
      --lime-11: color(display-p3 0.2169 0.5168 0.1352);
      --lime-12: color(display-p3 0.1424 0.2578 0.1142);
      --lime-a1: color(display-p3 0.2157 0.8039 0.0196 / 0.02);
      --lime-a2: color(display-p3 0.1098 0.7333 0.0196 / 0.044);
      --lime-a3: color(display-p3 0.1882 0.851 0.0078 / 0.13);
      --lime-a4: color(display-p3 0.1725 0.851 0.0039 / 0.208);
      --lime-a5: color(display-p3 0.1647 0.7882 0.0039 / 0.291);
      --lime-a6: color(display-p3 0.149 0.7255 0.0039 / 0.381);
      --lime-a7: color(display-p3 0.1373 0.6627 0.0039 / 0.499);
      --lime-a8: color(display-p3 0.1294 0.6353 0 / 0.675);
      --lime-a9: color(display-p3 0.1608 0.7686 0 / 0.734);
      --lime-a10: color(display-p3 0.1686 0.7216 0 / 0.769);
      --lime-a11: color(display-p3 0.102 0.4392 0 / 0.859);
      --lime-a12: color(display-p3 0.0314 0.1647 0 / 0.887);
      --magenta-1: color(display-p3 0.9996 0.9883 0.9916);
      --magenta-2: color(display-p3 0.9948 0.9673 0.9755);
      --magenta-3: color(display-p3 0.9991 0.9113 0.9382);
      --magenta-4: color(display-p3 0.9933 0.8568 0.8996);
      --magenta-5: color(display-p3 0.9765 0.8008 0.8571);
      --magenta-6: color(display-p3 0.9485 0.7399 0.8082);
      --magenta-7: color(display-p3 0.9129 0.664 0.7479);
      --magenta-8: color(display-p3 0.8739 0.5664 0.6751);
      --magenta-9: color(display-p3 0.9175 0.2003 0.5465);
      --magenta-10: color(display-p3 0.8588 0.1293 0.4997);
      --magenta-11: color(display-p3 0.7746 0 0.4344);
      --magenta-12: color(display-p3 0.3807 0.0555 0.2136);
      --magenta-a1: color(display-p3 0.6745 0.0235 0.349 / 0.012);
      --magenta-a2: color(display-p3 0.7843 0.0235 0.349 / 0.036);
      --magenta-a3: color(display-p3 0.8314 0.0118 0.3137 / 0.091);
      --magenta-a4: color(display-p3 0.8392 0.0078 0.3294 / 0.146);
      --magenta-a5: color(display-p3 0.8431 0 0.2941 / 0.2);
      --magenta-a6: color(display-p3 0.8039 0.0039 0.2588 / 0.259);
      --magenta-a7: color(display-p3 0.7451 0.0039 0.2471 / 0.338);
      --magenta-a8: color(display-p3 0.7098 0.0039 0.2471 / 0.432);
      --magenta-a9: color(display-p3 0.898 0 0.4314 / 0.8);
      --magenta-a10: color(display-p3 0.8275 0 0.3882 / 0.816);
      --magenta-a11: color(display-p3 0.7137 0 0.3255 / 0.84);
      --magenta-a12: color(display-p3 0.3451 0 0.1608 / 0.942);
        --gray-surface: color(display-p3 1 1 1 / 0.8);
        --mauve-surface: color(display-p3 1 1 1 / 0.8);
        --slate-surface: color(display-p3 1 1 1 / 0.8);
        --sage-surface: color(display-p3 1 1 1 / 0.8);
        --olive-surface: color(display-p3 1 1 1 / 0.8);
        --sand-surface: color(display-p3 1 1 1 / 0.8);
        --tomato-surface: color(display-p3 0.9922 0.9647 0.9608 / 0.8);
        --red-surface: color(display-p3 0.9961 0.9647 0.9647 / 0.8);
        --ruby-surface: color(display-p3 0.9961 0.9647 0.9647 / 0.8);
        --crimson-surface: color(display-p3 0.9922 0.9608 0.9725 / 0.8);
        --pink-surface: color(display-p3 0.9922 0.9608 0.9804 / 0.8);
        --plum-surface: color(display-p3 0.9843 0.9647 0.9843 / 0.8);
        --purple-surface: color(display-p3 0.9804 0.9647 0.9922 / 0.8);
        --violet-surface: color(display-p3 0.9725 0.9647 0.9961 / 0.8);
        --iris-surface: color(display-p3 0.9647 0.9647 0.9961 / 0.8);
        --cyan-surface: color(display-p3 0.9412 0.9765 0.9804 / 0.8);
        --teal-surface: color(display-p3 0.9451 0.9804 0.9725 / 0.8);
        --jade-surface: color(display-p3 0.9529 0.9804 0.9608 / 0.8);
        --green-surface: color(display-p3 0.9569 0.9804 0.9608 / 0.8);
        --grass-surface: color(display-p3 0.9569 0.9804 0.9569 / 0.8);
        --brown-surface: color(display-p3 0.9843 0.9725 0.9569 / 0.8);
        --bronze-surface: color(display-p3 0.9843 0.9608 0.9529 / 0.8);
        --gold-surface: color(display-p3 0.9765 0.9725 0.9412 / 0.8);
        --sky-surface: color(display-p3 0.9412 0.9765 0.9843 / 0.8);
        --mint-surface: color(display-p3 0.9451 0.9804 0.9725 / 0.8);
        --yellow-surface: color(display-p3 0.9961 0.9922 0.902 / 0.8);
        --amber-surface: color(display-p3 0.9922 0.9843 0.902 / 0.8);
        --blue-surface: color(display-p3 0.9608 0.9725 1 / 0.8);
        --orange-surface: color(display-p3 1 0.9608 0.9529 / 0.8);
        --indigo-surface: color(display-p3 0.9647 0.9647 1 / 0.8);
        --magenta-surface: color(display-p3 0.9961 0.9608 0.9725 / 0.8);

        --lemon-surface: color(display-p3 0.9725 0.9843 0.9216 / 0.8);
        --lime-surface: color(display-p3 0.9529 0.9804 0.9451 / 0.8);
    }
  }
}

.dark, .dark-theme {
  --tomato-1: #181111;
  --tomato-2: #1f1513;
  --tomato-3: #391714;
  --tomato-4: #4e1511;
  --tomato-5: #5e1c16;
  --tomato-6: #6e2920;
  --tomato-7: #853a2d;
  --tomato-8: #ac4d39;
  --tomato-9: #e54d2e;
  --tomato-10: #ec6142;
  --tomato-11: #ff977d;
  --tomato-12: #fbd3cb;
  --tomato-a1: #f1121208;
  --tomato-a2: #ff55330f;
  --tomato-a3: #ff35232b;
  --tomato-a4: #fd201142;
  --tomato-a5: #fe332153;
  --tomato-a6: #ff4f3864;
  --tomato-a7: #fd644a7d;
  --tomato-a8: #fe6d4ea7;
  --tomato-a9: #fe5431e4;
  --tomato-a10: #ff6847eb;
  --tomato-a11: #ff977d;
  --tomato-a12: #ffd6cefb;
  --red-1: #191111;
  --red-2: #201314;
  --red-3: #3b1219;
  --red-4: #500f1c;
  --red-5: #611623;
  --red-6: #72232d;
  --red-7: #8c333a;
  --red-8: #b54548;
  --red-9: #e5484d;
  --red-10: #ec5d5e;
  --red-11: #ff9592;
  --red-12: #ffd1d9;
  --red-a1: #f4121209;
  --red-a2: #f22f3e11;
  --red-a3: #ff173f2d;
  --red-a4: #fe0a3b44;
  --red-a5: #ff204756;
  --red-a6: #ff3e5668;
  --red-a7: #ff536184;
  --red-a8: #ff5d61b0;
  --red-a9: #fe4e54e4;
  --red-a10: #ff6465eb;
  --red-a11: #ff9592;
  --red-a12: #ffd1d9;
  --ruby-1: #191113;
  --ruby-2: #1e1517;
  --ruby-3: #3a141e;
  --ruby-4: #4e1325;
  --ruby-5: #5e1a2e;
  --ruby-6: #6f2539;
  --ruby-7: #883447;
  --ruby-8: #b3445a;
  --ruby-9: #e54666;
  --ruby-10: #ec5a72;
  --ruby-11: #ff949d;
  --ruby-12: #fed2e1;
  --ruby-a1: #f4124a09;
  --ruby-a2: #fe5a7f0e;
  --ruby-a3: #ff235d2c;
  --ruby-a4: #fd195e42;
  --ruby-a5: #fe2d6b53;
  --ruby-a6: #ff447665;
  --ruby-a7: #ff577d80;
  --ruby-a8: #ff5c7cae;
  --ruby-a9: #fe4c70e4;
  --ruby-a10: #ff617beb;
  --ruby-a11: #ff949d;
  --ruby-a12: #ffd3e2fe;
  --crimson-1: #191114;
  --crimson-2: #201318;
  --crimson-3: #381525;
  --crimson-4: #4d122f;
  --crimson-5: #5c1839;
  --crimson-6: #6d2545;
  --crimson-7: #873356;
  --crimson-8: #b0436e;
  --crimson-9: #e93d82;
  --crimson-10: #ee518a;
  --crimson-11: #ff92ad;
  --crimson-12: #fdd3e8;
  --crimson-a1: #f4126709;
  --crimson-a2: #f22f7a11;
  --crimson-a3: #fe2a8b2a;
  --crimson-a4: #fd158741;
  --crimson-a5: #fd278f51;
  --crimson-a6: #fe459763;
  --crimson-a7: #fd559b7f;
  --crimson-a8: #fe5b9bab;
  --crimson-a9: #fe418de8;
  --crimson-a10: #ff5693ed;
  --crimson-a11: #ff92ad;
  --crimson-a12: #ffd5eafd;
  --pink-1: #191117;
  --pink-2: #21121d;
  --pink-3: #37172f;
  --pink-4: #4b143d;
  --pink-5: #591c47;
  --pink-6: #692955;
  --pink-7: #833869;
  --pink-8: #a84885;
  --pink-9: #d6409f;
  --pink-10: #de51a8;
  --pink-11: #ff8dcc;
  --pink-12: #fdd1ea;
  --pink-a1: #f412bc09;
  --pink-a2: #f420bb12;
  --pink-a3: #fe37cc29;
  --pink-a4: #fc1ec43f;
  --pink-a5: #fd35c24e;
  --pink-a6: #fd51c75f;
  --pink-a7: #fd62c87b;
  --pink-a8: #ff68c8a2;
  --pink-a9: #fe49bcd4;
  --pink-a10: #ff5cc0dc;
  --pink-a11: #ff8dcc;
  --pink-a12: #ffd3ecfd;
  --plum-1: #181118;
  --plum-2: #201320;
  --plum-3: #351a35;
  --plum-4: #451d47;
  --plum-5: #512454;
  --plum-6: #5e3061;
  --plum-7: #734079;
  --plum-8: #92549c;
  --plum-9: #ab4aba;
  --plum-10: #b658c4;
  --plum-11: #e796f3;
  --plum-12: #f4d4f4;
  --plum-a1: #f112f108;
  --plum-a2: #f22ff211;
  --plum-a3: #fd4cfd27;
  --plum-a4: #f646ff3a;
  --plum-a5: #f455ff48;
  --plum-a6: #f66dff56;
  --plum-a7: #f07cfd70;
  --plum-a8: #ee84ff95;
  --plum-a9: #e961feb6;
  --plum-a10: #ed70ffc0;
  --plum-a11: #f19cfef3;
  --plum-a12: #feddfef4;
  --purple-1: #18111b;
  --purple-2: #1e1523;
  --purple-3: #301c3b;
  --purple-4: #3d224e;
  --purple-5: #48295c;
  --purple-6: #54346b;
  --purple-7: #664282;
  --purple-8: #8457aa;
  --purple-9: #8e4ec6;
  --purple-10: #9a5cd0;
  --purple-11: #d19dff;
  --purple-12: #ecd9fa;
  --purple-a1: #b412f90b;
  --purple-a2: #b744f714;
  --purple-a3: #c150ff2d;
  --purple-a4: #bb53fd42;
  --purple-a5: #be5cfd51;
  --purple-a6: #c16dfd61;
  --purple-a7: #c378fd7a;
  --purple-a8: #c47effa4;
  --purple-a9: #b661ffc2;
  --purple-a10: #bc6fffcd;
  --purple-a11: #d19dff;
  --purple-a12: #f1ddfffa;
  --violet-1: #14121f;
  --violet-2: #1b1525;
  --violet-3: #291f43;
  --violet-4: #33255b;
  --violet-5: #3c2e69;
  --violet-6: #473876;
  --violet-7: #56468b;
  --violet-8: #6958ad;
  --violet-9: #6e56cf;
  --violet-10: #7d66d9;
  --violet-11: #baa7ff;
  --violet-12: #e2ddfe;
  --violet-a1: #4422ff0f;
  --violet-a2: #853ff916;
  --violet-a3: #8354fe36;
  --violet-a4: #7d51fd50;
  --violet-a5: #845ffd5f;
  --violet-a6: #8f6cfd6d;
  --violet-a7: #9879ff83;
  --violet-a8: #977dfea8;
  --violet-a9: #8668ffcc;
  --violet-a10: #9176fed7;
  --violet-a11: #baa7ff;
  --violet-a12: #e3defffe;
  --iris-1: #13131e;
  --iris-2: #171625;
  --iris-3: #202248;
  --iris-4: #262a65;
  --iris-5: #303374;
  --iris-6: #3d3e82;
  --iris-7: #4a4a95;
  --iris-8: #5958b1;
  --iris-9: #5b5bd6;
  --iris-10: #6e6ade;
  --iris-11: #b1a9ff;
  --iris-12: #e0dffe;
  --iris-a1: #3636fe0e;
  --iris-a2: #564bf916;
  --iris-a3: #525bff3b;
  --iris-a4: #4d58ff5a;
  --iris-a5: #5b62fd6b;
  --iris-a6: #6d6ffd7a;
  --iris-a7: #7777fe8e;
  --iris-a8: #7b7afeac;
  --iris-a9: #6a6afed4;
  --iris-a10: #7d79ffdc;
  --iris-a11: #b1a9ff;
  --iris-a12: #e1e0fffe;
  --cyan-1: #0b161a;
  --cyan-2: #101b20;
  --cyan-3: #082c36;
  --cyan-4: #003848;
  --cyan-5: #004558;
  --cyan-6: #045468;
  --cyan-7: #12677e;
  --cyan-8: #11809c;
  --cyan-9: #00a2c7;
  --cyan-10: #23afd0;
  --cyan-11: #4ccce6;
  --cyan-12: #b6ecf7;
  --cyan-a1: #0091f70a;
  --cyan-a2: #02a7f211;
  --cyan-a3: #00befd28;
  --cyan-a4: #00baff3b;
  --cyan-a5: #00befd4d;
  --cyan-a6: #00c7fd5e;
  --cyan-a7: #14cdff75;
  --cyan-a8: #11cfff95;
  --cyan-a9: #00cfffc3;
  --cyan-a10: #28d6ffcd;
  --cyan-a11: #52e1fee5;
  --cyan-a12: #bbf3fef7;
  --teal-1: #0d1514;
  --teal-2: #111c1b;
  --teal-3: #0d2d2a;
  --teal-4: #023b37;
  --teal-5: #084843;
  --teal-6: #145750;
  --teal-7: #1c6961;
  --teal-8: #207e73;
  --teal-9: #12a594;
  --teal-10: #0eb39e;
  --teal-11: #0bd8b6;
  --teal-12: #adf0dd;
  --teal-a1: #00deab05;
  --teal-a2: #12fbe60c;
  --teal-a3: #00ffe61e;
  --teal-a4: #00ffe92d;
  --teal-a5: #00ffea3b;
  --teal-a6: #1cffe84b;
  --teal-a7: #2efde85f;
  --teal-a8: #32ffe775;
  --teal-a9: #13ffe49f;
  --teal-a10: #0dffe0ae;
  --teal-a11: #0afed5d6;
  --teal-a12: #b8ffebef;
  --jade-1: #0d1512;
  --jade-2: #121c18;
  --jade-3: #0f2e22;
  --jade-4: #0b3b2c;
  --jade-5: #114837;
  --jade-6: #1b5745;
  --jade-7: #246854;
  --jade-8: #2a7e68;
  --jade-9: #29a383;
  --jade-10: #27b08b;
  --jade-11: #1fd8a4;
  --jade-12: #adf0d4;
  --jade-a1: #00de4505;
  --jade-a2: #27fba60c;
  --jade-a3: #02f99920;
  --jade-a4: #00ffaa2d;
  --jade-a5: #11ffb63b;
  --jade-a6: #34ffc24b;
  --jade-a7: #45fdc75e;
  --jade-a8: #48ffcf75;
  --jade-a9: #38feca9d;
  --jade-a10: #31fec7ab;
  --jade-a11: #21fec0d6;
  --jade-a12: #b8ffe1ef;
  --green-1: #0e1512;
  --green-2: #121b17;
  --green-3: #132d21;
  --green-4: #113b29;
  --green-5: #174933;
  --green-6: #20573e;
  --green-7: #28684a;
  --green-8: #2f7c57;
  --green-9: #30a46c;
  --green-10: #33b074;
  --green-11: #3dd68c;
  --green-12: #b1f1cb;
  --green-a1: #00de4505;
  --green-a2: #29f99d0b;
  --green-a3: #22ff991e;
  --green-a4: #11ff992d;
  --green-a5: #2bffa23c;
  --green-a6: #44ffaa4b;
  --green-a7: #50fdac5e;
  --green-a8: #54ffad73;
  --green-a9: #44ffa49e;
  --green-a10: #43fea4ab;
  --green-a11: #46fea5d4;
  --green-a12: #bbffd7f0;
  --grass-1: #0e1511;
  --grass-2: #141a15;
  --grass-3: #1b2a1e;
  --grass-4: #1d3a24;
  --grass-5: #25482d;
  --grass-6: #2d5736;
  --grass-7: #366740;
  --grass-8: #3e7949;
  --grass-9: #46a758;
  --grass-10: #53b365;
  --grass-11: #71d083;
  --grass-12: #c2f0c2;
  --grass-a1: #00de1205;
  --grass-a2: #5ef7780a;
  --grass-a3: #70fe8c1b;
  --grass-a4: #57ff802c;
  --grass-a5: #68ff8b3b;
  --grass-a6: #71ff8f4b;
  --grass-a7: #77fd925d;
  --grass-a8: #77fd9070;
  --grass-a9: #65ff82a1;
  --grass-a10: #72ff8dae;
  --grass-a11: #89ff9fcd;
  --grass-a12: #ceffceef;
  --brown-1: #12110f;
  --brown-2: #1c1816;
  --brown-3: #28211d;
  --brown-4: #322922;
  --brown-5: #3e3128;
  --brown-6: #4d3c2f;
  --brown-7: #614a39;
  --brown-8: #7c5f46;
  --brown-9: #ad7f58;
  --brown-10: #b88c67;
  --brown-11: #dbb594;
  --brown-12: #f2e1ca;
  --brown-a1: #91110002;
  --brown-a2: #fba67c0c;
  --brown-a3: #fcb58c19;
  --brown-a4: #fbbb8a24;
  --brown-a5: #fcb88931;
  --brown-a6: #fdba8741;
  --brown-a7: #ffbb8856;
  --brown-a8: #ffbe8773;
  --brown-a9: #feb87da8;
  --brown-a10: #ffc18cb3;
  --brown-a11: #fed1aad9;
  --brown-a12: #feecd4f2;
  --sky-1: #0d141f;
  --sky-2: #111a27;
  --sky-3: #112840;
  --sky-4: #113555;
  --sky-5: #154467;
  --sky-6: #1b537b;
  --sky-7: #1f6692;
  --sky-8: #197cae;
  --sky-9: #7ce2fe;
  --sky-10: #a8eeff;
  --sky-11: #75c7f0;
  --sky-12: #c2f3ff;
  --sky-a1: #0044ff0f;
  --sky-a2: #1171fb18;
  --sky-a3: #1184fc33;
  --sky-a4: #128fff49;
  --sky-a5: #1c9dfd5d;
  --sky-a6: #28a5ff72;
  --sky-a7: #2badfe8b;
  --sky-a8: #1db2fea9;
  --sky-a9: #7ce3fffe;
  --sky-a10: #a8eeff;
  --sky-a11: #7cd3ffef;
  --sky-a12: #c2f3ff;
  --mint-1: #0e1515;
  --mint-2: #0f1b1b;
  --mint-3: #092c2b;
  --mint-4: #003a38;
  --mint-5: #004744;
  --mint-6: #105650;
  --mint-7: #1e685f;
  --mint-8: #277f70;
  --mint-9: #86ead4;
  --mint-10: #a8f5e5;
  --mint-11: #58d5ba;
  --mint-12: #c4f5e1;
  --mint-a1: #00dede05;
  --mint-a2: #00f9f90b;
  --mint-a3: #00fff61d;
  --mint-a4: #00fff42c;
  --mint-a5: #00fff23a;
  --mint-a6: #0effeb4a;
  --mint-a7: #34fde55e;
  --mint-a8: #41ffdf76;
  --mint-a9: #92ffe7e9;
  --mint-a10: #aefeedf5;
  --mint-a11: #67ffded2;
  --mint-a12: #cbfee9f5;
  --yellow-1: #14120b;
  --yellow-2: #1b180f;
  --yellow-3: #2d2305;
  --yellow-4: #362b00;
  --yellow-5: #433500;
  --yellow-6: #524202;
  --yellow-7: #665417;
  --yellow-8: #836a21;
  --yellow-9: #ffe629;
  --yellow-10: #ffff57;
  --yellow-11: #f5e147;
  --yellow-12: #f6eeb4;
  --yellow-a1: #d1510004;
  --yellow-a2: #f9b4000b;
  --yellow-a3: #ffaa001e;
  --yellow-a4: #fdb70028;
  --yellow-a5: #febb0036;
  --yellow-a6: #fec40046;
  --yellow-a7: #fdcb225c;
  --yellow-a8: #fdca327b;
  --yellow-a9: #ffe629;
  --yellow-a10: #ffff57;
  --yellow-a11: #fee949f5;
  --yellow-a12: #fef6baf6;
  --amber-1: #16120c;
  --amber-2: #1d180f;
  --amber-3: #302008;
  --amber-4: #3f2700;
  --amber-5: #4d3000;
  --amber-6: #5c3d05;
  --amber-7: #714f19;
  --amber-8: #8f6424;
  --amber-9: #ffc53d;
  --amber-10: #ffd60a;
  --amber-11: #ffca16;
  --amber-12: #ffe7b3;
  --amber-a1: #e63c0006;
  --amber-a2: #fd9b000d;
  --amber-a3: #fa820022;
  --amber-a4: #fc820032;
  --amber-a5: #fd8b0041;
  --amber-a6: #fd9b0051;
  --amber-a7: #ffab2567;
  --amber-a8: #ffae3587;
  --amber-a9: #ffc53d;
  --amber-a10: #ffd60a;
  --amber-a11: #ffca16;
  --amber-a12: #ffe7b3;
  --gold-1: #121211;
  --gold-2: #1b1a17;
  --gold-3: #24231f;
  --gold-4: #2d2b26;
  --gold-5: #38352e;
  --gold-6: #444039;
  --gold-7: #544f46;
  --gold-8: #696256;
  --gold-9: #978365;
  --gold-10: #a39073;
  --gold-11: #cbb99f;
  --gold-12: #e8e2d9;
  --gold-a1: #91911102;
  --gold-a2: #f9e29d0b;
  --gold-a3: #f8ecbb15;
  --gold-a4: #ffeec41e;
  --gold-a5: #feecc22a;
  --gold-a6: #feebcb37;
  --gold-a7: #ffedcd48;
  --gold-a8: #fdeaca5f;
  --gold-a9: #ffdba690;
  --gold-a10: #fedfb09d;
  --gold-a11: #fee7c6c8;
  --gold-a12: #fef7ede7;
  --bronze-1: #141110;
  --bronze-2: #1c1917;
  --bronze-3: #262220;
  --bronze-4: #302a27;
  --bronze-5: #3b3330;
  --bronze-6: #493e3a;
  --bronze-7: #5a4c47;
  --bronze-8: #6f5f58;
  --bronze-9: #a18072;
  --bronze-10: #ae8c7e;
  --bronze-11: #d4b3a5;
  --bronze-12: #ede0d9;
  --bronze-a1: #d1110004;
  --bronze-a2: #fbbc910c;
  --bronze-a3: #faceb817;
  --bronze-a4: #facdb622;
  --bronze-a5: #ffd2c12d;
  --bronze-a6: #ffd1c03c;
  --bronze-a7: #fdd0c04f;
  --bronze-a8: #ffd6c565;
  --bronze-a9: #fec7b09b;
  --bronze-a10: #fecab5a9;
  --bronze-a11: #ffd7c6d1;
  --bronze-a12: #fff1e9ec;
  --gray-1: #111111;
  --gray-2: #191919;
  --gray-3: #222222;
  --gray-4: #2a2a2a;
  --gray-5: #313131;
  --gray-6: #3a3a3a;
  --gray-7: #484848;
  --gray-8: #606060;
  --gray-9: #6e6e6e;
  --gray-10: #7b7b7b;
  --gray-11: #b4b4b4;
  --gray-12: #eeeeee;
  --gray-a1: #00000000;
  --gray-a2: #ffffff09;
  --gray-a3: #ffffff12;
  --gray-a4: #ffffff1b;
  --gray-a5: #ffffff22;
  --gray-a6: #ffffff2c;
  --gray-a7: #ffffff3b;
  --gray-a8: #ffffff55;
  --gray-a9: #ffffff64;
  --gray-a10: #ffffff72;
  --gray-a11: #ffffffaf;
  --gray-a12: #ffffffed;
  --mauve-1: #121113;
  --mauve-2: #1a191b;
  --mauve-3: #232225;
  --mauve-4: #2b292d;
  --mauve-5: #323035;
  --mauve-6: #3c393f;
  --mauve-7: #49474e;
  --mauve-8: #625f69;
  --mauve-9: #6f6d78;
  --mauve-10: #7c7a85;
  --mauve-11: #b5b2bc;
  --mauve-12: #eeeef0;
  --mauve-a1: #00000000;
  --mauve-a2: #f5f4f609;
  --mauve-a3: #ebeaf814;
  --mauve-a4: #eee5f81d;
  --mauve-a5: #efe6fe25;
  --mauve-a6: #f1e6fd30;
  --mauve-a7: #eee9ff40;
  --mauve-a8: #eee7ff5d;
  --mauve-a9: #eae6fd6e;
  --mauve-a10: #ece9fd7c;
  --mauve-a11: #f5f1ffb7;
  --mauve-a12: #fdfdffef;
  --slate-1: #111113;
  --slate-2: #18191b;
  --slate-3: #212225;
  --slate-4: #272a2d;
  --slate-5: #2e3135;
  --slate-6: #363a3f;
  --slate-7: #43484e;
  --slate-8: #5a6169;
  --slate-9: #696e77;
  --slate-10: #777b84;
  --slate-11: #b0b4ba;
  --slate-12: #edeef0;
  --slate-a1: #00000000;
  --slate-a2: #d8f4f609;
  --slate-a3: #ddeaf814;
  --slate-a4: #d3edf81d;
  --slate-a5: #d9edfe25;
  --slate-a6: #d6ebfd30;
  --slate-a7: #d9edff40;
  --slate-a8: #d9edff5d;
  --slate-a9: #dfebfd6d;
  --slate-a10: #e5edfd7b;
  --slate-a11: #f1f7feb5;
  --slate-a12: #fcfdffef;
  --sage-1: #101211;
  --sage-2: #171918;
  --sage-3: #202221;
  --sage-4: #272a29;
  --sage-5: #2e3130;
  --sage-6: #373b39;
  --sage-7: #444947;
  --sage-8: #5b625f;
  --sage-9: #63706b;
  --sage-10: #717d79;
  --sage-11: #adb5b2;
  --sage-12: #eceeed;
  --sage-a1: #00000000;
  --sage-a2: #f0f2f108;
  --sage-a3: #f3f5f412;
  --sage-a4: #f2fefd1a;
  --sage-a5: #f1fbfa22;
  --sage-a6: #edfbf42d;
  --sage-a7: #edfcf73c;
  --sage-a8: #ebfdf657;
  --sage-a9: #dffdf266;
  --sage-a10: #e5fdf674;
  --sage-a11: #f4fefbb0;
  --sage-a12: #fdfffeed;
  --olive-1: #111210;
  --olive-2: #181917;
  --olive-3: #212220;
  --olive-4: #282a27;
  --olive-5: #2f312e;
  --olive-6: #383a36;
  --olive-7: #454843;
  --olive-8: #5c625b;
  --olive-9: #687066;
  --olive-10: #767d74;
  --olive-11: #afb5ad;
  --olive-12: #eceeec;
  --olive-a1: #00000000;
  --olive-a2: #f1f2f008;
  --olive-a3: #f4f5f312;
  --olive-a4: #f3fef21a;
  --olive-a5: #f2fbf122;
  --olive-a6: #f4faed2c;
  --olive-a7: #f2fced3b;
  --olive-a8: #edfdeb57;
  --olive-a9: #ebfde766;
  --olive-a10: #f0fdec74;
  --olive-a11: #f6fef4b0;
  --olive-a12: #fdfffded;
  --sand-1: #111110;
  --sand-2: #191918;
  --sand-3: #222221;
  --sand-4: #2a2a28;
  --sand-5: #31312e;
  --sand-6: #3b3a37;
  --sand-7: #494844;
  --sand-8: #62605b;
  --sand-9: #6f6d66;
  --sand-10: #7c7b74;
  --sand-11: #b5b3ad;
  --sand-12: #eeeeec;
  --sand-a1: #00000000;
  --sand-a2: #f4f4f309;
  --sand-a3: #f6f6f513;
  --sand-a4: #fefef31b;
  --sand-a5: #fbfbeb23;
  --sand-a6: #fffaed2d;
  --sand-a7: #fffbed3c;
  --sand-a8: #fff9eb57;
  --sand-a9: #fffae965;
  --sand-a10: #fffdee73;
  --sand-a11: #fffcf4b0;
  --sand-a12: #fffffded;
  --blue-1: #0b111d;
  --blue-2: #0f1727;
  --blue-3: #11254c;
  --blue-4: #142f66;
  --blue-5: #1a3979;
  --blue-6: #23448a;
  --blue-7: #2b519e;
  --blue-8: #325eb9;
  --blue-9: #1754d8;
  --blue-10: #406cc8;
  --blue-11: #88b5ff;
  --blue-12: #d1e2ff;
  --blue-a1: #0012fd0d;
  --blue-a2: #0051fb18;
  --blue-a3: #1161fd40;
  --blue-a4: #1a65fd5c;
  --blue-a5: #256cfd70;
  --blue-a6: #3576ff82;
  --blue-a7: #3d7cfe98;
  --blue-a8: #407effb4;
  --blue-a9: #1861fed6;
  --blue-a10: #6397ffa8;
  --blue-a11: #88b5ff;
  --blue-a12: #d1e2ff;
  --orange-1: #170f0d;
  --orange-2: #1f1412;
  --orange-3: #3b150c;
  --orange-4: #521002;
  --orange-5: #631704;
  --orange-6: #732412;
  --orange-7: #8b3521;
  --orange-8: #b4452c;
  --orange-9: #fa4616;
  --orange-10: #eb3600;
  --orange-11: #ff9275;
  --orange-12: #ffd2c6;
  --orange-a1: #ec000007;
  --orange-a2: #ff44220f;
  --orange-a3: #ff28002d;
  --orange-a4: #fe0e0046;
  --orange-a5: #ff230058;
  --orange-a6: #ff401469;
  --orange-a7: #ff583183;
  --orange-a8: #ff5d39af;
  --orange-a9: #ff4716fa;
  --orange-a10: #ff3a00ea;
  --orange-a11: #ff9275;
  --orange-a12: #ffd2c6;
  --lemon-1: #10120a;
  --lemon-2: #17190f;
  --lemon-3: #232710;
  --lemon-4: #2d330d;
  --lemon-5: #383f0d;
  --lemon-6: #454d15;
  --lemon-7: #545e1f;
  --lemon-8: #677326;
  --lemon-9: #dbf505;
  --lemon-10: #d2e94c;
  --lemon-11: #cfe648;
  --lemon-12: #e8f5b2;
  --lemon-a1: #00910002;
  --lemon-a2: #bcf40009;
  --lemon-a3: #d1fb0718;
  --lemon-a4: #d2fb0025;
  --lemon-a5: #d8fc0032;
  --lemon-a6: #ddfd2141;
  --lemon-a7: #dffe3d53;
  --lemon-a8: #e2ff4469;
  --lemon-a9: #e3fe05f5;
  --lemon-a10: #e5fe51e8;
  --lemon-a11: #e4fe4ee5;
  --lemon-a12: #f0feb8f5;
  --indigo-1: #100f1f;
  --indigo-2: #16142b;
  --indigo-3: #241a57;
  --indigo-4: #30177b;
  --indigo-5: #3a218d;
  --indigo-6: #432d9c;
  --indigo-7: #5039b3;
  --indigo-8: #6045d6;
  --indigo-9: #6318f8;
  --indigo-10: #7855ff;
  --indigo-11: #ada8ff;
  --indigo-12: #ddf;
  --indigo-a1: #0000ff0f;
  --indigo-a2: #3f2dfe1c;
  --indigo-a3: #5230ff4b;
  --indigo-a4: #571fff72;
  --indigo-a5: #6030ff85;
  --indigo-a6: #6741ff95;
  --indigo-a7: #6e4cffae;
  --indigo-a8: #704ffed4;
  --indigo-a9: #6619fff8;
  --indigo-a10: #8869ffd7;
  --indigo-a11: #ada8ff;
  --indigo-a12: #ddddff;
  --lime-1: #0c130b;
  --lime-2: #121b11;
  --lime-3: #132e12;
  --lime-4: #103d0f;
  --lime-5: #154b15;
  --lime-6: #1d5a1c;
  --lime-7: #246b23;
  --lime-8: #297f28;
  --lime-9: #06d718;
  --lime-10: #00cb00;
  --lime-11: #1cdd24;
  --lime-12: #adf6a8;
  --lime-a1: #00bb0003;
  --lime-a2: #29f9120b;
  --lime-a3: #21f91920;
  --lime-a4: #0cfb0730;
  --lime-a5: #22fc223f;
  --lime-a6: #38fd354f;
  --lime-a7: #43fd4061;
  --lime-a8: #45ff4376;
  --lime-a9: #04fe19d5;
  --lime-a10: #00fe00c8;
  --lime-a11: #1eff28db;
  --lime-a12: #b2feadf6;
  --magenta-1: #180e11;
  --magenta-2: #211217;
  --magenta-3: #3d1022;
  --magenta-4: #55012b;
  --magenta-5: #650635;
  --magenta-6: #761642;
  --magenta-7: #912554;
  --magenta-8: #bc2f6e;
  --magenta-9: #ff008d;
  --magenta-10: #f00081;
  --magenta-11: #ff89b8;
  --magenta-12: #ffcfe0;
  --magenta-a1: #f1001208;
  --magenta-a2: #f4206612;
  --magenta-a3: #fb0c6c30;
  --magenta-a4: #ff006c49;
  --magenta-a5: #ff00775a;
  --magenta-a6: #fd1d846d;
  --magenta-a7: #fe368d8a;
  --magenta-a8: #fe3a92b8;
  --magenta-a9: #ff008d;
  --magenta-a10: #ff0089ef;
  --magenta-a11: #ff89b8;
  --magenta-a12: #ffcfe0;
  --gray-2-translucent: #1d1d1d80;
  --mauve-2-translucent: #1e1d1e80;
  --slate-2-translucent: #1b1d1e80;
  --sage-2-translucent: #1a1c1b80;
  --olive-2-translucent: #1b1c1a80;
  --sand-2-translucent: #1d1d1b80;
  --gray-surface: #21212180;
  --mauve-surface: #22212380;
  --slate-surface: #1f212380;
  --sage-surface: #1e201f80;
  --olive-surface: #1f201e80;
  --sand-surface: #21212080;
  --tomato-surface: #2d191580;
  --red-surface: #2f151780;
  --ruby-surface: #2b191d80;
  --crimson-surface: #2f151f80;
  --pink-surface: #31132980;
  --plum-surface: #2f152f80;
  --purple-surface: #2b173580;
  --violet-surface: #25193980;
  --iris-surface: #1d1b3980;
  --cyan-surface: #11252d80;
  --teal-surface: #13272580;
  --jade-surface: #13271f80;
  --green-surface: #15251d80;
  --grass-surface: #19231b80;
  --brown-surface: #271f1b80;
  --bronze-surface: #27211d80;
  --gold-surface: #25231d80;
  --sky-surface: #13233b80;
  --mint-surface: #15272780;
  --yellow-surface: #231f1380;
  --amber-surface: #271f1380;
  --blue-surface: #0e1d3d80;
  --orange-surface: #2d171380;
  --indigo-surface: #1b174580;
  --magenta-surface: #31131d80;

  --lemon-surface: #1d210e80;
  --lime-surface: #13251180;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .dark, .dark-theme {
      --tomato-1: color(display-p3 0.09 0.068 0.067);
      --tomato-2: color(display-p3 0.115 0.084 0.076);
      --tomato-3: color(display-p3 0.205 0.097 0.083);
      --tomato-4: color(display-p3 0.282 0.099 0.077);
      --tomato-5: color(display-p3 0.339 0.129 0.101);
      --tomato-6: color(display-p3 0.398 0.179 0.141);
      --tomato-7: color(display-p3 0.487 0.245 0.194);
      --tomato-8: color(display-p3 0.629 0.322 0.248);
      --tomato-9: color(display-p3 0.831 0.345 0.231);
      --tomato-10: color(display-p3 0.862 0.415 0.298);
      --tomato-11: color(display-p3 1 0.585 0.455);
      --tomato-12: color(display-p3 0.959 0.833 0.802);
      --tomato-a1: color(display-p3 0.973 0.071 0.071 / 0.026);
      --tomato-a2: color(display-p3 0.992 0.376 0.224 / 0.051);
      --tomato-a3: color(display-p3 0.996 0.282 0.176 / 0.148);
      --tomato-a4: color(display-p3 1 0.204 0.118 / 0.232);
      --tomato-a5: color(display-p3 1 0.286 0.192 / 0.29);
      --tomato-a6: color(display-p3 1 0.392 0.278 / 0.353);
      --tomato-a7: color(display-p3 1 0.459 0.349 / 0.45);
      --tomato-a8: color(display-p3 1 0.49 0.369 / 0.601);
      --tomato-a9: color(display-p3 1 0.408 0.267 / 0.82);
      --tomato-a10: color(display-p3 1 0.478 0.341 / 0.853);
      --tomato-a11: color(display-p3 1 0.585 0.455);
      --tomato-a12: color(display-p3 0.959 0.833 0.802);
      --red-1: color(display-p3 0.093 0.068 0.067);
      --red-2: color(display-p3 0.118 0.077 0.079);
      --red-3: color(display-p3 0.211 0.081 0.099);
      --red-4: color(display-p3 0.287 0.079 0.113);
      --red-5: color(display-p3 0.348 0.11 0.142);
      --red-6: color(display-p3 0.414 0.16 0.183);
      --red-7: color(display-p3 0.508 0.224 0.236);
      --red-8: color(display-p3 0.659 0.298 0.297);
      --red-9: color(display-p3 0.83 0.329 0.324);
      --red-10: color(display-p3 0.861 0.403 0.387);
      --red-11: color(display-p3 1 0.57 0.55);
      --red-12: color(display-p3 0.971 0.826 0.852);
      --red-a1: color(display-p3 0.984 0.071 0.071 / 0.03);
      --red-a2: color(display-p3 0.996 0.282 0.282 / 0.055);
      --red-a3: color(display-p3 1 0.169 0.271 / 0.156);
      --red-a4: color(display-p3 1 0.118 0.267 / 0.236);
      --red-a5: color(display-p3 1 0.212 0.314 / 0.303);
      --red-a6: color(display-p3 1 0.318 0.38 / 0.374);
      --red-a7: color(display-p3 1 0.4 0.424 / 0.475);
      --red-a8: color(display-p3 1 0.431 0.431 / 0.635);
      --red-a9: color(display-p3 1 0.388 0.384 / 0.82);
      --red-a10: color(display-p3 1 0.463 0.447 / 0.853);
      --red-a11: color(display-p3 1 0.57 0.55);
      --red-a12: color(display-p3 0.971 0.826 0.852);
      --ruby-1: color(display-p3 0.093 0.068 0.074);
      --ruby-2: color(display-p3 0.113 0.083 0.089);
      --ruby-3: color(display-p3 0.208 0.088 0.117);
      --ruby-4: color(display-p3 0.279 0.092 0.147);
      --ruby-5: color(display-p3 0.337 0.12 0.18);
      --ruby-6: color(display-p3 0.401 0.166 0.223);
      --ruby-7: color(display-p3 0.495 0.224 0.281);
      --ruby-8: color(display-p3 0.652 0.295 0.359);
      --ruby-9: color(display-p3 0.83 0.323 0.408);
      --ruby-10: color(display-p3 0.857 0.392 0.455);
      --ruby-11: color(display-p3 1 0.57 0.59);
      --ruby-12: color(display-p3 0.968 0.83 0.88);
      --ruby-a1: color(display-p3 0.984 0.071 0.329 / 0.03);
      --ruby-a2: color(display-p3 0.992 0.376 0.529 / 0.051);
      --ruby-a3: color(display-p3 0.996 0.196 0.404 / 0.152);
      --ruby-a4: color(display-p3 1 0.173 0.416 / 0.227);
      --ruby-a5: color(display-p3 1 0.259 0.459 / 0.29);
      --ruby-a6: color(display-p3 1 0.341 0.506 / 0.358);
      --ruby-a7: color(display-p3 1 0.412 0.541 / 0.458);
      --ruby-a8: color(display-p3 1 0.431 0.537 / 0.627);
      --ruby-a9: color(display-p3 1 0.376 0.482 / 0.82);
      --ruby-a10: color(display-p3 1 0.447 0.522 / 0.849);
      --ruby-a11: color(display-p3 1 0.57 0.59);
      --ruby-a12: color(display-p3 0.968 0.83 0.88);
      --crimson-1: color(display-p3 0.093 0.068 0.078);
      --crimson-2: color(display-p3 0.117 0.078 0.095);
      --crimson-3: color(display-p3 0.203 0.091 0.143);
      --crimson-4: color(display-p3 0.277 0.087 0.182);
      --crimson-5: color(display-p3 0.332 0.115 0.22);
      --crimson-6: color(display-p3 0.394 0.162 0.268);
      --crimson-7: color(display-p3 0.489 0.222 0.336);
      --crimson-8: color(display-p3 0.638 0.289 0.429);
      --crimson-9: color(display-p3 0.843 0.298 0.507);
      --crimson-10: color(display-p3 0.864 0.364 0.539);
      --crimson-11: color(display-p3 1 0.56 0.66);
      --crimson-12: color(display-p3 0.966 0.834 0.906);
      --crimson-a1: color(display-p3 0.984 0.071 0.463 / 0.03);
      --crimson-a2: color(display-p3 0.996 0.282 0.569 / 0.055);
      --crimson-a3: color(display-p3 0.996 0.227 0.573 / 0.148);
      --crimson-a4: color(display-p3 1 0.157 0.569 / 0.227);
      --crimson-a5: color(display-p3 1 0.231 0.604 / 0.286);
      --crimson-a6: color(display-p3 1 0.337 0.643 / 0.349);
      --crimson-a7: color(display-p3 1 0.416 0.663 / 0.454);
      --crimson-a8: color(display-p3 0.996 0.427 0.651 / 0.614);
      --crimson-a9: color(display-p3 1 0.345 0.596 / 0.832);
      --crimson-a10: color(display-p3 1 0.42 0.62 / 0.853);
      --crimson-a11: color(display-p3 1 0.56 0.66);
      --crimson-a12: color(display-p3 0.966 0.834 0.906);
      --pink-1: color(display-p3 0.093 0.068 0.089);
      --pink-2: color(display-p3 0.121 0.073 0.11);
      --pink-3: color(display-p3 0.198 0.098 0.179);
      --pink-4: color(display-p3 0.271 0.095 0.231);
      --pink-5: color(display-p3 0.32 0.127 0.273);
      --pink-6: color(display-p3 0.382 0.177 0.326);
      --pink-7: color(display-p3 0.477 0.238 0.405);
      --pink-8: color(display-p3 0.612 0.304 0.51);
      --pink-9: color(display-p3 0.775 0.297 0.61);
      --pink-10: color(display-p3 0.808 0.356 0.645);
      --pink-11: color(display-p3 1 0.535 0.78);
      --pink-12: color(display-p3 0.964 0.826 0.912);
      --pink-a1: color(display-p3 0.984 0.071 0.855 / 0.03);
      --pink-a2: color(display-p3 1 0.2 0.8 / 0.059);
      --pink-a3: color(display-p3 1 0.294 0.886 / 0.139);
      --pink-a4: color(display-p3 1 0.192 0.82 / 0.219);
      --pink-a5: color(display-p3 1 0.282 0.827 / 0.274);
      --pink-a6: color(display-p3 1 0.396 0.835 / 0.337);
      --pink-a7: color(display-p3 1 0.459 0.831 / 0.442);
      --pink-a8: color(display-p3 1 0.478 0.827 / 0.585);
      --pink-a9: color(display-p3 1 0.373 0.784 / 0.761);
      --pink-a10: color(display-p3 1 0.435 0.792 / 0.795);
      --pink-a11: color(display-p3 1 0.535 0.78);
      --pink-a12: color(display-p3 0.964 0.826 0.912);
      --plum-1: color(display-p3 0.09 0.068 0.092);
      --plum-2: color(display-p3 0.118 0.077 0.121);
      --plum-3: color(display-p3 0.192 0.105 0.202);
      --plum-4: color(display-p3 0.25 0.121 0.271);
      --plum-5: color(display-p3 0.293 0.152 0.319);
      --plum-6: color(display-p3 0.343 0.198 0.372);
      --plum-7: color(display-p3 0.424 0.262 0.461);
      --plum-8: color(display-p3 0.54 0.341 0.595);
      --plum-9: color(display-p3 0.624 0.313 0.708);
      --plum-10: color(display-p3 0.666 0.365 0.748);
      --plum-11: color(display-p3 0.86 0.602 0.933);
      --plum-12: color(display-p3 0.936 0.836 0.949);
      --plum-a1: color(display-p3 0.973 0.071 0.973 / 0.026);
      --plum-a2: color(display-p3 0.933 0.267 1 / 0.059);
      --plum-a3: color(display-p3 0.918 0.333 0.996 / 0.148);
      --plum-a4: color(display-p3 0.91 0.318 1 / 0.219);
      --plum-a5: color(display-p3 0.914 0.388 1 / 0.269);
      --plum-a6: color(display-p3 0.906 0.463 1 / 0.328);
      --plum-a7: color(display-p3 0.906 0.529 1 / 0.425);
      --plum-a8: color(display-p3 0.906 0.553 1 / 0.568);
      --plum-a9: color(display-p3 0.875 0.427 1 / 0.69);
      --plum-a10: color(display-p3 0.886 0.471 0.996 / 0.732);
      --plum-a11: color(display-p3 0.86 0.602 0.933);
      --plum-a12: color(display-p3 0.936 0.836 0.949);
      --purple-1: color(display-p3 0.09 0.068 0.103);
      --purple-2: color(display-p3 0.113 0.082 0.134);
      --purple-3: color(display-p3 0.175 0.112 0.224);
      --purple-4: color(display-p3 0.224 0.137 0.297);
      --purple-5: color(display-p3 0.264 0.167 0.349);
      --purple-6: color(display-p3 0.311 0.208 0.406);
      --purple-7: color(display-p3 0.381 0.266 0.496);
      --purple-8: color(display-p3 0.49 0.349 0.649);
      --purple-9: color(display-p3 0.523 0.318 0.751);
      --purple-10: color(display-p3 0.57 0.373 0.791);
      --purple-11: color(display-p3 0.8 0.62 1);
      --purple-12: color(display-p3 0.913 0.854 0.971);
      --purple-a1: color(display-p3 0.686 0.071 0.996 / 0.038);
      --purple-a2: color(display-p3 0.722 0.286 0.996 / 0.072);
      --purple-a3: color(display-p3 0.718 0.349 0.996 / 0.169);
      --purple-a4: color(display-p3 0.702 0.353 1 / 0.248);
      --purple-a5: color(display-p3 0.718 0.404 1 / 0.303);
      --purple-a6: color(display-p3 0.733 0.455 1 / 0.366);
      --purple-a7: color(display-p3 0.753 0.506 1 / 0.458);
      --purple-a8: color(display-p3 0.749 0.522 1 / 0.622);
      --purple-a9: color(display-p3 0.686 0.408 1 / 0.736);
      --purple-a10: color(display-p3 0.71 0.459 1 / 0.778);
      --purple-a11: color(display-p3 0.8 0.62 1);
      --purple-a12: color(display-p3 0.913 0.854 0.971);
      --violet-1: color(display-p3 0.077 0.071 0.118);
      --violet-2: color(display-p3 0.101 0.084 0.141);
      --violet-3: color(display-p3 0.154 0.123 0.256);
      --violet-4: color(display-p3 0.191 0.148 0.345);
      --violet-5: color(display-p3 0.226 0.182 0.396);
      --violet-6: color(display-p3 0.269 0.223 0.449);
      --violet-7: color(display-p3 0.326 0.277 0.53);
      --violet-8: color(display-p3 0.399 0.346 0.656);
      --violet-9: color(display-p3 0.417 0.341 0.784);
      --violet-10: color(display-p3 0.477 0.402 0.823);
      --violet-11: color(display-p3 0.72 0.65 1);
      --violet-12: color(display-p3 0.883 0.867 0.986);
      --violet-a1: color(display-p3 0.282 0.141 0.996 / 0.055);
      --violet-a2: color(display-p3 0.51 0.263 1 / 0.08);
      --violet-a3: color(display-p3 0.494 0.337 0.996 / 0.202);
      --violet-a4: color(display-p3 0.49 0.345 1 / 0.299);
      --violet-a5: color(display-p3 0.525 0.392 1 / 0.353);
      --violet-a6: color(display-p3 0.569 0.455 1 / 0.408);
      --violet-a7: color(display-p3 0.588 0.494 1 / 0.496);
      --violet-a8: color(display-p3 0.596 0.51 1 / 0.631);
      --violet-a9: color(display-p3 0.522 0.424 1 / 0.769);
      --violet-a10: color(display-p3 0.576 0.482 1 / 0.811);
      --violet-a11: color(display-p3 0.72 0.65 1);
      --violet-a12: color(display-p3 0.883 0.867 0.986);
      --iris-1: color(display-p3 0.075 0.075 0.114);
      --iris-2: color(display-p3 0.089 0.086 0.14);
      --iris-3: color(display-p3 0.128 0.134 0.272);
      --iris-4: color(display-p3 0.153 0.165 0.382);
      --iris-5: color(display-p3 0.192 0.201 0.44);
      --iris-6: color(display-p3 0.239 0.241 0.491);
      --iris-7: color(display-p3 0.291 0.289 0.565);
      --iris-8: color(display-p3 0.35 0.345 0.673);
      --iris-9: color(display-p3 0.357 0.357 0.81);
      --iris-10: color(display-p3 0.428 0.416 0.843);
      --iris-11: color(display-p3 0.685 0.662 1);
      --iris-12: color(display-p3 0.878 0.875 0.986);
      --iris-a1: color(display-p3 0.224 0.224 0.992 / 0.051);
      --iris-a2: color(display-p3 0.361 0.314 1 / 0.08);
      --iris-a3: color(display-p3 0.357 0.373 1 / 0.219);
      --iris-a4: color(display-p3 0.325 0.361 1 / 0.337);
      --iris-a5: color(display-p3 0.38 0.4 1 / 0.4);
      --iris-a6: color(display-p3 0.447 0.447 1 / 0.454);
      --iris-a7: color(display-p3 0.486 0.486 1 / 0.534);
      --iris-a8: color(display-p3 0.502 0.494 1 / 0.652);
      --iris-a9: color(display-p3 0.431 0.431 1 / 0.799);
      --iris-a10: color(display-p3 0.502 0.486 1 / 0.832);
      --iris-a11: color(display-p3 0.685 0.662 1);
      --iris-a12: color(display-p3 0.878 0.875 0.986);
      --cyan-1: color(display-p3 0.053 0.085 0.098);
      --cyan-2: color(display-p3 0.072 0.105 0.122);
      --cyan-3: color(display-p3 0.073 0.168 0.209);
      --cyan-4: color(display-p3 0.063 0.216 0.277);
      --cyan-5: color(display-p3 0.091 0.267 0.336);
      --cyan-6: color(display-p3 0.137 0.324 0.4);
      --cyan-7: color(display-p3 0.186 0.398 0.484);
      --cyan-8: color(display-p3 0.23 0.496 0.6);
      --cyan-9: color(display-p3 0.282 0.627 0.765);
      --cyan-10: color(display-p3 0.331 0.675 0.801);
      --cyan-11: color(display-p3 0.446 0.79 0.887);
      --cyan-12: color(display-p3 0.757 0.919 0.962);
      --cyan-a1: color(display-p3 0 0.647 0.992 / 0.034);
      --cyan-a2: color(display-p3 0.133 0.733 1 / 0.059);
      --cyan-a3: color(display-p3 0.122 0.741 0.996 / 0.152);
      --cyan-a4: color(display-p3 0.051 0.725 1 / 0.227);
      --cyan-a5: color(display-p3 0.149 0.757 1 / 0.29);
      --cyan-a6: color(display-p3 0.267 0.792 1 / 0.358);
      --cyan-a7: color(display-p3 0.333 0.808 1 / 0.446);
      --cyan-a8: color(display-p3 0.357 0.816 1 / 0.572);
      --cyan-a9: color(display-p3 0.357 0.82 1 / 0.748);
      --cyan-a10: color(display-p3 0.4 0.839 1 / 0.786);
      --cyan-a11: color(display-p3 0.446 0.79 0.887);
      --cyan-a12: color(display-p3 0.757 0.919 0.962);
      --teal-1: color(display-p3 0.059 0.083 0.079);
      --teal-2: color(display-p3 0.075 0.11 0.107);
      --teal-3: color(display-p3 0.087 0.175 0.165);
      --teal-4: color(display-p3 0.087 0.227 0.214);
      --teal-5: color(display-p3 0.12 0.277 0.261);
      --teal-6: color(display-p3 0.162 0.335 0.314);
      --teal-7: color(display-p3 0.205 0.406 0.379);
      --teal-8: color(display-p3 0.245 0.489 0.453);
      --teal-9: color(display-p3 0.297 0.637 0.581);
      --teal-10: color(display-p3 0.319 0.69 0.62);
      --teal-11: color(display-p3 0.388 0.835 0.719);
      --teal-12: color(display-p3 0.734 0.934 0.87);
      --teal-a1: color(display-p3 0 0.992 0.761 / 0.017);
      --teal-a2: color(display-p3 0.235 0.988 0.902 / 0.047);
      --teal-a3: color(display-p3 0.235 1 0.898 / 0.118);
      --teal-a4: color(display-p3 0.18 0.996 0.929 / 0.173);
      --teal-a5: color(display-p3 0.31 1 0.933 / 0.227);
      --teal-a6: color(display-p3 0.396 1 0.933 / 0.286);
      --teal-a7: color(display-p3 0.443 1 0.925 / 0.366);
      --teal-a8: color(display-p3 0.459 1 0.925 / 0.454);
      --teal-a9: color(display-p3 0.443 0.996 0.906 / 0.61);
      --teal-a10: color(display-p3 0.439 0.996 0.89 / 0.669);
      --teal-a11: color(display-p3 0.388 0.835 0.719);
      --teal-a12: color(display-p3 0.734 0.934 0.87);
      --jade-1: color(display-p3 0.059 0.083 0.071);
      --jade-2: color(display-p3 0.078 0.11 0.094);
      --jade-3: color(display-p3 0.091 0.176 0.138);
      --jade-4: color(display-p3 0.102 0.228 0.177);
      --jade-5: color(display-p3 0.133 0.279 0.221);
      --jade-6: color(display-p3 0.174 0.334 0.273);
      --jade-7: color(display-p3 0.219 0.402 0.335);
      --jade-8: color(display-p3 0.263 0.488 0.411);
      --jade-9: color(display-p3 0.319 0.63 0.521);
      --jade-10: color(display-p3 0.338 0.68 0.555);
      --jade-11: color(display-p3 0.4 0.835 0.656);
      --jade-12: color(display-p3 0.734 0.934 0.838);
      --jade-a1: color(display-p3 0 0.992 0.298 / 0.017);
      --jade-a2: color(display-p3 0.318 0.988 0.651 / 0.047);
      --jade-a3: color(display-p3 0.267 1 0.667 / 0.118);
      --jade-a4: color(display-p3 0.275 0.996 0.702 / 0.173);
      --jade-a5: color(display-p3 0.361 1 0.741 / 0.227);
      --jade-a6: color(display-p3 0.439 1 0.796 / 0.286);
      --jade-a7: color(display-p3 0.49 1 0.804 / 0.362);
      --jade-a8: color(display-p3 0.506 1 0.835 / 0.45);
      --jade-a9: color(display-p3 0.478 0.996 0.816 / 0.606);
      --jade-a10: color(display-p3 0.478 1 0.816 / 0.656);
      --jade-a11: color(display-p3 0.4 0.835 0.656);
      --jade-a12: color(display-p3 0.734 0.934 0.838);
      --green-1: color(display-p3 0.062 0.083 0.071);
      --green-2: color(display-p3 0.079 0.106 0.09);
      --green-3: color(display-p3 0.1 0.173 0.133);
      --green-4: color(display-p3 0.115 0.229 0.166);
      --green-5: color(display-p3 0.147 0.282 0.206);
      --green-6: color(display-p3 0.185 0.338 0.25);
      --green-7: color(display-p3 0.227 0.403 0.298);
      --green-8: color(display-p3 0.27 0.479 0.351);
      --green-9: color(display-p3 0.332 0.634 0.442);
      --green-10: color(display-p3 0.357 0.682 0.474);
      --green-11: color(display-p3 0.434 0.828 0.573);
      --green-12: color(display-p3 0.747 0.938 0.807);
      --green-a1: color(display-p3 0 0.992 0.298 / 0.017);
      --green-a2: color(display-p3 0.341 0.98 0.616 / 0.043);
      --green-a3: color(display-p3 0.376 0.996 0.655 / 0.114);
      --green-a4: color(display-p3 0.341 0.996 0.635 / 0.173);
      --green-a5: color(display-p3 0.408 1 0.678 / 0.232);
      --green-a6: color(display-p3 0.475 1 0.706 / 0.29);
      --green-a7: color(display-p3 0.514 1 0.706 / 0.362);
      --green-a8: color(display-p3 0.529 1 0.718 / 0.442);
      --green-a9: color(display-p3 0.502 0.996 0.682 / 0.61);
      --green-a10: color(display-p3 0.506 1 0.682 / 0.66);
      --green-a11: color(display-p3 0.434 0.828 0.573);
      --green-a12: color(display-p3 0.747 0.938 0.807);
      --grass-1: color(display-p3 0.062 0.083 0.067);
      --grass-2: color(display-p3 0.083 0.103 0.085);
      --grass-3: color(display-p3 0.118 0.163 0.122);
      --grass-4: color(display-p3 0.142 0.225 0.15);
      --grass-5: color(display-p3 0.178 0.279 0.186);
      --grass-6: color(display-p3 0.217 0.337 0.224);
      --grass-7: color(display-p3 0.258 0.4 0.264);
      --grass-8: color(display-p3 0.302 0.47 0.305);
      --grass-9: color(display-p3 0.38 0.647 0.378);
      --grass-10: color(display-p3 0.426 0.694 0.426);
      --grass-11: color(display-p3 0.535 0.807 0.542);
      --grass-12: color(display-p3 0.797 0.936 0.776);
      --grass-a1: color(display-p3 0 0.992 0.071 / 0.017);
      --grass-a2: color(display-p3 0.482 0.996 0.584 / 0.038);
      --grass-a3: color(display-p3 0.549 0.992 0.588 / 0.106);
      --grass-a4: color(display-p3 0.51 0.996 0.557 / 0.169);
      --grass-a5: color(display-p3 0.553 1 0.588 / 0.227);
      --grass-a6: color(display-p3 0.584 1 0.608 / 0.29);
      --grass-a7: color(display-p3 0.604 1 0.616 / 0.358);
      --grass-a8: color(display-p3 0.608 1 0.62 / 0.433);
      --grass-a9: color(display-p3 0.573 1 0.569 / 0.622);
      --grass-a10: color(display-p3 0.6 0.996 0.6 / 0.673);
      --grass-a11: color(display-p3 0.535 0.807 0.542);
      --grass-a12: color(display-p3 0.797 0.936 0.776);
      --brown-1: color(display-p3 0.071 0.067 0.059);
      --brown-2: color(display-p3 0.107 0.095 0.087);
      --brown-3: color(display-p3 0.151 0.13 0.115);
      --brown-4: color(display-p3 0.191 0.161 0.138);
      --brown-5: color(display-p3 0.235 0.194 0.162);
      --brown-6: color(display-p3 0.291 0.237 0.192);
      --brown-7: color(display-p3 0.365 0.295 0.232);
      --brown-8: color(display-p3 0.469 0.377 0.287);
      --brown-9: color(display-p3 0.651 0.505 0.368);
      --brown-10: color(display-p3 0.697 0.557 0.423);
      --brown-11: color(display-p3 0.835 0.715 0.597);
      --brown-12: color(display-p3 0.938 0.885 0.802);
      --brown-a1: color(display-p3 0.855 0.071 0 / 0.005);
      --brown-a2: color(display-p3 0.98 0.706 0.525 / 0.043);
      --brown-a3: color(display-p3 0.996 0.745 0.576 / 0.093);
      --brown-a4: color(display-p3 1 0.765 0.592 / 0.135);
      --brown-a5: color(display-p3 1 0.761 0.588 / 0.181);
      --brown-a6: color(display-p3 1 0.773 0.592 / 0.24);
      --brown-a7: color(display-p3 0.996 0.776 0.58 / 0.32);
      --brown-a8: color(display-p3 1 0.78 0.573 / 0.433);
      --brown-a9: color(display-p3 1 0.769 0.549 / 0.627);
      --brown-a10: color(display-p3 1 0.792 0.596 / 0.677);
      --brown-a11: color(display-p3 0.835 0.715 0.597);
      --brown-a12: color(display-p3 0.938 0.885 0.802);
      --sky-1: color(display-p3 0.056 0.078 0.116);
      --sky-2: color(display-p3 0.075 0.101 0.149);
      --sky-3: color(display-p3 0.089 0.154 0.244);
      --sky-4: color(display-p3 0.106 0.207 0.323);
      --sky-5: color(display-p3 0.135 0.261 0.394);
      --sky-6: color(display-p3 0.17 0.322 0.469);
      --sky-7: color(display-p3 0.205 0.394 0.557);
      --sky-8: color(display-p3 0.232 0.48 0.665);
      --sky-9: color(display-p3 0.585 0.877 0.983);
      --sky-10: color(display-p3 0.718 0.925 0.991);
      --sky-11: color(display-p3 0.536 0.772 0.924);
      --sky-12: color(display-p3 0.799 0.947 0.993);
      --sky-a1: color(display-p3 0 0.282 0.996 / 0.055);
      --sky-a2: color(display-p3 0.157 0.467 0.992 / 0.089);
      --sky-a3: color(display-p3 0.192 0.522 0.996 / 0.19);
      --sky-a4: color(display-p3 0.212 0.584 1 / 0.274);
      --sky-a5: color(display-p3 0.259 0.631 1 / 0.349);
      --sky-a6: color(display-p3 0.302 0.655 1 / 0.433);
      --sky-a7: color(display-p3 0.329 0.686 1 / 0.526);
      --sky-a8: color(display-p3 0.325 0.71 1 / 0.643);
      --sky-a9: color(display-p3 0.592 0.894 1 / 0.984);
      --sky-a10: color(display-p3 0.722 0.933 1 / 0.992);
      --sky-a11: color(display-p3 0.536 0.772 0.924);
      --sky-a12: color(display-p3 0.799 0.947 0.993);
      --mint-1: color(display-p3 0.059 0.082 0.081);
      --mint-2: color(display-p3 0.068 0.104 0.105);
      --mint-3: color(display-p3 0.077 0.17 0.168);
      --mint-4: color(display-p3 0.068 0.224 0.22);
      --mint-5: color(display-p3 0.104 0.275 0.264);
      --mint-6: color(display-p3 0.154 0.332 0.313);
      --mint-7: color(display-p3 0.207 0.403 0.373);
      --mint-8: color(display-p3 0.258 0.49 0.441);
      --mint-9: color(display-p3 0.62 0.908 0.834);
      --mint-10: color(display-p3 0.725 0.954 0.898);
      --mint-11: color(display-p3 0.482 0.825 0.733);
      --mint-12: color(display-p3 0.807 0.955 0.887);
      --mint-a1: color(display-p3 0 0.992 0.992 / 0.017);
      --mint-a2: color(display-p3 0.071 0.98 0.98 / 0.043);
      --mint-a3: color(display-p3 0.176 0.996 0.996 / 0.11);
      --mint-a4: color(display-p3 0.071 0.996 0.973 / 0.169);
      --mint-a5: color(display-p3 0.243 1 0.949 / 0.223);
      --mint-a6: color(display-p3 0.369 1 0.933 / 0.286);
      --mint-a7: color(display-p3 0.459 1 0.914 / 0.362);
      --mint-a8: color(display-p3 0.49 1 0.89 / 0.454);
      --mint-a9: color(display-p3 0.678 0.996 0.914 / 0.904);
      --mint-a10: color(display-p3 0.761 1 0.941 / 0.95);
      --mint-a11: color(display-p3 0.482 0.825 0.733);
      --mint-a12: color(display-p3 0.807 0.955 0.887);
      --yellow-1: color(display-p3 0.078 0.069 0.047);
      --yellow-2: color(display-p3 0.103 0.094 0.063);
      --yellow-3: color(display-p3 0.168 0.137 0.039);
      --yellow-4: color(display-p3 0.209 0.169 0);
      --yellow-5: color(display-p3 0.255 0.209 0);
      --yellow-6: color(display-p3 0.31 0.261 0.07);
      --yellow-7: color(display-p3 0.389 0.331 0.135);
      --yellow-8: color(display-p3 0.497 0.42 0.182);
      --yellow-9: color(display-p3 1 0.92 0.22);
      --yellow-10: color(display-p3 1 1 0.456);
      --yellow-11: color(display-p3 0.948 0.885 0.392);
      --yellow-12: color(display-p3 0.959 0.934 0.731);
      --yellow-a1: color(display-p3 0.973 0.369 0 / 0.013);
      --yellow-a2: color(display-p3 0.996 0.792 0 / 0.038);
      --yellow-a3: color(display-p3 0.996 0.71 0 / 0.11);
      --yellow-a4: color(display-p3 0.996 0.741 0 / 0.152);
      --yellow-a5: color(display-p3 0.996 0.765 0 / 0.202);
      --yellow-a6: color(display-p3 0.996 0.816 0.082 / 0.261);
      --yellow-a7: color(display-p3 1 0.831 0.263 / 0.345);
      --yellow-a8: color(display-p3 1 0.831 0.314 / 0.463);
      --yellow-a9: color(display-p3 1 0.922 0.22);
      --yellow-a10: color(display-p3 1 1 0.455);
      --yellow-a11: color(display-p3 0.948 0.885 0.392);
      --yellow-a12: color(display-p3 0.959 0.934 0.731);
      --amber-1: color(display-p3 0.082 0.07 0.05);
      --amber-2: color(display-p3 0.111 0.094 0.064);
      --amber-3: color(display-p3 0.178 0.128 0.049);
      --amber-4: color(display-p3 0.239 0.156 0);
      --amber-5: color(display-p3 0.29 0.193 0);
      --amber-6: color(display-p3 0.344 0.245 0.076);
      --amber-7: color(display-p3 0.422 0.314 0.141);
      --amber-8: color(display-p3 0.535 0.399 0.189);
      --amber-9: color(display-p3 1 0.77 0.26);
      --amber-10: color(display-p3 1 0.87 0.15);
      --amber-11: color(display-p3 1 0.8 0.29);
      --amber-12: color(display-p3 0.984 0.909 0.726);
      --amber-a1: color(display-p3 0.992 0.298 0 / 0.017);
      --amber-a2: color(display-p3 0.988 0.651 0 / 0.047);
      --amber-a3: color(display-p3 1 0.6 0 / 0.118);
      --amber-a4: color(display-p3 1 0.557 0 / 0.185);
      --amber-a5: color(display-p3 1 0.592 0 / 0.24);
      --amber-a6: color(display-p3 1 0.659 0.094 / 0.299);
      --amber-a7: color(display-p3 1 0.714 0.263 / 0.383);
      --amber-a8: color(display-p3 0.996 0.729 0.306 / 0.5);
      --amber-a9: color(display-p3 1 0.769 0.259);
      --amber-a10: color(display-p3 1 0.871 0.149);
      --amber-a11: color(display-p3 1 0.8 0.29);
      --amber-a12: color(display-p3 0.984 0.909 0.726);
      --gold-1: color(display-p3 0.071 0.071 0.067);
      --gold-2: color(display-p3 0.104 0.101 0.09);
      --gold-3: color(display-p3 0.141 0.136 0.122);
      --gold-4: color(display-p3 0.177 0.17 0.152);
      --gold-5: color(display-p3 0.217 0.207 0.185);
      --gold-6: color(display-p3 0.265 0.252 0.225);
      --gold-7: color(display-p3 0.327 0.31 0.277);
      --gold-8: color(display-p3 0.407 0.384 0.342);
      --gold-9: color(display-p3 0.579 0.517 0.41);
      --gold-10: color(display-p3 0.628 0.566 0.463);
      --gold-11: color(display-p3 0.784 0.728 0.635);
      --gold-12: color(display-p3 0.906 0.887 0.855);
      --gold-a1: color(display-p3 0.855 0.855 0.071 / 0.005);
      --gold-a2: color(display-p3 0.98 0.89 0.616 / 0.043);
      --gold-a3: color(display-p3 1 0.949 0.753 / 0.08);
      --gold-a4: color(display-p3 1 0.933 0.8 / 0.118);
      --gold-a5: color(display-p3 1 0.949 0.804 / 0.16);
      --gold-a6: color(display-p3 1 0.925 0.8 / 0.215);
      --gold-a7: color(display-p3 1 0.945 0.831 / 0.278);
      --gold-a8: color(display-p3 1 0.937 0.82 / 0.366);
      --gold-a9: color(display-p3 0.996 0.882 0.69 / 0.551);
      --gold-a10: color(display-p3 1 0.894 0.725 / 0.601);
      --gold-a11: color(display-p3 0.784 0.728 0.635);
      --gold-a12: color(display-p3 0.906 0.887 0.855);
      --bronze-1: color(display-p3 0.076 0.067 0.063);
      --bronze-2: color(display-p3 0.106 0.097 0.093);
      --bronze-3: color(display-p3 0.147 0.132 0.125);
      --bronze-4: color(display-p3 0.185 0.166 0.156);
      --bronze-5: color(display-p3 0.227 0.202 0.19);
      --bronze-6: color(display-p3 0.278 0.246 0.23);
      --bronze-7: color(display-p3 0.343 0.302 0.281);
      --bronze-8: color(display-p3 0.426 0.374 0.347);
      --bronze-9: color(display-p3 0.611 0.507 0.455);
      --bronze-10: color(display-p3 0.66 0.556 0.504);
      --bronze-11: color(display-p3 0.81 0.707 0.655);
      --bronze-12: color(display-p3 0.921 0.88 0.854);
      --bronze-a1: color(display-p3 0.941 0.067 0 / 0.009);
      --bronze-a2: color(display-p3 0.98 0.8 0.706 / 0.043);
      --bronze-a3: color(display-p3 0.988 0.851 0.761 / 0.085);
      --bronze-a4: color(display-p3 0.996 0.839 0.78 / 0.127);
      --bronze-a5: color(display-p3 0.996 0.863 0.773 / 0.173);
      --bronze-a6: color(display-p3 1 0.863 0.796 / 0.227);
      --bronze-a7: color(display-p3 1 0.867 0.8 / 0.295);
      --bronze-a8: color(display-p3 1 0.859 0.788 / 0.387);
      --bronze-a9: color(display-p3 1 0.82 0.733 / 0.585);
      --bronze-a10: color(display-p3 1 0.839 0.761 / 0.635);
      --bronze-a11: color(display-p3 0.81 0.707 0.655);
      --bronze-a12: color(display-p3 0.921 0.88 0.854);
      --gray-1: color(display-p3 0.067 0.067 0.067);
      --gray-2: color(display-p3 0.098 0.098 0.098);
      --gray-3: color(display-p3 0.135 0.135 0.135);
      --gray-4: color(display-p3 0.163 0.163 0.163);
      --gray-5: color(display-p3 0.192 0.192 0.192);
      --gray-6: color(display-p3 0.228 0.228 0.228);
      --gray-7: color(display-p3 0.283 0.283 0.283);
      --gray-8: color(display-p3 0.375 0.375 0.375);
      --gray-9: color(display-p3 0.431 0.431 0.431);
      --gray-10: color(display-p3 0.484 0.484 0.484);
      --gray-11: color(display-p3 0.706 0.706 0.706);
      --gray-12: color(display-p3 0.933 0.933 0.933);
      --gray-a1: color(display-p3 0 0 0 / 0);
      --gray-a2: color(display-p3 1 1 1 / 0.034);
      --gray-a3: color(display-p3 1 1 1 / 0.071);
      --gray-a4: color(display-p3 1 1 1 / 0.105);
      --gray-a5: color(display-p3 1 1 1 / 0.134);
      --gray-a6: color(display-p3 1 1 1 / 0.172);
      --gray-a7: color(display-p3 1 1 1 / 0.231);
      --gray-a8: color(display-p3 1 1 1 / 0.332);
      --gray-a9: color(display-p3 1 1 1 / 0.391);
      --gray-a10: color(display-p3 1 1 1 / 0.445);
      --gray-a11: color(display-p3 1 1 1 / 0.685);
      --gray-a12: color(display-p3 1 1 1 / 0.929);
      --mauve-1: color(display-p3 0.07 0.067 0.074);
      --mauve-2: color(display-p3 0.101 0.098 0.105);
      --mauve-3: color(display-p3 0.138 0.134 0.144);
      --mauve-4: color(display-p3 0.167 0.161 0.175);
      --mauve-5: color(display-p3 0.196 0.189 0.206);
      --mauve-6: color(display-p3 0.232 0.225 0.245);
      --mauve-7: color(display-p3 0.286 0.277 0.302);
      --mauve-8: color(display-p3 0.383 0.373 0.408);
      --mauve-9: color(display-p3 0.434 0.428 0.467);
      --mauve-10: color(display-p3 0.487 0.48 0.519);
      --mauve-11: color(display-p3 0.707 0.7 0.735);
      --mauve-12: color(display-p3 0.933 0.933 0.94);
      --mauve-a1: color(display-p3 0 0 0 / 0);
      --mauve-a2: color(display-p3 0.996 0.992 1 / 0.034);
      --mauve-a3: color(display-p3 0.937 0.933 0.992 / 0.077);
      --mauve-a4: color(display-p3 0.957 0.918 0.996 / 0.111);
      --mauve-a5: color(display-p3 0.937 0.906 0.996 / 0.145);
      --mauve-a6: color(display-p3 0.953 0.925 0.996 / 0.183);
      --mauve-a7: color(display-p3 0.945 0.929 1 / 0.246);
      --mauve-a8: color(display-p3 0.937 0.918 1 / 0.361);
      --mauve-a9: color(display-p3 0.933 0.918 1 / 0.424);
      --mauve-a10: color(display-p3 0.941 0.925 1 / 0.479);
      --mauve-a11: color(display-p3 0.965 0.961 1 / 0.712);
      --mauve-a12: color(display-p3 0.992 0.992 1 / 0.937);
      --slate-1: color(display-p3 0.067 0.067 0.074);
      --slate-2: color(display-p3 0.095 0.098 0.105);
      --slate-3: color(display-p3 0.13 0.135 0.145);
      --slate-4: color(display-p3 0.156 0.163 0.176);
      --slate-5: color(display-p3 0.183 0.191 0.206);
      --slate-6: color(display-p3 0.215 0.226 0.244);
      --slate-7: color(display-p3 0.265 0.28 0.302);
      --slate-8: color(display-p3 0.357 0.381 0.409);
      --slate-9: color(display-p3 0.415 0.431 0.463);
      --slate-10: color(display-p3 0.469 0.483 0.514);
      --slate-11: color(display-p3 0.692 0.704 0.728);
      --slate-12: color(display-p3 0.93 0.933 0.94);
      --slate-a1: color(display-p3 0 0 0 / 0);
      --slate-a2: color(display-p3 0.875 0.992 1 / 0.034);
      --slate-a3: color(display-p3 0.882 0.933 0.992 / 0.077);
      --slate-a4: color(display-p3 0.882 0.953 0.996 / 0.111);
      --slate-a5: color(display-p3 0.878 0.929 0.996 / 0.145);
      --slate-a6: color(display-p3 0.882 0.949 0.996 / 0.183);
      --slate-a7: color(display-p3 0.882 0.929 1 / 0.246);
      --slate-a8: color(display-p3 0.871 0.937 1 / 0.361);
      --slate-a9: color(display-p3 0.898 0.937 1 / 0.42);
      --slate-a10: color(display-p3 0.918 0.945 1 / 0.475);
      --slate-a11: color(display-p3 0.949 0.969 0.996 / 0.708);
      --slate-a12: color(display-p3 0.988 0.992 1 / 0.937);
      --sage-1: color(display-p3 0.064 0.07 0.067);
      --sage-2: color(display-p3 0.092 0.098 0.094);
      --sage-3: color(display-p3 0.128 0.135 0.131);
      --sage-4: color(display-p3 0.155 0.164 0.159);
      --sage-5: color(display-p3 0.183 0.193 0.188);
      --sage-6: color(display-p3 0.218 0.23 0.224);
      --sage-7: color(display-p3 0.269 0.285 0.277);
      --sage-8: color(display-p3 0.362 0.382 0.373);
      --sage-9: color(display-p3 0.398 0.438 0.421);
      --sage-10: color(display-p3 0.453 0.49 0.474);
      --sage-11: color(display-p3 0.685 0.709 0.697);
      --sage-12: color(display-p3 0.927 0.933 0.93);
      --sage-a1: color(display-p3 0 0 0 / 0);
      --sage-a2: color(display-p3 0.976 0.988 0.984 / 0.03);
      --sage-a3: color(display-p3 0.992 0.945 0.941 / 0.072);
      --sage-a4: color(display-p3 0.988 0.996 0.992 / 0.102);
      --sage-a5: color(display-p3 0.992 1 0.996 / 0.131);
      --sage-a6: color(display-p3 0.973 1 0.976 / 0.173);
      --sage-a7: color(display-p3 0.957 1 0.976 / 0.233);
      --sage-a8: color(display-p3 0.957 1 0.984 / 0.334);
      --sage-a9: color(display-p3 0.902 1 0.957 / 0.397);
      --sage-a10: color(display-p3 0.929 1 0.973 / 0.452);
      --sage-a11: color(display-p3 0.969 1 0.988 / 0.688);
      --sage-a12: color(display-p3 0.992 1 0.996 / 0.929);
      --olive-1: color(display-p3 0.067 0.07 0.063);
      --olive-2: color(display-p3 0.095 0.098 0.091);
      --olive-3: color(display-p3 0.131 0.135 0.126);
      --olive-4: color(display-p3 0.158 0.163 0.153);
      --olive-5: color(display-p3 0.186 0.192 0.18);
      --olive-6: color(display-p3 0.221 0.229 0.215);
      --olive-7: color(display-p3 0.273 0.284 0.266);
      --olive-8: color(display-p3 0.365 0.382 0.359);
      --olive-9: color(display-p3 0.414 0.438 0.404);
      --olive-10: color(display-p3 0.467 0.49 0.458);
      --olive-11: color(display-p3 0.69 0.709 0.682);
      --olive-12: color(display-p3 0.927 0.933 0.926);
      --olive-a1: color(display-p3 0 0 0 / 0);
      --olive-a2: color(display-p3 0.984 0.988 0.976 / 0.03);
      --olive-a3: color(display-p3 0.992 0.996 0.988 / 0.068);
      --olive-a4: color(display-p3 0.953 0.996 0.949 / 0.102);
      --olive-a5: color(display-p3 0.969 1 0.965 / 0.131);
      --olive-a6: color(display-p3 0.973 1 0.969 / 0.169);
      --olive-a7: color(display-p3 0.98 1 0.961 / 0.228);
      --olive-a8: color(display-p3 0.961 1 0.957 / 0.334);
      --olive-a9: color(display-p3 0.949 1 0.922 / 0.397);
      --olive-a10: color(display-p3 0.953 1 0.941 / 0.452);
      --olive-a11: color(display-p3 0.976 1 0.965 / 0.688);
      --olive-a12: color(display-p3 0.992 1 0.992 / 0.929);
      --sand-1: color(display-p3 0.067 0.067 0.063);
      --sand-2: color(display-p3 0.098 0.098 0.094);
      --sand-3: color(display-p3 0.135 0.135 0.129);
      --sand-4: color(display-p3 0.164 0.163 0.156);
      --sand-5: color(display-p3 0.193 0.192 0.183);
      --sand-6: color(display-p3 0.23 0.229 0.217);
      --sand-7: color(display-p3 0.285 0.282 0.267);
      --sand-8: color(display-p3 0.384 0.378 0.357);
      --sand-9: color(display-p3 0.434 0.428 0.403);
      --sand-10: color(display-p3 0.487 0.481 0.456);
      --sand-11: color(display-p3 0.707 0.703 0.68);
      --sand-12: color(display-p3 0.933 0.933 0.926);
      --sand-a1: color(display-p3 0 0 0 / 0);
      --sand-a2: color(display-p3 0.992 0.992 0.988 / 0.034);
      --sand-a3: color(display-p3 0.996 0.996 0.992 / 0.072);
      --sand-a4: color(display-p3 0.992 0.992 0.953 / 0.106);
      --sand-a5: color(display-p3 1 1 0.965 / 0.135);
      --sand-a6: color(display-p3 1 0.976 0.929 / 0.177);
      --sand-a7: color(display-p3 1 0.984 0.929 / 0.236);
      --sand-a8: color(display-p3 1 0.976 0.925 / 0.341);
      --sand-a9: color(display-p3 1 0.98 0.925 / 0.395);
      --sand-a10: color(display-p3 1 0.992 0.933 / 0.45);
      --sand-a11: color(display-p3 1 0.996 0.961 / 0.685);
      --sand-a12: color(display-p3 1 1 0.992 / 0.929);
      --blue-1: color(display-p3 0.0465 0.0657 0.1105);
      --blue-2: color(display-p3 0.0663 0.0906 0.1476);
      --blue-3: color(display-p3 0.0859 0.1415 0.2862);
      --blue-4: color(display-p3 0.1043 0.1801 0.3856);
      --blue-5: color(display-p3 0.1332 0.2211 0.4586);
      --blue-6: color(display-p3 0.1682 0.2649 0.5215);
      --blue-7: color(display-p3 0.2045 0.3132 0.5992);
      --blue-8: color(display-p3 0.2392 0.3661 0.7024);
      --blue-9: color(display-p3 0.1632 0.3246 0.8163);
      --blue-10: color(display-p3 0.290 0.420 0.760);
      --blue-11: color(display-p3 0.5682 0.7055 1);
      --blue-12: color(display-p3 0.8327 0.8855 1);
      --blue-a1: color(display-p3 0 0.0667 0.9882 / 0.047);
      --blue-a2: color(display-p3 0.0667 0.3333 0.9922 / 0.089);
      --blue-a3: color(display-p3 0.1529 0.4 1 / 0.236);
      --blue-a4: color(display-p3 0.1843 0.4039 1 / 0.341);
      --blue-a5: color(display-p3 0.2275 0.4314 1 / 0.421);
      --blue-a6: color(display-p3 0.2745 0.4667 1 / 0.488);
      --blue-a7: color(display-p3 0.3098 0.502 1 / 0.572);
      --blue-a8: color(display-p3 0.3216 0.5059 1 / 0.681);
      --blue-a9: color(display-p3 0.1922 0.3922 1 / 0.803);
      --blue-a10: color(display-p3 0.430 0.590 0.970 / 0.660);
      --blue-a11: color(display-p3 0.5843 0.7255 1 / 0.975);
      --blue-a12: color(display-p3 0.8431 0.8941 1 / 0.988);
      --orange-1: color(display-p3 0.0838 0.06 0.0528);
      --orange-2: color(display-p3 0.1165 0.0814 0.0709);
      --orange-3: color(display-p3 0.2127 0.0912 0.0582);
      --orange-4: color(display-p3 0.2949 0.0848 0.0301);
      --orange-5: color(display-p3 0.3542 0.1132 0.0498);
      --orange-6: color(display-p3 0.4147 0.164 0.098);
      --orange-7: color(display-p3 0.5064 0.2289 0.1549);
      --orange-8: color(display-p3 0.6542 0.2998 0.2054);
      --orange-9: color(display-p3 0.9049 0.3335 0.1831);
      --orange-10: color(display-p3 0.8499 0.2787 0.124);
      --orange-11: color(display-p3 1 0.5537 0.4215);
      --orange-12: color(display-p3 0.9711 0.8289 0.7855);
      --orange-a1: color(display-p3 0.9608 0 0 / 0.022);
      --orange-a2: color(display-p3 0.9922 0.298 0.1451 / 0.051);
      --orange-a3: color(display-p3 1 0.2196 0.0196 / 0.156);
      --orange-a4: color(display-p3 1 0.149 0 / 0.244);
      --orange-a5: color(display-p3 1 0.2196 0.0196 / 0.311);
      --orange-a6: color(display-p3 1 0.3294 0.149 / 0.374);
      --orange-a7: color(display-p3 1 0.4118 0.2588 / 0.471);
      --orange-a8: color(display-p3 1 0.4353 0.2941 / 0.631);
      --orange-a9: color(display-p3 0.9961 0.3608 0.1961 / 0.9);
      --orange-a10: color(display-p3 1 0.3176 0.149 / 0.837);
      --orange-a11: color(display-p3 1 0.6275 0.5137 / 0.937);
      --orange-a12: color(display-p3 1 0.8549 0.8078 / 0.971);
      --lemon-1: color(display-p3 0.0656 0.0703 0.044);
      --lemon-2: color(display-p3 0.0916 0.098 0.0617);
      --lemon-3: color(display-p3 0.1412 0.1537 0.0731);
      --lemon-4: color(display-p3 0.1825 0.2001 0.0756);
      --lemon-5: color(display-p3 0.226 0.2481 0.086);
      --lemon-6: color(display-p3 0.2762 0.3016 0.1182);
      --lemon-7: color(display-p3 0.3378 0.3671 0.1606);
      --lemon-8: color(display-p3 0.4128 0.4482 0.1964);
      --lemon-9: color(display-p3 0.8781 0.9576 0.3125);
      --lemon-10: color(display-p3 0.842 0.9116 0.4038);
      --lemon-11: color(display-p3 0.8302 0.8994 0.3913);
      --lemon-12: color(display-p3 0.9183 0.9596 0.7245);
      --lemon-a1: color(display-p3 0 0.8549 0 / 0.005);
      --lemon-a2: color(display-p3 0.7608 0.9922 0 / 0.034);
      --lemon-a3: color(display-p3 0.8706 0.9961 0.1529 / 0.093);
      --lemon-a4: color(display-p3 0.8627 1 0.1216 / 0.143);
      --lemon-a5: color(display-p3 0.8784 1 0.1686 / 0.194);
      --lemon-a6: color(display-p3 0.8902 0.9961 0.2706 / 0.253);
      --lemon-a7: color(display-p3 0.902 1 0.3608 / 0.324);
      --lemon-a8: color(display-p3 0.9059 1 0.3843 / 0.412);
      --lemon-a9: color(display-p3 0.9176 1 0.3255 / 0.954);
      --lemon-a10: color(display-p3 0.9216 0.9961 0.4392 / 0.904);
      --lemon-a11: color(display-p3 0.9216 1 0.4314 / 0.891);
      --lemon-a12: color(display-p3 0.9529 1 0.7529 / 0.958);
      --indigo-1: color(display-p3 0.0604 0.0576 0.1185);
      --indigo-2: color(display-p3 0.0833 0.0784 0.163);
      --indigo-3: color(display-p3 0.1353 0.1026 0.326);
      --indigo-4: color(display-p3 0.176 0.0967 0.4632);
      --indigo-5: color(display-p3 0.2121 0.1331 0.5303);
      --indigo-6: color(display-p3 0.251 0.1807 0.5884);
      --indigo-7: color(display-p3 0.3001 0.2284 0.6756);
      --indigo-8: color(display-p3 0.3613 0.2735 0.8079);
      --indigo-9: color(display-p3 0.3562 0.1176 0.9345);
      --indigo-10: color(display-p3 0.450 0.340 0.980);
      --indigo-11: color(display-p3 0.6727 0.6534 1);
      --indigo-12: color(display-p3 0.8675 0.8681 1);
      --indigo-a1: color(display-p3 0 0 0.9961 / 0.055);
      --indigo-a2: color(display-p3 0.2627 0.1843 1 / 0.101);
      --indigo-a3: color(display-p3 0.3059 0.1922 1 / 0.282);
      --indigo-a4: color(display-p3 0.3255 0.1333 1 / 0.425);
      --indigo-a5: color(display-p3 0.3529 0.1961 0.9961 / 0.5);
      --indigo-a6: color(display-p3 0.4 0.2706 1 / 0.559);
      --indigo-a7: color(display-p3 0.4275 0.3137 1 / 0.652);
      --indigo-a8: color(display-p3 0.4392 0.3294 1 / 0.795);
      --indigo-a9: color(display-p3 0.3804 0.1216 1 / 0.929);
      --indigo-a10: color(display-p3 0.510 0.410 1 / 0.84);
      --indigo-a11: color(display-p3 0.6902 0.6745 1 / 0.975);
      --indigo-a12: color(display-p3 0.8784 0.8784 1 / 0.988);
      --lime-1: color(display-p3 0.0529 0.075 0.0475);
      --lime-2: color(display-p3 0.0775 0.1066 0.0704);
      --lime-3: color(display-p3 0.1005 0.1769 0.0818);
      --lime-4: color(display-p3 0.1152 0.2353 0.0855);
      --lime-5: color(display-p3 0.1467 0.2898 0.1112);
      --lime-6: color(display-p3 0.1839 0.3473 0.1435);
      --lime-7: color(display-p3 0.2236 0.4142 0.1764);
      --lime-8: color(display-p3 0.2636 0.4926 0.2067);
      --lime-9: color(display-p3 0.3843 0.8306 0.2661);
      --lime-10: color(display-p3 0.3386 0.7852 0.2155);
      --lime-11: color(display-p3 0.4067 0.8532 0.29);
      --lime-12: color(display-p3 0.7388 0.9555 0.686);
      --lime-a1: color(display-p3 0 0.9412 0 / 0.009);
      --lime-a2: color(display-p3 0.3412 0.9804 0.1608 / 0.043);
      --lime-a3: color(display-p3 0.3686 1 0.2 / 0.118);
      --lime-a4: color(display-p3 0.3294 1 0.1765 / 0.181);
      --lime-a5: color(display-p3 0.3961 1 0.2627 / 0.24);
      --lime-a6: color(display-p3 0.4588 1 0.3294 / 0.303);
      --lime-a7: color(display-p3 0.4863 1 0.3608 / 0.374);
      --lime-a8: color(display-p3 0.502 1 0.3804 / 0.454);
      --lime-a9: color(display-p3 0.4549 1 0.3098 / 0.82);
      --lime-a10: color(display-p3 0.451 1 0.2824 / 0.769);
      --lime-a11: color(display-p3 0.4706 1 0.3294 / 0.845);
      --lime-a12: color(display-p3 0.7725 1 0.7137 / 0.954);
      --magenta-1: color(display-p3 0.0869 0.0558 0.066);
      --magenta-2: color(display-p3 0.1221 0.0724 0.0894);
      --magenta-3: color(display-p3 0.2188 0.074 0.133);
      --magenta-4: color(display-p3 0.3021 0.0442 0.1671);
      --magenta-5: color(display-p3 0.3605 0.0696 0.2057);
      --magenta-6: color(display-p3 0.4246 0.1213 0.2555);
      --magenta-7: color(display-p3 0.5232 0.1797 0.327);
      --magenta-8: color(display-p3 0.6796 0.233 0.4267);
      --magenta-9: color(display-p3 0.9175 0.2003 0.5465);
      --magenta-10: color(display-p3 0.8614 0.1183 0.4996);
      --magenta-11: color(display-p3 1 0.5058 0.7167);
      --magenta-12: color(display-p3 0.9986 0.8207 0.8777);
      --magenta-a1: color(display-p3 0.9608 0 0.0667 / 0.022);
      --magenta-a2: color(display-p3 1 0.2 0.4667 / 0.059);
      --magenta-a3: color(display-p3 1 0.1176 0.4745 / 0.164);
      --magenta-a4: color(display-p3 0.9961 0 0.4549 / 0.253);
      --magenta-a5: color(display-p3 0.9961 0.0784 0.502 / 0.316);
      --magenta-a6: color(display-p3 1 0.2118 0.5608 / 0.383);
      --magenta-a7: color(display-p3 1 0.298 0.5961 / 0.488);
      --magenta-a8: color(display-p3 1 0.3176 0.6157 / 0.656);
      --magenta-a9: color(display-p3 1 0.2157 0.5922 / 0.912);
      --magenta-a10: color(display-p3 1 0.2078 0.5804 / 0.853);
      --magenta-a11: color(display-p3 1 0.5961 0.7647 / 0.933);
      --magenta-a12: color(display-p3 0.9961 0.8431 0.902 / 0.967);
        --gray-2-translucent: color(display-p3 0.1137 0.1137 0.1137 / 0.5);
        --mauve-2-translucent: color(display-p3 0.1176 0.1137 0.1176 / 0.5);
        --slate-2-translucent: color(display-p3 0.1059 0.1137 0.1176 / 0.5);
        --sage-2-translucent: color(display-p3 0.102 0.1137 0.1059 / 0.5);
        --olive-2-translucent: color(display-p3 0.1059 0.1137 0.102 / 0.5);
        --sand-2-translucent: color(display-p3 0.1137 0.1137 0.1059 / 0.5);
        --gray-surface: color(display-p3 0.1255 0.1255 0.1255 / 0.5);
        --mauve-surface: color(display-p3 0.1333 0.1255 0.1333 / 0.5);
        --slate-surface: color(display-p3 0.1176 0.1255 0.1333 / 0.5);
        --sage-surface: color(display-p3 0.1176 0.1255 0.1176 / 0.5);
        --olive-surface: color(display-p3 0.1176 0.1255 0.1176 / 0.5);
        --sand-surface: color(display-p3 0.1255 0.1255 0.1255 / 0.5);
        --tomato-surface: color(display-p3 0.1569 0.0941 0.0784 / 0.5);
        --red-surface: color(display-p3 0.1647 0.0863 0.0863 / 0.5);
        --ruby-surface: color(display-p3 0.1569 0.0941 0.1098 / 0.5);
        --crimson-surface: color(display-p3 0.1647 0.0863 0.1176 / 0.5);
        --pink-surface: color(display-p3 0.1725 0.0784 0.149 / 0.5);
        --plum-surface: color(display-p3 0.1647 0.0863 0.1725 / 0.5);
        --purple-surface: color(display-p3 0.149 0.0941 0.1961 / 0.5);
        --violet-surface: color(display-p3 0.1333 0.102 0.2118 / 0.5);
        --iris-surface: color(display-p3 0.1098 0.102 0.2118 / 0.5);
        --cyan-surface: color(display-p3 0.0784 0.1412 0.1725 / 0.5);
        --teal-surface: color(display-p3 0.0863 0.149 0.1412 / 0.5);
        --jade-surface: color(display-p3 0.0863 0.149 0.1176 / 0.5);
        --green-surface: color(display-p3 0.0941 0.1412 0.1098 / 0.5);
        --grass-surface: color(display-p3 0.102 0.1333 0.102 / 0.5);
        --brown-surface: color(display-p3 0.1412 0.1176 0.102 / 0.5);
        --bronze-surface: color(display-p3 0.1412 0.1255 0.1176 / 0.5);
        --gold-surface: color(display-p3 0.1412 0.1333 0.1098 / 0.5);
        --sky-surface: color(display-p3 0.0863 0.1333 0.2196 / 0.5);
        --mint-surface: color(display-p3 0.0941 0.149 0.1412 / 0.5);
        --yellow-surface: color(display-p3 0.1333 0.1176 0.0706 / 0.5);
        --amber-surface: color(display-p3 0.1412 0.1176 0.0784 / 0.5);
        --blue-surface: color(display-p3 0.0627 0.1098 0.2275 / 0.5);
        --orange-surface: color(display-p3 0.1647 0.0941 0.0706 / 0.5);
        --indigo-surface: color(display-p3 0.0941 0.0863 0.2588 / 0.5);
        --magenta-surface: color(display-p3 0.1725 0.0706 0.1098 / 0.5);

        --lemon-surface: color(display-p3 0.1098 0.1255 0.0588 / 0.5);
        --lime-surface: color(display-p3 0.0863 0.1412 0.0706 / 0.5);
    }
  }
}

:root {
  --black-a1: rgba(0, 0, 0, 0.05);
  --black-a2: rgba(0, 0, 0, 0.1);
  --black-a3: rgba(0, 0, 0, 0.15);
  --black-a4: rgba(0, 0, 0, 0.2);
  --black-a5: rgba(0, 0, 0, 0.3);
  --black-a6: rgba(0, 0, 0, 0.4);
  --black-a7: rgba(0, 0, 0, 0.5);
  --black-a8: rgba(0, 0, 0, 0.6);
  --black-a9: rgba(0, 0, 0, 0.7);
  --black-a10: rgba(0, 0, 0, 0.8);
  --black-a11: rgba(0, 0, 0, 0.9);
  --black-a12: rgba(0, 0, 0, 0.95);
  --white-a1: rgba(255, 255, 255, 0.05);
  --white-a2: rgba(255, 255, 255, 0.1);
  --white-a3: rgba(255, 255, 255, 0.15);
  --white-a4: rgba(255, 255, 255, 0.2);
  --white-a5: rgba(255, 255, 255, 0.3);
  --white-a6: rgba(255, 255, 255, 0.4);
  --white-a7: rgba(255, 255, 255, 0.5);
  --white-a8: rgba(255, 255, 255, 0.6);
  --white-a9: rgba(255, 255, 255, 0.7);
  --white-a10: rgba(255, 255, 255, 0.8);
  --white-a11: rgba(255, 255, 255, 0.9);
  --white-a12: rgba(255, 255, 255, 0.95);
  --tomato-9-contrast: white;
  --red-9-contrast: white;
  --ruby-9-contrast: white;
  --crimson-9-contrast: white;
  --pink-9-contrast: white;
  --plum-9-contrast: white;
  --purple-9-contrast: white;
  --violet-9-contrast: white;
  --iris-9-contrast: white;
  --cyan-9-contrast: white;
  --teal-9-contrast: white;
  --jade-9-contrast: white;
  --green-9-contrast: white;
  --grass-9-contrast: white;
  --brown-9-contrast: white;
  --sky-9-contrast: #1c2024;
  --mint-9-contrast: #1a211e;
  --yellow-9-contrast: #21201c;
  --amber-9-contrast: #21201c;
  --gold-9-contrast: white;
  --bronze-9-contrast: white;
  --gray-9-contrast: white;
  --blue-9-contrast: white;
  --orange-9-contrast: white;
  --indigo-9-contrast: white;
  --magenta-9-contrast: #141212;
  --lemon-9-contrast: #20240d;
  --lime-9-contrast: #162715;
  --radius-factor: 1;
  --radius-full: 0px;
  --radius-thumb: 9999px;

  --radius-1: 3px;
  --radius-2: 4px;
  --radius-3: 6px;
  --radius-4: 8px;
  --radius-5: 12px;
  --radius-6: 16px;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    :root {
      --black-a1: color(display-p3 0 0 0 / 0.05);
      --black-a2: color(display-p3 0 0 0 / 0.1);
      --black-a3: color(display-p3 0 0 0 / 0.15);
      --black-a4: color(display-p3 0 0 0 / 0.2);
      --black-a5: color(display-p3 0 0 0 / 0.3);
      --black-a6: color(display-p3 0 0 0 / 0.4);
      --black-a7: color(display-p3 0 0 0 / 0.5);
      --black-a8: color(display-p3 0 0 0 / 0.6);
      --black-a9: color(display-p3 0 0 0 / 0.7);
      --black-a10: color(display-p3 0 0 0 / 0.8);
      --black-a11: color(display-p3 0 0 0 / 0.9);
      --black-a12: color(display-p3 0 0 0 / 0.95);
      --white-a1: color(display-p3 1 1 1 / 0.05);
      --white-a2: color(display-p3 1 1 1 / 0.1);
      --white-a3: color(display-p3 1 1 1 / 0.15);
      --white-a4: color(display-p3 1 1 1 / 0.2);
      --white-a5: color(display-p3 1 1 1 / 0.3);
      --white-a6: color(display-p3 1 1 1 / 0.4);
      --white-a7: color(display-p3 1 1 1 / 0.5);
      --white-a8: color(display-p3 1 1 1 / 0.6);
      --white-a9: color(display-p3 1 1 1 / 0.7);
      --white-a10: color(display-p3 1 1 1 / 0.8);
      --white-a11: color(display-p3 1 1 1 / 0.9);
      --white-a12: color(display-p3 1 1 1 / 0.95);
    }
  }
}

:where(.frosted-ui) {
  --color-background: white;
  --color-overlay: var(--black-a6);
  --color-panel-solid: white;
  --color-panel-translucent: rgba(255, 255, 255, 0.7);
  --color-surface: rgba(255, 255, 255, 0.9);
  --backdrop-filter-panel: blur(20px) saturate(190%) contrast(50%) brightness(130%);
  --color-stroke: var(--gray-a5);
  --color-panel-elevation-a1: rgba(255, 255, 255, 0.01);
  --color-panel-elevation-a2: rgba(255, 255, 255, 0.014);
  --color-panel-elevation-a3: rgba(255, 255, 255, 0.027);
  --color-panel-elevation-a4: rgba(255, 255, 255, 0.045);
  --color-panel-elevation-a5: rgba(255, 255, 255, 0.067);
  --color-panel-elevation-a6: rgba(255, 255, 255, 0.092);
  --color-panel-elevation-a7: rgba(255, 255, 255, 0.118);
  --color-panel-elevation-a8: rgba(255, 255, 255, 0.143);
  --color-panel-elevation-a9: rgba(255, 255, 255, 0.165);
  --color-panel-elevation-a10: rgba(255, 255, 255, 0.183);
  --color-panel-elevation-a11: rgba(255, 255, 255, 0.196);
  --color-panel-elevation-a12: rgba(255, 255, 255, 0.2);
  --color-transparent: rgb(0 0 0 / 0);
  --shadow-1:
    inset 0 0 0 1px var(--gray-a5),
    inset 0 1.5px 2px 0 var(--gray-a2),
    inset 0 1.5px 2px 0 var(--black-a2);

  --shadow-2:
    0 0 0 1px var(--gray-a3),
    0 0 0 0.5px var(--black-a1),
    0 1px 1px 0 var(--gray-a4),
    0 2px 1px -1px var(--black-a1),
    0 1px 3px 0 var(--black-a1);

  --shadow-3:
    0 0 0 1px var(--gray-a3),
    0 2px 3px -2px var(--gray-a3),
    0 3px 12px -4px var(--black-a2),
    0 4px 16px -8px var(--black-a2);

  --shadow-4:
    0 0 0 1px var(--gray-a6),
    0 8px 40px var(--black-a1),
    0 12px 32px -16px var(--gray-a3);

  --shadow-5:
    0 0 0 1px var(--gray-a5),
    0 12px 60px var(--black-a3),
    0 12px 32px -16px var(--gray-a5);

  --shadow-6:
    0 0 0 1px var(--gray-a3),
    0 12px 60px var(--black-a3),
    0 16px 64px var(--gray-a2),
    0 16px 36px -20px var(--gray-a7);
  --base-button-classic-active-filter: brightness(0.92) saturate(1.1);
  --base-button-classic-high-contrast-hover-filter: contrast(0.88) saturate(1.1) brightness(1.1);
  --base-button-classic-high-contrast-active-filter: contrast(0.82) saturate(1.2) brightness(1.16);
  --base-button-solid-active-filter: brightness(0.92) saturate(1.1);
  --base-button-solid-high-contrast-hover-filter: contrast(0.88) saturate(1.1) brightness(1.1);
  --base-button-solid-high-contrast-active-filter: contrast(0.82) saturate(1.2) brightness(1.16);
  --card-classic-hover-box-shadow:
    0 0 0 1px var(--gray-a5),
    0 1px 1px 1px var(--black-a2),
    0 2px 1px -1px var(--gray-a3),
    0 2px 3px -2px var(--black-a1),
    0 3px 12px -4px var(--gray-a3),
    0 4px 16px -8px var(--black-a1);
  --card-background: var(--color-panel-solid);
  --color-base-menu-outline: transparent;
  --color-popover-outline: transparent;
  --kbd-box-shadow:
    inset 0 -0.05em 0.5em var(--gray-a2),
    inset 0 0.05em var(--white-a12),
    inset 0 0.25em 0.5em var(--gray-a2),
    inset 0 -0.05em var(--gray-a6),
    0 0 0 0.05em var(--gray-a5),
    0 0.08em 0.17em var(--gray-a7);
  --color-popover-outline: transparent;
  --color-segmented-control-thumb: var(--color-panel-solid);
  --select-trigger-classic-box-shadow:
		inset 0 0 0 1px var(--gray-a5),
		inset 0 2px 1px var(--white-a11),
		inset 0 -2px 1px var(--gray-a4)
	;
  --color-select-outline: transparent;
  --slider-range-high-contrast-background-image: linear-gradient(var(--black-a8), var(--black-a8));
  --slider-disabled-blend-mode: multiply;
  --switch-disabled-blend-mode: multiply;
  --switch-button-high-contrast-checked-color-overlay: var(--black-a8);
  --switch-button-high-contrast-checked-active-before-filter: contrast(0.82) saturate(1.2) brightness(1.16);
  --switch-button-surface-checked-active-filter: brightness(0.92) saturate(1.1);
  --data-table-border-color: color-mix(in oklab, var(--gray-a5), var(--gray-6));
  --color-tooltip-outline: transparent;
}

:is(.dark, .dark-theme),
:is(.dark, .dark-theme) :where(.frosted-ui:not(.light, .light-theme)) {
  --color-background: var(--gray-1);
  --color-overlay: var(--black-a8);
  --color-panel-solid: var(--gray-2);
  --color-panel-translucent: var(--gray-2-translucent);
  --color-surface: rgba(0, 0, 0, 0.25);
  --backdrop-filter-panel: blur(20px) saturate(190%) contrast(90%) brightness(80%);
  --color-stroke: var(--gray-a4);
  --shadow-1:
    inset 0 -1px 1px 0 var(--gray-a3),
    inset 0 0 0 1px var(--gray-a3),
    inset 0 3px 4px 0 var(--black-a5),
    inset 0 0 0 1px var(--gray-a4);

  --shadow-2:
    0 0 0 1px var(--gray-a6),
    0 0 0 0.5px var(--black-a3),
    0 1px 1px 0 var(--black-a6),
    0 2px 1px -1px var(--black-a6),
    0 1px 3px 0 var(--black-a8);

  --shadow-3:
    0 0 0 1px var(--gray-a6),
    0 2px 3px -2px var(--black-a3),
    0 3px 8px -2px var(--black-a6),
    0 4px 12px -4px var(--black-a7);

  --shadow-4:
    0 0 0 1px var(--gray-a6),
    0 8px 40px var(--black-a3),
    0 12px 32px -16px var(--black-a5);

  --shadow-5:
    0 0 0 1px var(--gray-a6) inset,
    0 12px 60px var(--black-a5),
    0 12px 32px -16px var(--black-a7);

  --shadow-6:
    0 0 0 1px var(--gray-a6),
    0 12px 60px var(--black-a4),
    0 16px 64px var(--black-a6),
    0 16px 36px -20px var(--black-a11);
  --base-button-classic-active-filter: brightness(1.08);
  --base-button-classic-high-contrast-hover-filter: contrast(0.88) saturate(1.3) brightness(1.14);
  --base-button-classic-high-contrast-active-filter: brightness(0.95) saturate(1.2);
  --base-button-solid-active-filter: brightness(1.08);
  --base-button-solid-high-contrast-hover-filter: contrast(0.88) saturate(1.3) brightness(1.18);
  --base-button-solid-high-contrast-active-filter: brightness(0.95) saturate(1.2);
  --card-classic-hover-box-shadow:
    0 0 0 1px var(--gray-a7),
    0 0 1px 1px var(--gray-a7),
    0 0 1px -1px var(--gray-a4),
    0 0 3px -2px var(--gray-a3),
    0 0 12px -2px var(--gray-a3),
    0 0 16px -8px var(--gray-a9);
  --card-background: var(--color-panel-solid);
  --color-base-menu-outline: black;
  --color-popover-outline: black;
  --kbd-box-shadow:
    inset 0 -0.05em 0.5em var(--gray-a3),
    inset 0 0.05em var(--gray-a11),
    inset 0 0.25em 0.5em var(--gray-a2),
    inset 0 -0.1em var(--black-a11),
    0 0 0 0.075em var(--gray-a7),
    0 0.08em 0.17em var(--black-a12);
  --color-popover-outline: black;
  --color-segmented-control-thumb: transparent;
  --select-trigger-classic-box-shadow:
    inset 0 0 0 1px var(--white-a4),
    inset 0 1px 1px var(--white-a4),
    inset 0 -1px 1px var(--black-a9)
	;
  --color-select-outline: black;
  --slider-range-high-contrast-background-image: none;
  --slider-disabled-blend-mode: screen;
  --switch-disabled-blend-mode: screen;
  --switch-button-high-contrast-checked-color-overlay: transparent;
  --switch-button-high-contrast-checked-active-before-filter: brightness(1.08);
  --switch-button-surface-checked-active-filter: brightness(1.08);
  --data-table-border-color: color-mix(in oklab, var(--gray-a3), var(--gray-4));
  --color-tooltip-outline: black;
}

@supports (color: color(display-p3 1 1 1)) {
  @media (color-gamut: p3) {
    .frosted-ui {
      --color-transparent: color(display-p3 0 0 0 / 0);
    }
  }
}

.frosted-ui:where(.light, .light-theme) {
  color-scheme: light;
}

.frosted-ui:where(.dark, .dark-theme) {
  color-scheme: dark;
}

.frosted-ui,
[data-accent-color]:where(:not([data-accent-color='gray'])) {
  --color-selection-root: var(--accent-a5);
  --color-focus-root: var(--accent-8);
}

.frosted-ui ::selection {
  background-color: var(--color-selection-root);
}

.frosted-ui:where([data-has-background='true']) {
    background-color: var(--color-background);
  }

.frosted-ui:where([data-panel-background='solid']) {
    --color-panel: var(--color-panel-solid);
  }

.frosted-ui:where([data-panel-background='translucent']) {
    --color-panel: var(--color-panel-translucent);
  }

[data-accent-color='tomato'] {
  --color-surface-accent: var(--tomato-surface);

  --accent-1: var(--tomato-1);
  --accent-2: var(--tomato-2);
  --accent-3: var(--tomato-3);
  --accent-4: var(--tomato-4);
  --accent-5: var(--tomato-5);
  --accent-6: var(--tomato-6);
  --accent-7: var(--tomato-7);
  --accent-8: var(--tomato-8);
  --accent-9: var(--tomato-9);
  --accent-9-contrast: var(--tomato-9-contrast);
  --accent-10: var(--tomato-10);
  --accent-11: var(--tomato-11);
  --accent-12: var(--tomato-12);

  --accent-a1: var(--tomato-a1);
  --accent-a2: var(--tomato-a2);
  --accent-a3: var(--tomato-a3);
  --accent-a4: var(--tomato-a4);
  --accent-a5: var(--tomato-a5);
  --accent-a6: var(--tomato-a6);
  --accent-a7: var(--tomato-a7);
  --accent-a8: var(--tomato-a8);
  --accent-a9: var(--tomato-a9);
  --accent-a10: var(--tomato-a10);
  --accent-a11: var(--tomato-a11);
  --accent-a12: var(--tomato-a12);
}

[data-accent-color='red'] {
  --color-surface-accent: var(--red-surface);

  --accent-1: var(--red-1);
  --accent-2: var(--red-2);
  --accent-3: var(--red-3);
  --accent-4: var(--red-4);
  --accent-5: var(--red-5);
  --accent-6: var(--red-6);
  --accent-7: var(--red-7);
  --accent-8: var(--red-8);
  --accent-9: var(--red-9);
  --accent-9-contrast: var(--red-9-contrast);
  --accent-10: var(--red-10);
  --accent-11: var(--red-11);
  --accent-12: var(--red-12);

  --accent-a1: var(--red-a1);
  --accent-a2: var(--red-a2);
  --accent-a3: var(--red-a3);
  --accent-a4: var(--red-a4);
  --accent-a5: var(--red-a5);
  --accent-a6: var(--red-a6);
  --accent-a7: var(--red-a7);
  --accent-a8: var(--red-a8);
  --accent-a9: var(--red-a9);
  --accent-a10: var(--red-a10);
  --accent-a11: var(--red-a11);
  --accent-a12: var(--red-a12);
}

[data-accent-color='ruby'] {
  --color-surface-accent: var(--ruby-surface);

  --accent-1: var(--ruby-1);
  --accent-2: var(--ruby-2);
  --accent-3: var(--ruby-3);
  --accent-4: var(--ruby-4);
  --accent-5: var(--ruby-5);
  --accent-6: var(--ruby-6);
  --accent-7: var(--ruby-7);
  --accent-8: var(--ruby-8);
  --accent-9: var(--ruby-9);
  --accent-9-contrast: var(--ruby-9-contrast);
  --accent-10: var(--ruby-10);
  --accent-11: var(--ruby-11);
  --accent-12: var(--ruby-12);

  --accent-a1: var(--ruby-a1);
  --accent-a2: var(--ruby-a2);
  --accent-a3: var(--ruby-a3);
  --accent-a4: var(--ruby-a4);
  --accent-a5: var(--ruby-a5);
  --accent-a6: var(--ruby-a6);
  --accent-a7: var(--ruby-a7);
  --accent-a8: var(--ruby-a8);
  --accent-a9: var(--ruby-a9);
  --accent-a10: var(--ruby-a10);
  --accent-a11: var(--ruby-a11);
  --accent-a12: var(--ruby-a12);
}

[data-accent-color='crimson'] {
  --color-surface-accent: var(--crimson-surface);

  --accent-1: var(--crimson-1);
  --accent-2: var(--crimson-2);
  --accent-3: var(--crimson-3);
  --accent-4: var(--crimson-4);
  --accent-5: var(--crimson-5);
  --accent-6: var(--crimson-6);
  --accent-7: var(--crimson-7);
  --accent-8: var(--crimson-8);
  --accent-9: var(--crimson-9);
  --accent-9-contrast: var(--crimson-9-contrast);
  --accent-10: var(--crimson-10);
  --accent-11: var(--crimson-11);
  --accent-12: var(--crimson-12);

  --accent-a1: var(--crimson-a1);
  --accent-a2: var(--crimson-a2);
  --accent-a3: var(--crimson-a3);
  --accent-a4: var(--crimson-a4);
  --accent-a5: var(--crimson-a5);
  --accent-a6: var(--crimson-a6);
  --accent-a7: var(--crimson-a7);
  --accent-a8: var(--crimson-a8);
  --accent-a9: var(--crimson-a9);
  --accent-a10: var(--crimson-a10);
  --accent-a11: var(--crimson-a11);
  --accent-a12: var(--crimson-a12);
}

[data-accent-color='pink'] {
  --color-surface-accent: var(--pink-surface);

  --accent-1: var(--pink-1);
  --accent-2: var(--pink-2);
  --accent-3: var(--pink-3);
  --accent-4: var(--pink-4);
  --accent-5: var(--pink-5);
  --accent-6: var(--pink-6);
  --accent-7: var(--pink-7);
  --accent-8: var(--pink-8);
  --accent-9: var(--pink-9);
  --accent-9-contrast: var(--pink-9-contrast);
  --accent-10: var(--pink-10);
  --accent-11: var(--pink-11);
  --accent-12: var(--pink-12);

  --accent-a1: var(--pink-a1);
  --accent-a2: var(--pink-a2);
  --accent-a3: var(--pink-a3);
  --accent-a4: var(--pink-a4);
  --accent-a5: var(--pink-a5);
  --accent-a6: var(--pink-a6);
  --accent-a7: var(--pink-a7);
  --accent-a8: var(--pink-a8);
  --accent-a9: var(--pink-a9);
  --accent-a10: var(--pink-a10);
  --accent-a11: var(--pink-a11);
  --accent-a12: var(--pink-a12);
}

[data-accent-color='plum'] {
  --color-surface-accent: var(--plum-surface);

  --accent-1: var(--plum-1);
  --accent-2: var(--plum-2);
  --accent-3: var(--plum-3);
  --accent-4: var(--plum-4);
  --accent-5: var(--plum-5);
  --accent-6: var(--plum-6);
  --accent-7: var(--plum-7);
  --accent-8: var(--plum-8);
  --accent-9: var(--plum-9);
  --accent-9-contrast: var(--plum-9-contrast);
  --accent-10: var(--plum-10);
  --accent-11: var(--plum-11);
  --accent-12: var(--plum-12);

  --accent-a1: var(--plum-a1);
  --accent-a2: var(--plum-a2);
  --accent-a3: var(--plum-a3);
  --accent-a4: var(--plum-a4);
  --accent-a5: var(--plum-a5);
  --accent-a6: var(--plum-a6);
  --accent-a7: var(--plum-a7);
  --accent-a8: var(--plum-a8);
  --accent-a9: var(--plum-a9);
  --accent-a10: var(--plum-a10);
  --accent-a11: var(--plum-a11);
  --accent-a12: var(--plum-a12);
}

[data-accent-color='purple'] {
  --color-surface-accent: var(--purple-surface);

  --accent-1: var(--purple-1);
  --accent-2: var(--purple-2);
  --accent-3: var(--purple-3);
  --accent-4: var(--purple-4);
  --accent-5: var(--purple-5);
  --accent-6: var(--purple-6);
  --accent-7: var(--purple-7);
  --accent-8: var(--purple-8);
  --accent-9: var(--purple-9);
  --accent-9-contrast: var(--purple-9-contrast);
  --accent-10: var(--purple-10);
  --accent-11: var(--purple-11);
  --accent-12: var(--purple-12);

  --accent-a1: var(--purple-a1);
  --accent-a2: var(--purple-a2);
  --accent-a3: var(--purple-a3);
  --accent-a4: var(--purple-a4);
  --accent-a5: var(--purple-a5);
  --accent-a6: var(--purple-a6);
  --accent-a7: var(--purple-a7);
  --accent-a8: var(--purple-a8);
  --accent-a9: var(--purple-a9);
  --accent-a10: var(--purple-a10);
  --accent-a11: var(--purple-a11);
  --accent-a12: var(--purple-a12);
}

[data-accent-color='violet'] {
  --color-surface-accent: var(--violet-surface);

  --accent-1: var(--violet-1);
  --accent-2: var(--violet-2);
  --accent-3: var(--violet-3);
  --accent-4: var(--violet-4);
  --accent-5: var(--violet-5);
  --accent-6: var(--violet-6);
  --accent-7: var(--violet-7);
  --accent-8: var(--violet-8);
  --accent-9: var(--violet-9);
  --accent-9-contrast: var(--violet-9-contrast);
  --accent-10: var(--violet-10);
  --accent-11: var(--violet-11);
  --accent-12: var(--violet-12);

  --accent-a1: var(--violet-a1);
  --accent-a2: var(--violet-a2);
  --accent-a3: var(--violet-a3);
  --accent-a4: var(--violet-a4);
  --accent-a5: var(--violet-a5);
  --accent-a6: var(--violet-a6);
  --accent-a7: var(--violet-a7);
  --accent-a8: var(--violet-a8);
  --accent-a9: var(--violet-a9);
  --accent-a10: var(--violet-a10);
  --accent-a11: var(--violet-a11);
  --accent-a12: var(--violet-a12);
}

[data-accent-color='iris'] {
  --color-surface-accent: var(--iris-surface);

  --accent-1: var(--iris-1);
  --accent-2: var(--iris-2);
  --accent-3: var(--iris-3);
  --accent-4: var(--iris-4);
  --accent-5: var(--iris-5);
  --accent-6: var(--iris-6);
  --accent-7: var(--iris-7);
  --accent-8: var(--iris-8);
  --accent-9: var(--iris-9);
  --accent-9-contrast: var(--iris-9-contrast);
  --accent-10: var(--iris-10);
  --accent-11: var(--iris-11);
  --accent-12: var(--iris-12);

  --accent-a1: var(--iris-a1);
  --accent-a2: var(--iris-a2);
  --accent-a3: var(--iris-a3);
  --accent-a4: var(--iris-a4);
  --accent-a5: var(--iris-a5);
  --accent-a6: var(--iris-a6);
  --accent-a7: var(--iris-a7);
  --accent-a8: var(--iris-a8);
  --accent-a9: var(--iris-a9);
  --accent-a10: var(--iris-a10);
  --accent-a11: var(--iris-a11);
  --accent-a12: var(--iris-a12);
}

[data-accent-color='cyan'] {
  --color-surface-accent: var(--cyan-surface);

  --accent-1: var(--cyan-1);
  --accent-2: var(--cyan-2);
  --accent-3: var(--cyan-3);
  --accent-4: var(--cyan-4);
  --accent-5: var(--cyan-5);
  --accent-6: var(--cyan-6);
  --accent-7: var(--cyan-7);
  --accent-8: var(--cyan-8);
  --accent-9: var(--cyan-9);
  --accent-9-contrast: var(--cyan-9-contrast);
  --accent-10: var(--cyan-10);
  --accent-11: var(--cyan-11);
  --accent-12: var(--cyan-12);

  --accent-a1: var(--cyan-a1);
  --accent-a2: var(--cyan-a2);
  --accent-a3: var(--cyan-a3);
  --accent-a4: var(--cyan-a4);
  --accent-a5: var(--cyan-a5);
  --accent-a6: var(--cyan-a6);
  --accent-a7: var(--cyan-a7);
  --accent-a8: var(--cyan-a8);
  --accent-a9: var(--cyan-a9);
  --accent-a10: var(--cyan-a10);
  --accent-a11: var(--cyan-a11);
  --accent-a12: var(--cyan-a12);
}

[data-accent-color='teal'] {
  --color-surface-accent: var(--teal-surface);

  --accent-1: var(--teal-1);
  --accent-2: var(--teal-2);
  --accent-3: var(--teal-3);
  --accent-4: var(--teal-4);
  --accent-5: var(--teal-5);
  --accent-6: var(--teal-6);
  --accent-7: var(--teal-7);
  --accent-8: var(--teal-8);
  --accent-9: var(--teal-9);
  --accent-9-contrast: var(--teal-9-contrast);
  --accent-10: var(--teal-10);
  --accent-11: var(--teal-11);
  --accent-12: var(--teal-12);

  --accent-a1: var(--teal-a1);
  --accent-a2: var(--teal-a2);
  --accent-a3: var(--teal-a3);
  --accent-a4: var(--teal-a4);
  --accent-a5: var(--teal-a5);
  --accent-a6: var(--teal-a6);
  --accent-a7: var(--teal-a7);
  --accent-a8: var(--teal-a8);
  --accent-a9: var(--teal-a9);
  --accent-a10: var(--teal-a10);
  --accent-a11: var(--teal-a11);
  --accent-a12: var(--teal-a12);
}

[data-accent-color='jade'] {
  --color-surface-accent: var(--jade-surface);

  --accent-1: var(--jade-1);
  --accent-2: var(--jade-2);
  --accent-3: var(--jade-3);
  --accent-4: var(--jade-4);
  --accent-5: var(--jade-5);
  --accent-6: var(--jade-6);
  --accent-7: var(--jade-7);
  --accent-8: var(--jade-8);
  --accent-9: var(--jade-9);
  --accent-9-contrast: var(--jade-9-contrast);
  --accent-10: var(--jade-10);
  --accent-11: var(--jade-11);
  --accent-12: var(--jade-12);

  --accent-a1: var(--jade-a1);
  --accent-a2: var(--jade-a2);
  --accent-a3: var(--jade-a3);
  --accent-a4: var(--jade-a4);
  --accent-a5: var(--jade-a5);
  --accent-a6: var(--jade-a6);
  --accent-a7: var(--jade-a7);
  --accent-a8: var(--jade-a8);
  --accent-a9: var(--jade-a9);
  --accent-a10: var(--jade-a10);
  --accent-a11: var(--jade-a11);
  --accent-a12: var(--jade-a12);
}

[data-accent-color='green'] {
  --color-surface-accent: var(--green-surface);

  --accent-1: var(--green-1);
  --accent-2: var(--green-2);
  --accent-3: var(--green-3);
  --accent-4: var(--green-4);
  --accent-5: var(--green-5);
  --accent-6: var(--green-6);
  --accent-7: var(--green-7);
  --accent-8: var(--green-8);
  --accent-9: var(--green-9);
  --accent-9-contrast: var(--green-9-contrast);
  --accent-10: var(--green-10);
  --accent-11: var(--green-11);
  --accent-12: var(--green-12);

  --accent-a1: var(--green-a1);
  --accent-a2: var(--green-a2);
  --accent-a3: var(--green-a3);
  --accent-a4: var(--green-a4);
  --accent-a5: var(--green-a5);
  --accent-a6: var(--green-a6);
  --accent-a7: var(--green-a7);
  --accent-a8: var(--green-a8);
  --accent-a9: var(--green-a9);
  --accent-a10: var(--green-a10);
  --accent-a11: var(--green-a11);
  --accent-a12: var(--green-a12);
}

[data-accent-color='grass'] {
  --color-surface-accent: var(--grass-surface);

  --accent-1: var(--grass-1);
  --accent-2: var(--grass-2);
  --accent-3: var(--grass-3);
  --accent-4: var(--grass-4);
  --accent-5: var(--grass-5);
  --accent-6: var(--grass-6);
  --accent-7: var(--grass-7);
  --accent-8: var(--grass-8);
  --accent-9: var(--grass-9);
  --accent-9-contrast: var(--grass-9-contrast);
  --accent-10: var(--grass-10);
  --accent-11: var(--grass-11);
  --accent-12: var(--grass-12);

  --accent-a1: var(--grass-a1);
  --accent-a2: var(--grass-a2);
  --accent-a3: var(--grass-a3);
  --accent-a4: var(--grass-a4);
  --accent-a5: var(--grass-a5);
  --accent-a6: var(--grass-a6);
  --accent-a7: var(--grass-a7);
  --accent-a8: var(--grass-a8);
  --accent-a9: var(--grass-a9);
  --accent-a10: var(--grass-a10);
  --accent-a11: var(--grass-a11);
  --accent-a12: var(--grass-a12);
}

[data-accent-color='brown'] {
  --color-surface-accent: var(--brown-surface);

  --accent-1: var(--brown-1);
  --accent-2: var(--brown-2);
  --accent-3: var(--brown-3);
  --accent-4: var(--brown-4);
  --accent-5: var(--brown-5);
  --accent-6: var(--brown-6);
  --accent-7: var(--brown-7);
  --accent-8: var(--brown-8);
  --accent-9: var(--brown-9);
  --accent-9-contrast: var(--brown-9-contrast);
  --accent-10: var(--brown-10);
  --accent-11: var(--brown-11);
  --accent-12: var(--brown-12);

  --accent-a1: var(--brown-a1);
  --accent-a2: var(--brown-a2);
  --accent-a3: var(--brown-a3);
  --accent-a4: var(--brown-a4);
  --accent-a5: var(--brown-a5);
  --accent-a6: var(--brown-a6);
  --accent-a7: var(--brown-a7);
  --accent-a8: var(--brown-a8);
  --accent-a9: var(--brown-a9);
  --accent-a10: var(--brown-a10);
  --accent-a11: var(--brown-a11);
  --accent-a12: var(--brown-a12);
}

[data-accent-color='sky'] {
  --color-surface-accent: var(--sky-surface);

  --accent-1: var(--sky-1);
  --accent-2: var(--sky-2);
  --accent-3: var(--sky-3);
  --accent-4: var(--sky-4);
  --accent-5: var(--sky-5);
  --accent-6: var(--sky-6);
  --accent-7: var(--sky-7);
  --accent-8: var(--sky-8);
  --accent-9: var(--sky-9);
  --accent-9-contrast: var(--sky-9-contrast);
  --accent-10: var(--sky-10);
  --accent-11: var(--sky-11);
  --accent-12: var(--sky-12);

  --accent-a1: var(--sky-a1);
  --accent-a2: var(--sky-a2);
  --accent-a3: var(--sky-a3);
  --accent-a4: var(--sky-a4);
  --accent-a5: var(--sky-a5);
  --accent-a6: var(--sky-a6);
  --accent-a7: var(--sky-a7);
  --accent-a8: var(--sky-a8);
  --accent-a9: var(--sky-a9);
  --accent-a10: var(--sky-a10);
  --accent-a11: var(--sky-a11);
  --accent-a12: var(--sky-a12);
}

[data-accent-color='mint'] {
  --color-surface-accent: var(--mint-surface);

  --accent-1: var(--mint-1);
  --accent-2: var(--mint-2);
  --accent-3: var(--mint-3);
  --accent-4: var(--mint-4);
  --accent-5: var(--mint-5);
  --accent-6: var(--mint-6);
  --accent-7: var(--mint-7);
  --accent-8: var(--mint-8);
  --accent-9: var(--mint-9);
  --accent-9-contrast: var(--mint-9-contrast);
  --accent-10: var(--mint-10);
  --accent-11: var(--mint-11);
  --accent-12: var(--mint-12);

  --accent-a1: var(--mint-a1);
  --accent-a2: var(--mint-a2);
  --accent-a3: var(--mint-a3);
  --accent-a4: var(--mint-a4);
  --accent-a5: var(--mint-a5);
  --accent-a6: var(--mint-a6);
  --accent-a7: var(--mint-a7);
  --accent-a8: var(--mint-a8);
  --accent-a9: var(--mint-a9);
  --accent-a10: var(--mint-a10);
  --accent-a11: var(--mint-a11);
  --accent-a12: var(--mint-a12);
}

[data-accent-color='yellow'] {
  --color-surface-accent: var(--yellow-surface);

  --accent-1: var(--yellow-1);
  --accent-2: var(--yellow-2);
  --accent-3: var(--yellow-3);
  --accent-4: var(--yellow-4);
  --accent-5: var(--yellow-5);
  --accent-6: var(--yellow-6);
  --accent-7: var(--yellow-7);
  --accent-8: var(--yellow-8);
  --accent-9: var(--yellow-9);
  --accent-9-contrast: var(--yellow-9-contrast);
  --accent-10: var(--yellow-10);
  --accent-11: var(--yellow-11);
  --accent-12: var(--yellow-12);

  --accent-a1: var(--yellow-a1);
  --accent-a2: var(--yellow-a2);
  --accent-a3: var(--yellow-a3);
  --accent-a4: var(--yellow-a4);
  --accent-a5: var(--yellow-a5);
  --accent-a6: var(--yellow-a6);
  --accent-a7: var(--yellow-a7);
  --accent-a8: var(--yellow-a8);
  --accent-a9: var(--yellow-a9);
  --accent-a10: var(--yellow-a10);
  --accent-a11: var(--yellow-a11);
  --accent-a12: var(--yellow-a12);
}

[data-accent-color='amber'] {
  --color-surface-accent: var(--amber-surface);

  --accent-1: var(--amber-1);
  --accent-2: var(--amber-2);
  --accent-3: var(--amber-3);
  --accent-4: var(--amber-4);
  --accent-5: var(--amber-5);
  --accent-6: var(--amber-6);
  --accent-7: var(--amber-7);
  --accent-8: var(--amber-8);
  --accent-9: var(--amber-9);
  --accent-9-contrast: var(--amber-9-contrast);
  --accent-10: var(--amber-10);
  --accent-11: var(--amber-11);
  --accent-12: var(--amber-12);

  --accent-a1: var(--amber-a1);
  --accent-a2: var(--amber-a2);
  --accent-a3: var(--amber-a3);
  --accent-a4: var(--amber-a4);
  --accent-a5: var(--amber-a5);
  --accent-a6: var(--amber-a6);
  --accent-a7: var(--amber-a7);
  --accent-a8: var(--amber-a8);
  --accent-a9: var(--amber-a9);
  --accent-a10: var(--amber-a10);
  --accent-a11: var(--amber-a11);
  --accent-a12: var(--amber-a12);
}

[data-accent-color='gold'] {
  --color-surface-accent: var(--gold-surface);

  --accent-1: var(--gold-1);
  --accent-2: var(--gold-2);
  --accent-3: var(--gold-3);
  --accent-4: var(--gold-4);
  --accent-5: var(--gold-5);
  --accent-6: var(--gold-6);
  --accent-7: var(--gold-7);
  --accent-8: var(--gold-8);
  --accent-9: var(--gold-9);
  --accent-9-contrast: var(--gold-9-contrast);
  --accent-10: var(--gold-10);
  --accent-11: var(--gold-11);
  --accent-12: var(--gold-12);

  --accent-a1: var(--gold-a1);
  --accent-a2: var(--gold-a2);
  --accent-a3: var(--gold-a3);
  --accent-a4: var(--gold-a4);
  --accent-a5: var(--gold-a5);
  --accent-a6: var(--gold-a6);
  --accent-a7: var(--gold-a7);
  --accent-a8: var(--gold-a8);
  --accent-a9: var(--gold-a9);
  --accent-a10: var(--gold-a10);
  --accent-a11: var(--gold-a11);
  --accent-a12: var(--gold-a12);
}

[data-accent-color='bronze'] {
  --color-surface-accent: var(--bronze-surface);

  --accent-1: var(--bronze-1);
  --accent-2: var(--bronze-2);
  --accent-3: var(--bronze-3);
  --accent-4: var(--bronze-4);
  --accent-5: var(--bronze-5);
  --accent-6: var(--bronze-6);
  --accent-7: var(--bronze-7);
  --accent-8: var(--bronze-8);
  --accent-9: var(--bronze-9);
  --accent-9-contrast: var(--bronze-9-contrast);
  --accent-10: var(--bronze-10);
  --accent-11: var(--bronze-11);
  --accent-12: var(--bronze-12);

  --accent-a1: var(--bronze-a1);
  --accent-a2: var(--bronze-a2);
  --accent-a3: var(--bronze-a3);
  --accent-a4: var(--bronze-a4);
  --accent-a5: var(--bronze-a5);
  --accent-a6: var(--bronze-a6);
  --accent-a7: var(--bronze-a7);
  --accent-a8: var(--bronze-a8);
  --accent-a9: var(--bronze-a9);
  --accent-a10: var(--bronze-a10);
  --accent-a11: var(--bronze-a11);
  --accent-a12: var(--bronze-a12);
}

[data-accent-color='gray'] {
  --color-surface-accent: var(--gray-surface);

  --accent-1: var(--gray-1);
  --accent-2: var(--gray-2);
  --accent-3: var(--gray-3);
  --accent-4: var(--gray-4);
  --accent-5: var(--gray-5);
  --accent-6: var(--gray-6);
  --accent-7: var(--gray-7);
  --accent-8: var(--gray-8);
  --accent-9: var(--gray-9);
  --accent-9-contrast: var(--gray-9-contrast);
  --accent-10: var(--gray-10);
  --accent-11: var(--gray-11);
  --accent-12: var(--gray-12);

  --accent-a1: var(--gray-a1);
  --accent-a2: var(--gray-a2);
  --accent-a3: var(--gray-a3);
  --accent-a4: var(--gray-a4);
  --accent-a5: var(--gray-a5);
  --accent-a6: var(--gray-a6);
  --accent-a7: var(--gray-a7);
  --accent-a8: var(--gray-a8);
  --accent-a9: var(--gray-a9);
  --accent-a10: var(--gray-a10);
  --accent-a11: var(--gray-a11);
  --accent-a12: var(--gray-a12);
}

[data-accent-color='blue'] {
  --color-surface-accent: var(--blue-surface);

  --accent-1: var(--blue-1);
  --accent-2: var(--blue-2);
  --accent-3: var(--blue-3);
  --accent-4: var(--blue-4);
  --accent-5: var(--blue-5);
  --accent-6: var(--blue-6);
  --accent-7: var(--blue-7);
  --accent-8: var(--blue-8);
  --accent-9: var(--blue-9);
  --accent-9-contrast: var(--blue-9-contrast);
  --accent-10: var(--blue-10);
  --accent-11: var(--blue-11);
  --accent-12: var(--blue-12);

  --accent-a1: var(--blue-a1);
  --accent-a2: var(--blue-a2);
  --accent-a3: var(--blue-a3);
  --accent-a4: var(--blue-a4);
  --accent-a5: var(--blue-a5);
  --accent-a6: var(--blue-a6);
  --accent-a7: var(--blue-a7);
  --accent-a8: var(--blue-a8);
  --accent-a9: var(--blue-a9);
  --accent-a10: var(--blue-a10);
  --accent-a11: var(--blue-a11);
  --accent-a12: var(--blue-a12);
}

[data-accent-color='orange'] {
  --color-surface-accent: var(--orange-surface);

  --accent-1: var(--orange-1);
  --accent-2: var(--orange-2);
  --accent-3: var(--orange-3);
  --accent-4: var(--orange-4);
  --accent-5: var(--orange-5);
  --accent-6: var(--orange-6);
  --accent-7: var(--orange-7);
  --accent-8: var(--orange-8);
  --accent-9: var(--orange-9);
  --accent-9-contrast: var(--orange-9-contrast);
  --accent-10: var(--orange-10);
  --accent-11: var(--orange-11);
  --accent-12: var(--orange-12);

  --accent-a1: var(--orange-a1);
  --accent-a2: var(--orange-a2);
  --accent-a3: var(--orange-a3);
  --accent-a4: var(--orange-a4);
  --accent-a5: var(--orange-a5);
  --accent-a6: var(--orange-a6);
  --accent-a7: var(--orange-a7);
  --accent-a8: var(--orange-a8);
  --accent-a9: var(--orange-a9);
  --accent-a10: var(--orange-a10);
  --accent-a11: var(--orange-a11);
  --accent-a12: var(--orange-a12);
}

[data-accent-color='indigo'] {
  --color-surface-accent: var(--indigo-surface);

  --accent-1: var(--indigo-1);
  --accent-2: var(--indigo-2);
  --accent-3: var(--indigo-3);
  --accent-4: var(--indigo-4);
  --accent-5: var(--indigo-5);
  --accent-6: var(--indigo-6);
  --accent-7: var(--indigo-7);
  --accent-8: var(--indigo-8);
  --accent-9: var(--indigo-9);
  --accent-9-contrast: var(--indigo-9-contrast);
  --accent-10: var(--indigo-10);
  --accent-11: var(--indigo-11);
  --accent-12: var(--indigo-12);

  --accent-a1: var(--indigo-a1);
  --accent-a2: var(--indigo-a2);
  --accent-a3: var(--indigo-a3);
  --accent-a4: var(--indigo-a4);
  --accent-a5: var(--indigo-a5);
  --accent-a6: var(--indigo-a6);
  --accent-a7: var(--indigo-a7);
  --accent-a8: var(--indigo-a8);
  --accent-a9: var(--indigo-a9);
  --accent-a10: var(--indigo-a10);
  --accent-a11: var(--indigo-a11);
  --accent-a12: var(--indigo-a12);
}

[data-accent-color='magenta'] {
  --color-surface-accent: var(--magenta-surface);

  --accent-1: var(--magenta-1);
  --accent-2: var(--magenta-2);
  --accent-3: var(--magenta-3);
  --accent-4: var(--magenta-4);
  --accent-5: var(--magenta-5);
  --accent-6: var(--magenta-6);
  --accent-7: var(--magenta-7);
  --accent-8: var(--magenta-8);
  --accent-9: var(--magenta-9);
  --accent-9-contrast: var(--magenta-9-contrast);
  --accent-10: var(--magenta-10);
  --accent-11: var(--magenta-11);
  --accent-12: var(--magenta-12);

  --accent-a1: var(--magenta-a1);
  --accent-a2: var(--magenta-a2);
  --accent-a3: var(--magenta-a3);
  --accent-a4: var(--magenta-a4);
  --accent-a5: var(--magenta-a5);
  --accent-a6: var(--magenta-a6);
  --accent-a7: var(--magenta-a7);
  --accent-a8: var(--magenta-a8);
  --accent-a9: var(--magenta-a9);
  --accent-a10: var(--magenta-a10);
  --accent-a11: var(--magenta-a11);
  --accent-a12: var(--magenta-a12);
}

[data-accent-color='lemon'] {
  --color-surface-accent: var(--lemon-surface);

  --accent-1: var(--lemon-1);
  --accent-2: var(--lemon-2);
  --accent-3: var(--lemon-3);
  --accent-4: var(--lemon-4);
  --accent-5: var(--lemon-5);
  --accent-6: var(--lemon-6);
  --accent-7: var(--lemon-7);
  --accent-8: var(--lemon-8);
  --accent-9: var(--lemon-9);
  --accent-9-contrast: var(--lemon-9-contrast);
  --accent-10: var(--lemon-10);
  --accent-11: var(--lemon-11);
  --accent-12: var(--lemon-12);

  --accent-a1: var(--lemon-a1);
  --accent-a2: var(--lemon-a2);
  --accent-a3: var(--lemon-a3);
  --accent-a4: var(--lemon-a4);
  --accent-a5: var(--lemon-a5);
  --accent-a6: var(--lemon-a6);
  --accent-a7: var(--lemon-a7);
  --accent-a8: var(--lemon-a8);
  --accent-a9: var(--lemon-a9);
  --accent-a10: var(--lemon-a10);
  --accent-a11: var(--lemon-a11);
  --accent-a12: var(--lemon-a12);
}

[data-accent-color='lime'] {
  --color-surface-accent: var(--lime-surface);

  --accent-1: var(--lime-1);
  --accent-2: var(--lime-2);
  --accent-3: var(--lime-3);
  --accent-4: var(--lime-4);
  --accent-5: var(--lime-5);
  --accent-6: var(--lime-6);
  --accent-7: var(--lime-7);
  --accent-8: var(--lime-8);
  --accent-9: var(--lime-9);
  --accent-9-contrast: var(--lime-9-contrast);
  --accent-10: var(--lime-10);
  --accent-11: var(--lime-11);
  --accent-12: var(--lime-12);

  --accent-a1: var(--lime-a1);
  --accent-a2: var(--lime-a2);
  --accent-a3: var(--lime-a3);
  --accent-a4: var(--lime-a4);
  --accent-a5: var(--lime-a5);
  --accent-a6: var(--lime-a6);
  --accent-a7: var(--lime-a7);
  --accent-a8: var(--lime-a8);
  --accent-a9: var(--lime-a9);
  --accent-a10: var(--lime-a10);
  --accent-a11: var(--lime-a11);
  --accent-a12: var(--lime-a12);
}

.frosted-ui:where([data-gray-color='mauve']) {
    --gray-surface: var(--mauve-surface);

    --gray-1: var(--mauve-1);
    --gray-2: var(--mauve-2);
    --gray-2-translucent: var(--mauve-2-translucent);
    --gray-3: var(--mauve-3);
    --gray-4: var(--mauve-4);
    --gray-5: var(--mauve-5);
    --gray-6: var(--mauve-6);
    --gray-7: var(--mauve-7);
    --gray-8: var(--mauve-8);
    --gray-9: var(--mauve-9);
    --gray-10: var(--mauve-10);
    --gray-11: var(--mauve-11);
    --gray-12: var(--mauve-12);

    --gray-a1: var(--mauve-a1);
    --gray-a2: var(--mauve-a2);
    --gray-a3: var(--mauve-a3);
    --gray-a4: var(--mauve-a4);
    --gray-a5: var(--mauve-a5);
    --gray-a6: var(--mauve-a6);
    --gray-a7: var(--mauve-a7);
    --gray-a8: var(--mauve-a8);
    --gray-a9: var(--mauve-a9);
    --gray-a10: var(--mauve-a10);
    --gray-a11: var(--mauve-a11);
    --gray-a12: var(--mauve-a12);
  }

.frosted-ui:where([data-gray-color='slate']) {
    --gray-surface: var(--slate-surface);

    --gray-1: var(--slate-1);
    --gray-2: var(--slate-2);
    --gray-2-translucent: var(--slate-2-translucent);
    --gray-3: var(--slate-3);
    --gray-4: var(--slate-4);
    --gray-5: var(--slate-5);
    --gray-6: var(--slate-6);
    --gray-7: var(--slate-7);
    --gray-8: var(--slate-8);
    --gray-9: var(--slate-9);
    --gray-10: var(--slate-10);
    --gray-11: var(--slate-11);
    --gray-12: var(--slate-12);

    --gray-a1: var(--slate-a1);
    --gray-a2: var(--slate-a2);
    --gray-a3: var(--slate-a3);
    --gray-a4: var(--slate-a4);
    --gray-a5: var(--slate-a5);
    --gray-a6: var(--slate-a6);
    --gray-a7: var(--slate-a7);
    --gray-a8: var(--slate-a8);
    --gray-a9: var(--slate-a9);
    --gray-a10: var(--slate-a10);
    --gray-a11: var(--slate-a11);
    --gray-a12: var(--slate-a12);
  }

.frosted-ui:where([data-gray-color='sage']) {
    --gray-surface: var(--sage-surface);

    --gray-1: var(--sage-1);
    --gray-2: var(--sage-2);
    --gray-2-translucent: var(--sage-2-translucent);
    --gray-3: var(--sage-3);
    --gray-4: var(--sage-4);
    --gray-5: var(--sage-5);
    --gray-6: var(--sage-6);
    --gray-7: var(--sage-7);
    --gray-8: var(--sage-8);
    --gray-9: var(--sage-9);
    --gray-10: var(--sage-10);
    --gray-11: var(--sage-11);
    --gray-12: var(--sage-12);

    --gray-a1: var(--sage-a1);
    --gray-a2: var(--sage-a2);
    --gray-a3: var(--sage-a3);
    --gray-a4: var(--sage-a4);
    --gray-a5: var(--sage-a5);
    --gray-a6: var(--sage-a6);
    --gray-a7: var(--sage-a7);
    --gray-a8: var(--sage-a8);
    --gray-a9: var(--sage-a9);
    --gray-a10: var(--sage-a10);
    --gray-a11: var(--sage-a11);
    --gray-a12: var(--sage-a12);
  }

.frosted-ui:where([data-gray-color='olive']) {
    --gray-surface: var(--olive-surface);

    --gray-1: var(--olive-1);
    --gray-2: var(--olive-2);
    --gray-2-translucent: var(--olive-2-translucent);
    --gray-3: var(--olive-3);
    --gray-4: var(--olive-4);
    --gray-5: var(--olive-5);
    --gray-6: var(--olive-6);
    --gray-7: var(--olive-7);
    --gray-8: var(--olive-8);
    --gray-9: var(--olive-9);
    --gray-10: var(--olive-10);
    --gray-11: var(--olive-11);
    --gray-12: var(--olive-12);

    --gray-a1: var(--olive-a1);
    --gray-a2: var(--olive-a2);
    --gray-a3: var(--olive-a3);
    --gray-a4: var(--olive-a4);
    --gray-a5: var(--olive-a5);
    --gray-a6: var(--olive-a6);
    --gray-a7: var(--olive-a7);
    --gray-a8: var(--olive-a8);
    --gray-a9: var(--olive-a9);
    --gray-a10: var(--olive-a10);
    --gray-a11: var(--olive-a11);
    --gray-a12: var(--olive-a12);
  }

.frosted-ui:where([data-gray-color='sand']) {
    --gray-surface: var(--sand-surface);

    --gray-1: var(--sand-1);
    --gray-2: var(--sand-2);
    --gray-2-translucent: var(--sand-2-translucent);
    --gray-3: var(--sand-3);
    --gray-4: var(--sand-4);
    --gray-5: var(--sand-5);
    --gray-6: var(--sand-6);
    --gray-7: var(--sand-7);
    --gray-8: var(--sand-8);
    --gray-9: var(--sand-9);
    --gray-10: var(--sand-10);
    --gray-11: var(--sand-11);
    --gray-12: var(--sand-12);

    --gray-a1: var(--sand-a1);
    --gray-a2: var(--sand-a2);
    --gray-a3: var(--sand-a3);
    --gray-a4: var(--sand-a4);
    --gray-a5: var(--sand-a5);
    --gray-a6: var(--sand-a6);
    --gray-a7: var(--sand-a7);
    --gray-a8: var(--sand-a8);
    --gray-a9: var(--sand-a9);
    --gray-a10: var(--sand-a10);
    --gray-a11: var(--sand-a11);
    --gray-a12: var(--sand-a12);
  }

:root,
[data-danger-color='red'] {
  --color-surface-danger: var(--red-surface);

  --danger-1: var(--red-1);
  --danger-2: var(--red-2);
  --danger-3: var(--red-3);
  --danger-4: var(--red-4);
  --danger-5: var(--red-5);
  --danger-6: var(--red-6);
  --danger-7: var(--red-7);
  --danger-8: var(--red-8);
  --danger-9: var(--red-9);
  --danger-9-contrast: var(--red-9-contrast);
  --danger-10: var(--red-10);
  --danger-11: var(--red-11);
  --danger-12: var(--red-12);

  --danger-a1: var(--red-a1);
  --danger-a2: var(--red-a2);
  --danger-a3: var(--red-a3);
  --danger-a4: var(--red-a4);
  --danger-a5: var(--red-a5);
  --danger-a6: var(--red-a6);
  --danger-a7: var(--red-a7);
  --danger-a8: var(--red-a8);
  --danger-a9: var(--red-a9);
  --danger-a10: var(--red-a10);
  --danger-a11: var(--red-a11);
  --danger-a12: var(--red-a12);
}

[data-danger-color='tomato'] {
  --color-surface-danger: var(--tomato-surface);

  --danger-1: var(--tomato-1);
  --danger-2: var(--tomato-2);
  --danger-3: var(--tomato-3);
  --danger-4: var(--tomato-4);
  --danger-5: var(--tomato-5);
  --danger-6: var(--tomato-6);
  --danger-7: var(--tomato-7);
  --danger-8: var(--tomato-8);
  --danger-9: var(--tomato-9);
  --danger-9-contrast: var(--tomato-9-contrast);
  --danger-10: var(--tomato-10);
  --danger-11: var(--tomato-11);
  --danger-12: var(--tomato-12);

  --danger-a1: var(--tomato-a1);
  --danger-a2: var(--tomato-a2);
  --danger-a3: var(--tomato-a3);
  --danger-a4: var(--tomato-a4);
  --danger-a5: var(--tomato-a5);
  --danger-a6: var(--tomato-a6);
  --danger-a7: var(--tomato-a7);
  --danger-a8: var(--tomato-a8);
  --danger-a9: var(--tomato-a9);
  --danger-a10: var(--tomato-a10);
  --danger-a11: var(--tomato-a11);
  --danger-a12: var(--tomato-a12);
}

[data-danger-color='ruby'] {
  --color-surface-danger: var(--ruby-surface);

  --danger-1: var(--ruby-1);
  --danger-2: var(--ruby-2);
  --danger-3: var(--ruby-3);
  --danger-4: var(--ruby-4);
  --danger-5: var(--ruby-5);
  --danger-6: var(--ruby-6);
  --danger-7: var(--ruby-7);
  --danger-8: var(--ruby-8);
  --danger-9: var(--ruby-9);
  --danger-9-contrast: var(--ruby-9-contrast);
  --danger-10: var(--ruby-10);
  --danger-11: var(--ruby-11);
  --danger-12: var(--ruby-12);

  --danger-a1: var(--ruby-a1);
  --danger-a2: var(--ruby-a2);
  --danger-a3: var(--ruby-a3);
  --danger-a4: var(--ruby-a4);
  --danger-a5: var(--ruby-a5);
  --danger-a6: var(--ruby-a6);
  --danger-a7: var(--ruby-a7);
  --danger-a8: var(--ruby-a8);
  --danger-a9: var(--ruby-a9);
  --danger-a10: var(--ruby-a10);
  --danger-a11: var(--ruby-a11);
  --danger-a12: var(--ruby-a12);
}

:root,
[data-warning-color='amber'] {
  --color-surface-warning: var(--amber-surface);

  --warning-1: var(--amber-1);
  --warning-2: var(--amber-2);
  --warning-3: var(--amber-3);
  --warning-4: var(--amber-4);
  --warning-5: var(--amber-5);
  --warning-6: var(--amber-6);
  --warning-7: var(--amber-7);
  --warning-8: var(--amber-8);
  --warning-9: var(--amber-9);
  --warning-9-contrast: var(--amber-9-contrast);
  --warning-10: var(--amber-10);
  --warning-11: var(--amber-11);
  --warning-12: var(--amber-12);

  --warning-a1: var(--amber-a1);
  --warning-a2: var(--amber-a2);
  --warning-a3: var(--amber-a3);
  --warning-a4: var(--amber-a4);
  --warning-a5: var(--amber-a5);
  --warning-a6: var(--amber-a6);
  --warning-a7: var(--amber-a7);
  --warning-a8: var(--amber-a8);
  --warning-a9: var(--amber-a9);
  --warning-a10: var(--amber-a10);
  --warning-a11: var(--amber-a11);
  --warning-a12: var(--amber-a12);
}

[data-warning-color='yellow'] {
  --color-surface-warning: var(--yellow-surface);

  --warning-1: var(--yellow-1);
  --warning-2: var(--yellow-2);
  --warning-3: var(--yellow-3);
  --warning-4: var(--yellow-4);
  --warning-5: var(--yellow-5);
  --warning-6: var(--yellow-6);
  --warning-7: var(--yellow-7);
  --warning-8: var(--yellow-8);
  --warning-9: var(--yellow-9);
  --warning-9-contrast: var(--yellow-9-contrast);
  --warning-10: var(--yellow-10);
  --warning-11: var(--yellow-11);
  --warning-12: var(--yellow-12);

  --warning-a1: var(--yellow-a1);
  --warning-a2: var(--yellow-a2);
  --warning-a3: var(--yellow-a3);
  --warning-a4: var(--yellow-a4);
  --warning-a5: var(--yellow-a5);
  --warning-a6: var(--yellow-a6);
  --warning-a7: var(--yellow-a7);
  --warning-a8: var(--yellow-a8);
  --warning-a9: var(--yellow-a9);
  --warning-a10: var(--yellow-a10);
  --warning-a11: var(--yellow-a11);
  --warning-a12: var(--yellow-a12);
}

:root,
[data-success-color='green'] {
  --color-surface-success: var(--green-surface);

  --success-1: var(--green-1);
  --success-2: var(--green-2);
  --success-3: var(--green-3);
  --success-4: var(--green-4);
  --success-5: var(--green-5);
  --success-6: var(--green-6);
  --success-7: var(--green-7);
  --success-8: var(--green-8);
  --success-9: var(--green-9);
  --success-9-contrast: var(--green-9-contrast);
  --success-10: var(--green-10);
  --success-11: var(--green-11);
  --success-12: var(--green-12);

  --success-a1: var(--green-a1);
  --success-a2: var(--green-a2);
  --success-a3: var(--green-a3);
  --success-a4: var(--green-a4);
  --success-a5: var(--green-a5);
  --success-a6: var(--green-a6);
  --success-a7: var(--green-a7);
  --success-a8: var(--green-a8);
  --success-a9: var(--green-a9);
  --success-a10: var(--green-a10);
  --success-a11: var(--green-a11);
  --success-a12: var(--green-a12);
}

[data-success-color='teal'] {
  --color-surface-success: var(--teal-surface);

  --success-1: var(--teal-1);
  --success-2: var(--teal-2);
  --success-3: var(--teal-3);
  --success-4: var(--teal-4);
  --success-5: var(--teal-5);
  --success-6: var(--teal-6);
  --success-7: var(--teal-7);
  --success-8: var(--teal-8);
  --success-9: var(--teal-9);
  --success-9-contrast: var(--teal-9-contrast);
  --success-10: var(--teal-10);
  --success-11: var(--teal-11);
  --success-12: var(--teal-12);

  --success-a1: var(--teal-a1);
  --success-a2: var(--teal-a2);
  --success-a3: var(--teal-a3);
  --success-a4: var(--teal-a4);
  --success-a5: var(--teal-a5);
  --success-a6: var(--teal-a6);
  --success-a7: var(--teal-a7);
  --success-a8: var(--teal-a8);
  --success-a9: var(--teal-a9);
  --success-a10: var(--teal-a10);
  --success-a11: var(--teal-a11);
  --success-a12: var(--teal-a12);
}

[data-success-color='jade'] {
  --color-surface-success: var(--jade-surface);

  --success-1: var(--jade-1);
  --success-2: var(--jade-2);
  --success-3: var(--jade-3);
  --success-4: var(--jade-4);
  --success-5: var(--jade-5);
  --success-6: var(--jade-6);
  --success-7: var(--jade-7);
  --success-8: var(--jade-8);
  --success-9: var(--jade-9);
  --success-9-contrast: var(--jade-9-contrast);
  --success-10: var(--jade-10);
  --success-11: var(--jade-11);
  --success-12: var(--jade-12);

  --success-a1: var(--jade-a1);
  --success-a2: var(--jade-a2);
  --success-a3: var(--jade-a3);
  --success-a4: var(--jade-a4);
  --success-a5: var(--jade-a5);
  --success-a6: var(--jade-a6);
  --success-a7: var(--jade-a7);
  --success-a8: var(--jade-a8);
  --success-a9: var(--jade-a9);
  --success-a10: var(--jade-a10);
  --success-a11: var(--jade-a11);
  --success-a12: var(--jade-a12);
}

[data-success-color='grass'] {
  --color-surface-success: var(--grass-surface);

  --success-1: var(--grass-1);
  --success-2: var(--grass-2);
  --success-3: var(--grass-3);
  --success-4: var(--grass-4);
  --success-5: var(--grass-5);
  --success-6: var(--grass-6);
  --success-7: var(--grass-7);
  --success-8: var(--grass-8);
  --success-9: var(--grass-9);
  --success-9-contrast: var(--grass-9-contrast);
  --success-10: var(--grass-10);
  --success-11: var(--grass-11);
  --success-12: var(--grass-12);

  --success-a1: var(--grass-a1);
  --success-a2: var(--grass-a2);
  --success-a3: var(--grass-a3);
  --success-a4: var(--grass-a4);
  --success-a5: var(--grass-a5);
  --success-a6: var(--grass-a6);
  --success-a7: var(--grass-a7);
  --success-a8: var(--grass-a8);
  --success-a9: var(--grass-a9);
  --success-a10: var(--grass-a10);
  --success-a11: var(--grass-a11);
  --success-a12: var(--grass-a12);
}

:root,
[data-info-color='sky'] {
  --color-surface-info: var(--sky-surface);

  --info-1: var(--sky-1);
  --info-2: var(--sky-2);
  --info-3: var(--sky-3);
  --info-4: var(--sky-4);
  --info-5: var(--sky-5);
  --info-6: var(--sky-6);
  --info-7: var(--sky-7);
  --info-8: var(--sky-8);
  --info-9: var(--sky-9);
  --info-9-contrast: var(--sky-9-contrast);
  --info-10: var(--sky-10);
  --info-11: var(--sky-11);
  --info-12: var(--sky-12);

  --info-a1: var(--sky-a1);
  --info-a2: var(--sky-a2);
  --info-a3: var(--sky-a3);
  --info-a4: var(--sky-a4);
  --info-a5: var(--sky-a5);
  --info-a6: var(--sky-a6);
  --info-a7: var(--sky-a7);
  --info-a8: var(--sky-a8);
  --info-a9: var(--sky-a9);
  --info-a10: var(--sky-a10);
  --info-a11: var(--sky-a11);
  --info-a12: var(--sky-a12);
}

[data-info-color='blue'] {
  --color-surface-info: var(--blue-surface);

  --info-1: var(--blue-1);
  --info-2: var(--blue-2);
  --info-3: var(--blue-3);
  --info-4: var(--blue-4);
  --info-5: var(--blue-5);
  --info-6: var(--blue-6);
  --info-7: var(--blue-7);
  --info-8: var(--blue-8);
  --info-9: var(--blue-9);
  --info-9-contrast: var(--blue-9-contrast);
  --info-10: var(--blue-10);
  --info-11: var(--blue-11);
  --info-12: var(--blue-12);

  --info-a1: var(--blue-a1);
  --info-a2: var(--blue-a2);
  --info-a3: var(--blue-a3);
  --info-a4: var(--blue-a4);
  --info-a5: var(--blue-a5);
  --info-a6: var(--blue-a6);
  --info-a7: var(--blue-a7);
  --info-a8: var(--blue-a8);
  --info-a9: var(--blue-a9);
  --info-a10: var(--blue-a10);
  --info-a11: var(--blue-a11);
  --info-a12: var(--blue-a12);
}

[data-accent-color='danger'] {
  --color-surface-accent: var(--color-surface-danger);

  --accent-1: var(--danger-1);
  --accent-2: var(--danger-2);
  --accent-3: var(--danger-3);
  --accent-4: var(--danger-4);
  --accent-5: var(--danger-5);
  --accent-6: var(--danger-6);
  --accent-7: var(--danger-7);
  --accent-8: var(--danger-8);
  --accent-9: var(--danger-9);
  --accent-9-contrast: var(--danger-9-contrast);
  --accent-10: var(--danger-10);
  --accent-11: var(--danger-11);
  --accent-12: var(--danger-12);

  --accent-a1: var(--danger-a1);
  --accent-a2: var(--danger-a2);
  --accent-a3: var(--danger-a3);
  --accent-a4: var(--danger-a4);
  --accent-a5: var(--danger-a5);
  --accent-a6: var(--danger-a6);
  --accent-a7: var(--danger-a7);
  --accent-a8: var(--danger-a8);
  --accent-a9: var(--danger-a9);
  --accent-a10: var(--danger-a10);
  --accent-a11: var(--danger-a11);
  --accent-a12: var(--danger-a12);
}

[data-accent-color='warning'] {
  --color-surface-accent: var(--color-surface-warning);

  --accent-1: var(--warning-1);
  --accent-2: var(--warning-2);
  --accent-3: var(--warning-3);
  --accent-4: var(--warning-4);
  --accent-5: var(--warning-5);
  --accent-6: var(--warning-6);
  --accent-7: var(--warning-7);
  --accent-8: var(--warning-8);
  --accent-9: var(--warning-9);
  --accent-9-contrast: var(--warning-9-contrast);
  --accent-10: var(--warning-10);
  --accent-11: var(--warning-11);
  --accent-12: var(--warning-12);

  --accent-a1: var(--warning-a1);
  --accent-a2: var(--warning-a2);
  --accent-a3: var(--warning-a3);
  --accent-a4: var(--warning-a4);
  --accent-a5: var(--warning-a5);
  --accent-a6: var(--warning-a6);
  --accent-a7: var(--warning-a7);
  --accent-a8: var(--warning-a8);
  --accent-a9: var(--warning-a9);
  --accent-a10: var(--warning-a10);
  --accent-a11: var(--warning-a11);
  --accent-a12: var(--warning-a12);
}

[data-accent-color='success'] {
  --color-surface-accent: var(--color-surface-success);

  --accent-1: var(--success-1);
  --accent-2: var(--success-2);
  --accent-3: var(--success-3);
  --accent-4: var(--success-4);
  --accent-5: var(--success-5);
  --accent-6: var(--success-6);
  --accent-7: var(--success-7);
  --accent-8: var(--success-8);
  --accent-9: var(--success-9);
  --accent-9-contrast: var(--success-9-contrast);
  --accent-10: var(--success-10);
  --accent-11: var(--success-11);
  --accent-12: var(--success-12);

  --accent-a1: var(--success-a1);
  --accent-a2: var(--success-a2);
  --accent-a3: var(--success-a3);
  --accent-a4: var(--success-a4);
  --accent-a5: var(--success-a5);
  --accent-a6: var(--success-a6);
  --accent-a7: var(--success-a7);
  --accent-a8: var(--success-a8);
  --accent-a9: var(--success-a9);
  --accent-a10: var(--success-a10);
  --accent-a11: var(--success-a11);
  --accent-a12: var(--success-a12);
}

[data-accent-color='info'] {
  --color-surface-accent: var(--color-surface-info);

  --accent-1: var(--info-1);
  --accent-2: var(--info-2);
  --accent-3: var(--info-3);
  --accent-4: var(--info-4);
  --accent-5: var(--info-5);
  --accent-6: var(--info-6);
  --accent-7: var(--info-7);
  --accent-8: var(--info-8);
  --accent-9: var(--info-9);
  --accent-9-contrast: var(--info-9-contrast);
  --accent-10: var(--info-10);
  --accent-11: var(--info-11);
  --accent-12: var(--info-12);

  --accent-a1: var(--info-a1);
  --accent-a2: var(--info-a2);
  --accent-a3: var(--info-a3);
  --accent-a4: var(--info-a4);
  --accent-a5: var(--info-a5);
  --accent-a6: var(--info-a6);
  --accent-a7: var(--info-a7);
  --accent-a8: var(--info-a8);
  --accent-a9: var(--info-a9);
  --accent-a10: var(--info-a10);
  --accent-a11: var(--info-a11);
  --accent-a12: var(--info-a12);
}

.frosted-ui {
  --cursor-button: default;
  --cursor-checkbox: default;
  --cursor-disabled: not-allowed;
  --cursor-link: pointer;
  --cursor-menu-item: default;
  --cursor-radio: default;
  --cursor-slider-thumb: default;
  --cursor-slider-thumb-active: default;
  --cursor-switch: default;
  --space-1: 4px;
  --space-2: 8px;
  --space-3: 12px;
  --space-4: 16px;
  --space-5: 24px;
  --space-6: 32px;
  --space-7: 40px;
  --space-8: 48px;
  --space-9: 64px;
  --font-size-0: 10px;
  --font-size-1: 12px;
  --font-size-2: 14px;
  --font-size-3: 16px;
  --font-size-4: 18px;
  --font-size-5: 20px;
  --font-size-6: 24px;
  --font-size-7: 28px;
  --font-size-8: 32px;
  --font-size-9: 40px;

  --font-weight-light: 300;
  --font-weight-regular: 400;
  --font-weight-medium: 500;
  --font-weight-semi-bold: 600;
  --font-weight-bold: 700;

  --line-height-0: 12px;
  --line-height-1: 16px;
  --line-height-2: 20px;
  --line-height-3: 24px;
  --line-height-4: 26px;
  --line-height-5: 28px;
  --line-height-6: 30px;
  --line-height-7: 34px;
  --line-height-8: 38px;
  --line-height-9: 48px;

  --letter-spacing-0: 0.01em;
  --letter-spacing-1: 0.01em;
  --letter-spacing-2: 0.01em;
  --letter-spacing-3: 0.01em;
  --letter-spacing-4: 0.01em;
  --letter-spacing-5: 0.01em;
  --letter-spacing-6: 0.01em;
  --letter-spacing-7: 0.005em;
  --letter-spacing-8: 0em;
  --letter-spacing-9: 0em;

  --default-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI (Custom)', Roboto, 'Helvetica Neue',
    'Open Sans (Custom)', system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';
  --default-font-size: var(--font-size-3);
  --default-font-style: normal;
  --default-font-weight: var(--font-weight-regular);
  --default-line-height: 1.5;
  --default-letter-spacing: 0em;
  --default-leading-trim-start: 0.42em;
  --default-leading-trim-end: 0.36em;

  --heading-font-family: var(--default-font-family);
  --heading-font-size-adjust: 1;
  --heading-font-style: normal;
  --heading-leading-trim-start: var(--default-leading-trim-start);
  --heading-leading-trim-end: var(--default-leading-trim-end);
  --heading-letter-spacing: 0em;

  --heading-line-height-1: 16px;
  --heading-line-height-2: 18px;
  --heading-line-height-3: 22px;
  --heading-line-height-4: 24px;
  --heading-line-height-5: 26px;
  --heading-line-height-6: 30px;
  --heading-line-height-7: 36px;
  --heading-line-height-8: 40px;
  --heading-line-height-9: 60px;

  --code-font-family: 'Menlo', 'Consolas (Custom)', 'Bitstream Vera Sans Mono', monospace, 'Apple Color Emoji',
    'Segoe UI Emoji';
  --code-font-size-adjust: 0.95;
  --code-font-style: normal;
  --code-font-weight: inherit;
  --code-letter-spacing: -0.007em;
  --code-padding-top: 0.1em;
  --code-padding-bottom: 0.1em;

  --strong-font-family: var(--default-font-family);
  --strong-font-size-adjust: 1;
  --strong-font-style: inherit;
  --strong-font-weight: var(--font-weight-bold);
  --strong-letter-spacing: 0em;

  --em-font-family: 'Times New Roman', 'Times', serif;
  --em-font-size-adjust: 1.18;
  --em-font-style: italic;
  --em-font-weight: inherit;
  --em-letter-spacing: -0.025em;

  --quote-font-family: 'Times New Roman', 'Times', serif;
  --quote-font-size-adjust: 1.18;
  --quote-font-style: italic;
  --quote-font-weight: inherit;
  --quote-letter-spacing: -0.025em;

  --tabs-trigger-active-letter-spacing: -0.01em;
  --tabs-trigger-active-word-spacing: 0em;
  --tabs-trigger-inactive-letter-spacing: 0em;
  --tabs-trigger-inactive-word-spacing: 0em;
  overflow-wrap: break-word;
  font-family: var(--default-font-family);
  font-size: var(--default-font-size);
  font-weight: var(--default-font-weight);
  font-style: var(--default-font-style);
  line-height: var(--default-line-height);
  letter-spacing: var(--default-letter-spacing);
  -webkit-text-size-adjust: none;
     -moz-text-size-adjust: none;
          text-size-adjust: none;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  --scrollarea-scrollbar-horizontal-margin-top: var(--space-1);
  --scrollarea-scrollbar-horizontal-margin-bottom: var(--space-1);
  --scrollarea-scrollbar-horizontal-margin-left: var(--space-1);
  --scrollarea-scrollbar-horizontal-margin-right: var(--space-1);
  --scrollarea-scrollbar-vertical-margin-top: var(--space-1);
  --scrollarea-scrollbar-vertical-margin-bottom: var(--space-1);
  --scrollarea-scrollbar-vertical-margin-left: var(--space-1);
  --scrollarea-scrollbar-vertical-margin-right: var(--space-1);
  --spinner-opacity: 0.65;
  color: var(--gray-12);
}

@supports (color: color-mix(in oklab, white, black)) {
  :is(.dark, .dark-theme),
  :is(.dark, .dark-theme) :where(.frosted-ui:not(.light, .light-theme)) {
    --shadow-1:
      inset 0 -1px 1px 0 var(--gray-a3),
      inset 0 0 0 1px var(--gray-a3),
      inset 0 3px 4px 0 var(--black-a5),
      inset 0 0 0 1px var(--gray-a4);

    --shadow-2:
      0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),
      0 0 1px 1px var(--gray-a5),
      0 1px 1px 0 var(--black-a6),
      0 2px 1px -1px var(--black-a6),
      0 1px 3px 0 var(--black-a8);

    --shadow-3:
      0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),
      0 2px 3px -2px var(--black-a3),
      0 3px 8px -2px var(--black-a6),
      0 4px 12px -4px var(--black-a7);

    --shadow-4:
      0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),
      0 8px 40px var(--black-a3),
      0 12px 32px -16px var(--black-a5);

    --shadow-5:
      0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)) inset,
      0 12px 60px var(--black-a5),
      0 12px 32px -16px var(--black-a7);

    --shadow-6:
      0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),
      0 12px 60px var(--black-a4),
      0 16px 64px var(--black-a6),
      0 16px 36px -20px var(--black-a11);
    --card-classic-hover-box-shadow:
      0 0 0 1px color-mix(in oklab, var(--gray-a7), var(--gray-8)),
      0 0 1px 1px var(--gray-a7),
      0 0 1px -1px var(--gray-a4),
      0 0 3px -2px var(--gray-a3),
      0 0 12px -2px var(--gray-a3),
      0 0 16px -8px var(--gray-a9);
  }
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 300;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI Semilight'), local('Segoe UI');
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 300;
  font-style: italic;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI Semilight Italic'), local('Segoe UI Italic');
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 400;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI');
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 400;
  font-style: italic;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI Italic');
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 500;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI Semibold'), local('Segoe UI');
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 500;
  font-style: italic;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI Semibold Italic'), local('Segoe UI Italic');
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 700;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI Bold');
}

@font-face {
  font-family: 'Segoe UI (Custom)';
  font-weight: 700;
  font-style: italic;
  size-adjust: 103%;
  descent-override: 35%;
  ascent-override: 105%;
  src: local('Segoe UI Bold Italic');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 300;
  descent-override: 35%;
  src: local('Open Sans Light'), local('Open Sans Regular');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 300;
  font-style: italic;
  descent-override: 35%;
  src: local('Open Sans Light Italic'), local('Open Sans Italic');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 400;
  descent-override: 35%;
  src: local('Open Sans Regular');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 400;
  font-style: italic;
  descent-override: 35%;
  src: local('Open Sans Italic');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 500;
  descent-override: 35%;
  src: local('Open Sans Medium'), local('Open Sans Regular');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 500;
  font-style: italic;
  descent-override: 35%;
  src: local('Open Sans Medium Italic'), local('Open Sans Italic');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 700;
  descent-override: 35%;
  src: local('Open Sans Bold');
}

@font-face {
  font-family: 'Open Sans (Custom)';
  font-weight: 700;
  font-style: italic;
  descent-override: 35%;
  src: local('Open Sans Bold Italic');
}

@font-face {
  font-family: 'Consolas (Custom)';
  font-weight: 400;
  size-adjust: 110%;
  ascent-override: 85%;
  descent-override: 22%;
  src: local('Consolas');
}

@font-face {
  font-family: 'Consolas (Custom)';
  font-weight: 400;
  font-style: italic;
  size-adjust: 110%;
  ascent-override: 85%;
  descent-override: 22%;
  src: local('Consolas Italic');
}

@font-face {
  font-family: 'Consolas (Custom)';
  font-weight: 700;
  size-adjust: 110%;
  ascent-override: 85%;
  descent-override: 22%;
  src: local('Consolas Bold');
}

@font-face {
  font-family: 'Consolas (Custom)';
  font-weight: 700;
  font-style: italic;
  size-adjust: 110%;
  ascent-override: 85%;
  descent-override: 22%;
  src: local('Consolas Bold Italic');
}

.fui-Heading {
  margin: 0;
  font-family: var(--heading-font-family);
  font-style: var(--heading-font-style);
  --leading-trim-start: var(--heading-leading-trim-start);
  --leading-trim-end: var(--heading-leading-trim-end);
  line-height: var(--line-height);
}

.fui-Heading:where([data-accent-color]) {
    color: var(--accent-a11);
  }

.fui-Heading:where([data-accent-color]):where(.fui-high-contrast) {
      color: var(--accent-12);
    }

.fui-Heading:where(.fui-r-size-0) {
    font-size: calc(var(--font-size-0) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-0);
    letter-spacing: calc(var(--letter-spacing-0) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-1) {
    font-size: calc(var(--font-size-1) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-1);
    letter-spacing: calc(var(--letter-spacing-1) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-2) {
    font-size: calc(var(--font-size-2) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-2);
    letter-spacing: calc(var(--letter-spacing-2) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-3) {
    font-size: calc(var(--font-size-3) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-3);
    letter-spacing: calc(var(--letter-spacing-3) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-4) {
    font-size: calc(var(--font-size-4) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-4);
    letter-spacing: calc(var(--letter-spacing-4) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-5) {
    font-size: calc(var(--font-size-5) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-5);
    letter-spacing: calc(var(--letter-spacing-5) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-6) {
    font-size: calc(var(--font-size-6) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-6);
    letter-spacing: calc(var(--letter-spacing-6) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-7) {
    font-size: calc(var(--font-size-7) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-7);
    letter-spacing: calc(var(--letter-spacing-7) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-8) {
    font-size: calc(var(--font-size-8) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-8);
    letter-spacing: calc(var(--letter-spacing-8) + var(--heading-letter-spacing));
  }

.fui-Heading:where(.fui-r-size-9) {
    font-size: calc(var(--font-size-9) * var(--heading-font-size-adjust));
    --line-height: var(--heading-line-height-9);
    letter-spacing: calc(var(--letter-spacing-9) + var(--heading-letter-spacing));
  }

.fui-Text {
  margin: 0;
  line-height: var(--line-height, var(--default-line-height));
  letter-spacing: var(--letter-spacing, inherit);
}

.fui-Text:where([data-accent-color]) {
    color: var(--accent-a11);
  }

.fui-Text:where([data-accent-color]):where(.fui-high-contrast),
  :where([data-accent-color]):where(.fui-Text, .fui-Heading) .fui-Text:where(.fui-high-contrast) {
    color: var(--accent-12);
  }

.fui-Text:where(.fui-r-size-0) {
    font-size: var(--font-size-0);
    --line-height: var(--line-height-0);
    --letter-spacing: var(--letter-spacing-0);
  }

.fui-Text:where(.fui-r-size-1) {
    font-size: var(--font-size-1);
    --line-height: var(--line-height-1);
    --letter-spacing: var(--letter-spacing-1);
  }

.fui-Text:where(.fui-r-size-2) {
    font-size: var(--font-size-2);
    --line-height: var(--line-height-2);
    --letter-spacing: var(--letter-spacing-2);
  }

.fui-Text:where(.fui-r-size-3) {
    font-size: var(--font-size-3);
    --line-height: var(--line-height-3);
    --letter-spacing: var(--letter-spacing-3);
  }

.fui-Text:where(.fui-r-size-4) {
    font-size: var(--font-size-4);
    --line-height: var(--line-height-4);
    --letter-spacing: var(--letter-spacing-4);
  }

.fui-Text:where(.fui-r-size-5) {
    font-size: var(--font-size-5);
    --line-height: var(--line-height-5);
    --letter-spacing: var(--letter-spacing-5);
  }

.fui-Text:where(.fui-r-size-6) {
    font-size: var(--font-size-6);
    --line-height: var(--line-height-6);
    --letter-spacing: var(--letter-spacing-6);
  }

.fui-Text:where(.fui-r-size-7) {
    font-size: var(--font-size-7);
    --line-height: var(--line-height-7);
    --letter-spacing: var(--letter-spacing-7);
  }

.fui-Text:where(.fui-r-size-8) {
    font-size: var(--font-size-8);
    --line-height: var(--line-height-8);
    --letter-spacing: var(--letter-spacing-8);
  }

.fui-Text:where(.fui-r-size-9) {
    font-size: var(--font-size-9);
    --line-height: var(--line-height-9);
    --letter-spacing: var(--letter-spacing-9);
  }

.fui-DialogOverlay {
  display: flex;
  align-items: center;
  justify-content: center;
  position: fixed;
  inset: 0;

  --dialog-overlay-padding-top: var(--space-4);
  --dialog-overlay-padding-bottom: max(var(--space-4), 4vh);
  padding-top: var(--dialog-overlay-padding-top);
  padding-bottom: var(--dialog-overlay-padding-bottom);
  padding-left: var(--space-4);
  padding-right: var(--space-4);
}

.fui-DialogOverlay::before {
    content: '';
    position: absolute;
    inset: 0;
  }

.fui-DialogContent {
  width: 100%;
  max-width: 580px;
  outline: none;
  overflow: auto;
  background-color: var(--color-panel-solid);
  box-shadow: var(--shadow-6);
  box-sizing: border-box;
  z-index: 1;

  --inset-padding: var(--dialog-content-padding);
  padding: var(--inset-padding);
  max-height: calc(100vh - var(--dialog-overlay-padding-top) - var(--dialog-overlay-padding-bottom));
}

@supports (max-height: 100dvh) {

.fui-DialogContent {
    max-height: calc(100dvh - var(--dialog-overlay-padding-top) - var(--dialog-overlay-padding-bottom));
}
  }

.fui-DialogContent:where(.fui-r-size-1) {
    --dialog-title-mb: var(--space-1);
    --dialog-description-mb: var(--space-3);
    --dialog-content-padding: var(--space-3);
    border-radius: 8px;
  }

.fui-DialogContent:where(.fui-r-size-2) {
    --dialog-title-mb: var(--space-2);
    --dialog-description-mb: var(--space-4);
    --dialog-content-padding: var(--space-4);
    border-radius: 12px;
  }

.fui-DialogContent:where(.fui-r-size-3) {
    --dialog-title-mb: var(--space-3);
    --dialog-description-mb: 20px;
    --dialog-content-padding: 20px;
    border-radius: 16px;
  }

.fui-DialogContent:where(.fui-r-size-4) {
    --dialog-title-mb: var(--space-3);
    --dialog-description-mb: var(--space-5);
    --dialog-content-padding: var(--space-5);
    border-radius: 20px;
  }

.fui-DialogTitle:where(.fui-Heading) {
  margin-bottom: var(--dialog-title-mb);
}

.fui-DialogDescription {
  margin-bottom: var(--dialog-description-mb);
}

@property --overlay-blur {
  syntax: '<length>';
  inherits: false;
  initial-value: 0px;
}

@property --overlay-brightness {
  syntax: '<number>';
  inherits: false;
  initial-value: 0;
}

@keyframes fui-blur-in {
  from {
    --overlay-blur: 0px;
    --overlay-brightness: 1;
  }
  to {
    --overlay-blur: 3px;
    --overlay-brightness: 0.7;
  }
}

@keyframes fui-blur-out {
  from {
    --overlay-blur: 3px;
    --overlay-brightness: 0.7;
  }
  to {
    --overlay-blur: 0px;
    --overlay-brightness: 1;
  }
}

@media (prefers-reduced-motion: no-preference) {
  @keyframes fui-dialog-overlay-no-op {
    from {
      opacity: 1;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes fui-dialog-content-show {
    from {
      opacity: 0;
      transform: translateY(5px) scale(0.97);
    }
    to {
      opacity: 1;
      transform: translateY(0px) scale(1);
    }
  }

  @keyframes fui-dialog-content-hide {
    from {
      opacity: 1;
      transform: translateY(0px) scale(1);
    }
    to {
      opacity: 0;
      transform: translateY(5px) scale(0.99);
    }
  }
    .fui-DialogOverlay::before {
      -webkit-backdrop-filter: blur(var(--overlay-blur)) brightness(var(--overlay-brightness));
              backdrop-filter: blur(var(--overlay-blur)) brightness(var(--overlay-brightness));
    }
    .fui-DialogOverlay:where([data-state='closed']) {
      animation: fui-blur-out 400ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
    }

    .fui-DialogOverlay:where([data-state='open'])::before {
      animation: fui-blur-in 400ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
    }
    .fui-DialogOverlay:where([data-state='closed'])::before {
      animation: fui-blur-out 400ms cubic-bezier(0.16, 1, 0.3, 1) forwards;
    }
    .fui-DialogContent:where([data-state='open']) {
      animation: fui-dialog-content-show 400ms cubic-bezier(0.16, 1, 0.3, 1);
    }
    .fui-DialogContent:where([data-state='closed']) {
      animation: fui-dialog-content-hide 150ms cubic-bezier(0.16, 1, 0.3, 1);
    }
}

.fui-DrawerContent {
  z-index: 1;
  position: fixed;
  top: 8px;
  right: 8px;
  bottom: 8px;

  display: flex;
  flex-direction: column;

  width: 100%;
  max-width: 380px;
  box-sizing: border-box;
  background-color: var(--color-panel-solid);

  border-radius: 12px;
  box-shadow: 0px 0px 0px 1px var(--gray-a5);
  --drawer-content-padding: 16px;
}

.fui-DrawerHeader {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: var(--space-3);
  --inset-padding: var(--drawer-content-padding);
  padding: var(--inset-padding);
  border-bottom: 1px solid var(--color-stroke);
}

.fui-BodyScrollArea {
  flex-shrink: 0;
  flex: 1;
  overflow-y: auto;
  flex-grow: 1;
}

.fui-DrawerBody {
  --inset-padding: var(--drawer-content-padding);
  padding: var(--inset-padding);
}

.fui-DrawerStickyFooter {
  flex-shrink: 0;
  --inset-padding: var(--drawer-content-padding);
  padding: var(--inset-padding);
  border-top: 1px solid var(--color-stroke);
}

@media (prefers-reduced-motion: no-preference) {
  @keyframes fui-drawer-content-show {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0%);
    }
  }

  @keyframes fui-drawer-content-hide {
    from {
      transform: translateX(0%);
    }
    to {
      transform: translateX(100%);
    }
  }
    .fui-DrawerContent:where([data-state='open']) {
      animation: fui-drawer-content-show 400ms cubic-bezier(0.32, 0.72, 0, 1);
      animation: fui-drawer-content-show 400ms cubic-bezier(0.16, 1, 0.3, 1);
    }

    .fui-DrawerContent:where([data-state='closed']) {
      animation: fui-drawer-content-hide 400ms cubic-bezier(0.32, 0.72, 0, 1);
      animation: fui-drawer-content-hide 300ms cubic-bezier(0.16, 1, 0.3, 1);
    }
}

.fui-SheetOverlay {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.4);
  -webkit-user-select: none;
          user-select: none;
}

.fui-SheetContent {
  --sheet-content-padding: 20px;
  --inset-padding: var(--sheet-content-padding);
  --sheet-border-color: var(--gray-a3);

  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  max-height: calc(100% - 48px);
  display: flex;
  flex-direction: column;
  height: auto;
  border-top-left-radius: 24px;
  border-top-right-radius: 24px;
  border: 1px solid var(--sheet-border-color);
  background-color: var(--color-panel-solid);
  -webkit-user-select: none;
          user-select: none;
}

.fui-SheetContent,
.fui-SheetContent:focus,
.fui-SheetContent:focus-visible {
  outline: none;
}

.fui-SheetContent[vaul-drawer][vaul-drawer-direction='bottom']::after {
  border-left: 1px solid var(--sheet-border-color);
  border-right: 1px solid var(--sheet-border-color);
  left: -1px;
  right: -1px;
  -webkit-user-select: none;
          user-select: none;
}

.fui-SheetBody {
  padding: var(--inset-padding);
  flex: 1;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.fui-SheetContentHandle {
  margin-left: auto;
  margin-right: auto;
  margin-top: 16px;
  margin-bottom: 12px;
  height: 5px;
  width: 40px;
  border-radius: 999px;
  background-color: var(--gray-a4);
  -webkit-user-select: none;
          user-select: none;
  pointer-events: none;
}

.fui-SheetHeader {
  display: grid;
  padding: 0 var(--inset-padding) var(--inset-padding) var(--inset-padding);
  gap: 12px;
}

.fui-SheetContentHandle,
.fui-SheetHeader,
.fui-SheetBody {
  min-height: 0;
}

.fui-SheetContentHandle,
.fui-SheetHeader {
  flex-shrink: 0;
}

.fui-DatePickerRoot {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.fui-DateRangePickerRoot {
  display: flex;
  align-items: center;
  gap: var(--space-1);
}

.fui-AccordionItem:focus-within {
  position: relative;
  z-index: 1;
  box-shadow: 0 0 0 2px var(--color-focus-root) inset;
  border-radius: var(--radius-4);
}

.fui-AccordionTrigger {
  display: flex;
  align-items: center;
  width: 100%;
  gap: var(--space-2);
  background: var(--gray-a3);
  border-radius: var(--radius-4);
  padding: var(--space-2) var(--space-4);
  box-shadow: 0px 0px 0px 1px var(--gray-a5) inset;

  font-size: var(--font-size-1);
  color: var(--gray-a11);
  font-weight: var(--font-weight-bold);
  text-transform: uppercase;
  letter-spacing: 0.06em;
}

.fui-AccordionTriggerIcon {
  transition: 300ms transform ease-out;
  transform-origin: center;
  transform: rotate(180deg);
}

.fui-AccordionTrigger[data-state='open'] .fui-AccordionTriggerIcon {
  transform: rotate(0deg);
}

.fui-AccordionContent {
  overflow: hidden;
}

.fui-AccordionContent[data-state='open'] {
  animation: fui-accordion-slide-down 300ms ease-out;
}

.fui-AccordionContent[data-state='closed'] {
  animation: fui-accordion-slide-up 300ms ease-out;
}

@keyframes fui-accordion-slide-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes fui-accordion-slide-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.fui-AccordionContentInner {
  padding: var(--space-4) var(--space-5);
}

.fui-AvatarRoot {
  container-type: inline-size;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  -webkit-user-select: none;
          user-select: none;
  width: var(--avatar-size);
  height: var(--avatar-size);
  flex-shrink: 0;

  outline: 1px solid var(--accent-a5);
  outline-offset: -1px;

  background-color: var(--accent-a3);
  border-radius: max(25%, var(--radius-full));
}

.fui-AvatarRoot :where(.fui-AvatarFallback) {
    color: var(--accent-a11);
  }

.fui-AvatarRoot:where(.fui-high-contrast) :where(.fui-AvatarFallback) {
    color: var(--accent-12);
  }

.fui-AvatarRoot:where(.fui-variant-round) {
    --radius-full: var(--radius-thumb);
  }

.fui-AvatarRoot:where([data-status='loaded']) {
    outline: none;
    background: none;
  }

.fui-AvatarImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: inherit;
  transform: translateZ(0px);
}

.fui-AvatarImage:where(.fui-AvatarRoot[data-status='loaded'] .fui-AvatarImage) {
    outline: 1px solid var(--gray-a5);
    outline-offset: -1px;
  }

.fui-AvatarFallback {
  z-index: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  line-height: 1;
  border-radius: inherit;
  font-weight: var(--font-weight-medium);
  text-transform: uppercase;

  line-height: 1.4;
  letter-spacing: 0.05em;
}

.fui-AvatarFallback:where(.fui-one-letter) {
    font-size: 45cqw;
  }

.fui-AvatarFallback:where(.fui-two-letters) {
    font-size: 40cqw;
  }

.fui-AvatarRoot:where(.fui-r-size-1) {
    --avatar-size: var(--space-5);
  }

.fui-AvatarRoot:where(.fui-r-size-2) {
    --avatar-size: var(--space-6);
  }

.fui-AvatarRoot:where(.fui-r-size-3) {
    --avatar-size: var(--space-7);
  }

.fui-AvatarRoot:where(.fui-r-size-4) {
    --avatar-size: var(--space-8);
  }

.fui-AvatarRoot:where(.fui-r-size-5) {
    --avatar-size: var(--space-9);
  }

.fui-AvatarRoot:where(.fui-r-size-6) {
    --avatar-size: 80px;
  }

.fui-AvatarRoot:where(.fui-r-size-7) {
    --avatar-size: 96px;
  }

.fui-AvatarRoot:where(.fui-r-size-8) {
    --avatar-size: 128px;
  }

.fui-AvatarRoot:where(.fui-r-size-9) {
    --avatar-size: 160px;
  }

.fui-AvatarGroupRoot {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  vertical-align: middle;
  -webkit-user-select: none;
          user-select: none;
  width: var(--avatar-group-size);
  height: var(--avatar-group-size);
  flex-shrink: 0;

  contain: size layout;
  overflow: hidden;
  background-color: var(--accent-a3);

  --avatar-group-inner-inset: 0.5;
  --size-int: tan(atan2(var(--avatar-group-size), 1px));
  --base-size-int: tan(atan2(var(--space-7), 1px));
  --avatar-group-inner-scale: calc((var(--size-int) - var(--avatar-group-inner-inset) * 2) / var(--base-size-int));

  border-radius: max(25%, var(--radius-full));
}

.fui-AvatarGroupRoot:where(.fui-high-contrast) :where(.fui-AvatarFallback) {
    color: var(--accent-12);
  }

.fui-AvatarGroupRoot:where(.fui-variant-round) {
    --radius-full: var(--radius-thumb);
  }

.fui-AvatarGroupRoot:where(.fui-r-size-1) {
    --avatar-group-size: var(--space-5);
  }

.fui-AvatarGroupRoot:where(.fui-r-size-2) {
    --avatar-group-size: var(--space-6);
  }

.fui-AvatarGroupRoot:where(.fui-r-size-3) {
    --avatar-group-size: var(--space-7);
  }

.fui-AvatarGroupRoot:where(.fui-r-size-4) {
    --avatar-group-size: var(--space-8);
  }

.fui-AvatarGroupRoot:where(.fui-r-size-5) {
    --avatar-group-size: var(--space-9);
  }

.fui-AvatarGroupRoot:where(.fui-r-size-6) {
    --avatar-group-size: 80px;
  }

.fui-AvatarGroupRoot:where(.fui-r-size-7) {
    --avatar-group-size: 96px;
  }

.fui-AvatarGroupRoot:where(.fui-r-size-8) {
    --avatar-group-size: 128px;
  }

.fui-AvatarGroupRoot:where(.fui-r-size-9) {
    --avatar-group-size: 160px;
  }

.fui-AvatarGroupRootInner {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;

  transform-origin: 0 0;
  transform: scale(var(--avatar-group-inner-scale));
  pointer-events: none;
  left: calc(var(--avatar-group-inner-inset) * 1px);
  top: calc(var(--avatar-group-inner-inset) * 1px);
}

.fui-AvatarGroupRoot[data-status='loaded'] {
  outline: 1px solid var(--gray-a5);
  background: none;
}

.fui-AvatarGroupAvatar {
  position: absolute;
  left: 0;
  top: 0;
  transition: transform 0.5s cubic-bezier(0.32, 0.72, 0, 1);
  transform-origin: center;
  visibility: hidden;
  background-color: var(--accent-4);
}

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(1)) .fui-AvatarGroupAvatar:nth-child(1) {
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(2)) .fui-AvatarGroupAvatar:nth-child(1) {
    transform: scale(0.5) translate(-12px, -12px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(2)) .fui-AvatarGroupAvatar:nth-child(2) {
    transform: scale(0.4) translate(18px, 18px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(3)) .fui-AvatarGroupAvatar:nth-child(1) {
    transform: scale(0.5) translate(-12px, -12px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(3)) .fui-AvatarGroupAvatar:nth-child(2) {
    transform: scale(0.4) translate(26px, 10px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(3)) .fui-AvatarGroupAvatar:nth-child(3) {
    transform: scale(0.35) translate(-10px, 33px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(1) {
    transform: scale(0.5) translate(-10px, -14px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(2) {
    transform: scale(0.4) translate(23px, 12px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(3) {
    transform: scale(0.35) translate(-16px, 30px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(4) {
    transform: scale(0.25) translate(42px, -36px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(1) {
    transform: scale(0.46) translate(-16px, -12px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(2) {
    transform: scale(0.4) translate(18px, 18px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(3) {
    transform: scale(0.32) translate(-23px, 32px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(4) {
    transform: scale(0.28) translate(40px, -22px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(5) {
    transform: scale(0.22) translate(18px, -64px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(1) {
    transform: scale(0.46) translate(-16px, -12px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(2) {
    transform: scale(0.4) translate(22px, 12px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(3) {
    transform: scale(0.32) translate(-14px, 36px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(4) {
    transform: scale(0.24) translate(44px, -34px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(5) {
    transform: scale(0.2) translate(16px, -72px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(6) {
    transform: scale(0.18) translate(-76px, 36px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(1) {
    transform: scale(0.46) translate(-16px, -12px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(2) {
    transform: scale(0.4) translate(24px, 16px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(3) {
    transform: scale(0.32) translate(-14px, 36px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(4) {
    transform: scale(0.22) translate(50px, -44px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(5) {
    transform: scale(0.2) translate(16px, -72px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(6) {
    transform: scale(0.18) translate(-76px, 36px);
    visibility: visible;
  }

.fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(7) {
    transform: scale(0.16) translate(34px, -26px);
    visibility: visible;
  }

.fui-Badge {
  display: inline-flex;
  align-items: center;
  box-sizing: border-box;
  white-space: nowrap;
  font-weight: var(--font-weight-medium);
  flex-shrink: 0;
  line-height: 1;
  -webkit-user-select: none;
          user-select: none;
  cursor: default;
}

.fui-Badge:where(.fui-r-size-1) {
    font-size: var(--font-size-1);
    line-height: var(--line-height-1);
    letter-spacing: var(--letter-spacing-1);
    padding: 2px 8px;
    gap: 4px;
    border-radius: max(var(--radius-3), var(--radius-full));
  }

.fui-Badge:where(.fui-r-size-2) {
    font-size: var(--font-size-2);
    line-height: var(--line-height-2);
    letter-spacing: var(--letter-spacing-2);
    padding: 4px 12px;
    gap: 6px;
    border-radius: max(var(--radius-4), var(--radius-full));
  }

.fui-Badge:where(.fui-variant-solid) {
  background-color: var(--accent-9);
  color: var(--accent-9-contrast);
}

.fui-Badge:where(.fui-variant-solid):where(.fui-high-contrast) {
    background-color: var(--accent-12);
    color: var(--accent-1);
  }

.fui-Badge:where(.fui-variant-surface) {
  background-color: var(--color-surface-accent);
  box-shadow: inset 0 0 0 1px var(--accent-a7);
  color: var(--accent-a11);
}

.fui-Badge:where(.fui-variant-surface):where(.fui-high-contrast) {
    color: var(--accent-12);
  }

.fui-Badge:where(.fui-variant-soft) {
  background-color: var(--accent-a3);
  color: var(--accent-a11);
}

.fui-Badge:where(.fui-variant-soft):where(.fui-high-contrast) {
    color: var(--accent-12);
  }

.fui-Badge:where(.fui-variant-outline) {
  box-shadow: inset 0 0 0 1px var(--accent-a8);
  color: var(--accent-a11);
}

.fui-Badge:where(.fui-variant-outline):where(.fui-high-contrast) {
    box-shadow: inset 0 0 0 1px var(--accent-a11);
    color: var(--accent-12);
  }

.fui-Blockquote {
  border-left: clamp(2px, 0.125em, 6px) solid var(--accent-a6);
  padding-left: min(var(--space-5), max(var(--space-3), 0.5em));
}

.fui-BreadcrumbsRoot {
  display: inline-flex;
  align-items: center;
  gap: 10px;
  -webkit-user-select: none;
          user-select: none;
  user-select: none;
}

.fui-BreadcrumbsItem {
  margin-left: -8px;
  margin-right: -8px;
  font-weight: var(--font-weight-medium);
}

.fui-BreadcrumbsItem:not(:hover) {
  color: var(--accent-a10);
}

.fui-BreadcrumbsItem:last-child,
.fui-BreadcrumbsRoot .fui-BreadcrumbsLastItem {
  color: var(--accent-a12);
  font-weight: var(--font-weight-medium);
}

.fui-BreadcrumbsSeparator {
  color: var(--accent-a8);
}

.fui-BaseButton:where([aria-busy]) {
    position: relative;
  }

.fui-BaseButton {
  height: var(--base-button-height);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  flex-shrink: 0;
  -webkit-user-select: none;
          user-select: none;
  vertical-align: top;
  color: var(--base-button-color);
}

.fui-BaseButton:where(.fui-r-size-1) {
    --base-button-classic-active-padding-top: 1px;
    --base-button-height: var(--space-5);
    border-radius: 6px;
    --base-button-spinner-size: 12px;
  }

.fui-BaseButton:where(.fui-r-size-2) {
    --base-button-classic-active-padding-top: 2px;
    --base-button-height: var(--space-6);
    border-radius: 8px;
    --base-button-spinner-size: 16px;
  }

.fui-BaseButton:where(.fui-r-size-3) {
    --base-button-classic-active-padding-top: 2px;
    --base-button-height: var(--space-7);
    border-radius: 10px;
    --base-button-spinner-size: 18px;
  }

.fui-BaseButton:where(.fui-r-size-4) {
    --base-button-classic-active-padding-top: 2px;
    --base-button-height: var(--space-8);
    border-radius: 14px;
    --base-button-spinner-size: 22px;
  }

.fui-BaseButton:where(.fui-variant-classic) {
  background: var(--accent-9);
  --base-button-color: var(--accent-9-contrast);
  position: relative;
  z-index: 0;
  text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.32);

  box-shadow:
    0 0 0 1px var(--accent-9) inset,
    0px 1px 2px 0px rgba(0, 0, 0, 0.15),
    0px -1px 1px 1px rgba(0, 0, 0, 0.08) inset,
    0px 1px 1px 1px rgba(255, 255, 255, 0.25) inset;
}

.fui-BaseButton:where(.fui-variant-classic):where(.fui-high-contrast) {
    background-color: var(--accent-12);
    --base-button-color: var(--gray-1);
  }

.fui-BaseButton:where(.fui-variant-classic):where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: 2px;
  }

@media (hover: hover) {
    .fui-BaseButton:where(.fui-variant-classic):where(:hover) {
      background-color: var(--accent-10);
    }
      .fui-BaseButton:where(.fui-variant-classic):where(:hover):where(.fui-high-contrast) {
        background-color: var(--accent-12);
        filter: var(--base-button-classic-high-contrast-hover-filter);
      }
  }

.fui-BaseButton:where(.fui-variant-classic):where([data-state='open'])::after {
      background-color: var(--accent-10);
    }

.fui-BaseButton:where(.fui-variant-classic):where([data-state='open']):where(.fui-high-contrast) {
      filter: var(--base-button-classic-high-contrast-hover-filter);
    }

.fui-BaseButton:where(.fui-variant-classic):where(:active:not([data-state='open'], [data-disabled])) {
    background: var(--accent-9) linear-gradient(to bottom, transparent, var(--white-a4));
    padding-top: var(--base-button-classic-active-padding-top);

    box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.2) inset;
  }

.fui-BaseButton:where(.fui-variant-classic):where(:active:not([data-state='open'], [data-disabled])):where(.fui-high-contrast) {
      background-color: var(--accent-12);
      filter: var(--base-button-classic-high-contrast-active-filter);
    }

.fui-BaseButton:where(.fui-variant-classic):where([data-disabled]) {
    cursor: var(--cursor-disabled);
    --base-button-color: var(--gray-a8);
    background-color: var(--gray-3);

    box-shadow:
      0 0 0 1px var(--gray-a4) inset,
      0px 1px 2px 0px rgba(0, 0, 0, 0.15),
      0px -1px 1px 1px rgba(0, 0, 0, 0.08) inset,
      0px 1px 1px 1px rgba(255, 255, 255, 0.25) inset;
    background-image: none;
    filter: none;
    text-shadow: none;
  }

.fui-BaseButton:where(.fui-variant-solid) {
  position: relative;
  background-color: var(--accent-9);
  color: var(--accent-9-contrast);
}

@media (hover: hover) {
    .fui-BaseButton:where(.fui-variant-solid):where(:hover) {
      background-color: var(--accent-10);
    }
  }

.fui-BaseButton:where(.fui-variant-solid):where([data-state='open']) {
    background-color: var(--accent-10);
  }

.fui-BaseButton:where(.fui-variant-solid):where(:active:not([data-state='open'])) {
    background-color: var(--accent-10);
    filter: var(--base-button-solid-active-filter);
  }

@media (pointer: coarse) {
    .fui-BaseButton:where(.fui-variant-solid):where(:active:not([data-state='open'])) {
      outline: 0.5em solid var(--accent-a4);
      outline-offset: 0;
    }
  }

.fui-BaseButton:where(.fui-variant-solid):where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: 2px;
  }

.fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast) {
    background-color: var(--accent-12);
    color: var(--gray-1);
  }

@media (hover: hover) {
      .fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast):where(:hover) {
        background-color: var(--accent-12);
        filter: var(--base-button-solid-high-contrast-hover-filter);
      }
    }

.fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast):where([data-state='open']) {
      background-color: var(--accent-12);
      filter: var(--base-button-solid-high-contrast-hover-filter);
    }

.fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast):where(:active:not([data-state='open'])) {
      background-color: var(--accent-12);
      filter: var(--base-button-solid-high-contrast-active-filter);
    }

.fui-BaseButton:where(.fui-variant-solid):where([data-disabled]) {
    color: var(--gray-a8);
    background-color: var(--gray-a3);
    outline: none;
    filter: none;
  }

.fui-BaseButton:where(.fui-variant-soft, .fui-variant-ghost) {
  --base-button-color: var(--accent-a11);
}

.fui-BaseButton:where(.fui-variant-soft, .fui-variant-ghost):where(.fui-high-contrast) {
    --base-button-color: var(--accent-12);
  }

.fui-BaseButton:where(.fui-variant-soft, .fui-variant-ghost):where([data-disabled]) {
    cursor: var(--cursor-disabled);
    --base-button-color: var(--gray-a8);
    background-color: var(--gray-a3);
  }

.fui-BaseButton:where(.fui-variant-soft) {
  background-color: var(--accent-a3);
}

.fui-BaseButton:where(.fui-variant-soft):where(:focus-visible) {
    outline: 2px solid var(--accent-8);
    outline-offset: -1px;
  }

@media (hover: hover) {
    .fui-BaseButton:where(.fui-variant-soft):where(:hover) {
      background-color: var(--accent-a4);
    }
  }

.fui-BaseButton:where(.fui-variant-soft):where([data-state='open']) {
    background-color: var(--accent-a4);
  }

.fui-BaseButton:where(.fui-variant-soft):where(:active:not([data-state='open'])) {
    background-color: var(--accent-a5);
  }

.fui-BaseButton:where(.fui-variant-soft):where([data-disabled]) {
    cursor: var(--cursor-disabled);
    --base-button-color: var(--gray-a8);
    background-color: var(--gray-a3);
  }

@media (hover: hover) {
    .fui-BaseButton:where(.fui-variant-ghost):where(:hover) {
      background-color: var(--accent-a3);
    }
  }

.fui-BaseButton:where(.fui-variant-ghost):where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -1px;
  }

.fui-BaseButton:where(.fui-variant-ghost):where([data-state='open']) {
    background-color: var(--accent-a3);
  }

.fui-BaseButton:where(.fui-variant-ghost):where(:active:not([data-state='open'])) {
    background-color: var(--accent-a4);
  }

.fui-BaseButton:where(.fui-variant-ghost):where([data-disabled]) {
    cursor: var(--cursor-disabled);
    --base-button-color: var(--gray-a8);
    background-color: transparent;
  }

.fui-BaseButton:where(.fui-variant-surface) {
  background-color: var(--color-panel-solid);
  box-shadow:
    inset 0 0 0 1px var(--gray-a5),
    0px 1px 2px 0px rgba(0, 0, 0, 0.05);

  --base-button-color: var(--accent-11);
}

.fui-BaseButton:where(.fui-variant-surface):where([data-accent-color='gray']) {
    --base-button-color: var(--accent-12);
  }

@media (hover: hover) {
    .fui-BaseButton:where(.fui-variant-surface):where(:hover) {
      box-shadow:
        inset 0 0 0 1px var(--gray-a7),
        0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    }
  }

.fui-BaseButton:where(.fui-variant-surface):where([data-state='open']),
  .fui-BaseButton:where(.fui-variant-surface):where(:active) {
    background-color: var(--gray-a3);
    box-shadow: inset 0 0 0 1px var(--gray-a6);
  }

.fui-BaseButton:where(.fui-variant-surface):where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -1px;
  }

.fui-BaseButton:where(.fui-variant-surface):where(.fui-high-contrast) {
    --base-button-color: var(--accent-12);
  }

.fui-BaseButton:where(.fui-variant-surface):where([data-disabled]) {
    cursor: var(--cursor-disabled);
    --base-button-color: var(--gray-a8);
    box-shadow: inset 0 0 0 1px var(--gray-a6);
    background-color: var(--gray-a2);
  }

.fui-Button:where(:not(.fui-variant-ghost)) :where(svg) {
      opacity: 0.9;
    }

.fui-Button:where(.fui-r-size-1) {
    gap: var(--space-1);
    font-size: var(--font-size-1);
    line-height: var(--line-height-1);
    letter-spacing: var(--letter-spacing-1);

    padding-left: var(--space-2);
    padding-right: var(--space-2);
  }

.fui-Button:where(.fui-r-size-2) {
    gap: var(--space-2);
    font-size: var(--font-size-2);
    line-height: var(--line-height-2);
    letter-spacing: var(--letter-spacing-2);

    padding-left: var(--space-3);
    padding-right: var(--space-3);
  }

.fui-Button:where(.fui-r-size-3) {
    gap: var(--space-3);
    font-size: var(--font-size-3);
    line-height: var(--line-height-3);
    letter-spacing: var(--letter-spacing-3);

    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }

.fui-Button:where(.fui-r-size-4) {
    gap: var(--space-3);
    font-size: var(--font-size-4);
    line-height: var(--line-height-4);
    letter-spacing: var(--letter-spacing-4);

    padding-left: var(--space-5);
    padding-right: var(--space-5);
  }

.fui-Button:where(:not(.fui-variant-ghost)) {
  font-weight: var(--font-weight-medium);
}

.fui-CalendarRoot {
  display: inline-block;
}

.fui-CalendarHeader {
  display: flex;
  align-items: center;
  gap: 6px;
  margin: 0 2px 12px 2px;
}

.fui-CalendarDropdowns {
  flex: 1;
  display: flex;
  gap: 6px;
  justify-content: center;
}

.fui-CalendarDropdowns > * {
  flex: 1;
}

.fui-CalendarGrid {
  width: 100%;
  -webkit-user-select: none;
          user-select: none;
  border-spacing: 0;
  border-collapse: collapse;
  isolation: isolate;
  font-size: var(--font-size-2);
  font-weight: var(--font-weight-regular);
  color: var(--gray-a10);
}

.fui-CalendarGridCellInner {
  position: relative;
  cursor: default;
  text-align: center;
  padding: 6px 8px;
  margin: 2px 0;
  position: relative;
  outline: none;

  font-size: var(--font-size-2);
  font-weight: var(--font-weight-regular);
  line-height: var(--line-height-2);
  color: var(--gray-a12);
}

.fui-CalendarGridCellInner::before {
  content: '';
  position: absolute;
  inset: 0;
  z-index: -1;
}

.fui-CalendarGridCellInner[data-selection-state]::before {
  background: var(--accent-a4);
  border-top-right-radius: inherit;
  border-bottom-right-radius: inherit;
  border-top-left-radius: inherit;
  border-bottom-left-radius: inherit;
}

.fui-CalendarGridCellInner[data-selection-state='selected'] {
  border-radius: 6px;
}

.fui-CalendarGridCellInner:where(:not([aria-disabled]):hover) {
  background: var(--gray-a3);
  border-radius: 6px;
}

.fui-CalendarGridCellInner[data-selection-state='range']::before {
  border-radius: 0;
}

.fui-CalendarGridCellInner.fui-CalendarGridCell-selected + .fui-CalendarGridCellInner.fui-CalendarGridCell-selected {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.fui-CalendarGridCellInner:where([aria-disabled]:hover) {
  cursor: var(--cursor-disabled);
}

.fui-CalendarGridCellInner[data-rounded='left']::before {
  border-top-left-radius: 6px;
  border-bottom-left-radius: 6px;
}

.fui-CalendarGridCellInner[data-rounded='right']::before {
  border-top-right-radius: 6px;
  border-bottom-right-radius: 6px;
}

.fui-CalendarGridCellInner.fui-CalendarGridCell-selected {
  background: var(--accent-9);
  color: white;
}

.fui-CalendarGridCellInner[data-selection-state='start'],
.fui-CalendarGridCellInner[data-selection-state='end'] {
  border-radius: 6px;
}

.fui-CalendarGridCellInner[data-selection-state='end']::before {
  border-top-left-radius: unset;
  border-bottom-left-radius: unset;
}

.fui-CalendarGridCellInner[data-selection-state='start']::before {
  border-top-right-radius: unset;
  border-bottom-right-radius: unset;
}

.fui-CalendarGridCell:has([data-selection-state='start'])
  + .fui-CalendarGridCell:has([data-selection-state='end'])
  .fui-CalendarGridCellInner {
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.fui-CalendarGridCell:has([data-selection-state='start']):has(+ .fui-CalendarGridCell [data-selection-state='end'])
  .fui-CalendarGridCellInner {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.fui-CalendarGridCellInner.fui-CalendarGridCell-focusRing:focus-visible:after {
  content: '';
  position: absolute;
  inset: 0px;
  border-radius: 6px;
  z-index: -1;
  outline: 2px solid var(--accent-9);
  outline-offset: 2px;
  z-index: 1;
  mix-blend-mode: screen;
}

.fui-CalendarGridCellInner[aria-disabled] {
  opacity: 0.6;
}

.fui-CalendarGridCellInner.fui-CalendarGridCell-unavailable {
  color: var(--danger-9);
  text-decoration: line-through;
}

.fui-CalendarGridCellInnerPlaceholder {
  opacity: 0;
  pointer-events: none;
}

.fui-CalloutRoot {
  display: grid;
  align-items: flex-start;
  justify-content: flex-start;
  text-align: left;
  color: var(--accent-a11);
}

.fui-CalloutRoot:where(.fui-high-contrast) {
    color: var(--accent-12);
  }

.fui-CalloutIcon {
  display: flex;
  align-items: center;
  grid-column-start: -2;
  height: var(--line-height);
}

.fui-CalloutRoot > :where(:not(.fui-CalloutIcon)) {
  grid-column-start: -1;
}

.fui-CalloutRoot:where(.fui-r-size-1) {
    row-gap: var(--space-2);
    column-gap: var(--space-2);
    padding: var(--space-3) var(--space-4);
    border-radius: var(--radius-5);
  }

.fui-CalloutRoot:where(.fui-r-size-2) {
    row-gap: var(--space-2);
    column-gap: var(--space-3);
    padding: var(--space-4);
    border-radius: var(--radius-5);
  }

.fui-CalloutRoot:where(.fui-r-size-3) {
    row-gap: var(--space-3);
    column-gap: var(--space-3);
    padding: var(--space-5);
    border-radius: var(--radius-6);
  }

.fui-CalloutRoot:where(.fui-variant-soft) {
  background-color: var(--accent-a3);
}

.fui-CalloutRoot:where(.fui-variant-surface) {
  box-shadow: inset 0 0 0 1px var(--accent-a6);
  background-color: var(--accent-a2);
}

.fui-CalloutRoot:where(.fui-variant-outline) {
  box-shadow: inset 0 0 0 1px var(--accent-a7);
}

.fui-Card {
  border-radius: var(--card-border-radius);
  position: relative;
  text-align: initial;
  --card-after-border-radius: calc(var(--card-border-radius) - var(--card-border-width));
  border: var(--card-border-width) solid transparent;
  background-clip: padding-box;
}

.fui-Card:where(button, a) {
    display: block;
  }

.fui-Card::after {
    inset: 0;
    position: absolute;
    pointer-events: none;
    border-radius: inherit;
    content: '';
  }

.fui-Card:where(:focus-visible)::after {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -1px;
  }

.fui-CardInner {
  --inset-border-width: var(--card-border-width);
  --inset-border-radius: var(--card-border-radius);
  position: relative;
  box-sizing: border-box;
  border-radius: inherit;
  overflow: hidden;
  padding: var(--card-padding);
  --inset-padding: calc(var(--card-padding) - var(--card-border-width));
  width: calc(100% + var(--card-border-width) * 2);
  height: calc(100% + var(--card-border-width) * 2);
  margin: calc(-1 * var(--card-border-width));
}

.fui-Card:where(.fui-variant-ghost) {
    --margin-top: 0px;
    --margin-right: 0px;
    --margin-bottom: 0px;
    --margin-left: 0px;
    --margin-top-override: calc(var(--margin-top) - var(--card-padding));
    --margin-right-override: calc(var(--margin-right) - var(--card-padding));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--card-padding));
    --margin-left-override: calc(var(--margin-left) - var(--card-padding));

    margin: var(--margin-top-override) var(--margin-right-override) var(--margin-bottom-override)
      var(--margin-left-override);
  --card-border-width: 0px;
  }

:where(.fui-Card:where(.fui-variant-ghost)) > * {
      --margin-top-override: initial;
      --margin-right-override: initial;
      --margin-bottom-override: initial;
      --margin-left-override: initial;
    }

.fui-Card:where(.fui-r-size-1) {
    --card-padding: var(--space-3);
    --card-border-radius: 12px;
  }

.fui-Card:where(.fui-r-size-2) {
    --card-padding: var(--space-4);
    --card-border-radius: 16px;
  }

.fui-Card:where(.fui-r-size-3) {
    --card-padding: var(--space-5);
    --card-border-radius: 16px;
  }

.fui-Card:where(.fui-r-size-4) {
    --card-padding: var(--space-6);
    --card-border-radius: 20px;
  }

.fui-Card:where(.fui-r-size-5) {
    --card-padding: var(--space-7);
    --card-border-radius: 24px;
  }

.fui-Card:where(.fui-variant-surface) {
  --card-border-width: 1px;
  background: var(--card-background);
}

.fui-Card:where(.fui-variant-surface)::after {
    border-radius: var(--card-after-border-radius);
    box-shadow: 0 0 0 1px var(--gray-a6);
    box-shadow: 0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6) 25%);
  }

@media (hover: hover) {
      .fui-Card:where(.fui-variant-surface):where(button, [href], [type='button']):where(:hover)::after {
        box-shadow: 0 0 0 1px var(--gray-a8);
        box-shadow: 0 0 0 1px color-mix(in oklab, var(--gray-a8), var(--gray-8) 25%);
      }
    }

.fui-Card:where(.fui-variant-surface):where(button, [href], [type='button']):where(:active:not([data-state='open']))::after {
        box-shadow:
          0 0 0 1px var(--gray-a8),
          0 0 0 1px var(--gray-a6);
        box-shadow:
          0 0 0 1px color-mix(in oklab, var(--gray-a8), var(--gray-8) 25%),
          0 0 0 1px var(--gray-a6);
      }

.fui-Card:where(.fui-variant-surface):where(button, [href], [type='button']):where(:active:not([data-state='open'])):where(:focus-visible) {
        background-color: var(--gray-a3);
      }

.fui-Card:where(.fui-variant-classic) {
  --card-border-width: 1px;
  background: var(--card-background);
}

.fui-Card:where(.fui-variant-classic)::after {
    border-radius: var(--card-after-border-radius);
    box-shadow:
      0 0 0 1px var(--color-transparent),
      var(--shadow-2);
    transition: box-shadow 120ms;
  }

@media (hover: hover) {
        .fui-Card:where(.fui-variant-classic):where(button, [href], [type='button']):where(:hover)::after {
          transition-duration: 40ms;
          box-shadow: var(--card-classic-hover-box-shadow);
        }
    }

.fui-Card:where(.fui-variant-classic):where(button, [href], [type='button']):where(:active:not([data-state='open']))::after {
        transition-duration: 40ms;
        box-shadow:
          0 0 0 1px var(--gray-a5),
          var(--shadow-2);
      }

.fui-Card:where(.fui-variant-classic):where(button, [href], [type='button']):where(:active:not([data-state='open'])):where(:focus-visible) {
        background-color: var(--gray-a3);
      }

@media (hover: hover) {
      .fui-Card:where(.fui-variant-ghost):where(button, [href], [type='button']):where(:hover) {
        background-color: var(--gray-a3);
      }
    }

.fui-Card:where(.fui-variant-ghost):where(button, [href], [type='button']):where(:active:not([data-state='open'])) {
      background-color: var(--gray-a4);
    }

.fui-CheckboxRoot {
  display: inline-flex;
  align-items: center;
  vertical-align: top;
  flex-shrink: 0;
  height: var(--line-height, var(--checkbox-size));
  -webkit-user-select: none;
          user-select: none;
}

.fui-CheckboxButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: var(--checkbox-size);
  width: var(--checkbox-size);
  cursor: var(--cursor-checkbox);
  position: relative;
}

.fui-CheckboxButton:where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: 2px;
  }

.fui-CheckboxButton:where([data-state='unchecked']) {
    background-color: var(--color-surface);
    box-shadow: inset 0 0 0 1px var(--gray-a7);
  }

.fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])) {
    background-color: var(--accent-9);

    color: var(--accent-9-contrast);
  }

.fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])):where(.fui-high-contrast) {
      background-color: var(--accent-12);
      color: var(--accent-1);
    }

.fui-CheckboxButton:where(:disabled) {
    box-shadow: inset 0 0 0 1px var(--gray-a5);
    background-color: var(--gray-a2);
    background-image: none;
    cursor: var(--cursor-disabled);
    color: var(--gray-a8);
  }

@media (hover: hover) {
    .fui-CheckboxButton:where([data-state='unchecked']):not(:disabled):hover {
      box-shadow: inset 0 0 0 1px var(--gray-a8);
    }
    .fui-CheckboxButton:where([data-state='unchecked']):not(:disabled):active {
      box-shadow: inset 0 0 0 1px var(--gray-a9);
    }
    .fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])):not(:disabled):not(.fui-high-contrast):hover {
      background-color: var(--accent-10);
    }
    .fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])):not(:disabled):not(.fui-high-contrast):active {
      background-color: var(--accent-11);
    }
  }

.fui-CheckboxIndicator {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  width: 100%;
  position: absolute;
}

.fui-CheckboxRoot:where(.fui-r-size-1) {
    --checkbox-size: var(--space-4);
    gap: var(--space-2);
    font-size: var(--font-size-2);
    line-height: var(--line-height-2);
    letter-spacing: var(--letter-spacing-2);
  }

.fui-CheckboxRoot:where(.fui-r-size-1) :where(.fui-CheckboxButton) {
      border-radius: var(--radius-2);
    }

.fui-CheckboxRoot:where(.fui-r-size-1) :where(.fui-CheckboxIndicatorIcon) {
      width: var(--checkbox-size);
      height: var(--checkbox-size);
    }

.fui-CheckboxRoot:where(.fui-r-size-2) {
    --checkbox-size: calc(var(--space-4) * 1.25);
    gap: var(--space-2);
    font-size: var(--font-size-3);
    line-height: var(--line-height-3);
    letter-spacing: var(--letter-spacing-3);
  }

.fui-CheckboxRoot:where(.fui-r-size-2) :where(.fui-CheckboxButton) {
      border-radius: var(--radius-3);
    }

.fui-CheckboxRoot:where(.fui-r-size-2) :where(.fui-CheckboxIndicatorIcon) {
      width: var(--checkbox-size);
      height: var(--checkbox-size);
    }

.fui-CheckboxRoot:where(.fui-r-size-3) {
    --checkbox-size: var(--space-5);
    gap: var(--space-3);
    font-size: var(--font-size-4);
    line-height: var(--line-height-4);
    letter-spacing: var(--letter-spacing-4);
  }

.fui-CheckboxRoot:where(.fui-r-size-3) :where(.fui-CheckboxButton) {
      border-radius: var(--radius-3);
    }

.fui-CheckboxRoot:where(.fui-r-size-3) :where(.fui-CheckboxIndicatorIcon) {
      width: var(--checkbox-size);
      height: var(--checkbox-size);
    }

.fui-Code {
  --code-variant-font-size-adjust: calc(var(--code-font-size-adjust) * 0.95);
  box-sizing: border-box;
  font-family: var(--code-font-family);
  font-size: calc(var(--code-variant-font-size-adjust) * 1em);
  font-style: var(--code-font-style);
  font-weight: var(--code-font-weight);
  line-height: 1.25;
  letter-spacing: calc(var(--code-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));
  border-radius: calc((0.5px + 0.2em) * var(--radius-factor));
}

.fui-Code:where(.fui-r-size-1) {
    font-size: calc(var(--font-size-1) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-1);
    --letter-spacing: var(--letter-spacing-1);
  }

.fui-Code:where(.fui-r-size-2) {
    font-size: calc(var(--font-size-2) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-2);
    --letter-spacing: var(--letter-spacing-2);
  }

.fui-Code:where(.fui-r-size-3) {
    font-size: calc(var(--font-size-3) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-3);
    --letter-spacing: var(--letter-spacing-3);
  }

.fui-Code:where(.fui-r-size-4) {
    font-size: calc(var(--font-size-4) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-4);
    --letter-spacing: var(--letter-spacing-4);
  }

.fui-Code:where(.fui-r-size-5) {
    font-size: calc(var(--font-size-5) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-5);
    --letter-spacing: var(--letter-spacing-5);
  }

.fui-Code:where(.fui-r-size-6) {
    font-size: calc(var(--font-size-6) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-6);
    --letter-spacing: var(--letter-spacing-6);
  }

.fui-Code:where(.fui-r-size-7) {
    font-size: calc(var(--font-size-7) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-7);
    --letter-spacing: var(--letter-spacing-7);
  }

.fui-Code:where(.fui-r-size-8) {
    font-size: calc(var(--font-size-8) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-8);
    --letter-spacing: var(--letter-spacing-8);
  }

.fui-Code:where(.fui-r-size-9) {
    font-size: calc(var(--font-size-9) * var(--code-variant-font-size-adjust));
    line-height: var(--line-height-9);
    --letter-spacing: var(--letter-spacing-9);
  }

.fui-Code:where(.fui-variant-ghost) {
  --code-variant-font-size-adjust: var(--code-font-size-adjust);
  color: var(--accent-a11);
}

.fui-Code:where(.fui-variant-ghost):where(.fui-high-contrast) {
    color: var(--accent-12);
  }

:where(.fui-Link) .fui-Code:where(.fui-variant-ghost) {
    text-decoration-line: underline;
    text-decoration-color: inherit;
    text-decoration-thickness: inherit;
  }

:where(.fui-Link:has(.fui-Code:where(.fui-variant-ghost):only-child)):where(:focus-visible) .fui-Code {
      text-decoration: none;
      outline: 2px solid var(--color-focus-root);
      outline-offset: 2px;
    }

.fui-Code:where(.fui-variant-solid) {
  padding: var(--code-padding-top) 0.25em var(--code-padding-bottom);
  background-color: var(--accent-a9);
  color: var(--accent-9-contrast);
}

.fui-Code:where(.fui-variant-solid):where(.fui-high-contrast) {
    background-color: var(--accent-12);
    color: var(--accent-1);
  }

.fui-Code:where(.fui-variant-solid)::selection {
    background-color: var(--accent-7);
    color: var(--accent-12);
  }

@media (hover: hover) {
        :where(.fui-Link:has(.fui-Code:where(.fui-variant-solid):only-child)) .fui-Code:where(:hover) {
          background-color: var(--accent-10);
        }
        :where(.fui-Link:has(.fui-Code:where(.fui-variant-solid):only-child)) .fui-Code:where(.fui-high-contrast:hover) {
          background-color: var(--accent-12);
          filter: var(--base-button-solid-high-contrast-hover-filter);
        }
      }

:where(.fui-Link:has(.fui-Code:where(.fui-variant-solid):only-child)):where(:focus-visible) .fui-Code {
      outline: 2px solid var(--color-focus-root);
      outline-offset: 2px;
    }

.fui-Code:where(.fui-variant-soft) {
  padding: var(--code-padding-top) 0.25em var(--code-padding-bottom);
  background-color: var(--accent-a3);
  color: var(--accent-a11);
}

.fui-Code:where(.fui-variant-soft):where(.fui-high-contrast) {
    background-color: var(--accent-a4);
    color: var(--accent-12);
  }

@media (hover: hover) {
        :where(.fui-Link:has(.fui-Code:where(.fui-variant-soft):only-child)) .fui-Code:where(:hover) {
          background-color: var(--accent-a4);
        }
      }

:where(.fui-Link:has(.fui-Code:where(.fui-variant-soft):only-child)):where(:focus-visible) .fui-Code {
      outline: 2px solid var(--accent-8);
      outline-offset: -1px;
    }

.fui-Code:where(.fui-variant-outline) {
  padding: var(--code-padding-top) 0.25em var(--code-padding-bottom);
  box-shadow: inset 0 0 0 max(1px, 0.033em) var(--accent-a8);
  color: var(--accent-a11);
}

.fui-Code:where(.fui-variant-outline):where(.fui-high-contrast) {
    box-shadow:
      inset 0 0 0 max(1px, 0.033em) var(--accent-a7),
      inset 0 0 0 max(1px, 0.033em) var(--gray-a11);
    color: var(--accent-12);
  }

@media (hover: hover) {
        :where(.fui-Link:has(.fui-Code:where(.fui-variant-outline):only-child)) .fui-Code:where(:hover) {
          background-color: var(--accent-a2);
        }
      }

:where(.fui-Link:has(.fui-Code:where(.fui-variant-outline):only-child)):where(:focus-visible) .fui-Code {
      outline: 2px solid var(--color-focus-root);
      outline-offset: -1px;
    }

.fui-BaseMenuContent {
  --scrollarea-scrollbar-vertical-margin-top: var(--base-menu-content-padding);
  --scrollarea-scrollbar-vertical-margin-bottom: var(--base-menu-content-padding);
  --scrollarea-scrollbar-horizontal-margin-left: var(--base-menu-content-padding);
  --scrollarea-scrollbar-horizontal-margin-right: var(--base-menu-content-padding);

  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  overflow: hidden;
  background-color: var(--base-menu-bg);
  --base-menu-content-padding: var(--space-1);
  --base-menu-bg: var(--color-panel-translucent);
  -webkit-backdrop-filter: var(--backdrop-filter-panel);
  backdrop-filter: var(--backdrop-filter-panel);
  box-shadow: var(--shadow-5);
  outline: 0.5px solid var(--color-base-menu-outline) !important;
}

.fui-BaseMenuViewport {
  flex: 1 1 0%;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  padding: var(--base-menu-content-padding);
}

:where(.fui-BaseMenuContent:has(.fui-ScrollAreaScrollbar[data-orientation='vertical'])) .fui-BaseMenuViewport {
    padding-right: var(--space-3);
  }

.fui-BaseMenuItem {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: var(--base-menu-item-height);
  padding-left: var(--base-menu-item-padding-left);
  padding-right: var(--base-menu-item-padding-right);
  position: relative;
  box-sizing: border-box;
  outline: none;
  cursor: var(--cursor-menu-item);
  scroll-margin: var(--base-menu-content-padding) 0;
  -webkit-user-select: none;
          user-select: none;
}

.fui-BaseMenuShortcut {
  display: flex;
  align-items: center;
  margin-left: var(--space-5);
  color: var(--gray-a10);
}

.fui-BaseMenuSubTriggerIcon {
  color: inherit;
  margin-right: -2px;
}

.fui-BaseMenuItemIndicator {
  position: absolute;
  left: 0;
  width: var(--base-menu-item-padding-left);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.fui-BaseMenuSeparator {
  height: 1px;
  margin-top: 4px;
  margin-bottom: 4px;
  margin-left: calc(-1 * var(--base-menu-content-padding) + 1px);
  margin-right: calc(-1 * var(--base-menu-content-padding) + 1px);
  background-color: var(--gray-a6);
}

.fui-BaseMenuLabel {
  display: flex;
  align-items: center;
  height: var(--base-menu-item-height);
  padding-left: var(--base-menu-item-padding-left);
  padding-right: var(--base-menu-item-padding-right);
  color: var(--gray-a10);
  -webkit-user-select: none;
          user-select: none;
  cursor: default;
  font-weight: var(--font-weight-semi-bold);
}

:where(.fui-BaseMenuItem) + .fui-BaseMenuLabel {
    margin-top: var(--space-2);
  }

.fui-BaseMenuArrow {
  fill: var(--base-menu-bg);
}

.fui-BaseMenuContent:where(.fui-r-size-1) {
    --base-menu-item-padding-left: 8px;
    --base-menu-item-padding-right: 8px;
    --base-menu-item-height: var(--space-5);
    border-radius: 8px;
  }

.fui-BaseMenuContent:where(.fui-r-size-1) :where(.fui-BaseMenuItem) {
      font-size: var(--font-size-1);
      line-height: var(--line-height-1);
      letter-spacing: var(--letter-spacing-1);
      border-radius: 4px;
    }

.fui-BaseMenuContent:where(.fui-r-size-1) :where(.fui-BaseMenuLabel) {
      font-size: var(--font-size-0);
      line-height: var(--line-height-0);
      letter-spacing: var(--letter-spacing-0);
    }

.fui-BaseMenuContent:where(.fui-r-size-1) :where(.fui-BaseMenuItemIndicatorIcon, .fui-BaseMenuSubTriggerIcon) {
      width: 8px;
      height: 8px;
    }

.fui-BaseMenuContent:where(.fui-r-size-1):where(:not(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem))) {
      --base-menu-item-padding-left: 8px;
    }

.fui-BaseMenuContent:where(.fui-r-size-1):where(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem)) {
      --base-menu-item-padding-left: 24px;
    }

.fui-BaseMenuContent:where(.fui-r-size-2) {
    --base-menu-item-padding-left: 10px;
    --base-menu-item-padding-right: 10px;
    --base-menu-item-height: var(--space-6);
    border-radius: 10px;
  }

.fui-BaseMenuContent:where(.fui-r-size-2) :where(.fui-BaseMenuItem) {
      border-radius: var(--radius-3);
      font-size: var(--font-size-2);
      line-height: var(--line-height-2);
      letter-spacing: var(--letter-spacing-2);
      border-radius: 6px;
    }

.fui-BaseMenuContent:where(.fui-r-size-2) :where(.fui-BaseMenuLabel) {
      font-size: var(--font-size-1);
      line-height: var(--line-height-1);
      letter-spacing: var(--letter-spacing-1);
    }

.fui-BaseMenuContent:where(.fui-r-size-2) :where(.fui-BaseMenuItemIndicatorIcon, .fui-BaseMenuSubTriggerIcon) {
      width: 10px;
      height: 10px;
    }

.fui-BaseMenuContent:where(.fui-r-size-2):where(:not(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem))) {
      --base-menu-item-padding-left: 10px;
    }

.fui-BaseMenuContent:where(.fui-r-size-2):where(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem)) {
      --base-menu-item-padding-left: 26px;
    }

.fui-BaseMenuContent:where(.fui-r-size-3) {
    --base-menu-item-padding-left: 12px;
    --base-menu-item-padding-right: 12px;
    --base-menu-item-height: var(--space-7);
    border-radius: 12px;
  }

.fui-BaseMenuContent:where(.fui-r-size-3) :where(.fui-BaseMenuItem) {
      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);
      border-radius: 8px;
    }

.fui-BaseMenuContent:where(.fui-r-size-3) :where(.fui-BaseMenuLabel) {
      font-size: var(--font-size-2);
      line-height: var(--line-height-2);
      letter-spacing: var(--letter-spacing-2);
    }

.fui-BaseMenuContent:where(.fui-r-size-3) :where(.fui-BaseMenuItemIndicatorIcon, .fui-BaseMenuSubTriggerIcon) {
      width: 12px;
      height: 12px;
    }

.fui-BaseMenuContent:where(.fui-r-size-3):where(:not(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem))) {
      --base-menu-item-padding-left: 12px;
    }

.fui-BaseMenuContent:where(.fui-r-size-3):where(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem)) {
      --base-menu-item-padding-left: 28px;
    }

.fui-BaseMenuContent:where(.fui-variant-translucent) {
    background-color: var(--color-panel-translucent);
    -webkit-backdrop-filter: var(--backdrop-filter-panel);
    backdrop-filter: var(--backdrop-filter-panel);
    outline: 0.5px solid var(--color-popover-outline);
  }

.fui-BaseMenuContent:where(.fui-variant-solid) {
    background-color: var(--color-panel-solid);
  }

.fui-BaseMenuContent :where(.fui-BaseMenuSubTrigger[data-state='open']) {
    background-color: var(--gray-a3);
  }

.fui-BaseMenuContent :where(.fui-BaseMenuItem[data-highlighted]) {
    background-color: var(--gray-a4);
  }

.fui-BaseMenuContent :where(.fui-BaseMenuItem[data-highlighted]):where([data-accent-color]) {
      background-color: var(--accent-a5);
      color: var(--accent-12);
    }

.fui-BaseMenuItem:where([data-accent-color]) {
  color: var(--accent-a11);
}

.fui-BaseMenuItem:where([data-disabled]) {
  color: var(--gray-a8);
    pointer-events: none;
}

.fui-BaseMenuItem:where([data-disabled], [data-highlighted]) :where(.fui-BaseMenuShortcut), .fui-BaseMenuSubTrigger:where([data-state='open']) :where(.fui-BaseMenuShortcut) {
    color: inherit;
  }

.fui-ContextMenuContent {
  max-height: var(--radix-context-menu-content-available-height);
  transform-origin: var(--radix-context-menu-content-transform-origin);
}

.fui-r-ai-start {
  align-items: flex-start;
}

.fui-r-ai-center {
  align-items: center;
}

.fui-r-ai-end {
  align-items: flex-end;
}

.fui-r-ai-baseline {
  align-items: baseline;
}

.fui-r-ai-stretch {
  align-items: stretch;
}

.fui-DataListRoot {
  font-family: var(--default-font-family);
  font-weight: var(--font-weight-normal);
  font-style: normal;
  text-align: start;
  --data-list-leading-trim-start: calc(var(--default-leading-trim-start) - var(--line-height) / 2);
  --data-list-leading-trim-end: calc(var(--default-leading-trim-end) - var(--line-height) / 2);
}

.fui-DataListLabel {
  display: flex;
  color: var(--gray-a11);
}

.fui-DataListLabel:where(.fui-high-contrast) {
    color: var(--gray-12);
  }

.fui-DataListLabel:where([data-accent-color]) {
    color: var(--accent-a11);
  }

.fui-DataListLabel:where([data-accent-color]):where(.fui-high-contrast) {
      color: var(--accent-12);
    }

.fui-DataListValue {
  display: flex;
  margin: 0;
  min-width: 0px;
  margin-top: var(--data-list-value-margin-top);
  margin-bottom: var(--data-list-value-margin-bottom);
}

.fui-DataListItem {
  --data-list-value-margin-top: 0px;
  --data-list-value-margin-bottom: 0px;
  --data-list-first-item-value-margin-top: 0px;
  --data-list-last-item-value-margin-bottom: 0px;
  --data-list-value-trim-start: -0.25em;
  --data-list-value-trim-end: -0.25em;
  --data-list-first-item-value-trim-start: 0px;
  --data-list-last-item-value-trim-end: 0px;
}

:where(.fui-DataListItem:first-child) .fui-DataListValue {
    margin-top: var(--data-list-first-item-value-margin-top);
  }

:where(.fui-DataListItem:last-child) .fui-DataListValue {
    margin-bottom: var(--data-list-last-item-value-margin-bottom);
  }

.fui-DataListRoot:where(.fui-r-size-1) {
    gap: var(--space-3);
  }

.fui-DataListRoot:where(.fui-r-size-2) {
    gap: var(--space-4);
  }

.fui-DataListRoot:where(.fui-r-size-3) {
    gap: calc(var(--space-4) * 1.25);
  }

.fui-DataListRoot:where(.fui-r-orientation-vertical) {
    display: flex;
    flex-direction: column;
  }

.fui-DataListRoot:where(.fui-r-orientation-vertical) :where(.fui-DataListItem) {
      --data-list-value-margin-top: 0px;
      --data-list-value-margin-bottom: 0px;
      --data-list-first-item-value-margin-top: 0px;
      --data-list-last-item-value-margin-bottom: 0px;

      display: flex;
      flex-direction: column;
      gap: var(--space-1);
    }

.fui-DataListRoot:where(.fui-r-orientation-vertical) :where(.fui-DataListLabel) {
      min-width: 0px;
    }

.fui-DataListRoot:where(.fui-r-orientation-horizontal) {
    display: grid;
    grid-template-columns: auto 1fr;
  }

.fui-DataListRoot:where(.fui-r-orientation-horizontal) :where(.fui-DataListItem) {
      --data-list-value-margin-top: var(--data-list-value-trim-start);
      --data-list-value-margin-bottom: var(--data-list-value-trim-end);
      --data-list-first-item-value-margin-top: var(--data-list-first-item-value-trim-start);
      --data-list-last-item-value-margin-bottom: var(--data-list-last-item-value-trim-end);

      display: grid;
      grid-template-columns: inherit;
      grid-template-columns: subgrid;
      gap: inherit;
      grid-column: span 2;
      align-items: baseline;
    }

.fui-DataListRoot:where(.fui-r-orientation-horizontal) :where(.fui-DataListLabel) {
      min-width: 120px;
    }

.fui-DataListLabel::before, .fui-DataListValue::before {
    content: '‍';
  }

.fui-DataListItem:where(.fui-r-ai-baseline) {
    --data-list-value-trim-start: -0.25em;
    --data-list-value-trim-end: -0.25em;
    --data-list-first-item-value-trim-start: 0px;
    --data-list-last-item-value-trim-end: 0px;
  }

.fui-DataListItem:where(.fui-r-ai-start) {
    --data-list-value-trim-start: 0px;
    --data-list-value-trim-end: -0.25em;
    --data-list-first-item-value-trim-start: 0px;
    --data-list-last-item-value-trim-end: 0px;
  }

.fui-DataListItem:where(.fui-r-ai-center) {
    --data-list-value-trim-start: -0.25em;
    --data-list-value-trim-end: -0.25em;
    --data-list-first-item-value-trim-start: -0.25em;
    --data-list-last-item-value-trim-end: -0.25em;
  }

.fui-DataListItem:where(.fui-r-ai-end) {
    --data-list-value-trim-start: -0.25em;
    --data-list-value-trim-end: 0px;
    --data-list-first-item-value-trim-start: 0px;
    --data-list-last-item-value-trim-end: 0px;
  }

.fui-DataListItem:where(.fui-r-ai-stretch) {
    --data-list-value-trim-start: 0px;
    --data-list-value-trim-end: 0px;
    --data-list-first-item-value-trim-start: 0px;
    --data-list-last-item-value-trim-end: 0px;
  }

.fui-DataListItem:where(:first-child) {
    margin-top: var(--leading-trim-start);
  }

.fui-DataListItem:where(:last-child) {
    margin-bottom: var(--leading-trim-end);
  }

.fui-DataListRoot:where(.fui-r-trim-normal) {
    --leading-trim-start: initial;
    --leading-trim-end: initial;
  }

.fui-DataListRoot:where(.fui-r-trim-start) {
    --leading-trim-start: var(--data-list-leading-trim-start);
    --leading-trim-end: initial;
  }

.fui-DataListRoot:where(.fui-r-trim-end) {
    --leading-trim-start: initial;
    --leading-trim-end: var(--data-list-leading-trim-end);
  }

.fui-DataListRoot:where(.fui-r-trim-both) {
    --leading-trim-start: var(--data-list-leading-trim-start);
    --leading-trim-end: var(--data-list-leading-trim-end);
  }

.fui-DropdownMenuContent {
  max-height: var(--radix-dropdown-menu-content-available-height);
  transform-origin: var(--radix-dropdown-menu-content-transform-origin);
}

.fui-Em {
  box-sizing: border-box;
  font-family: var(--em-font-family);
  font-size: calc(var(--em-font-size-adjust) * 1em);
  font-style: var(--em-font-style);
  font-weight: var(--em-font-weight);
  line-height: 1.25;
  letter-spacing: calc(var(--em-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));
  color: inherit;
}

.fui-BaseChip {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  flex-shrink: 0;
  -webkit-user-select: none;
          user-select: none;
  vertical-align: top;
  color: var(--base-chip-color);
  height: var(--base-chip-height);
  border-radius: var(--radius-thumb);
}

.fui-BaseChip:where(.fui-r-size-1) {
    --base-chip-height: var(--space-5);
    padding-left: var(--space-2);
    padding-right: var(--space-2);

    gap: var(--space-1);
    font-size: var(--font-size-1);
    line-height: var(--line-height-1);
    letter-spacing: var(--letter-spacing-1);
    font-weight: var(--font-weight-medium);
  }

.fui-BaseChip:where(.fui-r-size-2) {
    --base-chip-height: var(--space-6);
    padding-left: var(--space-3);
    padding-right: var(--space-3);

    gap: calc(1.5 * var(--space-1));
    font-size: var(--font-size-2);
    line-height: var(--line-height-2);
    letter-spacing: var(--letter-spacing-2);
    font-weight: var(--font-weight-medium);
  }

.fui-BaseChip:where(.fui-r-size-3) {
    --base-chip-height: var(--space-7);
    padding-left: var(--space-4);
    padding-right: var(--space-4);

    gap: var(--space-2);
    font-size: var(--font-size-3);
    line-height: var(--line-height-3);
    letter-spacing: var(--letter-spacing-3);
    font-weight: var(--font-weight-medium);
  }

.fui-BaseChip:where([data-state='unchecked']) {
  box-shadow: inset 0 0 0 1px var(--gray-a5);
  --base-chip-color: var(--gray-a12);
}

.fui-BaseChip:where([data-state='unchecked']):where(:hover) {
    background-color: var(--gray-a2);
  }

.fui-BaseChip:where([data-state='unchecked']):where(:active) {
    background-color: var(--gray-a3);
  }

.fui-BaseChip:where([data-state='unchecked']):where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -1px;
  }

.fui-BaseChip:where([data-state='unchecked']):where([data-disabled]) {
    cursor: var(--cursor-disabled);
    --base-chip-color: var(--gray-a8);
    box-shadow: inset 0 0 0 1px var(--gray-a4);
    background-color: transparent;
  }

.fui-BaseChip:where([data-state='unchecked']):where(:not([data-disabled])) > svg {
    color: var(--gray-a11);
  }

.fui-BaseChip:where([data-state='checked']) {
  --base-chip-color: var(--accent-11);
  background-color: var(--accent-a3);
  box-shadow: inset 0 0 0 1px var(--accent-a6);
}

.fui-BaseChip:where([data-state='checked']):where(:focus-visible) {
    outline: 2px solid var(--accent-8);
    outline-offset: -1px;
  }

@media (hover: hover) {
    .fui-BaseChip:where([data-state='checked']):where(:hover) {
      background-color: var(--accent-a4);
    }
  }

.fui-BaseChip:where([data-state='checked']):where(:active) {
    background-color: var(--accent-a5);
  }

.fui-BaseChip:where([data-state='checked']):where([data-disabled]) {
    cursor: var(--cursor-disabled);
    --base-chip-color: var(--gray-8);
    background-color: var(--gray-a3);
    box-shadow: inset 0 0 0 1px var(--gray-a5);
  }

.fui-BaseChip:where([data-state='checked']):where(:not([data-disabled])) > svg {
    color: var(--accent-a11);
  }

.fui-HoverCardContent {
  background-color: var(--color-panel-solid);
  box-shadow: var(--shadow-4);
  overflow: auto;

  --inset-padding: var(--hover-card-content-padding);
  padding: var(--hover-card-content-padding);

  transform-origin: var(--radix-hover-card-content-transform-origin);
}

.fui-HoverCardContent:where(.fui-variant-translucent) {
    background-color: var(--color-panel-translucent);
    -webkit-backdrop-filter: var(--backdrop-filter-panel);
    backdrop-filter: var(--backdrop-filter-panel);
    outline: 0.5px solid var(--color-popover-outline);
  }

.fui-HoverCardContent:where(.fui-variant-solid) {
    background-color: var(--color-panel-solid);
  }

.fui-HoverCardContent:where(.fui-r-size-1) {
    --hover-card-content-padding: var(--space-3);
    border-radius: 8px;
  }

.fui-HoverCardContent:where(.fui-r-size-2) {
    --hover-card-content-padding: var(--space-4);
    border-radius: 12px;
  }

.fui-HoverCardContent:where(.fui-r-size-3) {
    --hover-card-content-padding: var(--space-5);
    border-radius: 16px;
  }

.fui-IconButton {
  height: var(--base-button-height);
  width: var(--base-button-height);
}

.fui-r-p-0 {
  padding: 0;
}

.fui-r-p-current {
  padding: var(--inset-padding);
}

.fui-r-px-0 {
  padding-left: 0;
  padding-right: 0;
}

.fui-r-px-current {
  padding-left: var(--inset-padding);
  padding-right: var(--inset-padding);
}

.fui-r-py-0 {
  padding-top: 0;
  padding-bottom: 0;
}

.fui-r-py-current {
  padding-top: var(--inset-padding);
  padding-bottom: var(--inset-padding);
}

.fui-r-pt-0 {
  padding-top: 0;
}

.fui-r-pt-current {
  padding-top: var(--inset-padding);
}

.fui-r-pr-0 {
  padding-right: 0;
}

.fui-r-pr-current {
  padding-right: var(--inset-padding);
}

.fui-r-pb-0 {
  padding-bottom: 0;
}

.fui-r-pb-current {
  padding-bottom: var(--inset-padding);
}

.fui-r-pl-0 {
  padding-left: 0;
}

.fui-r-pl-current {
  padding-left: var(--inset-padding);
}

.fui-Inset {
  --margin-top: 0px;
  --margin-right: 0px;
  --margin-bottom: 0px;
  --margin-left: 0px;
  overflow: hidden;
}

:where(.fui-Inset) > * {
    --margin-top-override: initial;
    --margin-right-override: initial;
    --margin-bottom-override: initial;
    --margin-left-override: initial;
  }

.fui-Inset:where(.fui-r-clip-border-box) {
    --inset-border-radius-calc: calc(var(--inset-border-radius, 0px) - var(--inset-border-width, 0px));
    --inset-padding-calc: var(--inset-padding, 0px);
  }

.fui-Inset:where(.fui-r-clip-padding-box) {
    --inset-border-radius-calc: var(--inset-border-radius, 0px);
    --inset-padding-calc: calc(var(--inset-padding, 0px) + var(--inset-border-width, 0px));
  }

.fui-Inset:where(.fui-r-side-top) {
    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));
    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));
    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));
    margin-top: var(--margin-top-override);
    margin-left: var(--margin-left-override);
    margin-right: var(--margin-right-override);
    border-top-left-radius: var(--inset-border-radius-calc);
    border-top-right-radius: var(--inset-border-radius-calc);
  }

.fui-Inset:where(.fui-r-side-bottom) {
    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));
    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));
    margin-left: var(--margin-left-override);
    margin-right: var(--margin-right-override);
    margin-bottom: var(--margin-bottom-override);
    border-bottom-left-radius: var(--inset-border-radius-calc);
    border-bottom-right-radius: var(--inset-border-radius-calc);
  }

.fui-Inset:where(.fui-r-side-left) {
    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));
    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));
    margin-top: var(--margin-top-override);
    margin-bottom: var(--margin-bottom-override);
    margin-left: var(--margin-left-override);
    border-top-left-radius: var(--inset-border-radius-calc);
    border-bottom-left-radius: var(--inset-border-radius-calc);
  }

.fui-Inset:where(.fui-r-side-right) {
    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));
    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));
    margin-top: var(--margin-top-override);
    margin-bottom: var(--margin-bottom-override);
    margin-right: var(--margin-right-override);
    border-top-right-radius: var(--inset-border-radius-calc);
    border-bottom-right-radius: var(--inset-border-radius-calc);
  }

.fui-Inset:where(.fui-r-side-x) {
    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));
    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));
    margin-left: var(--margin-left-override);
    margin-right: var(--margin-right-override);
  }

.fui-Inset:where(.fui-r-side-y) {
    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));
    margin-top: var(--margin-top-override);
    margin-bottom: var(--margin-bottom-override);
  }

.fui-Inset:where(.fui-r-side-all) {
    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));
    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));
    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));
    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));
    margin: var(--margin-top-override) var(--margin-right-override) var(--margin-bottom-override)
      var(--margin-left-override);
    border-radius: var(--inset-border-radius-calc);
  }

.fui-Kbd {
  box-sizing: border-box;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  font-family: var(--default-font-family);
  font-weight: var(--font-weight-medium);
  vertical-align: text-top;
  white-space: nowrap;
  -webkit-user-select: none;
          user-select: none;
  cursor: default;

  position: relative;
  top: -0.03em;

  font-size: 0.75em;
  min-width: 1.75em;
  line-height: 1.7em;
  padding-left: 0.5em;
  padding-right: 0.5em;
  padding-bottom: 0.05em;
  word-spacing: -0.1em;
  border-radius: calc(var(--radius-factor) * 0.35em);
  letter-spacing: var(--letter-spacing, var(--default-letter-spacing));

  color: var(--gray-12);
  background-color: var(--gray-1);
  box-shadow: var(--kbd-box-shadow);
}

.fui-Kbd:where(.fui-r-size-1) {
    font-size: calc(var(--font-size-1) * 0.8);
    --letter-spacing: var(--letter-spacing-1);
  }

.fui-Kbd:where(.fui-r-size-2) {
    font-size: calc(var(--font-size-2) * 0.8);
    --letter-spacing: var(--letter-spacing-2);
  }

.fui-Kbd:where(.fui-r-size-3) {
    font-size: calc(var(--font-size-3) * 0.8);
    --letter-spacing: var(--letter-spacing-3);
  }

.fui-Kbd:where(.fui-r-size-4) {
    font-size: calc(var(--font-size-4) * 0.8);
    --letter-spacing: var(--letter-spacing-4);
  }

.fui-Kbd:where(.fui-r-size-5) {
    font-size: calc(var(--font-size-5) * 0.8);
    --letter-spacing: var(--letter-spacing-5);
  }

.fui-Kbd:where(.fui-r-size-6) {
    font-size: calc(var(--font-size-6) * 0.8);
    --letter-spacing: var(--letter-spacing-6);
  }

.fui-Kbd:where(.fui-r-size-7) {
    font-size: calc(var(--font-size-7) * 0.8);
    --letter-spacing: var(--letter-spacing-7);
  }

.fui-Kbd:where(.fui-r-size-8) {
    font-size: calc(var(--font-size-8) * 0.8);
    --letter-spacing: var(--letter-spacing-8);
  }

.fui-Kbd:where(.fui-r-size-9) {
    font-size: calc(var(--font-size-9) * 0.8);
    --letter-spacing: var(--letter-spacing-9);
  }

.fui-Link {
  cursor: var(--cursor-link);
  color: var(--accent-a11);
  border-radius: calc(0.07em * var(--radius-factor));
  text-decoration-line: none;
  text-decoration-style: solid;
  text-decoration-thickness: min(2px, max(1px, 0.05em));
  text-underline-offset: calc(0.025em + 2px);
  text-decoration-color: var(--accent-a7);
}

.fui-Link:where(.fui-high-contrast),
  :where(.fui-CalloutRoot:not(.fui-high-contrast)) .fui-Link,
  :where(.fui-Text, .fui-Heading):where([data-accent-color]:not(.fui-high-contrast)) .fui-Link {
    color: var(--accent-12);
  }

@supports (color: color-mix(in oklab, white, black)) {
    .fui-Link {
      text-decoration-color: color-mix(in oklab, var(--accent-a7), var(--gray-a7));
    }
  }

@media (hover: hover) {
      .fui-Link:where(.fui-underline-auto):where(:hover) {
        text-decoration-line: underline;
      }
    }

.fui-Link:where(.fui-underline-auto):where(.fui-high-contrast),
    :where(.fui-CalloutRoot:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto),
    :where(.fui-Text, .fui-Heading):where([data-accent-color]:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto) {
      text-decoration-line: underline;
      text-decoration-color: var(--accent-a8);
    }

@supports (color: color-mix(in oklab, white, black)) {
        .fui-Link:where(.fui-underline-auto):where(.fui-high-contrast), :where(.fui-CalloutRoot:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto), :where(.fui-Text, .fui-Heading):where([data-accent-color]:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto) {
          text-decoration-color: color-mix(in oklab, var(--accent-a8), var(--gray-a6));
        }
      }

@media (hover: hover) {
      .fui-Link:where(.fui-underline-hover):where(:hover) {
        text-decoration-line: underline;
      }
    }

.fui-Link:where(.fui-underline-always) {
    text-decoration-line: underline;
  }

.fui-Link:where(:focus-visible) {
  text-decoration-line: none;
    outline-color: var(--color-focus-root);
    outline-width: 2px;
    outline-style: solid;
    outline-offset: 2px;
}

.fui-Link:where(:has(.fui-Code:not(.fui-variant-ghost):only-child)) {
  text-decoration-line: none;
}

.fui-Link:where(:has(.fui-Code:only-child)) {
  outline: none;
}

.fui-PopoverContent {
  box-shadow: var(--shadow-5);
  min-width: var(--radix-popover-trigger-width);
  outline: 0;
  overflow: auto;

  --inset-padding: var(--popover-content-padding);
  padding: var(--popover-content-padding);

  transform-origin: var(--radix-popover-content-transform-origin);
}

.fui-PopoverContent:where(.fui-variant-translucent) {
    background-color: var(--color-panel-translucent);
    -webkit-backdrop-filter: var(--backdrop-filter-panel);
    backdrop-filter: var(--backdrop-filter-panel);
    outline: 0.5px solid var(--color-popover-outline);
  }

.fui-PopoverContent:where(.fui-variant-solid) {
    background-color: var(--color-panel-solid);
  }

.fui-PopoverContent:where(.fui-r-size-1) {
    --popover-content-padding: var(--space-3);
    border-radius: 8px;
  }

.fui-PopoverContent:where(.fui-r-size-2) {
    --popover-content-padding: var(--space-4);
    border-radius: 12px;
  }

.fui-PopoverContent:where(.fui-r-size-3) {
    --popover-content-padding: var(--space-5);
    border-radius: 16px;
  }

.fui-PopoverContent:where(.fui-r-size-4) {
    --popover-content-padding: var(--space-6);
    border-radius: 20px;
  }

.fui-ProgressRoot {
  position: relative;
  display: block;
  width: 100%;
  height: var(--progress-height);
  border-radius: var(--radius-thumb);
  overflow: hidden;

  background-color: var(--gray-a4);
}

.fui-ProgressIndicator {
  height: 100%;
  width: 0%;
  background-color: var(--accent-9);
  border-radius: inherit;
}

.fui-ProgressRoot:where(.fui-r-size-1) {
    --progress-height: 2px;
  }

.fui-ProgressRoot:where(.fui-r-size-2) {
    --progress-height: 4px;
  }

.fui-ProgressRoot:where(.fui-r-size-3) {
    --progress-height: 6px;
  }

.fui-ProgressRoot:where(.fui-r-size-4) {
    --progress-height: 8px;
  }

.fui-ProgressRoot:where(.fui-r-size-5) {
    --progress-height: 12px;
  }

.fui-ProgressRoot:where(.fui-r-size-6) {
    --progress-height: 16px;
  }

.fui-ProgressRoot:where(.fui-high-contrast) :where(.fui-ProgressIndicator) {
    background-color: var(--accent-12);
  }

.fui-CircularProgressRoot {
  position: relative;
  display: block;
  width: var(--circular-progress-size);
  height: var(--circular-progress-size);
  border-radius: 50%;
  overflow: hidden;
  --circular-progress-color: var(--accent-9);
}

.fui-CircularProgressRoot::before {
  content: '';
  inset: 0;
  z-index: 1;
  position: absolute;
  border-radius: inherit;
  border: var(--circular-progress-border-thickness) solid var(--accent-a3);
}

.fui-CircularProgressRoot:where(.fui-r-size-1) {
    --circular-progress-size: 16px;
    --circular-progress-border-thickness: 3px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-2) {
    --circular-progress-size: 20px;
    --circular-progress-border-thickness: 4px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-3) {
    --circular-progress-size: 24px;
    --circular-progress-border-thickness: 5px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-4) {
    --circular-progress-size: 32px;
    --circular-progress-border-thickness: 5px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-5) {
    --circular-progress-size: 40px;
    --circular-progress-border-thickness: 6px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-6) {
    --circular-progress-size: 48px;
    --circular-progress-border-thickness: 7px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-7) {
    --circular-progress-size: 56px;
    --circular-progress-border-thickness: 8px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-8) {
    --circular-progress-size: 64px;
    --circular-progress-border-thickness: 9px;
  }

.fui-CircularProgressRoot:where(.fui-r-size-9) {
    --circular-progress-size: 72px;
    --circular-progress-border-thickness: 10px;
  }

.fui-CircularProgressIndicator {
  position: absolute;
  inset: 0;
  border: var(--circular-progress-border-thickness) solid var(--circular-progress-color);
  border-radius: inherit;
  --mask: conic-gradient(
    black calc(1turn * var(--circular-progress-progress)),
    /* 0.001turn to smoothen out the cut out part */ transparent
      calc(1turn * var(--circular-progress-progress) + 0.001turn)
  );
  -webkit-mask: var(--mask);
          mask: var(--mask);
  -webkit-mask-image: var(--mask);
          mask-image: var(--mask);
}

.fui-CircularProgressRoot:where(.fui-high-contrast) {
  --circular-progress-color: var(--accent-12);
}

.fui-Quote {
  box-sizing: border-box;
  font-family: var(--quote-font-family);
  font-size: calc(var(--quote-font-size-adjust) * 1em);
  font-style: var(--quote-font-style);
  font-weight: var(--quote-font-weight);
  line-height: 1.25;
  letter-spacing: calc(var(--quote-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));
  color: inherit;
}

.fui-RadioGroupItem {
  display: inline-flex;
  align-items: center;
  vertical-align: top;
  flex-shrink: 0;
  height: var(--line-height, var(--radio-group-item-size));
  gap: var(--gap);
  font-size: var(--font-size);
  line-height: var(--line-height);
  letter-spacing: var(--letter-spacing);
}

.fui-RadioGroupButton {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  height: var(--radio-group-item-size);
  width: var(--radio-group-item-size);
  cursor: var(--cursor-radio);
  border-radius: 100%;
}

.fui-RadioGroupButton:where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: 2px;
  }

.fui-RadioGroupIndicator {
  background-color: currentColor;
  height: 100%;
  width: 100%;
  border-radius: 100%;
  transform: scale(0.4);
}

.fui-RadioGroupRoot:where(.fui-r-size-1) {
    --gap: var(--space-2);
    --font-size: var(--font-size-2);
    --line-height: var(--line-height-2);
    --letter-spacing: var(--letter-spacing-2);
    --radio-group-item-size: var(--space-4);
  }

.fui-RadioGroupRoot:where(.fui-r-size-2) {
    --gap: var(--space-2);
    --font-size: var(--font-size-3);
    --line-height: var(--line-height-3);
    --letter-spacing: var(--letter-spacing-3);
    --radio-group-item-size: calc(var(--space-4) * 1.25);
  }

.fui-RadioGroupRoot:where(.fui-r-size-3) {
    --gap: var(--space-3);
    --font-size: var(--font-size-4);
    --line-height: var(--line-height-4);
    --letter-spacing: var(--letter-spacing-4);
    --radio-group-item-size: var(--space-5);
  }

.fui-RadioGroupRoot :where(.fui-RadioGroupButton[data-state='unchecked']) {
    background-color: var(--color-surface);
    box-shadow: inset 0 0 0 1px var(--gray-a7);
  }

.fui-RadioGroupRoot :where(.fui-RadioGroupButton[data-state='checked']) {
    background-color: var(--accent-9);
    color: var(--accent-9-contrast);
  }

.fui-RadioGroupRoot:where(.fui-high-contrast) :where(.fui-RadioGroupButton[data-state='checked']) {
    background-color: var(--accent-12);
    color: var(--accent-1);
  }

.fui-RadioGroupRoot :where(.fui-RadioGroupButton[data-disabled]) {
    box-shadow: inset 0 0 0 1px var(--gray-a6);
    background-color: var(--gray-a3);
    cursor: var(--cursor-disabled);
    color: var(--gray-a8);
  }

.fui-RadioButtonGroupRoot {
  --radio-items-base-color: var(--accent-9);
}

.fui-RadioButtonGroupRoot:where(.fui-high-contrast) {
    --radio-items-base-color: var(--accent-12);
  }

.fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton) {
    position: relative;
    cursor: default;
    -webkit-user-select: none;
            user-select: none;
  }

.fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton:focus-visible) {
    outline: 2px solid var(--accent-a6);
    outline-offset: 2px;
  }

.fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupOverlay) {
    position: absolute;
    pointer-events: none;
  }

.fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton[data-state='checked'] .fui-RadioButtonGroupOverlay) {
    inset: calc(-1 * var(--parent-border-width));
    border-radius: var(--parent-border-radius);
    box-shadow:
      inset 0 0 0 2px var(--radio-items-base-color),
      inset 0 0 0 4px var(--color-background);
  }

.fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupIcon) {
    visibility: hidden;
    width: 20px;
    pointer-events: none;
    height: 20px;
    border-radius: 999px;
    color: var(--accent-9-contrast);
  }

.fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton[data-state='checked'] .fui-RadioButtonGroupIcon) {
    visibility: visible;
    background: var(--accent-9);
  }

.fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton.fui-Card)::after {
    outline: none;
  }

.fui-ScrollAreaRoot {
  display: flex;
  flex-direction: column;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.fui-ScrollAreaViewport {
  width: 100%;
  height: 100%;
  overscroll-behavior-x: contain;
}

.fui-ScrollAreaViewport:where(:focus-visible) + :where(.fui-ScrollAreaViewportFocusRing) {
    position: absolute;
    inset: 0;
    pointer-events: none;
    outline: 2px solid var(--color-focus-root);
    outline-offset: -2px;
  }

.fui-ScrollAreaScrollbar {
  display: flex;
  -webkit-user-select: none;
          user-select: none;
  touch-action: none;
  background-color: var(--gray-a3);
  border-radius: var(--scrollarea-scrollbar-border-radius);

  animation-duration: 150ms;
  animation-timing-function: ease-out;
}

.fui-ScrollAreaScrollbar:where([data-orientation='vertical']) {
    flex-direction: column;
    width: var(--scrollarea-scrollbar-size);
    margin-top: var(--scrollarea-scrollbar-vertical-margin-top);
    margin-bottom: var(--scrollarea-scrollbar-vertical-margin-bottom);
    margin-left: var(--scrollarea-scrollbar-vertical-margin-left);
    margin-right: var(--scrollarea-scrollbar-vertical-margin-right);
  }

.fui-ScrollAreaScrollbar:where([data-orientation='horizontal']) {
    flex-direction: row;
    height: var(--scrollarea-scrollbar-size);
    margin-top: var(--scrollarea-scrollbar-horizontal-margin-top);
    margin-bottom: var(--scrollarea-scrollbar-horizontal-margin-bottom);
    margin-left: var(--scrollarea-scrollbar-horizontal-margin-left);
    margin-right: var(--scrollarea-scrollbar-horizontal-margin-right);
  }

.fui-ScrollAreaThumb {
  position: relative;
  background-color: var(--gray-a8);
  border-radius: inherit;
  transition: background-color 100ms;
}

.fui-ScrollAreaThumb::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    height: 100%;
    min-width: var(--space-4);
    min-height: var(--space-4);
  }

.fui-ScrollAreaScrollbar:where(.fui-r-size-1) {
    --scrollarea-scrollbar-size: var(--space-1);
    --scrollarea-scrollbar-border-radius: max(var(--radius-1), var(--radius-full));
  }

.fui-ScrollAreaScrollbar:where(.fui-r-size-2) {
    --scrollarea-scrollbar-size: var(--space-2);
    --scrollarea-scrollbar-border-radius: max(var(--radius-1), var(--radius-full));
  }

.fui-ScrollAreaScrollbar:where(.fui-r-size-3) {
    --scrollarea-scrollbar-size: var(--space-3);
    --scrollarea-scrollbar-border-radius: max(var(--radius-1), var(--radius-full));
  }

.fui-ScrollAreaScrollbar:where([data-state='visible']) {
    animation-name: fui-fade-in;
  }

.fui-ScrollAreaScrollbar:where([data-state='hidden']) {
    animation-name: fui-fade-out;
  }

@media (hover: hover) {
    .fui-ScrollAreaThumb:where(:hover) {
      background-color: var(--gray-a9);
    }
  }

.fui-BaseSegmentedControlList::-webkit-scrollbar {
    display: none;
  }

.fui-BaseSegmentedControlTrigger {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  flex-shrink: 0;
  position: relative;
  -webkit-user-select: none;
          user-select: none;
  border-radius: var(--radius-3);
  padding: 0 var(--space-2);
  font-weight: var(--font-weight-medium);
  flex: 1;
  color: var(--gray-a9);
}

@media (hover: hover) {
    .fui-BaseSegmentedControlTrigger:where(:hover) {
      color: var(--gray-a11);
    }
  }

.fui-BaseSegmentedControlTrigger:where([data-state='active'], [data-state='checked'], [data-active]) {
    color: var(--gray-a12);
  }

.fui-BaseSegmentedControlTrigger:before {
    box-sizing: border-box;
    content: '';
    position: absolute;
    inset: 0;
    border-radius: inherit;
  }

.fui-BaseSegmentedControlTrigger:where(:focus-visible)::before {
    outline: 2px solid var(--color-focus-root);
    outline-offset: 2px;
  }

.fui-BaseSegmentedControlTrigger:where([data-state='active'], [data-state='checked'], [data-active])::before {
    background: var(--color-segmented-control-thumb);
    background-image: linear-gradient(var(--white-a3), var(--white-a3));
    box-shadow:
      0px 1px 1px 0px rgba(0, 0, 0, 0.05),
      0px 2px 4px 0px rgba(0, 0, 0, 0.05);
  }

.fui-BaseSegmentedControlTriggerInner {
  display: flex;
  gap: var(--space-2);
  align-items: center;
  justify-content: center;
  position: relative;
}

.fui-BaseSegmentedControlList {
  height: var(--space-7);
  font-size: var(--font-size-2);
  line-height: var(--line-height-2);
  letter-spacing: var(--letter-spacing-2);
  font-weight: var(--font-weight-medium);
  box-sizing: border-box;
  display: flex;
  overflow-x: auto;
  white-space: nowrap;

  background: var(--gray-a3);
  border-radius: var(--radius-4);
  scrollbar-width: none;
  padding: var(--space-1);
}

.fui-SegmentedControlContent {
  position: relative;
  outline: 0;
}

.fui-SegmentedControlNavItem {
  display: flex;
  flex: 1;
}

.fui-SelectTrigger:where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -1px;
  }

.fui-SelectTriggerInner {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.fui-SelectIcon {
  flex-shrink: 0;
}

:where(.fui-SelectTrigger:not(.fui-variant-ghost)) .fui-SelectIcon {
    opacity: 0.9;
  }

.fui-SelectContent:where([data-side]) {
    min-width: var(--radix-select-trigger-width);
    max-height: var(--radix-select-content-available-height);
    transform-origin: var(--radix-select-content-transform-origin);
  }

.fui-SelectContent :where(.fui-SelectItem[data-highlighted]) {
    background-color: var(--gray-a4);
  }

.fui-SelectViewport {
  box-sizing: border-box;
  padding: var(--select-content-padding);
}

:where(.fui-SelectContent:has(.fui-ScrollAreaScrollbar[data-orientation='vertical'])) .fui-SelectViewport {
    padding-right: var(--space-3);
  }

.fui-SelectItem {
  display: flex;
  align-items: center;
  height: var(--select-item-height);
  padding-left: var(--select-item-indicator-width);
  padding-right: var(--select-item-indicator-width);
  position: relative;
  box-sizing: border-box;
  outline: none;
  cursor: var(--cursor-menu-item);
  scroll-margin: var(--select-content-padding) 0;
  -webkit-user-select: none;
          user-select: none;
}

.fui-SelectItemIndicator {
  position: absolute;
  left: 0;
  width: var(--select-item-indicator-width);
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.fui-SelectSeparator {
  height: 1px;
  margin-top: 4px;
  margin-bottom: 4px;
  margin-left: calc(-1 * var(--select-content-padding) + 1px);
  margin-right: calc(-1 * var(--select-content-padding) + 1px);
  background-color: var(--gray-a6);
}

.fui-SelectLabel {
  display: flex;
  align-items: center;
  height: var(--select-item-height);
  padding-left: var(--select-item-indicator-width);
  padding-right: var(--select-item-indicator-width);
  color: var(--gray-a10);
  -webkit-user-select: none;
          user-select: none;
  cursor: default;
  font-weight: var(--font-weight-semi-bold);
}

:where(.fui-SelectItem) + .fui-SelectLabel {
    margin-top: var(--space-2);
  }

.fui-SelectTrigger {
  color: var(--gray-12);
  height: var(--select-trigger-height);
  display: inline-flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
  flex-shrink: 0;
  -webkit-user-select: none;
          user-select: none;
  vertical-align: top;
  line-height: var(--height);
}

.fui-SelectTrigger:where(.fui-r-size-1) {
    --select-trigger-height: var(--space-5);
    gap: var(--space-1);
    font-size: var(--font-size-1);
    line-height: var(--line-height-1);
    letter-spacing: var(--letter-spacing-1);
    border-radius: 6px;

    padding-left: var(--space-2);
    padding-right: var(--space-2);
  }

.fui-SelectTrigger:where(.fui-r-size-1) :where(.fui-SelectIcon) {
      width: 8px;
      height: 8px;
    }

.fui-SelectTrigger:where(.fui-r-size-2) {
    --select-trigger-height: var(--space-6);
    gap: calc(var(--space-1) * 1.5);
    font-size: var(--font-size-2);
    line-height: var(--line-height-2);
    letter-spacing: var(--letter-spacing-2);
    border-radius: 8px;

    padding-left: var(--space-3);
    padding-right: var(--space-3);
  }

.fui-SelectTrigger:where(.fui-r-size-2) :where(.fui-SelectIcon) {
      width: 10px;
      height: 10px;
      margin-bottom: -1px;
    }

.fui-SelectTrigger:where(.fui-r-size-3) {
    --select-trigger-height: var(--space-7);
    gap: var(--space-2);
    font-size: var(--font-size-3);
    line-height: var(--line-height-3);
    letter-spacing: var(--letter-spacing-3);
    border-radius: 10px;

    padding-left: var(--space-4);
    padding-right: var(--space-4);
  }

.fui-SelectTrigger:where(.fui-r-size-3) :where(.fui-SelectIcon) {
      width: 12px;
      height: 12px;
      margin-bottom: -2px;
    }

.fui-SelectContent:where(.fui-r-size-1) {
    --select-item-height: var(--space-5);
    --select-item-indicator-width: calc(var(--space-5) / 1.2);
    --select-separator-margin-right: var(--space-2);
    border-radius: 8px;
  }

.fui-SelectContent:where(.fui-r-size-1) :where(.fui-SelectLabel) {
      font-size: var(--font-size-0);
      letter-spacing: var(--letter-spacing-0);
      line-height: var(--line-height-0);
    }

.fui-SelectContent:where(.fui-r-size-1) :where(.fui-SelectItem) {
      font-size: var(--font-size-1);
      line-height: var(--line-height-1);
      letter-spacing: var(--letter-spacing-1);
      border-radius: 4px;
    }

.fui-SelectContent:where(.fui-r-size-1) :where(.fui-SelectItemIndicatorIcon) {
      width: 8px;
      height: 8px;
    }

.fui-SelectContent:where(.fui-r-size-2) {
    --select-item-height: var(--space-6);
    --select-item-indicator-width: calc(var(--space-6) / 1.2);
    --select-separator-margin-right: var(--space-2);
    border-radius: 10px;
  }

.fui-SelectContent:where(.fui-r-size-2) :where(.fui-SelectLabel) {
      font-size: var(--font-size-1);
      letter-spacing: var(--letter-spacing-1);
      line-height: var(--line-height-1);
    }

.fui-SelectContent:where(.fui-r-size-2) :where(.fui-SelectItem) {
      font-size: var(--font-size-2);
      line-height: var(--line-height-2);
      letter-spacing: var(--letter-spacing-2);
      border-radius: 6px;
    }

.fui-SelectContent:where(.fui-r-size-2) :where(.fui-SelectItemIndicatorIcon) {
      width: 10px;
      height: 10px;
    }

.fui-SelectContent:where(.fui-r-size-3) {
    --select-item-height: var(--space-7);
    --select-item-indicator-width: calc(var(--space-7) / 1.2);
    --select-separator-margin-right: var(--space-2);
    border-radius: 12px;
  }

.fui-SelectContent:where(.fui-r-size-3) :where(.fui-SelectLabel) {
      font-size: var(--font-size-2);
      letter-spacing: var(--letter-spacing-2);
      line-height: var(--line-height-2);
    }

.fui-SelectContent:where(.fui-r-size-3) :where(.fui-SelectItem) {
      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);
      border-radius: 8px;
    }

.fui-SelectContent:where(.fui-r-size-3) :where(.fui-SelectItemIndicatorIcon) {
      width: 12px;
      height: 12px;
    }

.fui-SelectTrigger:where(.fui-variant-surface) {
  color: var(--gray-12);
  background-color: var(--color-surface);
  box-shadow:
    inset 0 0 0 1px var(--gray-a5),
    0px 1px 2px 0px rgba(0, 0, 0, 0.05);
}

@media (hover: hover) {
    .fui-SelectTrigger:where(.fui-variant-surface):where(:hover) {
      box-shadow:
        inset 0 0 0 1px var(--gray-a7),
        0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    }
  }

.fui-SelectTrigger:where(.fui-variant-surface):where([data-state='open']) {
    box-shadow: inset 0 0 0 1px var(--gray-a7);
  }

.fui-SelectTrigger:where(.fui-variant-surface):where(:disabled) {
    cursor: var(--cursor-disabled);
    color: var(--gray-a8);
    background-color: var(--gray-a2);
    box-shadow: inset 0 0 0 1px var(--gray-a6);
  }

.fui-SelectTrigger:where(.fui-variant-surface):where([data-placeholder]) :where(.fui-SelectTriggerInner) {
      color: var(--gray-a10);
    }

.fui-SelectTrigger:where(.fui-variant-classic) {
  color: var(--gray-12);
  background-image: linear-gradient(var(--gray-2), var(--gray-1));
  box-shadow: var(--select-trigger-classic-box-shadow);
  position: relative;
  z-index: 0;
}

.fui-SelectTrigger:where(.fui-variant-classic)::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0;
    border: 2px solid transparent;
    background-clip: content-box;
    border-radius: inherit;
    pointer-events: none;
    background-image:
      linear-gradient(var(--black-a1) -20%, transparent, var(--white-a1) 130%),
      linear-gradient(var(--color-surface), transparent);
  }

@media (hover: hover) {
    .fui-SelectTrigger:where(.fui-variant-classic):where(:hover) {
      box-shadow:
        inset 0 0 0 1px var(--gray-a3),
        var(--select-trigger-classic-box-shadow);
    }

      .fui-SelectTrigger:where(.fui-variant-classic):where(:hover)::before {
        background-image:
          linear-gradient(var(--black-a1) -15%, transparent, var(--white-a1) 120%),
          linear-gradient(var(--gray-2), var(--gray-1));
      }
  }

.fui-SelectTrigger:where(.fui-variant-classic):where([data-state='open']) {
    box-shadow:
      inset 0 0 0 1px var(--gray-a3),
      var(--select-trigger-classic-box-shadow);
  }

.fui-SelectTrigger:where(.fui-variant-classic):where([data-state='open'])::before {
      background-image:
        linear-gradient(var(--black-a1) -15%, transparent, var(--white-a1) 120%),
        linear-gradient(var(--gray-2), var(--gray-1));
    }

.fui-SelectTrigger:where(.fui-variant-classic):where(:disabled) {
    cursor: var(--cursor-disabled);
    color: var(--gray-a8);
    box-shadow: var(--base-button-classic-disabled-box-shadow);
  }

.fui-SelectTrigger:where(.fui-variant-classic):where(:disabled)::before {
      background-image:
        linear-gradient(var(--black-a1) -20%, transparent, var(--white-a1) 130%),
        linear-gradient(var(--color-surface), transparent);
    }

.fui-SelectTrigger:where(.fui-variant-classic):where([data-placeholder]) :where(.fui-SelectTriggerInner) {
      color: var(--gray-a10);
    }

.fui-SelectTrigger:where(.fui-variant-soft),
.fui-SelectTrigger:where(.fui-variant-ghost) {
  color: var(--accent-12);
}

.fui-SelectTrigger:where(.fui-variant-soft):where([data-placeholder]) :where(.fui-SelectTriggerInner), .fui-SelectTrigger:where(.fui-variant-ghost):where([data-placeholder]) :where(.fui-SelectTriggerInner) {
      color: var(--accent-12);
      opacity: 0.6;
    }

.fui-SelectTrigger:where(.fui-variant-soft) {
  background-color: var(--accent-a3);
}

@media (hover: hover) {
    .fui-SelectTrigger:where(.fui-variant-soft):where(:hover) {
      background-color: var(--accent-a4);
    }
  }

.fui-SelectTrigger:where(.fui-variant-soft):where([data-state='open']) {
    background-color: var(--accent-a4);
  }

.fui-SelectTrigger:where(.fui-variant-soft):where(:focus-visible) {
    outline-color: var(--accent-8);
  }

.fui-SelectTrigger:where(.fui-variant-soft):where(:disabled) {
    cursor: var(--cursor-disabled);
    color: var(--gray-a8);
    background-color: var(--gray-a3);
  }

@media (hover: hover) {
    .fui-SelectTrigger:where(.fui-variant-ghost):where(:hover) {
      background-color: var(--accent-a3);
    }
  }

.fui-SelectTrigger:where(.fui-variant-ghost):where([data-state='open']) {
    background-color: var(--accent-a3);
  }

.fui-SelectTrigger:where(.fui-variant-ghost):where(:disabled) {
    cursor: var(--cursor-disabled);
    color: var(--gray-a8);
    background-color: transparent;
  }

.fui-SelectContent {
  box-shadow: var(--shadow-5);
  outline: 0.5px solid var(--color-base-menu-outline) !important;
  --scrollarea-scrollbar-vertical-margin-top: var(--select-content-padding);
  --scrollarea-scrollbar-vertical-margin-bottom: var(--select-content-padding);
  --scrollarea-scrollbar-horizontal-margin-left: var(--select-content-padding);
  --scrollarea-scrollbar-horizontal-margin-right: var(--select-content-padding);

  overflow: hidden;
  background-color: var(--color-panel-solid);
  --select-content-padding: var(--space-1);
}

.fui-SelectItem:where([data-disabled]) {
  color: var(--gray-a8);
}

.fui-Separator {
  background-color: var(--accent-a6);
}

.fui-Separator:where([data-orientation='vertical']) {
    width: 1px;
    height: var(--separator-size);
  }

.fui-Separator:where([data-orientation='horizontal']) {
    width: var(--separator-size);
    height: 1px;
  }

.fui-Separator:where(.fui-r-size-1) {
    --separator-size: var(--space-4);
  }

.fui-Separator:where(.fui-r-size-2) {
    --separator-size: var(--space-6);
  }

.fui-Separator:where(.fui-r-size-3) {
    --separator-size: var(--space-9);
  }

.fui-Separator:where(.fui-r-size-4) {
    --separator-size: 100%;
  }

.fui-SkeletonAvatar {
  display: inline-block;
  vertical-align: middle;
  -webkit-user-select: none;
          user-select: none;
  width: var(--skeleton-avatar-size);
  height: var(--skeleton-avatar-size);
  flex-shrink: 0;

  background-size: 400% 100%;
  background-image: linear-gradient(
    270deg,
    var(--skeleton-background-color),
    var(--skeleton-background-shimmer-color),
    var(--skeleton-background-shimmer-color),
    var(--skeleton-background-color)
  );

  --skeleton-background-color: var(--accent-a2);
  --skeleton-background-shimmer-color: var(--accent-a3);
}

.fui-SkeletonAvatar:where(.fui-high-contrast) {
    --skeleton-background-color: var(--accent-a4);
    --skeleton-background-shimmer-color: var(--accent-a6);
  }

.fui-SkeletonAvatar:where(.fui-r-size-1) {
    --skeleton-avatar-size: var(--space-5);
    border-radius: max(var(--radius-2), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-2) {
    --skeleton-avatar-size: var(--space-6);
    border-radius: max(var(--radius-2), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-3) {
    --skeleton-avatar-size: var(--space-7);
    border-radius: max(var(--radius-3), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-4) {
    --skeleton-avatar-size: var(--space-8);
    border-radius: max(var(--radius-3), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-5) {
    --skeleton-avatar-size: var(--space-9);
    border-radius: max(var(--radius-4), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-6) {
    --skeleton-avatar-size: 80px;
    border-radius: max(var(--radius-5), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-7) {
    --skeleton-avatar-size: 96px;
    border-radius: max(var(--radius-5), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-8) {
    --skeleton-avatar-size: 128px;
    border-radius: max(var(--radius-6), var(--radius-full));
  }

.fui-SkeletonAvatar:where(.fui-r-size-9) {
    --skeleton-avatar-size: 160px;
    border-radius: max(var(--radius-6), var(--radius-full));
  }

.fui-SkeletonText {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  margin: 0;

  height: var(--skeleton-text-line-height);

  --skeleton-background-color: var(--accent-a2);
  --skeleton-background-shimmer-color: var(--accent-a3);
}

.fui-SkeletonText:where(.fui-high-contrast) {
    --skeleton-background-color: var(--accent-a4);
    --skeleton-background-shimmer-color: var(--accent-a6);
  }

.fui-SkeletonText::after {
  content: '';
  width: 100%;
  height: var(--skeleton-text-size);

  background-size: 400% 100%;
  background-image: linear-gradient(
    270deg,
    var(--skeleton-background-color),
    var(--skeleton-background-shimmer-color),
    var(--skeleton-background-shimmer-color),
    var(--skeleton-background-color)
  );

  border-radius: var(--skeleton-text-border-radius);
}

.fui-SkeletonText:where(.fui-r-size-0) {
    --skeleton-text-size: var(--font-size-0);
    --skeleton-text-line-height: var(--line-height-0);
    --skeleton-text-border-radius: var(--radius-1);
  }

.fui-SkeletonText:where(.fui-r-size-1) {
    --skeleton-text-size: var(--font-size-1);
    --skeleton-text-line-height: var(--line-height-1);
    --skeleton-text-border-radius: var(--radius-1);
  }

.fui-SkeletonText:where(.fui-r-size-2) {
    --skeleton-text-size: var(--font-size-2);
    --skeleton-text-line-height: var(--line-height-2);
    --skeleton-text-border-radius: var(--radius-1);
  }

.fui-SkeletonText:where(.fui-r-size-3) {
    --skeleton-text-size: var(--font-size-3);
    --skeleton-text-line-height: var(--line-height-3);
    --skeleton-text-border-radius: var(--radius-2);
  }

.fui-SkeletonText:where(.fui-r-size-4) {
    --skeleton-text-size: var(--font-size-4);
    --skeleton-text-line-height: var(--line-height-4);
    --skeleton-text-border-radius: var(--radius-2);
  }

.fui-SkeletonText:where(.fui-r-size-5) {
    --skeleton-text-size: var(--font-size-5);
    --skeleton-text-line-height: var(--line-height-5);
    --skeleton-text-border-radius: var(--radius-3);
  }

.fui-SkeletonText:where(.fui-r-size-6) {
    --skeleton-text-size: var(--font-size-6);
    --skeleton-text-line-height: var(--line-height-6);
    --skeleton-text-border-radius: var(--radius-3);
  }

.fui-SkeletonText:where(.fui-r-size-7) {
    --skeleton-text-size: var(--font-size-7);
    --skeleton-text-line-height: var(--line-height-7);
    --skeleton-text-border-radius: var(--radius-4);
  }

.fui-SkeletonText:where(.fui-r-size-8) {
    --skeleton-text-size: var(--font-size-8);
    --skeleton-text-line-height: var(--line-height-8);
    --skeleton-text-border-radius: var(--radius-4);
  }

.fui-SkeletonText:where(.fui-r-size-9) {
    --skeleton-text-size: var(--font-size-9);
    --skeleton-text-line-height: var(--line-height-9);
    --skeleton-text-border-radius: var(--radius-5);
  }

.fui-SkeletonRect {
  display: inline-block;
  vertical-align: middle;
  -webkit-user-select: none;
          user-select: none;
  flex-shrink: 0;

  background-size: 400% 100%;
  background-image: linear-gradient(
    270deg,
    var(--skeleton-background-color),
    var(--skeleton-background-shimmer-color),
    var(--skeleton-background-shimmer-color),
    var(--skeleton-background-color)
  );

  --skeleton-background-color: var(--accent-a2);
  --skeleton-background-shimmer-color: var(--accent-a3);
}

.fui-SkeletonRect:where(.fui-high-contrast) {
    --skeleton-background-color: var(--accent-a4);
    --skeleton-background-shimmer-color: var(--accent-a6);
  }

@media (prefers-reduced-motion: no-preference) {
  @keyframes fui-skeleton-shimmer {
    from {
      background-position: 200% 0;
    }
    to {
      background-position: -200% 0;
    }
  }

  .fui-SkeletonAvatar,
  .fui-SkeletonText::after,
  .fui-SkeletonRect {
    animation: fui-skeleton-shimmer 8s ease-in-out infinite;
  }

    .fui-DrawerContent:where([data-state='open'])::before {
      animation: fui-drawer-backdrop-show 400ms cubic-bezier(0.16, 1, 0.3, 1);
    }

    .fui-DrawerContent:where([data-state='closed'])::before {
      animation: fui-drawer-backdrop-hide 300ms cubic-bezier(0.16, 1, 0.3, 1);
    }
}

.fui-SliderRoot {
  --slider-thumb-size: calc(var(--slider-track-size) + var(--space-1));

  position: relative;
  display: flex;
  align-items: center;
  flex-shrink: 0;
  -webkit-user-select: none;
          user-select: none;
  touch-action: none;
}

.fui-SliderRoot:where([data-orientation='horizontal']) {
    height: var(--slider-thumb-size);
  }

.fui-SliderRoot:where([data-orientation='vertical']) {
    height: 100%;
    flex-direction: column;
    width: var(--slider-thumb-size);
  }

.fui-SliderRoot :where(.fui-SliderTrack) {
    background-color: var(--gray-a4);
    background-image: linear-gradient(var(--white-a1), var(--white-a1));
  }

.fui-SliderRoot :where(.fui-SliderTrack):where([data-disabled]) {
      background-color: var(--gray-a4);
      background-image: none;
    }

.fui-SliderRoot :where(.fui-SliderRange) {
    background-color: var(--accent-9);
  }

.fui-SliderRoot :where(.fui-SliderThumb) {
    --slider-thumb-box-shadow:
      0 0 0 1px var(--black-a3), 0 0 0 1px var(--gray-a2), 0 0 0 1px var(--accent-a2), 0 1px 2px var(--gray-a4),
      0 1px 3px -0.5px var(--gray-a3);
  }

.fui-SliderRoot :where(.fui-SliderThumb):where([data-disabled])::after {
      background-color: var(--gray-1);
      box-shadow: 0 0 0 1px var(--gray-5);
    }

.fui-SliderTrack {
  overflow: hidden;
  position: relative;
  flex-grow: 1;

  border-radius: max(
    calc(var(--radius-factor) * var(--slider-track-size) / 3),
    calc(var(--radius-factor) * var(--radius-thumb))
  );
}

.fui-SliderTrack:where([data-orientation='horizontal']) {
    height: var(--slider-track-size);
  }

.fui-SliderTrack:where([data-orientation='vertical']) {
    width: var(--slider-track-size);
  }

.fui-SliderRange {
  position: absolute;
  border-radius: inherit;
}

.fui-SliderRange:where([data-orientation='horizontal']) {
    height: 100%;
  }

.fui-SliderRange:where([data-orientation='vertical']) {
    width: 100%;
  }

.fui-SliderThumb {
  display: block;
  width: var(--slider-thumb-size);
  height: var(--slider-thumb-size);
}

.fui-SliderThumb::before {
    content: '';
    position: absolute;
    z-index: -1;
    width: calc(var(--slider-thumb-size) * 3);
    height: calc(var(--slider-thumb-size) * 3);
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

.fui-SliderThumb::after {
    content: '';
    position: absolute;
    inset: calc(-0.25 * var(--slider-track-size));
    background-color: white;
    border-radius: max(var(--radius-1), var(--radius-thumb));
    box-shadow: var(--slider-thumb-box-shadow);
    cursor: var(--cursor-slider-thumb);
  }

.fui-SliderThumb:where(:focus-visible)::after {
    box-shadow:
      var(--slider-thumb-box-shadow),
      0 0 0 3px var(--accent-3),
      0 0 0 5px var(--color-focus-root);
  }

.fui-SliderThumb:where(:active) {
    cursor: var(--cursor-slider-thumb-active);
  }

.fui-SliderRoot:where(.fui-r-size-1) {
    --slider-track-size: calc(var(--space-2) * 0.75);
  }

.fui-SliderRoot:where(.fui-r-size-2) {
    --slider-track-size: var(--space-2);
  }

.fui-SliderRoot:where(.fui-r-size-3) {
    --slider-track-size: calc(var(--space-2) * 1.25);
  }

.fui-SliderRoot:where(.fui-high-contrast) :where(.fui-SliderRange) {
    background: var(--accent-12);
  }

.fui-SliderRoot:where([data-disabled]) {
    cursor: var(--cursor-disabled);
    mix-blend-mode: var(--slider-disabled-blend-mode);
  }

.fui-SliderRange:where([data-disabled]) {
    background-color: var(--gray-a5);
  }

.fui-Spinner {
  display: block;
  position: relative;
  opacity: var(--spinner-opacity);
}

.fui-SpinnerLeaf {
  position: absolute;
  top: 0;
  left: calc(50% - 12.5% / 2);
  width: 12.5%;
  height: 100%;
  animation: fui-spinner-leaf-fade 800ms linear infinite;
}

.fui-SpinnerLeaf::before {
    content: '';
    display: block;
    width: 100%;
    height: 30%;
    border-radius: var(--radius-1);
    background-color: currentColor;
  }

.fui-SpinnerLeaf:where(:nth-child(1)) {
    transform: rotate(0deg);
    animation-delay: -800ms;
  }

.fui-SpinnerLeaf:where(:nth-child(2)) {
    transform: rotate(45deg);
    animation-delay: -700ms;
  }

.fui-SpinnerLeaf:where(:nth-child(3)) {
    transform: rotate(90deg);
    animation-delay: -600ms;
  }

.fui-SpinnerLeaf:where(:nth-child(4)) {
    transform: rotate(135deg);
    animation-delay: -500ms;
  }

.fui-SpinnerLeaf:where(:nth-child(5)) {
    transform: rotate(180deg);
    animation-delay: -400ms;
  }

.fui-SpinnerLeaf:where(:nth-child(6)) {
    transform: rotate(225deg);
    animation-delay: -300ms;
  }

.fui-SpinnerLeaf:where(:nth-child(7)) {
    transform: rotate(270deg);
    animation-delay: -200ms;
  }

.fui-SpinnerLeaf:where(:nth-child(8)) {
    transform: rotate(315deg);
    animation-delay: -100ms;
  }

@keyframes fui-spinner-leaf-fade {
  from {
    opacity: 1;
  }
  to {
    opacity: 0.25;
  }
}

.fui-Spinner:where(.fui-r-size-1) {
    width: var(--space-3);
    height: var(--space-3);
  }

.fui-Spinner:where(.fui-r-size-2) {
    width: var(--space-4);
    height: var(--space-4);
  }

.fui-Spinner:where(.fui-r-size-3) {
    width: calc(1.25 * var(--space-4));
    height: calc(1.25 * var(--space-4));
  }

.fui-Spinner:where(.fui-r-size-4) {
    width: var(--space-5);
    height: var(--space-5);
  }

.fui-Spinner:where(.fui-r-size-5) {
    width: var(--space-6);
    height: var(--space-6);
  }

.fui-Spinner:where(.fui-r-size-6) {
    width: var(--space-7);
    height: var(--space-7);
  }

.fui-StackedHorizontalBarChart {
  display: flex;
  gap: var(--space-1);
  height: 12px;
  width: 100%;
  overflow: hidden;
  position: relative;
  border-radius: var(--radius-2);
}

.fui-StackedHorizontalBarChartBar {
  height: 100%;
  background: linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, rgba(0, 0, 0, 0.15) 100%), var(--accent-9);
  transition: 0.2s width cubic-bezier(0.33, 1, 0.68, 1);
  min-width: 2px;
  border-radius: 1px;
}

.fui-StackedHorizontalBarChartTooltip {
  position: relative;
}

.fui-StackedHorizontalBarChartTooltip::after {
  content: '';
  position: absolute;
  inset: -1px;
  border-radius: inherit;
  background: linear-gradient(to bottom, transparent, transparent, var(--accent-a4));
  mix-blend-mode: overlay;
  pointer-events: none;
}

.fui-Strong {
  font-family: var(--strong-font-family);
  font-size: calc(var(--strong-font-size-adjust) * 1em);
  font-style: var(--strong-font-style);
  font-weight: var(--strong-font-weight);
  letter-spacing: calc(var(--strong-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));
}

.fui-SwitchRoot {
  display: inline-flex;
  align-items: center;
  vertical-align: top;
  flex-shrink: 0;
  height: var(--line-height, var(--switch-height));

  --switch-padding: 1px;
  --switch-width: calc(var(--switch-height) * 1.75);
  --switch-thumb-size: calc(var(--switch-height) - var(--switch-padding) * 2);
  --switch-thumb-translate-x: calc(var(--switch-width) - var(--switch-height));
}

.fui-SwitchButton {
  position: relative;
  display: inline-flex;
  align-items: center;
  width: var(--switch-width);
  height: var(--switch-height);
  padding: var(--switch-padding);
  border-radius: var(--radius-thumb);
  cursor: var(--cursor-switch);
}

.fui-SwitchButton::before {
    content: '';
    position: absolute;
    inset: 0;
    pointer-events: none;
    border-radius: inherit;
    transition: background-position, background-color, box-shadow, filter;
    transition-timing-function: linear, ease-in-out, ease-in-out, ease-in-out;
    background-repeat: no-repeat;
    background-size: calc(var(--switch-width) * 2 + var(--switch-height)) 100%;
  }

.fui-SwitchButton:where([data-state='unchecked'])::before {
    transition-duration: 120ms, 140ms, 140ms, 140ms;
    background-position-x: 100%;
  }

.fui-SwitchButton:where([data-state='checked'])::before {
    transition-duration: 160ms, 140ms, 140ms, 140ms;
    background-position: 0%;
  }

.fui-SwitchButton:where(:active)::before {
    transition-duration: 30ms;
  }

.fui-SwitchButton:where(:focus-visible) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: 2px;
  }

.fui-SwitchThumb {
  background-color: white;
  position: relative;
  width: var(--switch-thumb-size);
  height: var(--switch-thumb-size);
  border-radius: var(--radius-thumb);
  transition:
    transform 140ms cubic-bezier(0.45, 0.05, 0.55, 0.95),
    box-shadow 140ms ease-in-out;
}

.fui-SwitchThumb:where([data-state='checked']) {
    transform: translateX(var(--switch-thumb-translate-x));
  }

.fui-SwitchRoot:where(.fui-r-size-1) {
    --switch-height: var(--space-4);
  }

.fui-SwitchRoot:where(.fui-r-size-2) {
    --switch-height: var(--space-5);
  }

.fui-SwitchRoot:where(.fui-r-size-3) {
    --switch-height: calc(var(--space-6));
  }

.fui-SwitchRoot :where(.fui-SwitchButton)::before {
      background-image: linear-gradient(to right, var(--accent-9) 40%, transparent 60%);
      background-color: var(--gray-a4);
      box-shadow: var(--shadow-1);
    }

.fui-SwitchRoot :where(.fui-SwitchButton):where([data-state='unchecked']:active)::before {
      background-color: var(--gray-a5);
    }

.fui-SwitchRoot :where(.fui-SwitchButton):where([data-state='checked'])::before {
      box-shadow:
        inset 0 0 0 1px var(--gray-a3),
        inset 0 0 0 1px var(--accent-a4),
        inset 0 0 0 1px var(--black-a1),
        inset 0 1.5px 2px 0 var(--black-a2);
    }

.fui-SwitchRoot :where(.fui-SwitchButton):where([data-state='checked']:active)::before {
      filter: var(--switch-button-surface-checked-active-filter);
    }

.fui-SwitchRoot :where(.fui-SwitchButton):where(.fui-high-contrast)::before {
        box-shadow:
          inset 0 0 0 1px var(--gray-a3),
          inset 0 0 0 1px var(--black-a2),
          inset 0 1.5px 2px 0 var(--black-a2);
        background-image:
          linear-gradient(to right, var(--switch-button-high-contrast-checked-color-overlay) 40%, transparent 60%),
          linear-gradient(to right, var(--accent-9) 40%, transparent 60%);
      }

.fui-SwitchRoot :where(.fui-SwitchButton):where(.fui-high-contrast):where([data-state='checked']:active)::before {
        filter: var(--switch-button-high-contrast-checked-active-before-filter);
      }

.fui-SwitchRoot :where(.fui-SwitchButton):where([data-disabled]) {
      cursor: var(--cursor-disabled);
      background-color: var(--gray-a3);
      mix-blend-mode: var(--switch-disabled-blend-mode);
    }

.fui-SwitchRoot :where(.fui-SwitchButton):where([data-disabled])::before {
        filter: none;
        background-image: none;
        background-color: transparent;
        box-shadow: var(--shadow-1);
        opacity: 0.5;
      }

.fui-SwitchRoot :where(.fui-SwitchThumb):where([data-state='unchecked']) {
      box-shadow:
        0 1px 3px var(--black-a3),
        0 2px 4px -1px var(--black-a1),
        0 0 0 1px var(--black-a2);
    }

.fui-SwitchRoot :where(.fui-SwitchThumb):where([data-state='checked']) {
      box-shadow:
        0 1px 3px var(--black-a2),
        0 2px 4px -1px var(--black-a1),
        0 0 0 1px var(--black-a1),
        0 0 0 1px var(--accent-a4),
        -1px 0 1px var(--black-a2);
    }

.fui-SwitchRoot :where(.fui-SwitchThumb):where([data-state='checked']):where(.fui-high-contrast) {
        box-shadow:
          0 1px 3px var(--black-a2),
          0 2px 4px -1px var(--black-a1),
          0 0 0 1px var(--black-a2),
          -1px 0 1px var(--black-a2);
      }

.fui-SwitchRoot :where(.fui-SwitchThumb):where([data-disabled]) {
      background-color: var(--gray-2);
      box-shadow:
        0 0 0 1px var(--gray-a2),
        0 1px 3px var(--black-a1);
      transition: none;
    }

.fui-BaseTabsList::-webkit-scrollbar {
    display: none;
  }

.fui-BaseTabsTrigger {
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  flex-shrink: 0;
  position: relative;
  -webkit-user-select: none;
          user-select: none;
  padding-left: var(--tabs-trigger-padding-x);
  padding-right: var(--tabs-trigger-padding-x);
  color: var(--gray-a11);
}

.fui-BaseTabsTriggerInner,
.fui-BaseTabsTriggerInnerHidden {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--tabs-trigger-inner-padding-y) var(--tabs-trigger-inner-padding-x);
  border-radius: var(--tabs-trigger-inner-border-radius);
}

.fui-BaseTabsTriggerInner {
  position: absolute;
}

:where(.fui-BaseTabsTrigger[data-state='inactive'], .fui-TabsNavLink:not([data-active])) .fui-BaseTabsTriggerInner {
    letter-spacing: var(--tabs-trigger-inactive-letter-spacing);
    word-spacing: var(--tabs-trigger-inactive-word-spacing);
  }

:where(.fui-BaseTabsTrigger[data-state='active'], .fui-TabsNavLink[data-active]) .fui-BaseTabsTriggerInner {
    font-weight: var(--font-weight-medium);
    letter-spacing: var(--tabs-trigger-active-letter-spacing);
    word-spacing: var(--tabs-trigger-active-word-spacing);
  }

.fui-BaseTabsTriggerInnerHidden {
  visibility: hidden;
  font-weight: var(--font-weight-medium);
  letter-spacing: var(--tabs-trigger-active-letter-spacing);
  word-spacing: var(--tabs-trigger-active-word-spacing);
}

.fui-BaseTabsContent {
  position: relative;
  outline: 0;
}

.fui-BaseTabsList:where(.fui-r-size-1) {
    height: 36px;
    font-size: var(--font-size-1);
    line-height: var(--line-height-1);
    letter-spacing: var(--letter-spacing-1);
    --tabs-trigger-padding-x: var(--space-1);
    --tabs-trigger-inner-padding-x: calc(1.5 * var(--space-1));
    --tabs-trigger-inner-padding-y: var(--space-1);
    --tabs-trigger-inner-border-radius: var(--radius-2);
  }

.fui-BaseTabsList:where(.fui-r-size-2) {
    height: var(--space-7);
    font-size: var(--font-size-2);
    line-height: var(--line-height-2);
    letter-spacing: var(--letter-spacing-2);
    --tabs-trigger-padding-x: var(--space-1);
    --tabs-trigger-inner-padding-x: calc(1.25 * var(--space-2));
    --tabs-trigger-inner-padding-y: var(--space-1);
    --tabs-trigger-inner-border-radius: var(--radius-3);
  }

.fui-BaseTabsList {
  box-shadow: inset 0 -1px 0 0 var(--gray-a5);
  display: flex;
  overflow-x: auto;
  white-space: nowrap;

  scrollbar-width: none;
}

@media (hover: hover) {
    .fui-BaseTabsTrigger:where(:hover) {
      color: var(--gray-12);
    }
    .fui-BaseTabsTrigger:where(:hover) :where(.fui-BaseTabsTriggerInner) {
      background-color: var(--gray-a3);
    }
    .fui-BaseTabsTrigger:where(:focus-visible:hover) :where(.fui-BaseTabsTriggerInner) {
      background-color: var(--accent-a3);
    }
  }

.fui-BaseTabsTrigger:where([data-state='active'], [data-active]) {
    color: var(--gray-12);
  }

.fui-BaseTabsTrigger:where(:focus-visible) :where(.fui-BaseTabsTriggerInner) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -2px;
  }

.fui-BaseTabsTrigger:where([data-state='active'], [data-active])::before {
    box-sizing: border-box;
    content: '';
    height: 2px;
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: var(--accent-10);
  }

.fui-BaseTabsContent:where(:focus-visible) {
  outline: 2px solid var(--color-focus-root);
}

.fui-TabsContent {
  position: relative;
  outline: 0;
}

.fui-TabsContent:where(:focus-visible) {
  outline: 2px solid var(--color-focus-root);
}

.fui-TabsNavItem {
  display: flex;
}

.fui-TableTable {
  --table-row-background-color: transparent;

  width: 100%;
  text-align: left;
  vertical-align: middle;
  border-collapse: collapse;

  border-spacing: 0;
  box-sizing: border-box;
  height: 0;
}

.fui-TableHeader {
  vertical-align: inherit;
}

.fui-TableBody {
  vertical-align: inherit;
}

.fui-TableRow {
  vertical-align: inherit;
  color: var(--gray-12);
}

.fui-TableCell {
  background-color: var(--table-row-background-color);
  box-sizing: border-box;
  vertical-align: inherit;
  padding: var(--table-cell-padding);
  height: var(--table-cell-min-height);
  color: var(--gray-11);
  box-shadow: inset 0 -1px var(--data-table-border-color);
}

.fui-TableCell:where(.fui-TableHeader .fui-TableCell),
  .fui-TableCell:where(.fui-TableFooter .fui-TableCell) {
    padding: var(--table-column-header-cell-padding);
  }

.fui-TableCell:where(.fui-TableRoot:not(:has(.fui-TableBottomBar)) .fui-TableTable > :is(:last-child) .fui-TableRow:last-child .fui-TableCell) {
    box-shadow: none;
  }

.fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)) {
    padding-right: 0;
  }

.fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)):has(.fui-CheckboxRoot.fui-r-size-1) {
      width: var(--space-4);
      min-width: 30px;
    }

.fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)):has(.fui-CheckboxRoot.fui-r-size-2) {
      width: calc(var(--space-4) * 1.25);
      min-width: 30px;
    }

.fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)):has(.fui-CheckboxRoot.fui-r-size-3) {
      width: var(--space-5);
      min-width: 30px;
    }

.fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot)) .fui-CheckboxRoot {
    display: flex;
  }

.fui-Inset :where(.fui-TableCell:first-child) {
    padding-left: var(--inset-padding, var(--table-cell-padding));
  }

.fui-Inset :where(.fui-TableCell:last-child) {
    padding-right: var(--inset-padding, var(--table-cell-padding));
  }

.fui-TableColumnHeaderCell {
  font-weight: bold;
  color: var(--gray-12);
}

.fui-TableRowHeaderCell {
  font-weight: normal;
  color: var(--gray-12);
}

.fui-TableRowHeaderCell:where(.fui-TableFooter .fui-TableRowHeaderCell) {
    font-weight: bold;
  }

.fui-TableBottomBar {
  padding: var(--table-bottom-padding);
}

.fui-TableColumnHeaderCellButton:is(.fui-TableColumnHeaderCell .fui-TableColumnHeaderCellButton) {
  font-size: inherit;
  line-height: inherit;
  font-weight: inherit;
  color: inherit;
  letter-spacing: inherit;
  margin-left: calc(-1 * var(--space-2));
  margin-right: calc(-1 * var(--space-2));
  padding-left: var(--space-2);
  padding-right: var(--space-2);
}

.fui-TableRoot-vars:where(.fui-r-size-1) {
    --table-border-radius: var(--radius-3);
    --table-column-header-cell-padding: var(--space-2) var(--space-3);
    --table-cell-padding: var(--space-2) var(--space-3);
    --table-cell-min-height: 36px;
    --table-bottom-padding: var(--space-3) var(--space-3);
    font-size: var(--font-size-2);
    line-height: var(--line-height-2);
  }

.fui-TableRoot-vars:where(.fui-r-size-2) {
    --table-border-radius: var(--radius-4);
    --table-column-header-cell-padding: var(--space-2) var(--space-4);
    --table-cell-padding: var(--space-3) var(--space-4);
    --table-cell-min-height: 44px;
    --table-bottom-padding: var(--space-3) var(--space-4);
    font-size: var(--font-size-3);
    line-height: var(--line-height-3);
  }

.fui-TableRoot-vars:where(.fui-Inset > .fui-TableRoot-vars) {
    --table-border-radius: 0px;
  }

.fui-TableRoot:where(.fui-variant-surface) {
  border: 1px solid var(--gray-a5);
  border-radius: var(--table-border-radius);
  overflow: hidden;
  position: relative;
  border-color: var(--data-table-border-color);
}

.fui-TableRoot:where(.fui-variant-surface) :where(.fui-TableTable) {
    overflow: hidden;
  }

.fui-TableRoot:where(.fui-variant-surface) :where(.fui-TableTable) :where(.fui-TableHeader),
    .fui-TableRoot:where(.fui-variant-surface) :where(.fui-TableTable) :where(.fui-TableFooter) {
      --table-row-background-color: var(--gray-a2);
    }

.fui-TableRoot:where(.fui-variant-ghost) {
  --scrollarea-scrollbar-horizontal-margin-left: 0;
  --scrollarea-scrollbar-horizontal-margin-right: 0;
}

.fui-TableRoot:where(.fui-variant-ghost) :where(.fui-TableTable) {
    overflow: hidden;
  }

.fui-TextAreaRoot {
  display: flex;
  flex-direction: column;
  box-sizing: border-box;
  position: relative;
  z-index: 0;
}

.fui-TextAreaInput {
  -webkit-appearance: none;
          appearance: none;
  padding: 0;
  border-radius: inherit;
  background-color: transparent;
  font-family: inherit;
  -webkit-tap-highlight-color: transparent;
  resize: none;

  box-sizing: border-box;
  position: relative;
  display: block;
  width: 100%;
  flex-grow: 1;
  z-index: 1;
  border: var(--text-area-border-width) solid transparent;
  padding: var(--text-area-padding-y) var(--text-area-padding-x);
    cursor: auto;
    scrollbar-width: thin;
}

.fui-TextAreaInput:where(:focus) {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -1px;
  }

.fui-TextAreaInput::-webkit-scrollbar {
    width: var(--space-3);
    height: var(--space-3);
  }

.fui-TextAreaInput::-webkit-scrollbar-track,
  .fui-TextAreaInput::-webkit-scrollbar-thumb {
    background-clip: content-box;
    border: var(--space-1) solid transparent;
    border-radius: var(--space-3);
  }

.fui-TextAreaInput::-webkit-scrollbar-track {
    background-color: var(--gray-a3);
  }

.fui-TextAreaInput::-webkit-scrollbar-thumb {
    background-color: var(--gray-a8);
  }

@media (hover: hover) {
    :where(.fui-TextAreaInput:not(:disabled))::-webkit-scrollbar-thumb:hover {
      background-color: var(--gray-a9);
    }
  }

.fui-TextAreaChrome {
  position: absolute;
  border-radius: inherit;
  z-index: 0;
  inset: 0;
}

.fui-TextAreaRoot:where(.fui-r-size-1) {
    min-height: var(--space-8);
    border-radius: 6px;
  }

.fui-TextAreaRoot:where(.fui-r-size-1) :where(.fui-TextAreaInput) {
      --text-area-padding-y: calc(var(--space-1) - var(--text-area-border-width));
      --text-area-padding-x: calc(var(--space-1) * 1.5 - var(--text-area-border-width));
      font-size: var(--font-size-1);
      line-height: var(--line-height-1);
      letter-spacing: var(--letter-spacing-1);
    }

.fui-TextAreaRoot:where(.fui-r-size-2) {
    min-height: var(--space-9);
    border-radius: 8px;
  }

.fui-TextAreaRoot:where(.fui-r-size-2) :where(.fui-TextAreaInput) {
      --text-area-padding-y: calc(var(--space-1) * 1.5 - var(--text-area-border-width));
      --text-area-padding-x: calc(var(--space-2) - var(--text-area-border-width));
      font-size: var(--font-size-2);
      line-height: var(--line-height-2);
      letter-spacing: var(--letter-spacing-2);
    }

.fui-TextAreaRoot:where(.fui-r-size-3) {
    min-height: 80px;
    border-radius: 10px;
  }

.fui-TextAreaRoot:where(.fui-r-size-3) :where(.fui-TextAreaInput) {
      --text-area-padding-y: calc(var(--space-2) - var(--text-area-border-width));
      --text-area-padding-x: calc(var(--space-3) - var(--text-area-border-width));
      font-size: var(--font-size-3);
      line-height: var(--line-height-3);
      letter-spacing: var(--letter-spacing-3);
    }

.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput) {
    --text-area-border-width: 1px;
    color: var(--gray-12);
  }

:is(.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput)) + :where(.fui-TextAreaChrome) {
      box-shadow:
        inset 0 0 0 1px var(--gray-a5),
        0px 1px 2px 0px rgba(0, 0, 0, 0.05);
      background-color: var(--color-surface);
      padding: 1px;
      background-clip: content-box;
    }

.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput)::placeholder {
      color: var(--gray-a10);
      opacity: 1;
    }

.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled]) {
      -webkit-background-clip: text;
              background-clip: text;
      -webkit-text-fill-color: var(--gray-12);
    }

:is(.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled])) + :where(.fui-TextAreaChrome) {
        background-color: var(--accent-a4);
      }

:is(.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput):where(:disabled, :read-only)) + :where(.fui-TextAreaChrome) {
        background-image: linear-gradient(var(--gray-a3), var(--gray-a3));
      }

.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput) {
    --text-area-border-width: 0px;
    color: var(--accent-12);
  }

:is(.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput)) + :where(.fui-TextAreaChrome) {
      background-color: var(--accent-a3);
    }

.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput)::selection {
      background-color: var(--accent-a5);
    }

.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput)::placeholder {
      color: var(--accent-12);
      opacity: 0.65;
    }

.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled]) {
      -webkit-background-clip: text;
              background-clip: text;
      -webkit-text-fill-color: var(--accent-12);
    }

:is(.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled])) + :where(.fui-TextAreaChrome) {
        background-color: var(--accent-a5);
      }

.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:focus) {
      outline-color: var(--accent-8);
    }

:is(.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:disabled, :read-only)) + :where(.fui-TextAreaChrome) {
        background-color: var(--gray-a4);
      }

.fui-TextAreaInput:where(:disabled, :read-only) {
    cursor: text;
    color: var(--gray-a11);
    -webkit-text-fill-color: var(--gray-a11);
  }

.fui-TextAreaInput:where(:disabled, :read-only):where(:focus) {
      outline: 2px solid var(--gray-8);
    }

.fui-TextAreaInput:where(:disabled, :read-only)::placeholder {
      opacity: 0.5;
    }

.fui-TextAreaInput:where(:disabled, :read-only):where(:placeholder-shown) {
      cursor: default;
    }

.fui-TextAreaInput:where(:disabled, :read-only)::selection {
      background-color: var(--gray-a5);
    }

.fui-TextFieldRoot {
  display: flex;
  box-sizing: border-box;
  position: relative;
  z-index: 0;
  cursor: text;
}

.fui-TextFieldInput {
  display: block;
  box-sizing: border-box;
  padding: 0;
  width: 100%;
  -webkit-appearance: none;
          appearance: none;
  -webkit-tap-highlight-color: transparent;
  outline: none;
  font-family: inherit;
  background-color: transparent;
  position: relative;
  z-index: 1;
  border: var(--text-field-border-width) solid transparent;
}

.fui-TextFieldChrome {
  position: absolute;
  inset: 0;
  z-index: 0;
  pointer-events: none;
}

:where(.fui-TextFieldInput:focus) + .fui-TextFieldChrome {
    outline: 2px solid var(--color-focus-root);
    outline-offset: -1px;
  }

.fui-TextFieldSlot {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  position: relative;
  z-index: 1;
  color: var(--gray-a11);
}

.fui-TextFieldSlot:where([data-accent-color]) {
    color: var(--accent-a11);
  }

.fui-TextFieldSlot:where(:empty) {
    display: none;
  }

.fui-TextFieldSlot:where(.fui-r-size-1) {
    gap: var(--space-2);
    padding-left: var(--space-1);
    padding-right: var(--space-1);
  }

.fui-TextFieldSlot:where(.fui-r-size-2) {
    gap: var(--space-2);
    padding-left: var(--space-2);
    padding-right: var(--space-2);
  }

.fui-TextFieldSlot:where(.fui-r-size-3) {
    gap: var(--space-3);
    padding-left: var(--space-3);
    padding-right: var(--space-3);
  }

.fui-TextFieldInput:where(.fui-r-size-1) {
    height: var(--space-5);
    font-size: var(--font-size-1);
    letter-spacing: var(--letter-spacing-1);
    padding-top: 0.5px;
    padding-bottom: 1px;
  }

.fui-TextFieldInput:where(.fui-r-size-1):where(:first-child) {
      text-indent: calc(var(--space-1) * 1.5 - var(--text-field-border-width));
      border-radius: max(6px, var(--radius-full));
    }

.fui-TextFieldInput:where(.fui-r-size-1) + :where(.fui-TextFieldChrome) {
      border-radius: max(6px, var(--radius-full));
    }

.fui-TextFieldInput:where(.fui-r-size-2) {
    height: var(--space-6);
    font-size: var(--font-size-2);
    letter-spacing: var(--letter-spacing-2);
    padding-top: 0px;
    padding-bottom: 1px;
  }

.fui-TextFieldInput:where(.fui-r-size-2):where(:first-child) {
      text-indent: calc(var(--space-2) - var(--text-field-border-width));
      border-radius: max(8px, var(--radius-full));
    }

.fui-TextFieldInput:where(.fui-r-size-2) + :where(.fui-TextFieldChrome) {
      border-radius: max(8px, var(--radius-full));
    }

.fui-TextFieldInput:where(.fui-r-size-3) {
    height: var(--space-7);
    font-size: var(--font-size-3);
    letter-spacing: var(--letter-spacing-3);
    padding-top: 0.5px;
    padding-bottom: 1px;
  }

.fui-TextFieldInput:where(.fui-r-size-3):where(:first-child) {
      text-indent: calc(var(--space-3) - var(--text-field-border-width));
      border-radius: max(10px, var(--radius-full));
    }

.fui-TextFieldInput:where(.fui-r-size-3) + :where(.fui-TextFieldChrome) {
      border-radius: max(10px, var(--radius-full));
    }

.fui-TextFieldInput:where(:has(~ .fui-TextFieldSlot)) {
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
}

.fui-TextFieldInput:where(.fui-variant-surface) {
  --text-field-border-width: 1px;
  color: var(--gray-12);
}

.fui-TextFieldInput:where(.fui-variant-surface) + :where(.fui-TextFieldChrome) {
    background-color: var(--color-surface);
    box-shadow:
      inset 0 0 0 1px var(--gray-a5),
      0px 1px 2px 0px rgba(0, 0, 0, 0.05);
    padding: 1px;
    background-clip: content-box;
  }

.fui-TextFieldInput:where(.fui-variant-surface)::placeholder {
    color: var(--gray-a10);
    opacity: 1;
  }

.fui-TextFieldInput:where(.fui-variant-surface):where(:autofill, [data-com-onepassword-filled]) {
    -webkit-background-clip: text;
            background-clip: text;
    -webkit-text-fill-color: var(--gray-12);
  }

.fui-TextFieldInput:where(.fui-variant-surface):where(:autofill, [data-com-onepassword-filled]) + :where(.fui-TextFieldChrome) {
      background-color: var(--accent-a3);
      box-shadow:
        inset 0 0 0 1px var(--gray-a7),
        inset 0 0 0 1px var(--accent-a4);
    }

.fui-TextFieldInput:where(.fui-variant-surface):where(:disabled, :read-only) + :where(.fui-TextFieldChrome) {
      background-image: linear-gradient(var(--gray-a3), var(--gray-a3));
    }

.fui-TextFieldInput:where(.fui-variant-soft) {
  --text-field-border-width: 0px;
  color: var(--accent-12);
}

.fui-TextFieldInput:where(.fui-variant-soft) + :where(.fui-TextFieldChrome) {
    background-color: var(--accent-a3);
  }

.fui-TextFieldInput:where(.fui-variant-soft)::placeholder {
    color: var(--accent-12);
    opacity: 0.6;
  }

.fui-TextFieldInput:where(.fui-variant-soft):where(:autofill, [data-com-onepassword-filled]) {
    -webkit-background-clip: text;
            background-clip: text;
    -webkit-text-fill-color: var(--accent-12);
  }

.fui-TextFieldInput:where(.fui-variant-soft):where(:autofill, [data-com-onepassword-filled]) + :where(.fui-TextFieldChrome) {
      background-color: var(--accent-a4);
    }

.fui-TextFieldInput:where(.fui-variant-soft):where(:focus) + :where(.fui-TextFieldChrome) {
      outline-color: var(--accent-8);
    }

.fui-TextFieldInput:where(.fui-variant-soft):where(:disabled, :read-only) + :where(.fui-TextFieldChrome) {
      background-color: var(--gray-a4);
    }

.fui-TextFieldInput:where(.fui-variant-soft)::selection {
    background-color: var(--accent-a5);
  }

.fui-TextFieldInput:where(:disabled, :read-only) {
    cursor: text;
    color: var(--gray-a11);
    -webkit-text-fill-color: var(--gray-a11);
  }

.fui-TextFieldInput:where(:disabled, :read-only):where(:focus) + :where(.fui-TextFieldChrome) {
      outline: 2px solid var(--gray-8);
    }

.fui-TextFieldInput:where(:disabled, :read-only)::placeholder {
      opacity: 0.5;
    }

.fui-TextFieldInput:where(:disabled, :read-only):where(:placeholder-shown) {
      cursor: default;
    }

.fui-TextFieldInput:where(:disabled, :read-only)::selection {
      background-color: var(--gray-a5);
    }

.fui-TextFieldRoot:where(:has(.fui-TextFieldInput:where(:disabled, :read-only))) {
      cursor: text;
    }

.fui-TextFieldRoot:where(:has(.fui-TextFieldInput:where(:disabled, :read-only):placeholder-shown)) {
      cursor: default;
    }

.fui-OTPFieldRoot {
  display: flex;
  align-items: center;
  gap: 8px;
}

.fui-OTPFieldGroup {
  display: flex;
  align-items: center;
  --otp-focus-color: var(--color-focus-root);
}

.fui-OTPFieldSlot {
  position: relative;

  display: flex;
  align-items: center;
  justify-content: center;

  height: 40px;
  width: 40px;

  border-top-width: 1px;
  border-bottom-width: 1px;
  border-right-width: 1px;

  font-size: 0.875rem;
  transition-property: outline;
  transition-duration: 0.2s;
  transition-timing-function: ease;

  box-shadow: var(--shadow-1);

  background-color: var(--color-surface);
  border-color: transparent;
  font-variant-numeric: tabular-nums slashed-zero;
}

.fui-OTPFieldSlot:where(:first-child) {
    border-left-width: 1px;
    border-top-left-radius: 10px;
    border-bottom-left-radius: 10px;
  }

.fui-OTPFieldSlot:where(:last-child) {
    border-top-right-radius: 10px;
    border-bottom-right-radius: 10px;
  }

.fui-OTPFieldSlot:where([data-otp-active='true']) {
    outline: 2px solid var(--otp-focus-color);
    z-index: 1;
  }

.fui-OTPFieldCaret {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.fui-OTPFieldCaret::before {
  content: '';
  height: 16px;
  width: 1px;
  border-radius: 1px;
  background-color: var(--gray-a10);
  animation: fui-otp-caret-blink 1.25s ease-out infinite;
}

@keyframes fui-otp-caret-blink {
  0%,
  70%,
  100% {
    opacity: 1;
  }
  20%,
  50% {
    opacity: 0;
  }
}

.fui-OTPFieldSeparator {
  position: relative;
  height: 100%;
  display: flex;
  align-items: center;
}

.fui-OTPFieldSeparator::before {
  content: '';
  width: 10px;
  height: 4px;
  border-radius: 2px;
  box-shadow: var(--shadow-1);
}

.fui-DateFieldRoot {
  position: relative;
  display: inline-block;
}

.fui-DateFieldInput {
  height: var(--height);
  font-size: var(--font-size);
  line-height: var(--font-size);
  padding: var(--padding);

  display: inline-flex;
  flex-wrap: nowrap;
  gap: 2px;

  border-radius: var(--radius);

  background-color: var(--gray-a3);
  color: var(--gray-12);
  caret-color: transparent;
  overflow: hidden;
}

.fui-DateFieldInput:where([data-invalid]) {
  background-color: var(--danger-a3);
  color: var(--danger-12);
}

.fui-DateFieldInput:where([data-focus-visible]) {
  outline: 2px solid var(--color-focus-root);
  outline-offset: 1px;
}

.fui-DateFieldInput:where([data-invalid][data-focus-visible]) {
  outline: 2px solid var(--danger-8);
  outline-offset: 1px;
}

.fui-DateFieldSegment:where(:not([data-type='literal'])) {
  box-sizing: content-box;
  border-radius: max(calc(var(--radius) / 2), 4px);
  padding: 1px 2px 0px;
  color: var(--gray-12);
  display: flex;
  align-items: center;
  justify-content: flex-end;
  text-align: right;

  font-variant-numeric: tabular-nums;
  font-family: var(--code-font-family);
  outline: none;
}

.fui-DateFieldSegment:where([data-hovered]) {
  background: var(--gray-a4);
}

.fui-DateFieldSegment:where([data-invalid][data-hovered]) {
  background: var(--danger-a4);
}

.fui-DateFieldSegment:where([data-focused]) {
  background: var(--accent-a9);
  color: var(--accent-9-contrast);
}

.fui-DateFieldSegment:where([data-invalid][data-focused]) {
  background: var(--danger-a9);
  color: var(--danger-9-contrast);
}

.fui-DateFieldSegment[data-type='literal'] {
  color: var(--gray-a10);
  display: flex;
  align-items: center;
}

.fui-DateFieldRoot:where(.fui-r-size-1) {
    --height: var(--space-5);
    --font-size: var(--font-size-1);
    --letter-spacing: var(--letter-spacing-1);
    --padding: 4px;
    --radius: 6px;
  }

.fui-DateFieldRoot:where(.fui-r-size-2) {
    --height: var(--space-6);
    --font-size: var(--font-size-2);
    --letter-spacing: var(--letter-spacing-2);
    --padding: 6px;
    --radius: 8px;
  }

.fui-DateFieldRoot:where(.fui-r-size-3) {
    --height: var(--space-7);
    --font-size: var(--font-size-3);
    --letter-spacing: var(--letter-spacing-3);
    --padding: 8px;
    --radius: 10px;
  }

.fui-TooltipContent {
  padding: var(--space-1) var(--space-2);
  background-color: var(--color-panel-translucent);
  -webkit-backdrop-filter: var(--backdrop-filter-panel);
  backdrop-filter: var(--backdrop-filter-panel);

  border-radius: var(--radius-4);
  border: 1px solid var(--gray-a6);
  outline: 0.5px solid var(--color-tooltip-outline);

  box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.05);

  transform-origin: var(--radix-tooltip-content-transform-origin);

  animation-duration: 200ms;
  animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);
}

@media (prefers-reduced-motion: no-preference) {
      .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='top']) {
        animation-name: fui-slide-up, fui-fade-in;
      }
      .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='bottom']) {
        animation-name: fui-slide-down, fui-fade-in;
      }
      .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='left']) {
        animation-name: fui-slide-left, fui-fade-in;
      }
      .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='right']) {
        animation-name: fui-slide-right, fui-fade-in;
      }
  }

.fui-TooltipText {
  color: var(--gray-12);
  -webkit-user-select: none;
          user-select: none;
  cursor: default;
}

.fui-TooltipArrow {
  fill: transparent;
}

.fui-WidgetStackStack {
  --widget-stack-radius: 24px;
  --widget-stack-gutter: 8px;
  --widget-stack-background: var(--gray-a3);

  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  isolation: isolate;

  -webkit-user-select: none;

          user-select: none;
}

.fui-WidgetStackScrollArea {
  position: absolute;
  inset: calc(-1 * var(--widget-stack-gutter));

  contain: strict;
  will-change: scroll-position;
  scrollbar-width: none;
  background: var(--widget-stack-background);
  -webkit-backdrop-filter: blur(16px);
          backdrop-filter: blur(16px);

  transition: 0.2s clip-path ease-out;
  clip-path: inset(
    var(--widget-stack-gutter) var(--widget-stack-gutter) var(--widget-stack-gutter) var(--widget-stack-gutter) round
      var(--widget-stack-radius)
  );
  display: flex;
}

.fui-WidgetStackScrollArea:where(:not(:has(.fui-WidgetStackItem:first-child:last-child)):hover) {
    clip-path: inset(0 0 0 0 round calc(var(--widget-stack-radius) + var(--widget-stack-gutter)));
  }

.fui-WidgetStackScrollArea:where([data-orientation='vertical']) {
    flex-direction: column;
    scroll-snap-type: y mandatory;
    overflow-y: auto;
  }

.fui-WidgetStackScrollArea:where([data-orientation='horizontal']) {
    flex-direction: row;
    scroll-snap-type: x mandatory;
    overflow-x: auto;
  }

.fui-WidgetStackScrollArea::-webkit-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
  display: none;
}

.fui-WidgetStackItem {
  width: 100%;
  height: 100%;
  flex-shrink: 0;
  scroll-snap-align: center;

  border: var(--widget-stack-gutter) solid transparent;
  contain: strict;
}

.fui-WidgetStackItemContent {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  border-radius: var(--widget-stack-radius);
  overflow: hidden;
  transform: translateZ(0) scale(calc(1 + (1 - var(--intersection-ratio)) * -0.3));
}

.fui-r-weight-light {
  font-weight: var(--font-weight-light);
}

.fui-r-weight-regular {
  font-weight: var(--font-weight-regular);
}

.fui-r-weight-medium {
  font-weight: var(--font-weight-medium);
}

.fui-r-weight-semi-bold {
  font-weight: var(--font-weight-semi-bold);
}

.fui-r-weight-bold {
  font-weight: var(--font-weight-bold);
}

.fui-r-lt-normal::before,
.fui-r-lt-end::before,
.fui-r-lt-normal::after,
.fui-r-lt-start::after {
  content: none;
}

.fui-r-lt-start::before,
.fui-r-lt-both::before,
.fui-r-lt-end::after,
.fui-r-lt-both::after {
  content: '';
  display: table;
}

.fui-r-lt-start::before,
.fui-r-lt-both::before {
  margin-bottom: calc(
    var(--leading-trim-start, var(--default-leading-trim-start)) -
      var(--line-height, calc(1em * var(--default-line-height))) / 2
  );
}

.fui-r-lt-end::after,
.fui-r-lt-both::after {
  margin-top: calc(
    var(--leading-trim-end, var(--default-leading-trim-end)) -
      var(--line-height, calc(1em * var(--default-line-height))) / 2
  );
}

.fui-r-ta-left {
  text-align: left;
}

.fui-r-ta-center {
  text-align: center;
}

.fui-r-ta-right {
  text-align: right;
}

.fui-r-va-baseline {
  vertical-align: baseline;
}

.fui-r-va-top {
  vertical-align: top;
}

.fui-r-va-middle {
  vertical-align: middle;
}

.fui-r-va-bottom {
  vertical-align: bottom;
}

.fui-ThemePanelShortcut:where(:focus-visible) {
    outline-style: solid;
    outline-width: 2px;
    outline-offset: 2px;
    outline-color: var(--accent-9);
  }

.fui-ThemePanelSwatch,
.fui-ThemePanelRadioCard {
  position: relative;
}

.fui-ThemePanelSwatchInput,
.fui-ThemePanelRadioCardInput {
  -webkit-appearance: none;
          appearance: none;
  margin: 0;
  outline: none;
  outline-width: 2px;
  position: absolute;
  inset: 0;
  border-radius: inherit;
}

.fui-ThemePanelSwatch {
  width: var(--space-5);
  height: var(--space-5);
  border-radius: 100%;
}

.fui-ThemePanelSwatchInput {
  outline-offset: 2px;
}

.fui-ThemePanelSwatchInput:where(:checked) {
    outline-style: solid;
    outline-color: var(--gray-12);
  }

.fui-ThemePanelSwatchInput:where(:focus-visible) {
    outline-style: solid;
    outline-color: var(--accent-9);
  }

.fui-ThemePanelRadioCard {
  border-radius: var(--radius-1);
  box-shadow: 0 0 0 1px var(--gray-7);
}

.fui-ThemePanelRadioCardInput {
  outline-offset: -1px;
}

.fui-ThemePanelRadioCardInput:where(:checked) {
    outline-style: solid;
    outline-color: var(--gray-12);
  }

.fui-ThemePanelRadioCardInput:where(:focus-visible) {
    background-color: var(--accent-a3);
    outline-style: solid;
    outline-color: var(--accent-9);
  }

.fui-Shine {
  position: relative;
  -webkit-user-select: none;
          user-select: none;
}

.fui-ShineSvgFilter {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
}
