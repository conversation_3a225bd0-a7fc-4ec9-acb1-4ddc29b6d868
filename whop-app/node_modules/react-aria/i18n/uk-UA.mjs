export default{"@react-aria/autocomplete":{collectionLabel:`Пропозиції`},"@react-aria/breadcrumbs":{breadcrumbs:`Навігаційна стежка`},"@react-aria/calendar":{dateRange:e=>`${e.startDate} — ${e.endDate}`,dateSelected:e=>`Вибрано ${e.date}`,finishRangeSelectionPrompt:`Натисніть, щоб завершити вибір діапазону дат`,maximumDate:`Остання доступна дата`,minimumDate:`Перша доступна дата`,next:`Наступний`,previous:`Попередній`,selectedDateDescription:e=>`Вибрана дата: ${e.date}`,selectedRangeDescription:e=>`Вибраний діапазон: ${e.dateRange}`,startRangeSelectionPrompt:`Натисніть, щоб почати вибір діапазону дат`,todayDate:e=>`Сьогодні, ${e.date}`,todayDateSelected:e=>`Сьогодні, вибрано ${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`Палітра кольорів`,colorSwatch:`зразок кольору`,transparent:`прозорий`,twoDimensionalSlider:`Повзунок 2D`},"@react-aria/combobox":{buttonLabel:`Показати пропозиції`,countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} параметр`,other:()=>`${t.number(e.optionCount)} параметри(-ів)`})} доступно.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Введена група ${e.groupTitle}, з ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} параметр`,other:()=>`${t.number(e.groupCount)} параметри(-ів)`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:`, вибрано`,other:""},e.isSelected)}`,listboxLabel:`Пропозиції`,selectedAnnouncement:e=>`${e.optionText}, вибрано`},"@react-aria/datepicker":{calendar:`Календар`,day:`день`,dayPeriod:`дп/пп`,endDate:`Дата завершення`,era:`ера`,hour:`година`,minute:`хвилина`,month:`місяць`,second:`секунда`,selectedDateDescription:e=>`Вибрана дата: ${e.date}`,selectedRangeDescription:e=>`Вибраний діапазон: ${e.startDate} — ${e.endDate}`,selectedTimeDescription:e=>`Вибраний час: ${e.time}`,startDate:`Дата початку`,timeZoneName:`часовий пояс`,weekday:`день тижня`,year:`рік`},"@react-aria/dnd":{dragDescriptionKeyboard:`Натисніть Enter, щоб почати перетягування.`,dragDescriptionKeyboardAlt:`Натисніть Alt + Enter, щоб почати перетягування.`,dragDescriptionLongPress:`Натисніть і утримуйте, щоб почати перетягування.`,dragDescriptionTouch:`Натисніть двічі, щоб почати перетягування.`,dragDescriptionVirtual:`Натисніть, щоб почати перетягування.`,dragItem:e=>`Перетягнути ${e.itemText}`,dragSelectedItems:(e,t)=>`Перетягніть ${t.plural(e.count,{one:()=>`${t.number(e.count)} вибраний елемент`,other:()=>`${t.number(e.count)} вибраних елем`})}`,dragSelectedKeyboard:(e,t)=>`Натисніть Enter, щоб перетягнути ${t.plural(e.count,{one:()=>`${t.number(e.count)} вибраний елемент`,other:()=>`${t.number(e.count)} вибраних елементи(-ів)`})}.`,dragSelectedKeyboardAlt:(e,t)=>`Натисніть Alt + Enter, щоб перетягнути ${t.plural(e.count,{one:()=>`${t.number(e.count)} вибраний елемент`,other:()=>`${t.number(e.count)} вибраних елементи(-ів)`})}.`,dragSelectedLongPress:(e,t)=>`Утримуйте, щоб перетягнути ${t.plural(e.count,{one:()=>`${t.number(e.count)} вибраний елемент`,other:()=>`${t.number(e.count)} вибраних елементи(-ів)`})}.`,dragStartedKeyboard:`Перетягування почалося. Натисніть Tab, щоб перейти до цілі перетягування, потім натисніть Enter, щоб перетягнути, або Escape, щоб скасувати.`,dragStartedTouch:`Перетягування почалося. Перейдіть до цілі перетягування, потім натисніть двічі, щоб перетягнути.`,dragStartedVirtual:`Перетягування почалося. Перейдіть до цілі перетягування, потім натисніть Enter, щоб перетягнути.`,dropCanceled:`Перетягування скасовано.`,dropComplete:`Перетягування завершено.`,dropDescriptionKeyboard:`Натисніть Enter, щоб перетягнути. Натисніть Escape, щоб скасувати перетягування.`,dropDescriptionTouch:`Натисніть двічі, щоб перетягнути.`,dropDescriptionVirtual:`Натисніть, щоб перетягнути.`,dropIndicator:`індикатор перетягування`,dropOnItem:e=>`Перетягнути на ${e.itemText}`,dropOnRoot:`Перетягнути на`,endDragKeyboard:`Триває перетягування. Натисніть Enter, щоб скасувати перетягування.`,endDragTouch:`Триває перетягування. Натисніть двічі, щоб скасувати перетягування.`,endDragVirtual:`Триває перетягування. Натисніть, щоб скасувати перетягування.`,insertAfter:e=>`Вставити після ${e.itemText}`,insertBefore:e=>`Вставити перед ${e.itemText}`,insertBetween:e=>`Вставити між ${e.beforeItemText} і ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} не вибрано.`,longPressToSelect:`Виконайте довге натиснення, щоб перейти в режим вибору.`,select:`Вибрати`,selectedAll:`Усі елементи вибрано.`,selectedCount:(e,t)=>`${t.plural(e.count,{"=0":`Жодних елементів не вибрано`,one:()=>`${t.number(e.count)} елемент вибрано`,other:()=>`Вибрано елементів: ${t.number(e.count)}`})}.`,selectedItem:e=>`${e.item} вибрано.`},"@react-aria/gridlist":{hasActionAnnouncement:`рядок містить дію`,hasLinkAnnouncement:e=>`рядок містить посилання: ${e.link}`},"@react-aria/menu":{longPressMessage:`Довго або звичайно натисніть комбінацію клавіш Alt і стрілка вниз, щоб відкрити меню`},"@react-aria/numberfield":{decrease:e=>`Зменшити ${e.fieldLabel}`,increase:e=>`Збільшити ${e.fieldLabel}`,numberField:`Поле номера`},"@react-aria/overlays":{dismiss:`Скасувати`},"@react-aria/searchfield":{"Clear search":`Очистити пошук`},"@react-aria/spinbutton":{Empty:`Пусто`},"@react-aria/steplist":{steplist:`Список кроків`},"@react-aria/table":{ascending:`висхідний`,ascendingSort:e=>`відсортовано за стовпцем ${e.columnName} у висхідному порядку`,columnSize:e=>`${e.value} пікс.`,descending:`низхідний`,descendingSort:e=>`відсортовано за стовпцем ${e.columnName} у низхідному порядку`,resizerDescription:`Натисніть Enter, щоб почати зміну розміру`,select:`Вибрати`,selectAll:`Вибрати все`,sortable:`сортувальний стовпець`},"@react-aria/tag":{removeButtonLabel:`Вилучити`,removeDescription:`Натисніть Delete, щоб вилучити тег.`},"@react-aria/toast":{close:`Закрити`,notifications:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)} сповіщення`,other:()=>`${t.number(e.count)} сповіщення`})}.`},"@react-aria/tree":{collapse:`Згорнути`,expand:`Розгорнути`},"@react-stately/color":{alpha:`Альфа`,black:`чорний`,blue:`Синій`,"blue purple":`синьо-фіолетовий`,brightness:`Яскравість`,brown:`коричневий`,"brown yellow":`коричнево-жовтий`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`блакитний`,"cyan blue":`синьо-блакитний`,dark:`темний`,gray:`сірий`,grayish:`сіруватий`,green:`Зелений`,"green cyan":`зелено-блакитний`,hue:`Тон`,light:`світлий`,lightness:`Освітленість`,magenta:`пурпуровий`,"magenta pink":`пурпурово-рожевий`,orange:`помаранчевий`,"orange yellow":`помаранчево-жовтий`,pale:`блідий`,pink:`рожевий`,"pink red":`рожево-червоний`,purple:`фіолетовий`,"purple magenta":`фіолетово-пурпуровий`,red:`Червоний`,"red orange":`червоно-помаранчевий`,saturation:`Насиченість`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, прозорий на ${e.percentTransparent}`,"very dark":`дуже темний`,"very light":`дуже світлий`,vibrant:`яскравий`,white:`білий`,yellow:`жовтий`,"yellow green":`жовто-зелений`},"@react-stately/datepicker":{rangeOverflow:e=>`Значення має бути не пізніше ${e.maxValue}.`,rangeReversed:`Дата початку має передувати даті завершення.`,rangeUnderflow:e=>`Значення має бути не раніше ${e.minValue}.`,unavailableDate:`Вибрана дата недоступна.`}};