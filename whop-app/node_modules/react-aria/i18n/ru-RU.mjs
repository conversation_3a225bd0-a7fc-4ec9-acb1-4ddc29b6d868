export default{"@react-aria/autocomplete":{collectionLabel:`Предложения`},"@react-aria/breadcrumbs":{breadcrumbs:`Навигация`},"@react-aria/calendar":{dateRange:e=>`С ${e.startDate} по ${e.endDate}`,dateSelected:e=>`Выбрано ${e.date}`,finishRangeSelectionPrompt:`Щелкните, чтобы завершить выбор диапазона дат`,maximumDate:`Последняя доступная дата`,minimumDate:`Первая доступная дата`,next:`Далее`,previous:`Назад`,selectedDateDescription:e=>`Выбранная дата: ${e.date}`,selectedRangeDescription:e=>`Выбранный диапазон: ${e.dateRange}`,startRangeSelectionPrompt:`Щелкните, чтобы начать выбор диапазона дат`,todayDate:e=>`Сегодня, ${e.date}`,todayDateSelected:e=>`Сегодня, выбрано ${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`Палитра цветов`,colorSwatch:`цветовой образец`,transparent:`прозрачный`,twoDimensionalSlider:`Ползунок 2D`},"@react-aria/combobox":{buttonLabel:`Показать предложения`,countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} параметр`,other:()=>`${t.number(e.optionCount)} параметров`})} доступно.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Введенная группа ${e.groupTitle}, с ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} параметром`,other:()=>`${t.number(e.groupCount)} параметрами`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:`, выбранными`,other:""},e.isSelected)}`,listboxLabel:`Предложения`,selectedAnnouncement:e=>`${e.optionText}, выбрано`},"@react-aria/datepicker":{calendar:`Календарь`,day:`день`,dayPeriod:"AM/PM",endDate:`Дата окончания`,era:`эра`,hour:`час`,minute:`минута`,month:`месяц`,second:`секунда`,selectedDateDescription:e=>`Выбранная дата: ${e.date}`,selectedRangeDescription:e=>`Выбранный диапазон: с ${e.startDate} по ${e.endDate}`,selectedTimeDescription:e=>`Выбранное время: ${e.time}`,startDate:`Дата начала`,timeZoneName:`часовой пояс`,weekday:`день недели`,year:`год`},"@react-aria/dnd":{dragDescriptionKeyboard:`Нажмите клавишу Enter для начала перетаскивания.`,dragDescriptionKeyboardAlt:`Нажмите Alt + Enter, чтобы начать перетаскивать.`,dragDescriptionLongPress:`Нажмите и удерживайте, чтобы начать перетаскивать.`,dragDescriptionTouch:`Дважды нажмите для начала перетаскивания.`,dragDescriptionVirtual:`Щелкните для начала перетаскивания.`,dragItem:e=>`Перетащить ${e.itemText}`,dragSelectedItems:(e,t)=>`Перетащить ${t.plural(e.count,{one:()=>`${t.number(e.count)} выбранный элемент`,other:()=>`${t.number(e.count)} выбранных элем`})}`,dragSelectedKeyboard:(e,t)=>`Нажмите Enter для перетаскивания ${t.plural(e.count,{one:()=>`${t.number(e.count)} выбранного элемента`,other:()=>`${t.number(e.count)} выбранных элементов`})}.`,dragSelectedKeyboardAlt:(e,t)=>`Нажмите Alt + Enter для перетаскивания ${t.plural(e.count,{one:()=>`${t.number(e.count)} выбранного элемента`,other:()=>`${t.number(e.count)} выбранных элементов`})}.`,dragSelectedLongPress:(e,t)=>`Нажмите и удерживайте для перетаскивания ${t.plural(e.count,{one:()=>`${t.number(e.count)} выбранного элемента`,other:()=>`${t.number(e.count)} выбранных элементов`})}.`,dragStartedKeyboard:`Начато перетаскивание. Нажмите клавишу Tab для выбора цели, затем нажмите клавишу Enter, чтобы применить перетаскивание, или клавишу Escape для отмены действия.`,dragStartedTouch:`Начато перетаскивание. Выберите цель, затем дважды нажмите, чтобы применить перетаскивание.`,dragStartedVirtual:`Начато перетаскивание. Нажмите клавишу Tab для выбора цели, затем нажмите клавишу Enter, чтобы применить перетаскивание.`,dropCanceled:`Перетаскивание отменено.`,dropComplete:`Перетаскивание завершено.`,dropDescriptionKeyboard:`Нажмите клавишу Enter, чтобы применить перетаскивание. Нажмите клавишу Escape для отмены.`,dropDescriptionTouch:`Дважды нажмите, чтобы применить перетаскивание.`,dropDescriptionVirtual:`Щелкните, чтобы применить перетаскивание.`,dropIndicator:`индикатор перетаскивания`,dropOnItem:e=>`Перетащить на ${e.itemText}`,dropOnRoot:`Перетащить на`,endDragKeyboard:`Перетаскивание. Нажмите клавишу Enter для отмены.`,endDragTouch:`Перетаскивание. Дважды нажмите для отмены.`,endDragVirtual:`Перетаскивание. Щелкните для отмены.`,insertAfter:e=>`Вставить после ${e.itemText}`,insertBefore:e=>`Вставить перед ${e.itemText}`,insertBetween:e=>`Вставить между ${e.beforeItemText} и ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} не выбрано.`,longPressToSelect:`Нажмите и удерживайте для входа в режим выбора.`,select:`Выбрать`,selectedAll:`Выбраны все элементы.`,selectedCount:(e,t)=>`${t.plural(e.count,{"=0":`Нет выбранных элементов`,one:()=>`${t.number(e.count)} элемент выбран`,other:()=>`${t.number(e.count)} элементов выбрано`})}.`,selectedItem:e=>`${e.item} выбрано.`},"@react-aria/gridlist":{hasActionAnnouncement:`в строке есть действие`,hasLinkAnnouncement:e=>`в строке есть ссылка: ${e.link}`},"@react-aria/menu":{longPressMessage:`Нажмите и удерживайте или нажмите Alt + Стрелка вниз, чтобы открыть меню`},"@react-aria/numberfield":{decrease:e=>`Уменьшение ${e.fieldLabel}`,increase:e=>`Увеличение ${e.fieldLabel}`,numberField:`Числовое поле`},"@react-aria/overlays":{dismiss:`Пропустить`},"@react-aria/searchfield":{"Clear search":`Очистить поиск`},"@react-aria/spinbutton":{Empty:`Не заполнено`},"@react-aria/steplist":{steplist:`Список шагов`},"@react-aria/table":{ascending:`возрастание`,ascendingSort:e=>`сортировать столбец ${e.columnName} в порядке возрастания`,columnSize:e=>`${e.value} пикс.`,descending:`убывание`,descendingSort:e=>`сортировать столбец ${e.columnName} в порядке убывания`,resizerDescription:`Нажмите клавишу Enter для начала изменения размеров`,select:`Выбрать`,selectAll:`Выбрать все`,sortable:`сортируемый столбец`},"@react-aria/tag":{removeButtonLabel:`Удалить`,removeDescription:`Нажмите DELETE, чтобы удалить тег.`},"@react-aria/toast":{close:`Закрыть`,notifications:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)} уведомление`,other:()=>`${t.number(e.count)} уведомления`})}.`},"@react-aria/tree":{collapse:`Свернуть`,expand:`Развернуть`},"@react-stately/color":{alpha:`Альфа`,black:`черный`,blue:`Синий`,"blue purple":`сине-фиолетовый`,brightness:`Яркость`,brown:`коричневый`,"brown yellow":`коричнево-желтый`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`голубой`,"cyan blue":`цвет морской волны`,dark:`темный`,gray:`серый`,grayish:`сероватый`,green:`Зеленый`,"green cyan":`сине-зеленый`,hue:`Оттенок`,light:`светлый`,lightness:`Освещенность`,magenta:`пурпурный`,"magenta pink":`пурпурно-розовый`,orange:`оранжевый`,"orange yellow":`оранжево-желтый`,pale:`бледный`,pink:`розовый`,"pink red":`розово-красный`,purple:`фиолетовый`,"purple magenta":`фиолетово-пурпурный`,red:`Красный`,"red orange":`красно-оранжевый`,saturation:`Насыщенность`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, прозрачный на ${e.percentTransparent}`,"very dark":`очень темный`,"very light":`очень светлый`,vibrant:`яркий`,white:`белый`,yellow:`желтый`,"yellow green":`желто-зеленый`},"@react-stately/datepicker":{rangeOverflow:e=>`Значение должно быть не позже ${e.maxValue}.`,rangeReversed:`Дата начала должна предшествовать дате окончания.`,rangeUnderflow:e=>`Значение должно быть не раньше ${e.minValue}.`,unavailableDate:`Выбранная дата недоступна.`}};