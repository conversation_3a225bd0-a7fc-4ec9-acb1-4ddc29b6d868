export default{"@react-aria/autocomplete":{collectionLabel:`\xd6neriler`},"@react-aria/breadcrumbs":{breadcrumbs:`İ\xe7erik harita<PERSON>`},"@react-aria/calendar":{dateRange:e=>`${e.startDate} - ${e.endDate}`,dateSelected:e=>`${e.date} se\xe7ildi`,finishRangeSelectionPrompt:`<PERSON><PERSON><PERSON> aral<PERSON> se\xe7imini tamamlamak i\xe7in tıklayın`,maximumDate:`Son m\xfcsait tarih`,minimumDate:`İlk m\xfcsait tarih`,next:"Sonraki",previous:`\xd6nceki`,selectedDateDescription:e=>`Se\xe7ilen Tarih: ${e.date}`,selectedRangeDescription:e=>`Se\xe7ilen Aralık: ${e.dateRange}`,startRangeSelectionPrompt:`<PERSON><PERSON><PERSON> aral<PERSON>ı se\xe7imini başlatmak i\xe7in tıklayın`,todayDate:e=>`Bug\xfcn, ${e.date}`,todayDateSelected:e=>`Bug\xfcn, ${e.date} se\xe7ildi`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`Renk Se\xe7ici`,colorSwatch:`renk \xf6rneği`,transparent:"saydam",twoDimensionalSlider:`2D s\xfcrg\xfc`},"@react-aria/combobox":{buttonLabel:`\xd6nerileri g\xf6ster`,countAnnouncement:(e,a)=>`${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} se\xe7enek`,other:()=>`${a.number(e.optionCount)} se\xe7enekler`})} kullanılabilir.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Girilen grup ${e.groupTitle}, ile ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} se\xe7enek`,other:()=>`${a.number(e.groupCount)} se\xe7enekler`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:`, se\xe7ildi`,other:""},e.isSelected)}`,listboxLabel:`\xd6neriler`,selectedAnnouncement:e=>`${e.optionText}, se\xe7ildi`},"@react-aria/datepicker":{calendar:"Takvim",day:`g\xfcn`,dayPeriod:`\xd6\xd6/\xd6S`,endDate:`Bitiş Tarihi`,era:`\xe7ağ`,hour:"saat",minute:"dakika",month:"ay",second:"saniye",selectedDateDescription:e=>`Se\xe7ilen Tarih: ${e.date}`,selectedRangeDescription:e=>`Se\xe7ilen Aralık: ${e.startDate} - ${e.endDate}`,selectedTimeDescription:e=>`Se\xe7ilen Zaman: ${e.time}`,startDate:`Başlangı\xe7 Tarihi`,timeZoneName:"saat dilimi",weekday:`haftanın g\xfcn\xfc`,year:`yıl`},"@react-aria/dnd":{dragDescriptionKeyboard:`S\xfcr\xfcklemeyi başlatmak i\xe7in Enter'a basın.`,dragDescriptionKeyboardAlt:`S\xfcr\xfcklemeyi başlatmak i\xe7in Alt + Enter'a basın.`,dragDescriptionLongPress:`S\xfcr\xfcklemeye başlamak i\xe7in uzun basın.`,dragDescriptionTouch:`S\xfcr\xfcklemeyi başlatmak i\xe7in \xe7ift tıklayın.`,dragDescriptionVirtual:`S\xfcr\xfcklemeyi başlatmak i\xe7in tıklayın.`,dragItem:e=>`${e.itemText}’i s\xfcr\xfckle`,dragSelectedItems:(e,a)=>`S\xfcr\xfckle ${a.plural(e.count,{one:()=>`${a.number(e.count)} se\xe7ili \xf6ge`,other:()=>`${a.number(e.count)} se\xe7ili \xf6ge`})}`,dragSelectedKeyboard:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} se\xe7ilmiş \xf6ğe`,other:()=>`${a.number(e.count)} se\xe7ilmiş \xf6ğe`})} \xf6ğesini s\xfcr\xfcklemek i\xe7in Enter'a basın.`,dragSelectedKeyboardAlt:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} se\xe7ilmiş \xf6ğe`,other:()=>`${a.number(e.count)} se\xe7ilmiş \xf6ğe`})} \xf6ğesini s\xfcr\xfcklemek i\xe7in Alt + Enter tuşuna basın.`,dragSelectedLongPress:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} se\xe7ilmiş \xf6ğe`,other:()=>`${a.number(e.count)} se\xe7ilmiş \xf6ğe`})} \xf6ğesini s\xfcr\xfcklemek i\xe7in uzun basın.`,dragStartedKeyboard:`S\xfcr\xfckleme başlatıldı. Bir bırakma hedefine gitmek i\xe7in Tab’a basın, ardından bırakmak i\xe7in Enter’a basın veya iptal etmek i\xe7in Escape’e basın.`,dragStartedTouch:`S\xfcr\xfckleme başlatıldı. Bir bırakma hedefine gidin, ardından bırakmak i\xe7in \xe7ift tıklayın.`,dragStartedVirtual:`S\xfcr\xfckleme başlatıldı. Bir bırakma hedefine gidin, ardından bırakmak i\xe7in Enter’a tıklayın veya basın.`,dropCanceled:`Bırakma iptal edildi.`,dropComplete:`Bırakma tamamlandı.`,dropDescriptionKeyboard:`Bırakmak i\xe7in Enter'a basın. S\xfcr\xfcklemeyi iptal etmek i\xe7in Escape'e basın.`,dropDescriptionTouch:`Bırakmak i\xe7in \xe7ift tıklayın.`,dropDescriptionVirtual:`Bırakmak i\xe7in tıklayın.`,dropIndicator:`bırakma g\xf6stergesi`,dropOnItem:e=>`${e.itemText} \xfczerine bırak`,dropOnRoot:`Bırakın`,endDragKeyboard:`S\xfcr\xfckleme. S\xfcr\xfcklemeyi iptal etmek i\xe7in Enter'a basın.`,endDragTouch:`S\xfcr\xfckleme. S\xfcr\xfcklemeyi iptal etmek i\xe7in \xe7ift tıklayın.`,endDragVirtual:`S\xfcr\xfckleme. S\xfcr\xfcklemeyi iptal etmek i\xe7in tıklayın.`,insertAfter:e=>`${e.itemText}’den sonra gir`,insertBefore:e=>`${e.itemText}’den \xf6nce gir`,insertBetween:e=>`${e.beforeItemText} ve ${e.afterItemText} arasına gir`},"@react-aria/grid":{deselectedItem:e=>`${e.item} se\xe7ilmedi.`,longPressToSelect:`Se\xe7im moduna girmek i\xe7in uzun basın.`,select:`Se\xe7`,selectedAll:`T\xfcm \xf6geler se\xe7ildi.`,selectedCount:(e,a)=>`${a.plural(e.count,{"=0":`Hi\xe7bir \xf6ge se\xe7ilmedi`,one:()=>`${a.number(e.count)} \xf6ge se\xe7ildi`,other:()=>`${a.number(e.count)} \xf6ge se\xe7ildi`})}.`,selectedItem:e=>`${e.item} se\xe7ildi.`},"@react-aria/gridlist":{hasActionAnnouncement:`satırdaki işlem`,hasLinkAnnouncement:e=>`satırdaki bağlantı: ${e.link}`},"@react-aria/menu":{longPressMessage:`Men\xfcy\xfc a\xe7mak i\xe7in uzun basın veya Alt + Aşağı Ok tuşuna basın`},"@react-aria/numberfield":{decrease:e=>`${e.fieldLabel} azalt`,increase:e=>`${e.fieldLabel} arttır`,numberField:`Sayı alanı`},"@react-aria/overlays":{dismiss:"Kapat"},"@react-aria/searchfield":{"Clear search":`Aramayı temizle`},"@react-aria/spinbutton":{Empty:`Boş`},"@react-aria/steplist":{steplist:`Adım Listesi`},"@react-aria/table":{ascending:`artan sırada`,ascendingSort:e=>`${e.columnName} s\xfctuna g\xf6re artan d\xfczende sırala`,columnSize:e=>`${e.value} piksel`,descending:`azalan sırada`,descendingSort:e=>`${e.columnName} s\xfctuna g\xf6re azalan d\xfczende sırala`,resizerDescription:`Yeniden boyutlandırmak i\xe7in Enter'a basın`,select:`Se\xe7`,selectAll:`T\xfcm\xfcn\xfc Se\xe7`,sortable:`Sıralanabilir s\xfctun`},"@react-aria/tag":{removeButtonLabel:`Kaldır`,removeDescription:`Etiketi kaldırmak i\xe7in Sil tuşuna basın.`},"@react-aria/toast":{close:"Kapat",notifications:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} bildirim`,other:()=>`${a.number(e.count)} bildirim`})}.`},"@react-aria/tree":{collapse:"Daralt",expand:`Genişlet`},"@react-stately/color":{alpha:"Alfa",black:"siyah",blue:"Mavi","blue purple":"mavi mor",brightness:`Parlaklık`,brown:"kahverengi","brown yellow":`kahverengi sarı`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`camg\xf6beği`,"cyan blue":`camg\xf6beği mavi`,dark:"koyu",gray:"gri",grayish:"grimsi",green:`Yeşil`,"green cyan":`yeşil camg\xf6beği`,hue:"Ton",light:`a\xe7ık`,lightness:`Canlılık`,magenta:"eflatun","magenta pink":"eflatun pembe",orange:"turuncu","orange yellow":`turuncu sarı`,pale:"solgun",pink:"pembe","pink red":`pembe kırmızı`,purple:"mor","purple magenta":"mor eflatun",red:`Kırmızı`,"red orange":`kırmızı portakal`,saturation:"Doygunluk",transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} saydam`,"very dark":`\xe7ok koyu`,"very light":`\xe7ok a\xe7ık`,vibrant:`canlı`,white:"beyaz",yellow:`sarı`,"yellow green":`sarı yeşil`},"@react-stately/datepicker":{rangeOverflow:e=>`Değer, ${e.maxValue} veya \xf6ncesi olmalıdır.`,rangeReversed:`Başlangı\xe7 tarihi bitiş tarihinden \xf6nce olmalıdır.`,rangeUnderflow:e=>`Değer, ${e.minValue} veya sonrası olmalıdır.`,unavailableDate:`Se\xe7ilen tarih kullanılamıyor.`}};