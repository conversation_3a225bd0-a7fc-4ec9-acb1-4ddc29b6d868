export default{"@react-aria/autocomplete":{collectionLabel:`N\xe1vrhy`},"@react-aria/breadcrumbs":{breadcrumbs:`Navigačn\xe9 prvky Breadcrumbs`},"@react-aria/calendar":{dateRange:e=>`Od ${e.startDate} do ${e.endDate}`,dateSelected:e=>`Vybrat\xfd d\xe1tum ${e.date}`,finishRangeSelectionPrompt:`Kliknut\xedm dokonč\xedte v\xfdber rozsahu d\xe1tumov`,maximumDate:`Posledn\xfd dostupn\xfd d\xe1tum`,minimumDate:`Prv\xfd dostupn\xfd d\xe1tum`,next:`Nasleduj\xface`,previous:`Predch\xe1dzaj\xface`,selectedDateDescription:e=>`Vybrat\xfd d\xe1tum: ${e.date}`,selectedRangeDescription:e=>`Vybrat\xfd rozsah: ${e.dateRange}`,startRangeSelectionPrompt:`Kliknut\xedm spust\xedte v\xfdber rozsahu d\xe1tumov`,todayDate:e=>`Dnes ${e.date}`,todayDateSelected:e=>`Vybrat\xfd dnešn\xfd d\xe1tum ${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`V\xfdber farieb`,colorSwatch:`vzorkovn\xedk farieb`,transparent:`transparentn\xfd`,twoDimensionalSlider:"2D jazdec"},"@react-aria/combobox":{buttonLabel:`Zobraziť n\xe1vrhy`,countAnnouncement:(e,a)=>`${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} možnosť`,other:()=>`${a.number(e.optionCount)} možnosti/-\xed`})} k dispoz\xedcii.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Zadan\xe1 skupina ${e.groupTitle}, s ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} možnosťou`,other:()=>`${a.number(e.groupCount)} možnosťami`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:`, vybrat\xe9`,other:""},e.isSelected)}`,listboxLabel:`N\xe1vrhy`,selectedAnnouncement:e=>`${e.optionText}, vybrat\xe9`},"@react-aria/datepicker":{calendar:`Kalend\xe1r`,day:`deň`,dayPeriod:"AM/PM",endDate:`D\xe1tum ukončenia`,era:`letopočet`,hour:"hodina",minute:`min\xfata`,month:"mesiac",second:"sekunda",selectedDateDescription:e=>`Vybrat\xfd d\xe1tum: ${e.date}`,selectedRangeDescription:e=>`Vybrat\xfd rozsah: od ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Vybrat\xfd čas: ${e.time}`,startDate:`D\xe1tum začatia`,timeZoneName:`časov\xe9 p\xe1smo`,weekday:`deň t\xfdždňa`,year:"rok"},"@react-aria/dnd":{dragDescriptionKeyboard:`Stlačen\xedm kl\xe1vesu Enter začnete pres\xfavanie.`,dragDescriptionKeyboardAlt:`Stlačen\xedm kl\xe1vesov Alt + Enter začnete pres\xfavanie.`,dragDescriptionLongPress:`Dlh\xfdm stlačen\xedm začnete pres\xfavanie.`,dragDescriptionTouch:`Dvojit\xfdm kliknut\xedm začnete pres\xfavanie.`,dragDescriptionVirtual:`Kliknut\xedm začnete pres\xfavanie.`,dragItem:e=>`Presun\xfať položku ${e.itemText}`,dragSelectedItems:(e,a)=>`Presun\xfať ${a.plural(e.count,{one:()=>`${a.number(e.count)} vybrat\xfa položku`,other:()=>`${a.number(e.count)} vybrat\xe9 položky`})}`,dragSelectedKeyboard:(e,a)=>`Stlačen\xedm kl\xe1vesu Enter presuniete ${a.plural(e.count,{one:()=>`${a.number(e.count)} vybrat\xfa položku`,other:()=>`${a.number(e.count)} vybrat\xfdch položiek`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Stlačen\xedm kl\xe1vesov Alt + Enter presuniete ${a.plural(e.count,{one:()=>`${a.number(e.count)} vybrat\xfa položku`,other:()=>`${a.number(e.count)} vybrat\xfdch položiek`})}.`,dragSelectedLongPress:(e,a)=>`Dlh\xfdm stlačen\xedm presuniete ${a.plural(e.count,{one:()=>`${a.number(e.count)} vybrat\xfa položku`,other:()=>`${a.number(e.count)} vybrat\xfdch položiek`})}.`,dragStartedKeyboard:`Pres\xfavanie sa začalo. Do cieľov\xe9ho umiestnenia prejdete stlačen\xedm kl\xe1vesu Tab. Ak chcete položku umiestniť, stlačte kl\xe1ves Enter alebo stlačte kl\xe1ves Esc, ak chcete pres\xfavanie zrušiť.`,dragStartedTouch:`Pres\xfavanie sa začalo. Prejdite na cieľov\xe9 umiestnenie a dvojit\xfdm kliknut\xedm umiestnite položku.`,dragStartedVirtual:`Pres\xfavanie sa začalo. Prejdite na cieľov\xe9 umiestnenie a kliknut\xedm alebo stlačen\xedm kl\xe1vesu Enter umiestnite položku.`,dropCanceled:`Umiestnenie zrušen\xe9.`,dropComplete:`Umiestnenie dokončen\xe9.`,dropDescriptionKeyboard:`Stlačen\xedm kl\xe1vesu Enter umiestnite položku. Stlačen\xedm kl\xe1vesu Esc zruš\xedte pres\xfavanie.`,dropDescriptionTouch:`Dvojit\xfdm kliknut\xedm umiestnite položku.`,dropDescriptionVirtual:`Kliknut\xedm umiestnite položku.`,dropIndicator:`indik\xe1tor umiestnenia`,dropOnItem:e=>`Umiestniť na položku ${e.itemText}`,dropOnRoot:`Umiestniť na`,endDragKeyboard:`Prebieha pres\xfavanie. Ak ho chcete zrušiť, stlačte kl\xe1ves Enter.`,endDragTouch:`Prebieha pres\xfavanie. Dvojit\xfdm kliknut\xedm ho m\xf4žete zrušiť.`,endDragVirtual:`Prebieha pres\xfavanie.`,insertAfter:e=>`Vložiť za položku ${e.itemText}`,insertBefore:e=>`Vložiť pred položku ${e.itemText}`,insertBetween:e=>`Vložiť medzi položky ${e.beforeItemText} a ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`Nevybrat\xe9 položky: ${e.item}.`,longPressToSelect:`Dlhš\xedm stlačen\xedm prejdite do režimu v\xfdberu.`,select:`Vybrať`,selectedAll:`Všetky vybrat\xe9 položky.`,selectedCount:(e,a)=>`${a.plural(e.count,{"=0":`Žiadne vybrat\xe9 položky`,one:()=>`${a.number(e.count)} vybrat\xe1 položka`,other:()=>`Počet vybrat\xfdch položiek:${a.number(e.count)}`})}.`,selectedItem:e=>`Vybrat\xe9 položky: ${e.item}.`},"@react-aria/gridlist":{hasActionAnnouncement:"riadok obsahuje akciu",hasLinkAnnouncement:e=>`riadok obsahuje odkaz: ${e.link}`},"@react-aria/menu":{longPressMessage:`Ponuku otvor\xedte dlh\xfdm stlačen\xedm alebo stlačen\xedm kl\xe1vesu Alt + kl\xe1vesu so š\xedpkou nadol`},"@react-aria/numberfield":{decrease:e=>`Zn\xedžiť ${e.fieldLabel}`,increase:e=>`Zv\xfdšiť ${e.fieldLabel}`,numberField:`Č\xedseln\xe9 pole`},"@react-aria/overlays":{dismiss:`Zrušiť`},"@react-aria/searchfield":{"Clear search":`Vymazať vyhľad\xe1vanie`},"@react-aria/spinbutton":{Empty:`Pr\xe1zdne`},"@react-aria/steplist":{steplist:"Zoznam krokov"},"@react-aria/table":{ascending:"vzostupne",ascendingSort:e=>`zoraden\xe9 zostupne podľa stĺpca ${e.columnName}`,columnSize:e=>`Počet pixelov: ${e.value}`,descending:"zostupne",descendingSort:e=>`zoraden\xe9 zostupne podľa stĺpca ${e.columnName}`,resizerDescription:`Stlačen\xedm kl\xe1vesu Enter začnete zmenu veľkosti`,select:`Vybrať`,selectAll:`Vybrať všetko`,sortable:`zoraditeľn\xfd stĺpec`},"@react-aria/tag":{removeButtonLabel:`Odstr\xe1niť`,removeDescription:`Ak chcete odstr\xe1niť značku, stlačte kl\xe1ves Delete.`},"@react-aria/toast":{close:`Zatvoriť`,notifications:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} ozn\xe1menie`,few:()=>`${a.number(e.count)} ozn\xe1menia`,other:()=>`${a.number(e.count)} ozn\xe1men\xed`})}.`},"@react-aria/tree":{collapse:`Zbaliť`,expand:`Rozbaliť`},"@react-stately/color":{alpha:"Alfa",black:`čierna`,blue:`Modr\xe1`,"blue purple":`modrofialov\xe1`,brightness:"Jas",brown:`hned\xe1`,"brown yellow":`hnedožlt\xe1`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`az\xfarov\xe1`,"cyan blue":`az\xfarov\xe1 modr\xe1`,dark:`tmav\xe1`,gray:`siv\xe1`,grayish:`sivast\xe1`,green:`Zelen\xe1`,"green cyan":`zelen\xe1 az\xfarov\xe1`,hue:`Odtieň`,light:`svetl\xe1`,lightness:`Svetlosť`,magenta:`purpurov\xe1`,"magenta pink":`ružov\xe1 purpurov\xe1`,orange:`oranžov\xe1`,"orange yellow":`oranžovožlt\xe1`,pale:`bled\xe1`,pink:`ružov\xe1`,"pink red":`ružovočerven\xe1`,purple:`fialov\xe1`,"purple magenta":`fialov\xe1 purpurov\xe1`,red:`Červen\xe1`,"red orange":`červenooranžov\xe1`,saturation:`S\xfdtosť`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} priehľadn\xe1`,"very dark":`veľmi tmav\xe1`,"very light":`veľmi svetl\xe1`,vibrant:`energick\xe1`,white:"biela",yellow:`žlt\xe1`,"yellow green":`žltozelen\xe1`},"@react-stately/datepicker":{rangeOverflow:e=>`Hodnota mus\xed byť ${e.maxValue} alebo skoršia.`,rangeReversed:`D\xe1tum začiatku mus\xed byť skorš\xed ako d\xe1tum konca.`,rangeUnderflow:e=>`Hodnota mus\xed byť ${e.minValue} alebo neskoršia.`,unavailableDate:`Vybrat\xfd d\xe1tum je nedostupn\xfd.`}};