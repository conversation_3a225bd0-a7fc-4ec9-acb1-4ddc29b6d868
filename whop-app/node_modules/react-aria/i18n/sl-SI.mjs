export default{"@react-aria/autocomplete":{collectionLabel:"Predlogi"},"@react-aria/breadcrumbs":{breadcrumbs:"<PERSON>ob<PERSON>"},"@react-aria/calendar":{dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} izbrano`,finishRangeSelectionPrompt:`Kliknite za dokončanje izbire datumskega obsega`,maximumDate:`Zadnji razpoložljivi datum`,minimumDate:`Prvi razpol<PERSON>ljivi datum`,next:"Naprej",previous:"Nazaj",selectedDateDescription:e=>`Izbrani datum: ${e.date}`,selectedRangeDescription:e=>`Izbrano območje: ${e.dateRange}`,startRangeSelectionPrompt:`Kliknite za začetek izbire datumskega obsega`,todayDate:e=>`<PERSON><PERSON>, ${e.date}`,todayDateSelected:e=>`<PERSON><PERSON>, ${e.date} izbrano`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:"Izbirnik barv",colorSwatch:"barvna paleta",transparent:"prozorno",twoDimensionalSlider:"2D drsnik"},"@react-aria/combobox":{buttonLabel:`Prikaži predloge`,countAnnouncement:(e,a)=>`Na voljo je ${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} opcija`,other:()=>`${a.number(e.optionCount)} opcije`})}.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Vnesena skupina ${e.groupTitle}, z ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} opcija`,other:()=>`${a.number(e.groupCount)} opcije`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:", izbrano",other:""},e.isSelected)}`,listboxLabel:"Predlogi",selectedAnnouncement:e=>`${e.optionText}, izbrano`},"@react-aria/datepicker":{calendar:"Koledar",day:"dan",dayPeriod:"dop/pop",endDate:"Datum konca",era:"doba",hour:"ura",minute:"minuta",month:"mesec",second:"sekunda",selectedDateDescription:e=>`Izbrani datum: ${e.date}`,selectedRangeDescription:e=>`Izbrano območje: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Izbrani čas: ${e.time}`,startDate:`Datum začetka`,timeZoneName:`časovni pas`,weekday:"dan v tednu",year:"leto"},"@react-aria/dnd":{dragDescriptionKeyboard:`Pritisnite tipko Enter za začetek vlečenja.`,dragDescriptionKeyboardAlt:`Pritisnite tipki Alt + Enter za začetek vlečenja.`,dragDescriptionLongPress:`Pritisnite in zadržite za začetek vlečenja.`,dragDescriptionTouch:`Dvotapnite za začetek vlečenja.`,dragDescriptionVirtual:`Kliknite za začetek vlečenja.`,dragItem:e=>`Povleci ${e.itemText}`,dragSelectedItems:(e,a)=>`Povlecite ${a.plural(e.count,{one:()=>`${a.number(e.count)} izbran element`,other:()=>`izbrane elemente (${a.number(e.count)})`})}`,dragSelectedKeyboard:(e,a)=>`Pritisnite tipko Enter, da povlečete ${a.plural(e.count,{one:()=>`${a.number(e.count)} izbrani element`,other:()=>`${a.number(e.count)} izbranih elementov`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Pritisnite tipki Alt + Enter, da povlečete ${a.plural(e.count,{one:()=>`${a.number(e.count)} izbrani element`,other:()=>`${a.number(e.count)} izbranih elementov`})}.`,dragSelectedLongPress:(e,a)=>`Pritisnite in zadržite, da povlečete ${a.plural(e.count,{one:()=>`${a.number(e.count)} izbrani element`,other:()=>`${a.number(e.count)} izbranih elementov`})}.`,dragStartedKeyboard:`Vlečenje se je začelo. Pritisnite tipko Tab za pomik na mesto, kamor želite spustiti elemente, in pritisnite tipko Enter, da jih spustite, ali tipko Escape, da prekličete postopek.`,dragStartedTouch:`Vlečenje se je začelo. Pomaknite se na mesto, kamor želite spustiti elemente, in dvotapnite, da jih spustite.`,dragStartedVirtual:`Vlečenje se je začelo. Pomaknite se na mesto, kamor želite spustiti elemente, in kliknite ali pritisnite tipko Enter, da jih spustite.`,dropCanceled:"Spust je preklican.",dropComplete:`Spust je končan.`,dropDescriptionKeyboard:`Pritisnite tipko Enter, da spustite. Pritisnite tipko Escape, da prekličete vlečenje.`,dropDescriptionTouch:"Dvotapnite, da spustite.",dropDescriptionVirtual:"Kliknite, da spustite.",dropIndicator:"indikator spusta",dropOnItem:e=>`Spusti na mesto ${e.itemText}`,dropOnRoot:"Spusti na mesto",endDragKeyboard:`Vlečenje. Pritisnite tipko Enter za preklic vlečenja.`,endDragTouch:`Vlečenje. Dvotapnite za preklic vlečenja.`,endDragVirtual:`Vlečenje. Kliknite, da prekličete vlečenje.`,insertAfter:e=>`Vstavi za ${e.itemText}`,insertBefore:e=>`Vstavi pred ${e.itemText}`,insertBetween:e=>`Vstavi med ${e.beforeItemText} in ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`Element ${e.item} ni izbran.`,longPressToSelect:`Za izbirni način pritisnite in dlje časa držite.`,select:"Izberite",selectedAll:"Vsi elementi so izbrani.",selectedCount:(e,a)=>`${a.plural(e.count,{"=0":"Noben element ni izbran",one:()=>`${a.number(e.count)} element je izbran`,other:()=>`${a.number(e.count)} elementov je izbranih`})}.`,selectedItem:e=>`Element ${e.item} je izbran.`},"@react-aria/gridlist":{hasActionAnnouncement:"vrstica ima dejanje",hasLinkAnnouncement:e=>`vrstica ima povezavo: ${e.link}`},"@react-aria/menu":{longPressMessage:`Za odprtje menija pritisnite in držite gumb ali pritisnite Alt+puščica navzdol`},"@react-aria/numberfield":{decrease:e=>`Upadati ${e.fieldLabel}`,increase:e=>`Povečajte ${e.fieldLabel}`,numberField:`Številčno polje`},"@react-aria/overlays":{dismiss:"Opusti"},"@react-aria/searchfield":{"Clear search":`Počisti iskanje`},"@react-aria/spinbutton":{Empty:"Prazen"},"@react-aria/steplist":{steplist:"Seznam korakov"},"@react-aria/table":{ascending:`naraščajoče`,ascendingSort:e=>`razvrščeno po stolpcu ${e.columnName} v naraščajočem vrstnem redu`,columnSize:e=>`${e.value} slikovnih pik`,descending:`padajoče`,descendingSort:e=>`razvrščeno po stolpcu ${e.columnName} v padajočem vrstnem redu`,resizerDescription:`Pritisnite tipko Enter da začnete spreminjati velikost`,select:"Izberite",selectAll:"Izberite vse",sortable:"razvrstljivi stolpec"},"@react-aria/tag":{removeButtonLabel:"Odstrani",removeDescription:"Pritisnite Delete, da odstranite oznako."},"@react-aria/toast":{close:"Zapri",notifications:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} obvestilo`,two:()=>`${a.number(e.count)} obvestili`,few:()=>`${a.number(e.count)} obvestila`,other:()=>`${a.number(e.count)} obvestil`})}.`},"@react-aria/tree":{collapse:"Strni",expand:`Razširi`},"@react-stately/color":{alpha:"Alfa",black:`črna`,blue:"Modra","blue purple":`modro vijolična`,brightness:"Svetlost",brown:"rjava","brown yellow":"rjavo rumena",colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:"cian","cyan blue":"cian modra",dark:"temna",gray:"siva",grayish:"sivkasta",green:"Zelena","green cyan":"zelena cian",hue:"Barva",light:"svetla",lightness:"Lahkost",magenta:`škrlatna`,"magenta pink":`škrlatno roza`,orange:`oranžna`,"orange yellow":`oranžno rumena`,pale:"bleda",pink:"roza","pink red":`roza rdeča`,purple:`vijolična`,"purple magenta":`vijolično škrlatna`,red:`Rdeča`,"red orange":`rdeče oranžna`,saturation:`Nasičenost`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} prozorna`,"very dark":"zelo temna","very light":"zelo svetla",vibrant:`živahna`,white:"bela",yellow:"rumena","yellow green":"rumeno zelena"},"@react-stately/datepicker":{rangeOverflow:e=>`Vrednost mora biti ${e.maxValue} ali starejša.`,rangeReversed:`Začetni datum mora biti pred končnim datumom.`,rangeUnderflow:e=>`Vrednost mora biti ${e.minValue} ali novejša.`,unavailableDate:"Izbrani datum ni na voljo."}};