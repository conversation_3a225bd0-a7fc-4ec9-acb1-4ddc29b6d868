export default{"@react-aria/autocomplete":{collectionLabel:`建議`},"@react-aria/breadcrumbs":{breadcrumbs:`導覽列`},"@react-aria/calendar":{dateRange:e=>`${e.startDate} 至 ${e.endDate}`,dateSelected:e=>`已選取 ${e.date}`,finishRangeSelectionPrompt:`按一下以完成選取日期範圍`,maximumDate:`最後一個可用日期`,minimumDate:`第一個可用日期`,next:`下一頁`,previous:`上一頁`,selectedDateDescription:e=>`選定的日期：${e.date}`,selectedRangeDescription:e=>`選定的範圍：${e.dateRange}`,startRangeSelectionPrompt:`按一下以開始選取日期範圍`,todayDate:e=>`今天，${e.date}`,todayDateSelected:e=>`已選取今天，${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}，${e.channelLabel}`,colorNameAndValue:e=>`${e.name}：${e.value}`,colorPicker:`檢色器`,colorSwatch:`色票`,transparent:`透明`,twoDimensionalSlider:`2D 滑桿`},"@react-aria/combobox":{buttonLabel:`顯示建議`,countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} 選項`,other:()=>`${t.number(e.optionCount)} 選項`})} 可用。`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`輸入的群組 ${e.groupTitle}, 有 ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} 選項`,other:()=>`${t.number(e.groupCount)} 選項`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:`, 已選取`,other:""},e.isSelected)}`,listboxLabel:`建議`,selectedAnnouncement:e=>`${e.optionText}, 已選取`},"@react-aria/datepicker":{calendar:`日曆`,day:`日`,dayPeriod:`上午/下午`,endDate:`結束日期`,era:`纪元`,hour:`小时`,minute:`分钟`,month:`月`,second:`秒`,selectedDateDescription:e=>`選定的日期：${e.date}`,selectedRangeDescription:e=>`選定的範圍：${e.startDate} 至 ${e.endDate}`,selectedTimeDescription:e=>`選定的時間：${e.time}`,startDate:`開始日期`,timeZoneName:`时区`,weekday:`工作日`,year:`年`},"@react-aria/dnd":{dragDescriptionKeyboard:`按 Enter 鍵以開始拖曳。`,dragDescriptionKeyboardAlt:`按 Alt+Enter 鍵以開始拖曳。`,dragDescriptionLongPress:`長按以開始拖曳。`,dragDescriptionTouch:`輕點兩下以開始拖曳。`,dragDescriptionVirtual:`按一下滑鼠以開始拖曳。`,dragItem:e=>`拖曳「${e.itemText}」`,dragSelectedItems:(e,t)=>`拖曳 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 個選定項目`,other:()=>`${t.number(e.count)} 個選定項目`})}`,dragSelectedKeyboard:(e,t)=>`按 Enter 鍵以拖曳 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 個選定項目`,other:()=>`${t.number(e.count)} 個選定項目`})}。`,dragSelectedKeyboardAlt:(e,t)=>`按 Alt+Enter 鍵以拖曳 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 個選定項目`,other:()=>`${t.number(e.count)} 個選定項目`})}。`,dragSelectedLongPress:(e,t)=>`長按以拖曳 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 個選定項目`,other:()=>`${t.number(e.count)} 個選定項目`})}。`,dragStartedKeyboard:`已開始拖曳。按 Tab 鍵以瀏覽至放置目標，然後按 Enter 鍵以放置，或按 Escape 鍵以取消。`,dragStartedTouch:`已開始拖曳。瀏覽至放置目標，然後輕點兩下以放置。`,dragStartedVirtual:`已開始拖曳。瀏覽至放置目標，然後按一下滑鼠或按 Enter 鍵以放置。`,dropCanceled:`放置已取消。`,dropComplete:`放置已完成。`,dropDescriptionKeyboard:`按 Enter 鍵以放置。按 Escape 鍵以取消拖曳。`,dropDescriptionTouch:`輕點兩下以放置。`,dropDescriptionVirtual:`按一下滑鼠以放置。`,dropIndicator:`放置指示器`,dropOnItem:e=>`放置在「${e.itemText}」上`,dropOnRoot:`放置在`,endDragKeyboard:`拖曳中。按 Enter 鍵以取消拖曳。`,endDragTouch:`拖曳中。輕點兩下以取消拖曳。`,endDragVirtual:`拖曳中。按一下滑鼠以取消拖曳。`,insertAfter:e=>`插入至「${e.itemText}」之後`,insertBefore:e=>`插入至「${e.itemText}」之前`,insertBetween:e=>`插入至「${e.beforeItemText}」和「${e.afterItemText}」之間`},"@react-aria/grid":{deselectedItem:e=>`未選取「${e.item}」。`,longPressToSelect:`長按以進入選擇模式。`,select:`選取`,selectedAll:`已選取所有項目。`,selectedCount:(e,t)=>`${t.plural(e.count,{"=0":`未選取任何項目`,one:()=>`已選取 ${t.number(e.count)} 個項目`,other:()=>`已選取 ${t.number(e.count)} 個項目`})}。`,selectedItem:e=>`已選取「${e.item}」。`},"@react-aria/gridlist":{hasActionAnnouncement:`列有動作`,hasLinkAnnouncement:e=>`列有連結：${e.link}`},"@react-aria/menu":{longPressMessage:`長按或按 Alt+向下鍵以開啟功能表`},"@react-aria/numberfield":{decrease:e=>`縮小 ${e.fieldLabel}`,increase:e=>`放大 ${e.fieldLabel}`,numberField:`數字欄位`},"@react-aria/overlays":{dismiss:`關閉`},"@react-aria/searchfield":{"Clear search":`清除搜尋條件`},"@react-aria/spinbutton":{Empty:`空白`},"@react-aria/steplist":{steplist:`步驟清單`},"@react-aria/table":{ascending:`遞增`,ascendingSort:e=>`已依據「${e.columnName}」欄遞增排序`,columnSize:e=>`${e.value} 像素`,descending:`遞減`,descendingSort:e=>`已依據「${e.columnName}」欄遞減排序`,resizerDescription:`按 Enter 鍵以開始調整大小`,select:`選取`,selectAll:`全選`,sortable:`可排序的欄`},"@react-aria/tag":{removeButtonLabel:`移除`,removeDescription:`按 Delete 鍵以移除標記。`},"@react-aria/toast":{close:`關閉`,notifications:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)} 個通知`,other:()=>`${t.number(e.count)} 個通知`})}。`},"@react-aria/tree":{collapse:`收合`,expand:`展開`},"@react-stately/color":{alpha:"Alpha",black:`黑`,blue:`藍色`,"blue purple":`藍紫`,brightness:`亮度`,brown:`棕`,"brown yellow":`棕黃`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`青`,"cyan blue":`青藍`,dark:`暗`,gray:`灰`,grayish:`偏灰`,green:`綠色`,"green cyan":`青綠`,hue:`色相`,light:`淺`,lightness:`明亮`,magenta:`洋紅`,"magenta pink":`淺洋紅`,orange:`橙`,"orange yellow":`橙黃`,pale:`淡`,pink:`粉紅`,"pink red":`粉紅`,purple:`紫`,"purple magenta":`紫洋紅`,red:`紅色`,"red orange":`橙紅`,saturation:`飽和度`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} 透明`,"very dark":`很暗`,"very light":`很淺`,vibrant:`鮮豔`,white:`白`,yellow:`黃`,"yellow green":`黃綠`},"@react-stately/datepicker":{rangeOverflow:e=>`值必須是 ${e.maxValue} 或更早。`,rangeReversed:`開始日期必須在結束日期之前。`,rangeUnderflow:e=>`值必須是 ${e.minValue} 或更晚。`,unavailableDate:`所選日期無法使用。`}};