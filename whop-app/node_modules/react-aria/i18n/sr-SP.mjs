export default{"@react-aria/autocomplete":{collectionLabel:"Predlozi"},"@react-aria/breadcrumbs":{breadcrumbs:"Putanje navigacije"},"@react-aria/calendar":{dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`${e.date} izabran`,finishRangeSelectionPrompt:`Kliknite da dovršite opseg izabranih datuma`,maximumDate:`Zadnji raspoloživi datum`,minimumDate:`Prvi raspoloživi datum`,next:`<PERSON><PERSON><PERSON><PERSON><PERSON>`,previous:"Prethodni",selectedDateDescription:e=>`Izabrani datum: ${e.date}`,selectedRangeDescription:e=>`Izabrani period: ${e.dateRange}`,startRangeSelectionPrompt:`Kliknite da započnete opseg izabranih datuma`,todayDate:e=>`Dana<PERSON>, ${e.date}`,todayDateSelected:e=>`<PERSON><PERSON>, izabran ${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`Birač boja`,colorSwatch:"Uzorak boje",transparent:"providno",twoDimensionalSlider:`2D klizač`},"@react-aria/combobox":{buttonLabel:`Prikaži predloge`,countAnnouncement:(e,a)=>`Dostupno još: ${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} opcija`,other:()=>`${a.number(e.optionCount)} opcije/a`})}.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Unesena grupa ${e.groupTitle}, s ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} opcijom`,other:()=>`${a.number(e.groupCount)} optione/a`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:", izabranih",other:""},e.isSelected)}`,listboxLabel:"Predlozi",selectedAnnouncement:e=>`${e.optionText}, izabrano`},"@react-aria/datepicker":{calendar:"Kalendar",day:`дан`,dayPeriod:`пре подне/по подне`,endDate:`Datum završetka`,era:`ера`,hour:`сат`,minute:`минут`,month:`месец`,second:`секунд`,selectedDateDescription:e=>`Izabrani datum: ${e.date}`,selectedRangeDescription:e=>`Izabrani opseg: od ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Izabrano vreme: ${e.time}`,startDate:`Datum početka`,timeZoneName:`временска зона`,weekday:`дан у недељи`,year:`година`},"@react-aria/dnd":{dragDescriptionKeyboard:`Pritisnite Enter da biste započeli prevlačenje.`,dragDescriptionKeyboardAlt:`Pritisnite Alt + Enter da biste započeli prevlačenje.`,dragDescriptionLongPress:`Pritisnite dugo da biste započeli prevlačenje.`,dragDescriptionTouch:`Dvaput dodirnite da biste započeli prevlačenje.`,dragDescriptionVirtual:`Kliknite da biste započeli prevlačenje.`,dragItem:e=>`Prevucite ${e.itemText}`,dragSelectedItems:(e,a)=>`Prevucite ${a.plural(e.count,{one:()=>`${a.number(e.count)} izabranu stavku`,other:()=>`${a.number(e.count)} izabrane stavke`})}`,dragSelectedKeyboard:(e,a)=>`Pritisnite Enter da biste prevukli ${a.plural(e.count,{one:()=>`${a.number(e.count)} izabranu stavku`,other:()=>`${a.number(e.count)} izabranih stavki`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Pritisnite Alt + Enter da biste prevukli ${a.plural(e.count,{one:()=>`${a.number(e.count)} izabranu stavku`,other:()=>`${a.number(e.count)} izabranih stavki`})}.`,dragSelectedLongPress:(e,a)=>`Pritisnite dugo da biste prevukli ${a.plural(e.count,{one:()=>`${a.number(e.count)} izabranu stavku`,other:()=>`${a.number(e.count)} izabranih stavki`})}.`,dragStartedKeyboard:`Prevlačenje je započeto. Pritisnite Tab da biste otišli do cilja za otpuštanje, zatim pritisnite Enter za ispuštanje ili pritisnite Escape za otkazivanje.`,dragStartedTouch:`Prevlačenje je započeto. Idite do cilja za otpuštanje, a zatim dvaput dodirnite za otpuštanje.`,dragStartedVirtual:`Prevlačenje je započeto. Idite do cilja za otpuštanje, a zatim kliknite ili pritinite Enter za otpuštanje.`,dropCanceled:`Otpuštanje je otkazano.`,dropComplete:`Prevlačenje je završeno.`,dropDescriptionKeyboard:`Pritisnite Enter da biste otpustili. Pritisnite Escape da biste otkazali prevlačenje.`,dropDescriptionTouch:`Dvaput dodirnite za otpuštanje.`,dropDescriptionVirtual:`Kliknite za otpuštanje.`,dropIndicator:`Indikator otpuštanja`,dropOnItem:e=>`Otpusti na ${e.itemText}`,dropOnRoot:"Otpusti na",endDragKeyboard:`Prevlačenje u toku. Pritisnite Enter da biste otkazali prevlačenje.`,endDragTouch:`Prevlačenje u toku. Dvaput dodirnite da biste otkazali prevlačenje.`,endDragVirtual:`Prevlačenje u toku. Kliknite da biste otkazali prevlačenje.`,insertAfter:e=>`Umetnite posle ${e.itemText}`,insertBefore:e=>`Umetnite ispred ${e.itemText}`,insertBetween:e=>`Umetnite između ${e.beforeItemText} i ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} nije izabrano.`,longPressToSelect:`Dugo pritisnite za ulazak u režim biranja.`,select:"Izaberite",selectedAll:"Izabrane su sve stavke.",selectedCount:(e,a)=>`${a.plural(e.count,{"=0":"Nije izabrana nijedna stavka",one:()=>`Izabrana je ${a.number(e.count)} stavka`,other:()=>`Izabrano je ${a.number(e.count)} stavki`})}.`,selectedItem:e=>`${e.item} je izabrano.`},"@react-aria/gridlist":{hasActionAnnouncement:"red ima radnju",hasLinkAnnouncement:e=>`red ima vezu: ${e.veza}`},"@react-aria/menu":{longPressMessage:"Dugo pritisnite ili pritisnite Alt + strelicu prema dole da otvorite meni"},"@react-aria/numberfield":{decrease:e=>`Smanji ${e.fieldLabel}`,increase:e=>`Povećaj ${e.fieldLabel}`,numberField:"Polje broja"},"@react-aria/overlays":{dismiss:"Odbaci"},"@react-aria/searchfield":{"Clear search":`Obriši pretragu`},"@react-aria/spinbutton":{Empty:"Prazno"},"@react-aria/steplist":{steplist:"Lista koraka"},"@react-aria/table":{ascending:`rastući`,ascendingSort:e=>`sortirano po kolonama ${e.columnName} rastućim redosledom`,columnSize:e=>`${e.value} piksela`,descending:`padajući`,descendingSort:e=>`sortirano po kolonama ${e.columnName} padajućim redosledom`,resizerDescription:`Pritisnite Enter da biste započeli promenu veličine`,select:"Izaberite",selectAll:"Izaberite sve",sortable:`kolona koja se može sortirati`},"@react-aria/tag":{removeButtonLabel:"Ukloni",removeDescription:`Pritisnite Obriši da biste uklonili oznaku.`},"@react-aria/toast":{close:"Zatvori",notifications:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} obaveštenje`,other:()=>`${a.number(e.count)} obaveštenja`})}.`},"@react-aria/tree":{collapse:" Skupi",expand:`Proširi`},"@react-stately/color":{alpha:"Alfa",black:"crno",blue:"Plava","blue purple":`plavoljubičasta`,brightness:"Osvetljenost",brown:`smeđa`,"brown yellow":`smeđežuta`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:"cijan","cyan blue":"cijan plava",dark:"tamno",gray:"siva",grayish:"sivkasta",green:"Zelena","green cyan":"zeleno cijan",hue:"Nijansa",light:"svetla",lightness:"Osvetljenje",magenta:"purpurnocrvena","magenta pink":`magenta ružičasta`,orange:`narandžasta`,"orange yellow":`narandžastožuta`,pale:"bledo",pink:`ružičasta`,"pink red":`ružičastocrvena`,purple:`ljubičasta`,"purple magenta":`ljubičasta magenta`,red:"Crvena","red orange":`crvenonarandžasta`,saturation:`Zasićenje`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} prozirna`,"very dark":"veoma tamno","very light":"vrlo svetlo",vibrant:`živopisna`,white:"bela",yellow:`žuto`,"yellow green":`žutozelena`},"@react-stately/datepicker":{rangeOverflow:e=>`Vrednost mora da bude ${e.maxValue} ili starija.`,rangeReversed:`Datum početka mora biti pre datuma završetka.`,rangeUnderflow:e=>`Vrednost mora da bude ${e.minValue} ili novija.`,unavailableDate:"Izabrani datum nije dostupan."}};