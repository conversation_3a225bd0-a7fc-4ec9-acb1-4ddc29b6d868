export default{"@react-aria/autocomplete":{collectionLabel:`F\xf6rslag`},"@react-aria/breadcrumbs":{breadcrumbs:`S\xf6kv\xe4gar`},"@react-aria/calendar":{dateRange:e=>`${e.startDate} till ${e.endDate}`,dateSelected:e=>`${e.date} har valts`,finishRangeSelectionPrompt:`Klicka f\xf6r att avsluta val av datumintervall`,maximumDate:`Sista tillg\xe4ngliga datum`,minimumDate:`F\xf6rsta tillg\xe4ngliga datum`,next:`N\xe4sta`,previous:`F\xf6reg\xe5ende`,selectedDateDescription:e=>`Valt datum: ${e.date}`,selectedRangeDescription:e=>`Valt intervall: ${e.dateRange}`,startRangeSelectionPrompt:`Klicka f\xf6r att v\xe4lja datumintervall`,todayDate:e=>`Idag, ${e.date}`,todayDateSelected:e=>`Idag, ${e.date} har valts`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`F\xe4rgv\xe4ljaren`,colorSwatch:`f\xe4rgruta`,transparent:"genomskinlig",twoDimensionalSlider:"2D-reglage"},"@react-aria/combobox":{buttonLabel:`Visa f\xf6rslag`,countAnnouncement:(e,a)=>`${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} alternativ`,other:()=>`${a.number(e.optionCount)} alternativ`})} tillg\xe4ngliga.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Ingick i gruppen ${e.groupTitle} med ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} alternativ`,other:()=>`${a.number(e.groupCount)} alternativ`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:", valda",other:""},e.isSelected)}`,listboxLabel:`F\xf6rslag`,selectedAnnouncement:e=>`${e.optionText}, valda`},"@react-aria/datepicker":{calendar:"Kalender",day:"dag",dayPeriod:"fm/em",endDate:"Slutdatum",era:"era",hour:"timme",minute:"minut",month:`m\xe5nad`,second:"sekund",selectedDateDescription:e=>`Valt datum: ${e.date}`,selectedRangeDescription:e=>`Valt intervall: ${e.startDate} till ${e.endDate}`,selectedTimeDescription:e=>`Vald tid: ${e.time}`,startDate:"Startdatum",timeZoneName:"tidszon",weekday:"veckodag",year:`\xe5r`},"@react-aria/dnd":{dragDescriptionKeyboard:`Tryck p\xe5 enter f\xf6r att b\xf6rja dra.`,dragDescriptionKeyboardAlt:`Tryck p\xe5 Alt + Retur f\xf6r att b\xf6rja dra.`,dragDescriptionLongPress:`Tryck l\xe4nge f\xf6r att b\xf6rja dra.`,dragDescriptionTouch:`Dubbeltryck f\xf6r att b\xf6rja dra.`,dragDescriptionVirtual:`Klicka f\xf6r att b\xf6rja dra.`,dragItem:e=>`Dra ${e.itemText}`,dragSelectedItems:(e,a)=>`Dra ${a.plural(e.count,{one:()=>`${a.number(e.count)} valt objekt`,other:()=>`${a.number(e.count)} valda objekt`})}`,dragSelectedKeyboard:(e,a)=>`Tryck p\xe5 Retur f\xf6r att dra ${a.plural(e.count,{one:()=>`${a.number(e.count)} markerat objekt`,other:()=>`${a.number(e.count)} markerade objekt`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Tryck p\xe5 Alt + Retur f\xf6r att dra ${a.plural(e.count,{one:()=>`${a.number(e.count)} markerat objekt`,other:()=>`${a.number(e.count)} markerade objekt`})}.`,dragSelectedLongPress:(e,a)=>`Tryck l\xe4nge f\xf6r att dra ${a.plural(e.count,{one:()=>`${a.number(e.count)} markerat objekt`,other:()=>`${a.number(e.count)} markerade objekt`})}.`,dragStartedKeyboard:`B\xf6rja dra. Tryck p\xe5 tabb f\xf6r att navigera till m\xe5let, tryck p\xe5 enter f\xf6r att sl\xe4ppa eller p\xe5 escape f\xf6r att avbryta.`,dragStartedTouch:`B\xf6rja dra. Navigera till ett m\xe5l och dubbeltryck f\xf6r att sl\xe4ppa.`,dragStartedVirtual:`B\xf6rja dra. Navigera till ett m\xe5l och klicka eller tryck p\xe5 enter f\xf6r att sl\xe4ppa.`,dropCanceled:`Sl\xe4pp\xe5tg\xe4rd avbr\xf6ts.`,dropComplete:`Sl\xe4pp\xe5tg\xe4rd klar.`,dropDescriptionKeyboard:`Tryck p\xe5 enter f\xf6r att sl\xe4ppa. Tryck p\xe5 escape f\xf6r att avbryta drag\xe5tg\xe4rd.`,dropDescriptionTouch:`Dubbeltryck f\xf6r att sl\xe4ppa.`,dropDescriptionVirtual:`Klicka f\xf6r att sl\xe4ppa.`,dropIndicator:`sl\xe4ppindikator`,dropOnItem:e=>`Sl\xe4pp p\xe5 ${e.itemText}`,dropOnRoot:`Sl\xe4pp p\xe5`,endDragKeyboard:`Drar. Tryck p\xe5 enter f\xf6r att avbryta drag\xe5tg\xe4rd.`,endDragTouch:`Drar. Dubbeltryck f\xf6r att avbryta drag\xe5tg\xe4rd.`,endDragVirtual:`Drar. Klicka f\xf6r att avbryta drag\xe5tg\xe4rd.`,insertAfter:e=>`Infoga efter ${e.itemText}`,insertBefore:e=>`Infoga f\xf6re ${e.itemText}`,insertBetween:e=>`Infoga mellan ${e.beforeItemText} och ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} ej markerat.`,longPressToSelect:`Tryck l\xe4nge n\xe4r du vill \xf6ppna v\xe4ljarl\xe4ge.`,select:"Markera",selectedAll:"Alla markerade objekt.",selectedCount:(e,a)=>`${a.plural(e.count,{"=0":"Inga markerade objekt",one:()=>`${a.number(e.count)} markerat objekt`,other:()=>`${a.number(e.count)} markerade objekt`})}.`,selectedItem:e=>`${e.item} markerat.`},"@react-aria/gridlist":{hasActionAnnouncement:`rad har \xe5tg\xe4rd`,hasLinkAnnouncement:e=>`rad har l\xe4nk: ${e.link}`},"@react-aria/menu":{longPressMessage:`H\xe5ll nedtryckt eller tryck p\xe5 Alt + pil ned\xe5t f\xf6r att \xf6ppna menyn`},"@react-aria/numberfield":{decrease:e=>`Minska ${e.fieldLabel}`,increase:e=>`\xd6ka ${e.fieldLabel}`,numberField:`Nummerf\xe4lt`},"@react-aria/overlays":{dismiss:"Avvisa"},"@react-aria/searchfield":{"Clear search":`Rensa s\xf6kning`},"@react-aria/spinbutton":{Empty:"Tomt"},"@react-aria/steplist":{steplist:"Steglista"},"@react-aria/table":{ascending:"stigande",ascendingSort:e=>`sorterat p\xe5 kolumn ${e.columnName} i stigande ordning`,columnSize:e=>`${e.value} pixlar`,descending:"fallande",descendingSort:e=>`sorterat p\xe5 kolumn ${e.columnName} i fallande ordning`,resizerDescription:`Tryck p\xe5 Retur f\xf6r att b\xf6rja \xe4ndra storlek`,select:"Markera",selectAll:"Markera allt",sortable:"sorterbar kolumn"},"@react-aria/tag":{removeButtonLabel:"Ta bort",removeDescription:`Tryck p\xe5 Radera f\xf6r att ta bort taggen.`},"@react-aria/toast":{close:`St\xe4ng`,notifications:(e,a)=>`${a.plural(e.count,{one:()=>`${a.number(e.count)} meddelande`,other:()=>`${a.number(e.count)} meddelanden`})}.`},"@react-aria/tree":{collapse:`D\xf6lj`,expand:"Expandera"},"@react-stately/color":{alpha:"Alfa",black:"svart",blue:`Bl\xe5tt`,"blue purple":`bl\xe5lila`,brightness:"Ljusstyrka",brown:"brun","brown yellow":"brungul",colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:"cyan","cyan blue":`cyanbl\xe5`,dark:`m\xf6rk`,gray:`gr\xe5`,grayish:`gr\xe5aktig`,green:`Gr\xf6nt`,"green cyan":`gr\xf6n cyan`,hue:"Nyans",light:"ljus",lightness:"Ljushet",magenta:"magenta","magenta pink":"magentarosa",orange:"orange","orange yellow":"orangegul",pale:"blek",pink:"rosa","pink red":`rosar\xf6d`,purple:"lila","purple magenta":"lila magenta",red:`R\xf6tt`,"red orange":`r\xf6dorange`,saturation:`M\xe4ttnad`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} genomskinlig`,"very dark":`mycket m\xf6rk`,"very light":"mycket ljus",vibrant:"livfull",white:"vit",yellow:"gul","yellow green":`gulgr\xf6n`},"@react-stately/datepicker":{rangeOverflow:e=>`V\xe4rdet m\xe5ste vara ${e.maxValue} eller tidigare.`,rangeReversed:`Startdatumet m\xe5ste vara f\xf6re slutdatumet.`,rangeUnderflow:e=>`V\xe4rdet m\xe5ste vara ${e.minValue} eller senare.`,unavailableDate:`Det valda datumet \xe4r inte tillg\xe4ngligt.`}};