export default{"@react-aria/autocomplete":{collectionLabel:`建议`},"@react-aria/breadcrumbs":{breadcrumbs:`导航栏`},"@react-aria/calendar":{dateRange:e=>`${e.startDate} 至 ${e.endDate}`,dateSelected:e=>`已选择 ${e.date}`,finishRangeSelectionPrompt:`单击以完成选择日期范围`,maximumDate:`最后一个可用日期`,minimumDate:`第一个可用日期`,next:`下一页`,previous:`上一页`,selectedDateDescription:e=>`选定的日期：${e.date}`,selectedRangeDescription:e=>`选定的范围：${e.dateRange}`,startRangeSelectionPrompt:`单击以开始选择日期范围`,todayDate:e=>`今天，即 ${e.date}`,todayDateSelected:e=>`已选择今天，即 ${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}、${e.channelLabel}`,colorNameAndValue:e=>`${e.name}：${e.value}`,colorPicker:`拾色器`,colorSwatch:`颜色色板`,transparent:`透明`,twoDimensionalSlider:`2D 滑块`},"@react-aria/combobox":{buttonLabel:`显示建议`,countAnnouncement:(e,t)=>`有 ${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} 个选项`,other:()=>`${t.number(e.optionCount)} 个选项`})}可用。`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`进入了 ${e.groupTitle} 组，其中有 ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} 个选项`,other:()=>`${t.number(e.groupCount)} 个选项`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:`, 已选择`,other:""},e.isSelected)}`,listboxLabel:`建议`,selectedAnnouncement:e=>`${e.optionText}, 已选择`},"@react-aria/datepicker":{calendar:`日历`,day:`日`,dayPeriod:`上午/下午`,endDate:`结束日期`,era:`纪元`,hour:`小时`,minute:`分钟`,month:`月`,second:`秒`,selectedDateDescription:e=>`选定的日期：${e.date}`,selectedRangeDescription:e=>`选定的范围：${e.startDate} 至 ${e.endDate}`,selectedTimeDescription:e=>`选定的时间：${e.time}`,startDate:`开始日期`,timeZoneName:`时区`,weekday:`工作日`,year:`年`},"@react-aria/dnd":{dragDescriptionKeyboard:`按 Enter 开始拖动。`,dragDescriptionKeyboardAlt:`按 Alt + Enter 开始拖动。`,dragDescriptionLongPress:`长按以开始拖动。`,dragDescriptionTouch:`双击开始拖动。`,dragDescriptionVirtual:`单击开始拖动。`,dragItem:e=>`拖动 ${e.itemText}`,dragSelectedItems:(e,t)=>`拖动 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 选中项目`,other:()=>`${t.number(e.count)} 选中项目`})}`,dragSelectedKeyboard:(e,t)=>`按 Enter 以拖动 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 个选定项`,other:()=>`${t.number(e.count)} 个选定项`})}。`,dragSelectedKeyboardAlt:(e,t)=>`按 Alt + Enter 以拖动 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 个选定项`,other:()=>`${t.number(e.count)} 个选定项`})}。`,dragSelectedLongPress:(e,t)=>`长按以拖动 ${t.plural(e.count,{one:()=>`${t.number(e.count)} 个选定项`,other:()=>`${t.number(e.count)} 个选定项`})}。`,dragStartedKeyboard:`已开始拖动。按 Tab 导航到放置目标，然后按 Enter 放置或按 Escape 取消。`,dragStartedTouch:`已开始拖动。导航到放置目标，然后双击放置。`,dragStartedVirtual:`已开始拖动。导航到放置目标，然后单击或按 Enter 放置。`,dropCanceled:`放置已取消。`,dropComplete:`放置已完成。`,dropDescriptionKeyboard:`按 Enter 放置。按 Escape 取消拖动。`,dropDescriptionTouch:`双击放置。`,dropDescriptionVirtual:`单击放置。`,dropIndicator:`放置标记`,dropOnItem:e=>`放置于 ${e.itemText}`,dropOnRoot:`放置于`,endDragKeyboard:`正在拖动。按 Enter 取消拖动。`,endDragTouch:`正在拖动。双击取消拖动。`,endDragVirtual:`正在拖动。单击取消拖动。`,insertAfter:e=>`插入到 ${e.itemText} 之后`,insertBefore:e=>`插入到 ${e.itemText} 之前`,insertBetween:e=>`插入到 ${e.beforeItemText} 和 ${e.afterItemText} 之间`},"@react-aria/grid":{deselectedItem:e=>`未选择 ${e.item}。`,longPressToSelect:`长按以进入选择模式。`,select:`选择`,selectedAll:`已选择所有项目。`,selectedCount:(e,t)=>`${t.plural(e.count,{"=0":`未选择项目`,one:()=>`已选择 ${t.number(e.count)} 个项目`,other:()=>`已选择 ${t.number(e.count)} 个项目`})}。`,selectedItem:e=>`已选择 ${e.item}。`},"@react-aria/gridlist":{hasActionAnnouncement:`行有操作`,hasLinkAnnouncement:e=>`行有链接：${e.link}`},"@react-aria/menu":{longPressMessage:`长按或按 Alt + 向下方向键以打开菜单`},"@react-aria/numberfield":{decrease:e=>`降低 ${e.fieldLabel}`,increase:e=>`提高 ${e.fieldLabel}`,numberField:`数字字段`},"@react-aria/overlays":{dismiss:`取消`},"@react-aria/searchfield":{"Clear search":`清除搜索`},"@react-aria/spinbutton":{Empty:`空`},"@react-aria/steplist":{steplist:`步骤列表`},"@react-aria/table":{ascending:`升序`,ascendingSort:e=>`按列 ${e.columnName} 升序排序`,columnSize:e=>`${e.value} 像素`,descending:`降序`,descendingSort:e=>`按列 ${e.columnName} 降序排序`,resizerDescription:`按“输入”键开始调整大小。`,select:`选择`,selectAll:`全选`,sortable:`可排序的列`},"@react-aria/tag":{removeButtonLabel:`删除`,removeDescription:`按下“删除”以删除标记。`},"@react-aria/toast":{close:`关闭`,notifications:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)} 个通知`,other:()=>`${t.number(e.count)} 个通知`})}。`},"@react-aria/tree":{collapse:`折叠`,expand:`扩展`},"@react-stately/color":{alpha:"Alpha",black:`黑色`,blue:`蓝色`,"blue purple":`蓝紫色`,brightness:`亮度`,brown:`棕色的`,"brown yellow":`棕黄色`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`蓝绿色`,"cyan blue":`青蓝色`,dark:`深色`,gray:`灰色`,grayish:`浅灰色的`,green:`绿色`,"green cyan":`绿青色`,hue:`色相`,light:`浅色`,lightness:`明亮度`,magenta:`紫红色`,"magenta pink":`紫粉色`,orange:`橙色`,"orange yellow":`橙黄色`,pale:`苍白的`,pink:`粉色`,"pink red":`粉红色`,purple:`紫色`,"purple magenta":`紫洋红色`,red:`红色`,"red orange":`红橙色`,saturation:`饱和度`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} 透明`,"very dark":`很暗`,"very light":`很浅`,vibrant:`生机勃勃`,white:`白色`,yellow:`黄色`,"yellow green":`黄色/绿色`},"@react-stately/datepicker":{rangeOverflow:e=>`值必须是 ${e.maxValue} 或更早日期。`,rangeReversed:`开始日期必须早于结束日期。`,rangeUnderflow:e=>`值必须是 ${e.minValue} 或更晚日期。`,unavailableDate:`所选日期不可用。`}};