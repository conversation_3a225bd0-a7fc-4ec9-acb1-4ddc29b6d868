import {CollectionDocumentContext as $7135fc7d473fd974$export$8c25dea96356a8b6, CollectionPortal as $7135fc7d473fd974$export$813b5978dd974d8, useCachedChildren as $7135fc7d473fd974$export$727c8fc270210f13, useCollection as $7135fc7d473fd974$export$6cd28814d92fa9c9, useSSRCollectionNode as $7135fc7d473fd974$export$e7c29ae2353b16ea} from "./Collection.module.js";
import {HiddenContext as $64fa3d84918910a7$export$94b6d0abf7d33e8c, Provider as $64fa3d84918910a7$export$2881499e37b75b9a, useContextProps as $64fa3d84918910a7$export$29f1550f4b0d4415, useRenderProps as $64fa3d84918910a7$export$4d86445c2cf5e3, useSlot as $64fa3d84918910a7$export$9d4c57ee4c6ffdd8} from "./utils.module.js";
import {DragAndDropContext as $d8f176866e6dc039$export$d188a835a7bc5783, DropIndicator as $d8f176866e6dc039$export$62ed72bc21f6b8a6, DropIndicatorContext as $d8f176866e6dc039$export$f55761759794cf55} from "./useDragAndDrop.module.js";
import {Header as $72a5793c14baf454$export$8b251419efc915eb} from "./Header.module.js";
import {Separator as $431f98aba6844401$export$1ff3c3f08ae963c0, SeparatorContext as $431f98aba6844401$export$6615d83f6de245ce} from "./Separator.module.js";
import {TextContext as $514c0188e459b4c0$export$9afb8bc826b033ea} from "./Text.module.js";
import {useLocale as $e8Bmu$useLocale, useCollator as $e8Bmu$useCollator, ListKeyboardDelegate as $e8Bmu$ListKeyboardDelegate, useListBox as $e8Bmu$useListBox, useFocusRing as $e8Bmu$useFocusRing, FocusScope as $e8Bmu$FocusScope, mergeProps as $e8Bmu$mergeProps, useListBoxSection as $e8Bmu$useListBoxSection, useOption as $e8Bmu$useOption, useHover as $e8Bmu$useHover} from "react-aria";
import {useListState as $e8Bmu$useListState} from "react-stately";
import {filterDOMProps as $e8Bmu$filterDOMProps, mergeRefs as $e8Bmu$mergeRefs, useObjectRef as $e8Bmu$useObjectRef} from "@react-aria/utils";
import $e8Bmu$react, {createContext as $e8Bmu$createContext, useContext as $e8Bmu$useContext, forwardRef as $e8Bmu$forwardRef, useMemo as $e8Bmu$useMemo, useRef as $e8Bmu$useRef, useEffect as $e8Bmu$useEffect} from "react";

/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 









const $eed445e0843c11d0$export$7ff8f37d2d81a48d = /*#__PURE__*/ (0, $e8Bmu$createContext)(null);
const $eed445e0843c11d0$export$7c5906fe4f1f2af2 = /*#__PURE__*/ (0, $e8Bmu$createContext)(null);
function $eed445e0843c11d0$var$ListBox(props, ref) {
    [props, ref] = (0, $64fa3d84918910a7$export$29f1550f4b0d4415)(props, ref, $eed445e0843c11d0$export$7ff8f37d2d81a48d);
    let isHidden = (0, $e8Bmu$useContext)((0, $64fa3d84918910a7$export$94b6d0abf7d33e8c));
    let state = (0, $e8Bmu$useContext)($eed445e0843c11d0$export$7c5906fe4f1f2af2);
    let document = (0, $e8Bmu$useContext)((0, $7135fc7d473fd974$export$8c25dea96356a8b6));
    // The structure of ListBox is a bit strange because it needs to work inside other components like ComboBox and Select.
    // Those components render two copies of their children so that the collection can be built even when the popover is closed.
    // The first copy sends a collection document via context which we render the collection portal into.
    // The second copy sends a ListState object via context which we use to render the ListBox without rebuilding the state.
    // Otherwise, we have a standalone ListBox, so we need to create a collection and state ourselves.
    if (document) return /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $7135fc7d473fd974$export$813b5978dd974d8), props);
    if (state) return isHidden ? null : /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$ListBoxInner, {
        state: state,
        props: props,
        listBoxRef: ref
    });
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$StandaloneListBox, {
        props: props,
        listBoxRef: ref
    });
}
function $eed445e0843c11d0$var$StandaloneListBox({ props: props, listBoxRef: listBoxRef }) {
    let { portal: portal, collection: collection } = (0, $7135fc7d473fd974$export$6cd28814d92fa9c9)(props);
    props = {
        ...props,
        collection: collection,
        children: null,
        items: null
    };
    let state = (0, $e8Bmu$useListState)(props);
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $e8Bmu$react).Fragment, null, portal, /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$ListBoxInner, {
        state: state,
        props: props,
        listBoxRef: listBoxRef
    }));
}
/**
 * A listbox displays a list of options and allows a user to select one or more of them.
 */ const $eed445e0843c11d0$export$41f133550aa26f48 = /*#__PURE__*/ (0, $e8Bmu$forwardRef)($eed445e0843c11d0$var$ListBox);
function $eed445e0843c11d0$var$ListBoxInner({ state: state, props: props, listBoxRef: listBoxRef }) {
    let { dragAndDropHooks: dragAndDropHooks, layout: layout = 'stack', orientation: orientation = 'vertical' } = props;
    let { collection: collection, selectionManager: selectionManager } = state;
    let isListDraggable = !!(dragAndDropHooks === null || dragAndDropHooks === void 0 ? void 0 : dragAndDropHooks.useDraggableCollectionState);
    let isListDroppable = !!(dragAndDropHooks === null || dragAndDropHooks === void 0 ? void 0 : dragAndDropHooks.useDroppableCollectionState);
    let { direction: direction } = (0, $e8Bmu$useLocale)();
    let { disabledBehavior: disabledBehavior, disabledKeys: disabledKeys } = selectionManager;
    let collator = (0, $e8Bmu$useCollator)({
        usage: 'search',
        sensitivity: 'base'
    });
    let keyboardDelegate = (0, $e8Bmu$useMemo)(()=>props.keyboardDelegate || new (0, $e8Bmu$ListKeyboardDelegate)({
            collection: collection,
            collator: collator,
            ref: listBoxRef,
            disabledKeys: disabledKeys,
            disabledBehavior: disabledBehavior,
            layout: layout,
            orientation: orientation,
            direction: direction
        }), [
        collection,
        collator,
        listBoxRef,
        disabledBehavior,
        disabledKeys,
        orientation,
        direction,
        props.keyboardDelegate,
        layout
    ]);
    let { listBoxProps: listBoxProps } = (0, $e8Bmu$useListBox)({
        ...props,
        shouldSelectOnPressUp: isListDraggable || props.shouldSelectOnPressUp,
        keyboardDelegate: keyboardDelegate
    }, state, listBoxRef);
    let children = (0, $7135fc7d473fd974$export$727c8fc270210f13)({
        items: collection,
        children: (item)=>{
            switch(item.type){
                case 'section':
                    return /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$ListBoxSection, {
                        section: item
                    });
                case 'separator':
                    return /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $431f98aba6844401$export$1ff3c3f08ae963c0), item.props);
                case 'item':
                    return /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$Option, {
                        item: item
                    });
                default:
                    throw new Error('Unsupported node type in Menu: ' + item.type);
            }
        }
    });
    let dragHooksProvided = (0, $e8Bmu$useRef)(isListDraggable);
    let dropHooksProvided = (0, $e8Bmu$useRef)(isListDroppable);
    (0, $e8Bmu$useEffect)(()=>{
        if (dragHooksProvided.current !== isListDraggable) console.warn('Drag hooks were provided during one render, but not another. This should be avoided as it may produce unexpected behavior.');
        if (dropHooksProvided.current !== isListDroppable) console.warn('Drop hooks were provided during one render, but not another. This should be avoided as it may produce unexpected behavior.');
    }, [
        isListDraggable,
        isListDroppable
    ]);
    let dragState = undefined;
    let dropState = undefined;
    let droppableCollection = undefined;
    let isRootDropTarget = false;
    let dragPreview = null;
    let preview = (0, $e8Bmu$useRef)(null);
    if (isListDraggable && dragAndDropHooks) {
        dragState = dragAndDropHooks.useDraggableCollectionState({
            collection: collection,
            selectionManager: selectionManager,
            preview: dragAndDropHooks.renderDragPreview ? preview : undefined
        });
        dragAndDropHooks.useDraggableCollection({}, dragState, listBoxRef);
        let DragPreview = dragAndDropHooks.DragPreview;
        dragPreview = dragAndDropHooks.renderDragPreview ? /*#__PURE__*/ (0, $e8Bmu$react).createElement(DragPreview, {
            ref: preview
        }, dragAndDropHooks.renderDragPreview) : null;
    }
    if (isListDroppable && dragAndDropHooks) {
        dropState = dragAndDropHooks.useDroppableCollectionState({
            collection: collection,
            selectionManager: selectionManager
        });
        let dropTargetDelegate = dragAndDropHooks.dropTargetDelegate || new dragAndDropHooks.ListDropTargetDelegate(collection, listBoxRef, {
            orientation: orientation,
            layout: layout,
            direction: direction
        });
        droppableCollection = dragAndDropHooks.useDroppableCollection({
            keyboardDelegate: keyboardDelegate,
            dropTargetDelegate: dropTargetDelegate
        }, dropState, listBoxRef);
        isRootDropTarget = dropState.isDropTarget({
            type: 'root'
        });
    }
    let { focusProps: focusProps, isFocused: isFocused, isFocusVisible: isFocusVisible } = (0, $e8Bmu$useFocusRing)();
    let renderValues = {
        isDropTarget: isRootDropTarget,
        isEmpty: state.collection.size === 0,
        isFocused: isFocused,
        isFocusVisible: isFocusVisible,
        layout: props.layout || 'stack',
        state: state
    };
    let renderProps = (0, $64fa3d84918910a7$export$4d86445c2cf5e3)({
        className: props.className,
        style: props.style,
        defaultClassName: 'react-aria-ListBox',
        values: renderValues
    });
    let emptyState = null;
    if (state.collection.size === 0 && props.renderEmptyState) emptyState = /*#__PURE__*/ (0, $e8Bmu$react).createElement("div", {
        // eslint-disable-next-line
        role: "option",
        style: {
            display: 'contents'
        }
    }, props.renderEmptyState(renderValues));
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $e8Bmu$FocusScope), null, /*#__PURE__*/ (0, $e8Bmu$react).createElement("div", {
        ...(0, $e8Bmu$filterDOMProps)(props),
        ...(0, $e8Bmu$mergeProps)(listBoxProps, focusProps, droppableCollection === null || droppableCollection === void 0 ? void 0 : droppableCollection.collectionProps),
        ...renderProps,
        ref: listBoxRef,
        slot: props.slot || undefined,
        onScroll: props.onScroll,
        "data-drop-target": isRootDropTarget || undefined,
        "data-empty": state.collection.size === 0 || undefined,
        "data-focused": isFocused || undefined,
        "data-focus-visible": isFocusVisible || undefined,
        "data-layout": props.layout || 'stack',
        "data-orientation": props.orientation || 'vertical'
    }, /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $64fa3d84918910a7$export$2881499e37b75b9a), {
        values: [
            [
                $eed445e0843c11d0$export$7ff8f37d2d81a48d,
                props
            ],
            [
                $eed445e0843c11d0$export$7c5906fe4f1f2af2,
                state
            ],
            [
                (0, $d8f176866e6dc039$export$d188a835a7bc5783),
                {
                    dragAndDropHooks: dragAndDropHooks,
                    dragState: dragState,
                    dropState: dropState
                }
            ],
            [
                (0, $431f98aba6844401$export$6615d83f6de245ce),
                {
                    elementType: 'div'
                }
            ],
            [
                (0, $d8f176866e6dc039$export$f55761759794cf55),
                {
                    render: $eed445e0843c11d0$var$ListBoxDropIndicatorWrapper
                }
            ]
        ]
    }, children), emptyState, dragPreview));
}
function $eed445e0843c11d0$var$ListBoxSection({ section: section, className: className, style: style }) {
    var _section_props, _section_props1;
    let state = (0, $e8Bmu$useContext)($eed445e0843c11d0$export$7c5906fe4f1f2af2);
    let [headingRef, heading] = (0, $64fa3d84918910a7$export$9d4c57ee4c6ffdd8)();
    var _section_props_arialabel;
    let { headingProps: headingProps, groupProps: groupProps } = (0, $e8Bmu$useListBoxSection)({
        heading: heading,
        'aria-label': (_section_props_arialabel = section.props['aria-label']) !== null && _section_props_arialabel !== void 0 ? _section_props_arialabel : undefined
    });
    let children = (0, $7135fc7d473fd974$export$727c8fc270210f13)({
        items: state.collection.getChildren(section.key),
        children: (item)=>{
            switch(item.type){
                case 'header':
                    return /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$SectionHeader, {
                        item: item,
                        headingProps: headingProps,
                        headingRef: headingRef
                    });
                case 'item':
                    return /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$Option, {
                        item: item
                    });
                default:
                    throw new Error('Unsupported element type in Section: ' + item.type);
            }
        }
    });
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement("section", {
        ...(0, $e8Bmu$filterDOMProps)(section.props),
        ...groupProps,
        className: className || ((_section_props = section.props) === null || _section_props === void 0 ? void 0 : _section_props.className) || 'react-aria-Section',
        style: style || ((_section_props1 = section.props) === null || _section_props1 === void 0 ? void 0 : _section_props1.style),
        ref: section.props.ref
    }, children);
}
// This is a separate component so that headingProps.id doesn't override the item key in useCachedChildren.
function $eed445e0843c11d0$var$SectionHeader({ item: item, headingProps: headingProps, headingRef: headingRef }) {
    let { ref: ref, ...otherProps } = item.props;
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $72a5793c14baf454$export$8b251419efc915eb), {
        ...headingProps,
        ...otherProps,
        ref: (0, $e8Bmu$mergeRefs)(headingRef, ref)
    }, item.rendered);
}
function $eed445e0843c11d0$var$ListBoxItem(props, ref) {
    return (0, $7135fc7d473fd974$export$e7c29ae2353b16ea)('item', props, ref, props.children);
}
/**
 * A ListBoxItem represents an individual option in a ListBox.
 */ const $eed445e0843c11d0$export$a11e76429ed99b4 = /*#__PURE__*/ (0, $e8Bmu$forwardRef)($eed445e0843c11d0$var$ListBoxItem);
function $eed445e0843c11d0$var$Option({ item: item }) {
    var _item_props;
    let ref = (0, $e8Bmu$useObjectRef)(item.props.ref);
    let state = (0, $e8Bmu$useContext)($eed445e0843c11d0$export$7c5906fe4f1f2af2);
    let { dragAndDropHooks: dragAndDropHooks, dragState: dragState, dropState: dropState } = (0, $e8Bmu$useContext)((0, $d8f176866e6dc039$export$d188a835a7bc5783));
    let { optionProps: optionProps, labelProps: labelProps, descriptionProps: descriptionProps, ...states } = (0, $e8Bmu$useOption)({
        key: item.key,
        'aria-label': (_item_props = item.props) === null || _item_props === void 0 ? void 0 : _item_props['aria-label']
    }, state, ref);
    let { hoverProps: hoverProps, isHovered: isHovered } = (0, $e8Bmu$useHover)({
        isDisabled: !states.allowsSelection && !states.hasAction,
        onHoverStart: item.props.onHoverStart,
        onHoverChange: item.props.onHoverChange,
        onHoverEnd: item.props.onHoverEnd
    });
    let draggableItem = null;
    if (dragState && dragAndDropHooks) draggableItem = dragAndDropHooks.useDraggableItem({
        key: item.key
    }, dragState);
    let droppableItem = null;
    if (dropState && dragAndDropHooks) droppableItem = dragAndDropHooks.useDroppableItem({
        target: {
            type: 'item',
            key: item.key,
            dropPosition: 'on'
        }
    }, dropState, ref);
    let props = item.props;
    let isDragging = dragState && dragState.isDragging(item.key);
    let renderProps = (0, $64fa3d84918910a7$export$4d86445c2cf5e3)({
        ...props,
        id: undefined,
        children: item.rendered,
        defaultClassName: 'react-aria-ListBoxItem',
        values: {
            ...states,
            isHovered: isHovered,
            selectionMode: state.selectionManager.selectionMode,
            selectionBehavior: state.selectionManager.selectionBehavior,
            allowsDragging: !!dragState,
            isDragging: isDragging,
            isDropTarget: droppableItem === null || droppableItem === void 0 ? void 0 : droppableItem.isDropTarget
        }
    });
    let renderDropIndicator = (dragAndDropHooks === null || dragAndDropHooks === void 0 ? void 0 : dragAndDropHooks.renderDropIndicator) || ((target)=>/*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $d8f176866e6dc039$export$62ed72bc21f6b8a6), {
            target: target
        }));
    (0, $e8Bmu$useEffect)(()=>{
        if (!item.textValue) console.warn('A `textValue` prop is required for <ListBoxItem> elements with non-plain text children in order to support accessibility features such as type to select.');
    }, [
        item.textValue
    ]);
    let ElementType = props.href ? 'a' : 'div';
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $e8Bmu$react).Fragment, null, (dragAndDropHooks === null || dragAndDropHooks === void 0 ? void 0 : dragAndDropHooks.useDropIndicator) && renderDropIndicator({
        type: 'item',
        key: item.key,
        dropPosition: 'before'
    }), /*#__PURE__*/ (0, $e8Bmu$react).createElement(ElementType, {
        ...(0, $e8Bmu$mergeProps)(optionProps, hoverProps, draggableItem === null || draggableItem === void 0 ? void 0 : draggableItem.dragProps, droppableItem === null || droppableItem === void 0 ? void 0 : droppableItem.dropProps),
        ...renderProps,
        ref: ref,
        "data-allows-dragging": !!dragState || undefined,
        "data-selected": states.isSelected || undefined,
        "data-disabled": states.isDisabled || undefined,
        "data-hovered": isHovered || undefined,
        "data-focused": states.isFocused || undefined,
        "data-focus-visible": states.isFocusVisible || undefined,
        "data-pressed": states.isPressed || undefined,
        "data-dragging": isDragging || undefined,
        "data-drop-target": (droppableItem === null || droppableItem === void 0 ? void 0 : droppableItem.isDropTarget) || undefined,
        "data-selection-mode": state.selectionManager.selectionMode === 'none' ? undefined : state.selectionManager.selectionMode
    }, /*#__PURE__*/ (0, $e8Bmu$react).createElement((0, $64fa3d84918910a7$export$2881499e37b75b9a), {
        values: [
            [
                (0, $514c0188e459b4c0$export$9afb8bc826b033ea),
                {
                    slots: {
                        label: labelProps,
                        description: descriptionProps
                    }
                }
            ]
        ]
    }, renderProps.children)), (dragAndDropHooks === null || dragAndDropHooks === void 0 ? void 0 : dragAndDropHooks.useDropIndicator) && state.collection.getKeyAfter(item.key) == null && renderDropIndicator({
        type: 'item',
        key: item.key,
        dropPosition: 'after'
    }));
}
function $eed445e0843c11d0$var$ListBoxDropIndicatorWrapper(props, ref) {
    ref = (0, $e8Bmu$useObjectRef)(ref);
    let { dragAndDropHooks: dragAndDropHooks, dropState: dropState } = (0, $e8Bmu$useContext)((0, $d8f176866e6dc039$export$d188a835a7bc5783));
    let { dropIndicatorProps: dropIndicatorProps, isHidden: isHidden, isDropTarget: isDropTarget } = dragAndDropHooks.useDropIndicator(props, dropState, ref);
    if (isHidden) return null;
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement($eed445e0843c11d0$var$ListBoxDropIndicatorForwardRef, {
        ...props,
        dropIndicatorProps: dropIndicatorProps,
        isDropTarget: isDropTarget,
        ref: ref
    });
}
function $eed445e0843c11d0$var$ListBoxDropIndicator(props, ref) {
    let { dropIndicatorProps: dropIndicatorProps, isDropTarget: isDropTarget, ...otherProps } = props;
    let renderProps = (0, $64fa3d84918910a7$export$4d86445c2cf5e3)({
        ...otherProps,
        defaultClassName: 'react-aria-DropIndicator',
        values: {
            isDropTarget: isDropTarget
        }
    });
    return /*#__PURE__*/ (0, $e8Bmu$react).createElement("div", {
        ...dropIndicatorProps,
        ...renderProps,
        // eslint-disable-next-line
        role: "option",
        ref: ref,
        "data-drop-target": isDropTarget || undefined
    });
}
const $eed445e0843c11d0$var$ListBoxDropIndicatorForwardRef = /*#__PURE__*/ (0, $e8Bmu$forwardRef)($eed445e0843c11d0$var$ListBoxDropIndicator);


export {$eed445e0843c11d0$export$7ff8f37d2d81a48d as ListBoxContext, $eed445e0843c11d0$export$7c5906fe4f1f2af2 as ListStateContext, $eed445e0843c11d0$export$41f133550aa26f48 as ListBox, $eed445e0843c11d0$export$a11e76429ed99b4 as ListBoxItem};
//# sourceMappingURL=ListBox.module.js.map
