import {useCachedChildren as $7135fc7d473fd974$export$727c8fc270210f13, useCollection as $7135fc7d473fd974$export$6cd28814d92fa9c9, useSSRCollectionNode as $7135fc7d473fd974$export$e7c29ae2353b16ea} from "./Collection.module.js";
import {Provider as $64fa3d84918910a7$export$2881499e37b75b9a, useContextProps as $64fa3d84918910a7$export$29f1550f4b0d4415, useRenderProps as $64fa3d84918910a7$export$4d86445c2cf5e3, useSlot as $64fa3d84918910a7$export$9d4c57ee4c6ffdd8, useSlottedContext as $64fa3d84918910a7$export$fabf2dc03a41866e} from "./utils.module.js";
import {Header as $72a5793c14baf454$export$8b251419efc915eb} from "./Header.module.js";
import {KeyboardContext as $63df2425e2108aa8$export$744d98a3b8a94e1c} from "./Keyboard.module.js";
import {OverlayTriggerStateContext as $de32f1b87079253c$export$d2f961adcb0afbe} from "./Dialog.module.js";
import {PopoverContext as $07b14b47974efb58$export$9b9a0cd73afb7ca4} from "./Popover.module.js";
import {Separator as $431f98aba6844401$export$1ff3c3f08ae963c0, SeparatorContext as $431f98aba6844401$export$6615d83f6de245ce} from "./Separator.module.js";
import {TextContext as $514c0188e459b4c0$export$9afb8bc826b033ea} from "./Text.module.js";
import {useMenuTrigger as $kM2ZM$useMenuTrigger, useMenu as $kM2ZM$useMenu, FocusScope as $kM2ZM$FocusScope, useMenuSection as $kM2ZM$useMenuSection, useMenuItem as $kM2ZM$useMenuItem, useFocusRing as $kM2ZM$useFocusRing, mergeProps as $kM2ZM$mergeProps} from "react-aria";
import {useMenuTriggerState as $kM2ZM$useMenuTriggerState, useTreeState as $kM2ZM$useTreeState} from "react-stately";
import {useResizeObserver as $kM2ZM$useResizeObserver, filterDOMProps as $kM2ZM$filterDOMProps, mergeRefs as $kM2ZM$mergeRefs, useObjectRef as $kM2ZM$useObjectRef} from "@react-aria/utils";
import {PressResponder as $kM2ZM$PressResponder, useInteractOutside as $kM2ZM$useInteractOutside, useHover as $kM2ZM$useHover} from "@react-aria/interactions";
import $kM2ZM$react, {createContext as $kM2ZM$createContext, useRef as $kM2ZM$useRef, useState as $kM2ZM$useState, useCallback as $kM2ZM$useCallback, useContext as $kM2ZM$useContext, useEffect as $kM2ZM$useEffect, forwardRef as $kM2ZM$forwardRef} from "react";
import {useSubmenuTriggerState as $kM2ZM$useSubmenuTriggerState} from "@react-stately/menu";
import {useSubmenuTrigger as $kM2ZM$useSubmenuTrigger} from "@react-aria/menu";

/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 














const $3674c52c6b3c5bce$export$c7e742effb1c51e2 = /*#__PURE__*/ (0, $kM2ZM$createContext)(null);
const $3674c52c6b3c5bce$export$24aad8519b95b41b = /*#__PURE__*/ (0, $kM2ZM$createContext)(null);
const $3674c52c6b3c5bce$export$795aec4671cbae19 = /*#__PURE__*/ (0, $kM2ZM$createContext)(null);
function $3674c52c6b3c5bce$export$27d2ad3c5815583e(props) {
    let state = (0, $kM2ZM$useMenuTriggerState)(props);
    let ref = (0, $kM2ZM$useRef)(null);
    let { menuTriggerProps: menuTriggerProps, menuProps: menuProps } = (0, $kM2ZM$useMenuTrigger)({
        ...props,
        type: 'menu'
    }, state, ref);
    // Allows menu width to match button
    let [buttonWidth, setButtonWidth] = (0, $kM2ZM$useState)(null);
    let onResize = (0, $kM2ZM$useCallback)(()=>{
        if (ref.current) setButtonWidth(ref.current.offsetWidth + 'px');
    }, [
        ref
    ]);
    (0, $kM2ZM$useResizeObserver)({
        ref: ref,
        onResize: onResize
    });
    return /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $64fa3d84918910a7$export$2881499e37b75b9a), {
        values: [
            [
                $3674c52c6b3c5bce$export$c7e742effb1c51e2,
                menuProps
            ],
            [
                (0, $de32f1b87079253c$export$d2f961adcb0afbe),
                state
            ],
            [
                $3674c52c6b3c5bce$export$795aec4671cbae19,
                state
            ],
            [
                (0, $07b14b47974efb58$export$9b9a0cd73afb7ca4),
                {
                    trigger: 'MenuTrigger',
                    triggerRef: ref,
                    placement: 'bottom start',
                    style: {
                        '--trigger-width': buttonWidth
                    }
                }
            ]
        ]
    }, /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $kM2ZM$PressResponder), {
        ...menuTriggerProps,
        ref: ref,
        isPressed: state.isOpen
    }, props.children));
}
function $3674c52c6b3c5bce$export$ecabc99eeffab7ca(props, ref) {
    return (0, $7135fc7d473fd974$export$e7c29ae2353b16ea)('submenutrigger', props, ref, props.children, props.children[0]);
}
function $3674c52c6b3c5bce$var$SubmenuTriggerInner(props) {
    let { item: item, parentMenuRef: parentMenuRef } = props;
    let state = (0, $kM2ZM$useContext)($3674c52c6b3c5bce$export$24aad8519b95b41b);
    let children = (0, $7135fc7d473fd974$export$727c8fc270210f13)({
        items: state.collection.getChildren(item.key),
        children: (childItem)=>{
            switch(childItem.type){
                case 'item':
                    return /*#__PURE__*/ (0, $kM2ZM$react).createElement($3674c52c6b3c5bce$var$MenuItemTriggerInner, {
                        item: childItem,
                        popover: item.rendered[1],
                        parentMenuRef: parentMenuRef,
                        delay: item.props.delay
                    });
                default:
                    throw new Error('Unsupported element type in SubmenuTrigger: ' + item.type);
            }
        }
    });
    return children;
}
function $3674c52c6b3c5bce$var$Menu(props, ref) {
    [props, ref] = (0, $64fa3d84918910a7$export$29f1550f4b0d4415)(props, ref, $3674c52c6b3c5bce$export$c7e742effb1c51e2);
    let { portal: portal, collection: collection } = (0, $7135fc7d473fd974$export$6cd28814d92fa9c9)(props);
    // Delay rendering the actual menu until we have the collection so that auto focus works properly.
    return /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $kM2ZM$react).Fragment, null, collection.size > 0 && /*#__PURE__*/ (0, $kM2ZM$react).createElement($3674c52c6b3c5bce$var$MenuInner, {
        props: props,
        collection: collection,
        menuRef: ref
    }), portal);
}
function $3674c52c6b3c5bce$var$MenuInner({ props: props, collection: collection, menuRef: ref }) {
    let state = (0, $kM2ZM$useTreeState)({
        ...props,
        collection: collection,
        children: undefined
    });
    let [popoverContainer, setPopoverContainer] = (0, $kM2ZM$useState)(null);
    let { menuProps: menuProps } = (0, $kM2ZM$useMenu)(props, state, ref);
    let rootMenuTriggerState = (0, $kM2ZM$useContext)($3674c52c6b3c5bce$export$795aec4671cbae19);
    let popoverContext = (0, $kM2ZM$useContext)((0, $07b14b47974efb58$export$9b9a0cd73afb7ca4));
    let children = (0, $7135fc7d473fd974$export$727c8fc270210f13)({
        items: state.collection,
        children: (item)=>{
            switch(item.type){
                case 'section':
                    return /*#__PURE__*/ (0, $kM2ZM$react).createElement($3674c52c6b3c5bce$var$MenuSection, {
                        section: item,
                        parentMenuRef: ref
                    });
                case 'separator':
                    return /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $431f98aba6844401$export$1ff3c3f08ae963c0), item.props);
                case 'item':
                    return /*#__PURE__*/ (0, $kM2ZM$react).createElement($3674c52c6b3c5bce$var$MenuItemInner, {
                        item: item
                    });
                case 'submenutrigger':
                    return /*#__PURE__*/ (0, $kM2ZM$react).createElement($3674c52c6b3c5bce$var$SubmenuTriggerInner, {
                        item: item,
                        parentMenuRef: ref
                    });
                default:
                    throw new Error('Unsupported node type in Menu: ' + item.type);
            }
        }
    });
    let isSubmenu = (popoverContext === null || popoverContext === void 0 ? void 0 : popoverContext.trigger) === 'SubmenuTrigger';
    (0, $kM2ZM$useInteractOutside)({
        ref: ref,
        onInteractOutside: (e)=>{
            if (rootMenuTriggerState && !(popoverContainer === null || popoverContainer === void 0 ? void 0 : popoverContainer.contains(e.target))) rootMenuTriggerState.close();
        },
        isDisabled: isSubmenu || (rootMenuTriggerState === null || rootMenuTriggerState === void 0 ? void 0 : rootMenuTriggerState.expandedKeysStack.length) === 0
    });
    let prevPopoverContainer = (0, $kM2ZM$useRef)(null);
    let [leftOffset, setLeftOffset] = (0, $kM2ZM$useState)({
        left: 0
    });
    (0, $kM2ZM$useEffect)(()=>{
        if (popoverContainer && prevPopoverContainer.current !== popoverContainer && leftOffset.left === 0) {
            prevPopoverContainer.current = popoverContainer;
            let { left: left } = popoverContainer.getBoundingClientRect();
            setLeftOffset({
                left: -1 * left
            });
        }
    }, [
        leftOffset,
        popoverContainer
    ]);
    var _props_className;
    return /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $kM2ZM$FocusScope), null, /*#__PURE__*/ (0, $kM2ZM$react).createElement("div", {
        ...(0, $kM2ZM$filterDOMProps)(props),
        ...menuProps,
        ref: ref,
        slot: props.slot || undefined,
        onScroll: props.onScroll,
        style: props.style,
        className: (_props_className = props.className) !== null && _props_className !== void 0 ? _props_className : 'react-aria-Menu'
    }, /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $64fa3d84918910a7$export$2881499e37b75b9a), {
        values: [
            [
                $3674c52c6b3c5bce$export$24aad8519b95b41b,
                state
            ],
            [
                (0, $431f98aba6844401$export$6615d83f6de245ce),
                {
                    elementType: 'div'
                }
            ],
            [
                (0, $07b14b47974efb58$export$9b9a0cd73afb7ca4),
                {
                    UNSTABLE_portalContainer: popoverContainer || undefined
                }
            ]
        ]
    }, children)), /*#__PURE__*/ (0, $kM2ZM$react).createElement("div", {
        ref: setPopoverContainer,
        style: {
            width: '100vw',
            position: 'absolute',
            top: 0,
            ...leftOffset
        }
    }));
}
/**
 * A menu displays a list of actions or options that a user can choose.
 */ const $3674c52c6b3c5bce$export$d9b273488cd8ce6f = /*#__PURE__*/ (0, $kM2ZM$forwardRef)($3674c52c6b3c5bce$var$Menu);
function $3674c52c6b3c5bce$var$MenuSection({ section: section, className: className, style: style, parentMenuRef: parentMenuRef, ...otherProps }) {
    var _section_props, _section_props1;
    let state = (0, $kM2ZM$useContext)($3674c52c6b3c5bce$export$24aad8519b95b41b);
    let [headingRef, heading] = (0, $64fa3d84918910a7$export$9d4c57ee4c6ffdd8)();
    var _section_arialabel;
    let { headingProps: headingProps, groupProps: groupProps } = (0, $kM2ZM$useMenuSection)({
        heading: heading,
        'aria-label': (_section_arialabel = section['aria-label']) !== null && _section_arialabel !== void 0 ? _section_arialabel : undefined
    });
    let children = (0, $7135fc7d473fd974$export$727c8fc270210f13)({
        items: state.collection.getChildren(section.key),
        children: (item)=>{
            switch(item.type){
                case 'header':
                    {
                        let { ref: ref, ...otherProps } = item.props;
                        return /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $72a5793c14baf454$export$8b251419efc915eb), {
                            ...headingProps,
                            ...otherProps,
                            ref: (0, $kM2ZM$mergeRefs)(headingRef, ref)
                        }, item.rendered);
                    }
                case 'item':
                    return /*#__PURE__*/ (0, $kM2ZM$react).createElement($3674c52c6b3c5bce$var$MenuItemInner, {
                        item: item
                    });
                case 'submenutrigger':
                    return /*#__PURE__*/ (0, $kM2ZM$react).createElement($3674c52c6b3c5bce$var$SubmenuTriggerInner, {
                        item: item,
                        parentMenuRef: parentMenuRef
                    });
                default:
                    throw new Error('Unsupported element type in Section: ' + item.type);
            }
        }
    });
    return /*#__PURE__*/ (0, $kM2ZM$react).createElement("section", {
        ...(0, $kM2ZM$filterDOMProps)(otherProps),
        ...groupProps,
        className: className || ((_section_props = section.props) === null || _section_props === void 0 ? void 0 : _section_props.className) || 'react-aria-Section',
        style: style || ((_section_props1 = section.props) === null || _section_props1 === void 0 ? void 0 : _section_props1.style),
        ref: section.props.ref
    }, children);
}
function $3674c52c6b3c5bce$var$MenuItem(props, ref) {
    return (0, $7135fc7d473fd974$export$e7c29ae2353b16ea)('item', props, ref, props.children);
}
/**
 * A MenuItem represents an individual action in a Menu.
 */ const $3674c52c6b3c5bce$export$2ce376c2cc3355c8 = /*#__PURE__*/ (0, $kM2ZM$forwardRef)($3674c52c6b3c5bce$var$MenuItem);
function $3674c52c6b3c5bce$var$MenuItemInner({ item: item }) {
    var _item_props;
    let state = (0, $kM2ZM$useContext)($3674c52c6b3c5bce$export$24aad8519b95b41b);
    let ref = (0, $kM2ZM$useObjectRef)(item.props.ref);
    let { menuItemProps: menuItemProps, labelProps: labelProps, descriptionProps: descriptionProps, keyboardShortcutProps: keyboardShortcutProps, ...states } = (0, $kM2ZM$useMenuItem)({
        key: item.key,
        'aria-label': (_item_props = item.props) === null || _item_props === void 0 ? void 0 : _item_props['aria-label']
    }, state, ref);
    let props = item.props;
    let { isFocusVisible: isFocusVisible, focusProps: focusProps } = (0, $kM2ZM$useFocusRing)();
    let { hoverProps: hoverProps, isHovered: isHovered } = (0, $kM2ZM$useHover)({
        isDisabled: states.isDisabled,
        onHoverStart: item.props.onHoverStart,
        onHoverChange: item.props.onHoverChange,
        onHoverEnd: item.props.onHoverEnd
    });
    let renderProps = (0, $64fa3d84918910a7$export$4d86445c2cf5e3)({
        ...props,
        id: undefined,
        children: item.rendered,
        defaultClassName: 'react-aria-MenuItem',
        values: {
            ...states,
            isHovered: isHovered,
            isFocusVisible: isFocusVisible,
            selectionMode: state.selectionManager.selectionMode,
            selectionBehavior: state.selectionManager.selectionBehavior,
            hasSubmenu: false,
            isOpen: false
        }
    });
    let ElementType = props.href ? 'a' : 'div';
    return /*#__PURE__*/ (0, $kM2ZM$react).createElement(ElementType, {
        ...(0, $kM2ZM$mergeProps)(menuItemProps, focusProps, hoverProps),
        ...renderProps,
        ref: ref,
        "data-disabled": states.isDisabled || undefined,
        "data-hovered": isHovered || undefined,
        "data-focused": states.isFocused || undefined,
        "data-focus-visible": isFocusVisible || undefined,
        "data-pressed": states.isPressed || undefined,
        "data-selected": states.isSelected || undefined,
        "data-selection-mode": state.selectionManager.selectionMode === 'none' ? undefined : state.selectionManager.selectionMode
    }, /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $64fa3d84918910a7$export$2881499e37b75b9a), {
        values: [
            [
                (0, $514c0188e459b4c0$export$9afb8bc826b033ea),
                {
                    slots: {
                        label: labelProps,
                        description: descriptionProps
                    }
                }
            ],
            [
                (0, $63df2425e2108aa8$export$744d98a3b8a94e1c),
                keyboardShortcutProps
            ]
        ]
    }, renderProps.children));
}
function $3674c52c6b3c5bce$var$MenuItemTriggerInner({ item: item, popover: popover, parentMenuRef: parentMenuRef, delay: delay }) {
    let state = (0, $kM2ZM$useContext)($3674c52c6b3c5bce$export$24aad8519b95b41b);
    let popoverContext = (0, $64fa3d84918910a7$export$fabf2dc03a41866e)((0, $07b14b47974efb58$export$9b9a0cd73afb7ca4));
    let ref = (0, $kM2ZM$useObjectRef)(item.props.ref);
    let rootMenuTriggerState = (0, $kM2ZM$useContext)($3674c52c6b3c5bce$export$795aec4671cbae19);
    let submenuTriggerState = (0, $kM2ZM$useSubmenuTriggerState)({
        triggerKey: item.key
    }, rootMenuTriggerState);
    let submenuRef = (0, $kM2ZM$useRef)(null);
    let { submenuTriggerProps: submenuTriggerProps, submenuProps: submenuProps, popoverProps: popoverProps } = (0, $kM2ZM$useSubmenuTrigger)({
        node: item,
        parentMenuRef: parentMenuRef,
        submenuRef: submenuRef,
        delay: delay
    }, submenuTriggerState, ref);
    let { menuItemProps: menuItemProps, labelProps: labelProps, descriptionProps: descriptionProps, keyboardShortcutProps: keyboardShortcutProps, ...states } = (0, $kM2ZM$useMenuItem)({
        key: item.key,
        ...submenuTriggerProps
    }, state, ref);
    let props = item.props;
    let { hoverProps: hoverProps, isHovered: isHovered } = (0, $kM2ZM$useHover)({
        isDisabled: states.isDisabled
    });
    let { isFocusVisible: isFocusVisible, focusProps: focusProps } = (0, $kM2ZM$useFocusRing)();
    let renderProps = (0, $64fa3d84918910a7$export$4d86445c2cf5e3)({
        ...props,
        id: undefined,
        children: item.rendered,
        defaultClassName: 'react-aria-MenuItem',
        values: {
            ...states,
            isHovered: isHovered,
            isFocusVisible: isFocusVisible,
            selectionMode: state.selectionManager.selectionMode,
            selectionBehavior: state.selectionManager.selectionBehavior,
            hasSubmenu: true,
            isOpen: submenuTriggerState.isOpen
        }
    });
    return /*#__PURE__*/ (0, $kM2ZM$react).createElement((0, $64fa3d84918910a7$export$2881499e37b75b9a), {
        values: [
            [
                (0, $514c0188e459b4c0$export$9afb8bc826b033ea),
                {
                    slots: {
                        label: labelProps,
                        description: descriptionProps
                    }
                }
            ],
            [
                (0, $63df2425e2108aa8$export$744d98a3b8a94e1c),
                keyboardShortcutProps
            ],
            [
                $3674c52c6b3c5bce$export$c7e742effb1c51e2,
                submenuProps
            ],
            [
                (0, $de32f1b87079253c$export$d2f961adcb0afbe),
                submenuTriggerState
            ],
            [
                (0, $07b14b47974efb58$export$9b9a0cd73afb7ca4),
                {
                    ref: submenuRef,
                    trigger: 'SubmenuTrigger',
                    triggerRef: ref,
                    placement: 'end top',
                    UNSTABLE_portalContainer: popoverContext.UNSTABLE_portalContainer || undefined,
                    ...popoverProps
                }
            ]
        ]
    }, /*#__PURE__*/ (0, $kM2ZM$react).createElement("div", {
        ...(0, $kM2ZM$mergeProps)(menuItemProps, focusProps, hoverProps),
        ...renderProps,
        ref: ref,
        "data-disabled": states.isDisabled || undefined,
        "data-hovered": isHovered || undefined,
        "data-focused": states.isFocused || undefined,
        "data-focus-visible": isFocusVisible || undefined,
        "data-pressed": states.isPressed || undefined,
        "data-selected": states.isSelected || undefined,
        "data-selection-mode": state.selectionManager.selectionMode === 'none' ? undefined : state.selectionManager.selectionMode,
        "data-has-submenu": true,
        "data-open": submenuTriggerState.isOpen || undefined
    }, renderProps.children), popover);
}


export {$3674c52c6b3c5bce$export$c7e742effb1c51e2 as MenuContext, $3674c52c6b3c5bce$export$24aad8519b95b41b as MenuStateContext, $3674c52c6b3c5bce$export$795aec4671cbae19 as RootMenuTriggerStateContext, $3674c52c6b3c5bce$export$27d2ad3c5815583e as MenuTrigger, $3674c52c6b3c5bce$export$ecabc99eeffab7ca as SubmenuTrigger, $3674c52c6b3c5bce$export$d9b273488cd8ce6f as Menu, $3674c52c6b3c5bce$export$2ce376c2cc3355c8 as MenuItem};
//# sourceMappingURL=Menu.module.js.map
