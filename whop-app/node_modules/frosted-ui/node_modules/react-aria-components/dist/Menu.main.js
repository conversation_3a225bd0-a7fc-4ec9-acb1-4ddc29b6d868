var $3114c2382242bdc0$exports = require("./Collection.main.js");
var $c5ccf687772c0422$exports = require("./utils.main.js");
var $c7efb75a1a3fe2d2$exports = require("./Header.main.js");
var $70c886ca98ebdc2b$exports = require("./Keyboard.main.js");
var $2979ab89b336194b$exports = require("./Dialog.main.js");
var $61e2b7078adb18bc$exports = require("./Popover.main.js");
var $54b202ace195eaa4$exports = require("./Separator.main.js");
var $a8a589c28affdc40$exports = require("./Text.main.js");
var $2JTht$reactaria = require("react-aria");
var $2JTht$reactstately = require("react-stately");
var $2JTht$reactariautils = require("@react-aria/utils");
var $2JTht$reactariainteractions = require("@react-aria/interactions");
var $2JTht$react = require("react");
var $2JTht$reactstatelymenu = require("@react-stately/menu");
var $2JTht$reactariamenu = require("@react-aria/menu");


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "MenuContext", () => $a8f6e7d095d1cc86$export$c7e742effb1c51e2);
$parcel$export(module.exports, "MenuStateContext", () => $a8f6e7d095d1cc86$export$24aad8519b95b41b);
$parcel$export(module.exports, "RootMenuTriggerStateContext", () => $a8f6e7d095d1cc86$export$795aec4671cbae19);
$parcel$export(module.exports, "MenuTrigger", () => $a8f6e7d095d1cc86$export$27d2ad3c5815583e);
$parcel$export(module.exports, "SubmenuTrigger", () => $a8f6e7d095d1cc86$export$ecabc99eeffab7ca);
$parcel$export(module.exports, "Menu", () => $a8f6e7d095d1cc86$export$d9b273488cd8ce6f);
$parcel$export(module.exports, "MenuItem", () => $a8f6e7d095d1cc86$export$2ce376c2cc3355c8);
/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 














const $a8f6e7d095d1cc86$export$c7e742effb1c51e2 = /*#__PURE__*/ (0, $2JTht$react.createContext)(null);
const $a8f6e7d095d1cc86$export$24aad8519b95b41b = /*#__PURE__*/ (0, $2JTht$react.createContext)(null);
const $a8f6e7d095d1cc86$export$795aec4671cbae19 = /*#__PURE__*/ (0, $2JTht$react.createContext)(null);
function $a8f6e7d095d1cc86$export$27d2ad3c5815583e(props) {
    let state = (0, $2JTht$reactstately.useMenuTriggerState)(props);
    let ref = (0, $2JTht$react.useRef)(null);
    let { menuTriggerProps: menuTriggerProps, menuProps: menuProps } = (0, $2JTht$reactaria.useMenuTrigger)({
        ...props,
        type: 'menu'
    }, state, ref);
    // Allows menu width to match button
    let [buttonWidth, setButtonWidth] = (0, $2JTht$react.useState)(null);
    let onResize = (0, $2JTht$react.useCallback)(()=>{
        if (ref.current) setButtonWidth(ref.current.offsetWidth + 'px');
    }, [
        ref
    ]);
    (0, $2JTht$reactariautils.useResizeObserver)({
        ref: ref,
        onResize: onResize
    });
    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $c5ccf687772c0422$exports.Provider), {
        values: [
            [
                $a8f6e7d095d1cc86$export$c7e742effb1c51e2,
                menuProps
            ],
            [
                (0, $2979ab89b336194b$exports.OverlayTriggerStateContext),
                state
            ],
            [
                $a8f6e7d095d1cc86$export$795aec4671cbae19,
                state
            ],
            [
                (0, $61e2b7078adb18bc$exports.PopoverContext),
                {
                    trigger: 'MenuTrigger',
                    triggerRef: ref,
                    placement: 'bottom start',
                    style: {
                        '--trigger-width': buttonWidth
                    }
                }
            ]
        ]
    }, /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $2JTht$reactariainteractions.PressResponder), {
        ...menuTriggerProps,
        ref: ref,
        isPressed: state.isOpen
    }, props.children));
}
function $a8f6e7d095d1cc86$export$ecabc99eeffab7ca(props, ref) {
    return (0, $3114c2382242bdc0$exports.useSSRCollectionNode)('submenutrigger', props, ref, props.children, props.children[0]);
}
function $a8f6e7d095d1cc86$var$SubmenuTriggerInner(props) {
    let { item: item, parentMenuRef: parentMenuRef } = props;
    let state = (0, $2JTht$react.useContext)($a8f6e7d095d1cc86$export$24aad8519b95b41b);
    let children = (0, $3114c2382242bdc0$exports.useCachedChildren)({
        items: state.collection.getChildren(item.key),
        children: (childItem)=>{
            switch(childItem.type){
                case 'item':
                    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement($a8f6e7d095d1cc86$var$MenuItemTriggerInner, {
                        item: childItem,
                        popover: item.rendered[1],
                        parentMenuRef: parentMenuRef,
                        delay: item.props.delay
                    });
                default:
                    throw new Error('Unsupported element type in SubmenuTrigger: ' + item.type);
            }
        }
    });
    return children;
}
function $a8f6e7d095d1cc86$var$Menu(props, ref) {
    [props, ref] = (0, $c5ccf687772c0422$exports.useContextProps)(props, ref, $a8f6e7d095d1cc86$export$c7e742effb1c51e2);
    let { portal: portal, collection: collection } = (0, $3114c2382242bdc0$exports.useCollection)(props);
    // Delay rendering the actual menu until we have the collection so that auto focus works properly.
    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, ($parcel$interopDefault($2JTht$react))).Fragment, null, collection.size > 0 && /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement($a8f6e7d095d1cc86$var$MenuInner, {
        props: props,
        collection: collection,
        menuRef: ref
    }), portal);
}
function $a8f6e7d095d1cc86$var$MenuInner({ props: props, collection: collection, menuRef: ref }) {
    let state = (0, $2JTht$reactstately.useTreeState)({
        ...props,
        collection: collection,
        children: undefined
    });
    let [popoverContainer, setPopoverContainer] = (0, $2JTht$react.useState)(null);
    let { menuProps: menuProps } = (0, $2JTht$reactaria.useMenu)(props, state, ref);
    let rootMenuTriggerState = (0, $2JTht$react.useContext)($a8f6e7d095d1cc86$export$795aec4671cbae19);
    let popoverContext = (0, $2JTht$react.useContext)((0, $61e2b7078adb18bc$exports.PopoverContext));
    let children = (0, $3114c2382242bdc0$exports.useCachedChildren)({
        items: state.collection,
        children: (item)=>{
            switch(item.type){
                case 'section':
                    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement($a8f6e7d095d1cc86$var$MenuSection, {
                        section: item,
                        parentMenuRef: ref
                    });
                case 'separator':
                    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $54b202ace195eaa4$exports.Separator), item.props);
                case 'item':
                    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement($a8f6e7d095d1cc86$var$MenuItemInner, {
                        item: item
                    });
                case 'submenutrigger':
                    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement($a8f6e7d095d1cc86$var$SubmenuTriggerInner, {
                        item: item,
                        parentMenuRef: ref
                    });
                default:
                    throw new Error('Unsupported node type in Menu: ' + item.type);
            }
        }
    });
    let isSubmenu = (popoverContext === null || popoverContext === void 0 ? void 0 : popoverContext.trigger) === 'SubmenuTrigger';
    (0, $2JTht$reactariainteractions.useInteractOutside)({
        ref: ref,
        onInteractOutside: (e)=>{
            if (rootMenuTriggerState && !(popoverContainer === null || popoverContainer === void 0 ? void 0 : popoverContainer.contains(e.target))) rootMenuTriggerState.close();
        },
        isDisabled: isSubmenu || (rootMenuTriggerState === null || rootMenuTriggerState === void 0 ? void 0 : rootMenuTriggerState.expandedKeysStack.length) === 0
    });
    let prevPopoverContainer = (0, $2JTht$react.useRef)(null);
    let [leftOffset, setLeftOffset] = (0, $2JTht$react.useState)({
        left: 0
    });
    (0, $2JTht$react.useEffect)(()=>{
        if (popoverContainer && prevPopoverContainer.current !== popoverContainer && leftOffset.left === 0) {
            prevPopoverContainer.current = popoverContainer;
            let { left: left } = popoverContainer.getBoundingClientRect();
            setLeftOffset({
                left: -1 * left
            });
        }
    }, [
        leftOffset,
        popoverContainer
    ]);
    var _props_className;
    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $2JTht$reactaria.FocusScope), null, /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement("div", {
        ...(0, $2JTht$reactariautils.filterDOMProps)(props),
        ...menuProps,
        ref: ref,
        slot: props.slot || undefined,
        onScroll: props.onScroll,
        style: props.style,
        className: (_props_className = props.className) !== null && _props_className !== void 0 ? _props_className : 'react-aria-Menu'
    }, /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $c5ccf687772c0422$exports.Provider), {
        values: [
            [
                $a8f6e7d095d1cc86$export$24aad8519b95b41b,
                state
            ],
            [
                (0, $54b202ace195eaa4$exports.SeparatorContext),
                {
                    elementType: 'div'
                }
            ],
            [
                (0, $61e2b7078adb18bc$exports.PopoverContext),
                {
                    UNSTABLE_portalContainer: popoverContainer || undefined
                }
            ]
        ]
    }, children)), /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement("div", {
        ref: setPopoverContainer,
        style: {
            width: '100vw',
            position: 'absolute',
            top: 0,
            ...leftOffset
        }
    }));
}
/**
 * A menu displays a list of actions or options that a user can choose.
 */ const $a8f6e7d095d1cc86$export$d9b273488cd8ce6f = /*#__PURE__*/ (0, $2JTht$react.forwardRef)($a8f6e7d095d1cc86$var$Menu);
function $a8f6e7d095d1cc86$var$MenuSection({ section: section, className: className, style: style, parentMenuRef: parentMenuRef, ...otherProps }) {
    var _section_props, _section_props1;
    let state = (0, $2JTht$react.useContext)($a8f6e7d095d1cc86$export$24aad8519b95b41b);
    let [headingRef, heading] = (0, $c5ccf687772c0422$exports.useSlot)();
    var _section_arialabel;
    let { headingProps: headingProps, groupProps: groupProps } = (0, $2JTht$reactaria.useMenuSection)({
        heading: heading,
        'aria-label': (_section_arialabel = section['aria-label']) !== null && _section_arialabel !== void 0 ? _section_arialabel : undefined
    });
    let children = (0, $3114c2382242bdc0$exports.useCachedChildren)({
        items: state.collection.getChildren(section.key),
        children: (item)=>{
            switch(item.type){
                case 'header':
                    {
                        let { ref: ref, ...otherProps } = item.props;
                        return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $c7efb75a1a3fe2d2$exports.Header), {
                            ...headingProps,
                            ...otherProps,
                            ref: (0, $2JTht$reactariautils.mergeRefs)(headingRef, ref)
                        }, item.rendered);
                    }
                case 'item':
                    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement($a8f6e7d095d1cc86$var$MenuItemInner, {
                        item: item
                    });
                case 'submenutrigger':
                    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement($a8f6e7d095d1cc86$var$SubmenuTriggerInner, {
                        item: item,
                        parentMenuRef: parentMenuRef
                    });
                default:
                    throw new Error('Unsupported element type in Section: ' + item.type);
            }
        }
    });
    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement("section", {
        ...(0, $2JTht$reactariautils.filterDOMProps)(otherProps),
        ...groupProps,
        className: className || ((_section_props = section.props) === null || _section_props === void 0 ? void 0 : _section_props.className) || 'react-aria-Section',
        style: style || ((_section_props1 = section.props) === null || _section_props1 === void 0 ? void 0 : _section_props1.style),
        ref: section.props.ref
    }, children);
}
function $a8f6e7d095d1cc86$var$MenuItem(props, ref) {
    return (0, $3114c2382242bdc0$exports.useSSRCollectionNode)('item', props, ref, props.children);
}
/**
 * A MenuItem represents an individual action in a Menu.
 */ const $a8f6e7d095d1cc86$export$2ce376c2cc3355c8 = /*#__PURE__*/ (0, $2JTht$react.forwardRef)($a8f6e7d095d1cc86$var$MenuItem);
function $a8f6e7d095d1cc86$var$MenuItemInner({ item: item }) {
    var _item_props;
    let state = (0, $2JTht$react.useContext)($a8f6e7d095d1cc86$export$24aad8519b95b41b);
    let ref = (0, $2JTht$reactariautils.useObjectRef)(item.props.ref);
    let { menuItemProps: menuItemProps, labelProps: labelProps, descriptionProps: descriptionProps, keyboardShortcutProps: keyboardShortcutProps, ...states } = (0, $2JTht$reactaria.useMenuItem)({
        key: item.key,
        'aria-label': (_item_props = item.props) === null || _item_props === void 0 ? void 0 : _item_props['aria-label']
    }, state, ref);
    let props = item.props;
    let { isFocusVisible: isFocusVisible, focusProps: focusProps } = (0, $2JTht$reactaria.useFocusRing)();
    let { hoverProps: hoverProps, isHovered: isHovered } = (0, $2JTht$reactariainteractions.useHover)({
        isDisabled: states.isDisabled,
        onHoverStart: item.props.onHoverStart,
        onHoverChange: item.props.onHoverChange,
        onHoverEnd: item.props.onHoverEnd
    });
    let renderProps = (0, $c5ccf687772c0422$exports.useRenderProps)({
        ...props,
        id: undefined,
        children: item.rendered,
        defaultClassName: 'react-aria-MenuItem',
        values: {
            ...states,
            isHovered: isHovered,
            isFocusVisible: isFocusVisible,
            selectionMode: state.selectionManager.selectionMode,
            selectionBehavior: state.selectionManager.selectionBehavior,
            hasSubmenu: false,
            isOpen: false
        }
    });
    let ElementType = props.href ? 'a' : 'div';
    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement(ElementType, {
        ...(0, $2JTht$reactaria.mergeProps)(menuItemProps, focusProps, hoverProps),
        ...renderProps,
        ref: ref,
        "data-disabled": states.isDisabled || undefined,
        "data-hovered": isHovered || undefined,
        "data-focused": states.isFocused || undefined,
        "data-focus-visible": isFocusVisible || undefined,
        "data-pressed": states.isPressed || undefined,
        "data-selected": states.isSelected || undefined,
        "data-selection-mode": state.selectionManager.selectionMode === 'none' ? undefined : state.selectionManager.selectionMode
    }, /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $c5ccf687772c0422$exports.Provider), {
        values: [
            [
                (0, $a8a589c28affdc40$exports.TextContext),
                {
                    slots: {
                        label: labelProps,
                        description: descriptionProps
                    }
                }
            ],
            [
                (0, $70c886ca98ebdc2b$exports.KeyboardContext),
                keyboardShortcutProps
            ]
        ]
    }, renderProps.children));
}
function $a8f6e7d095d1cc86$var$MenuItemTriggerInner({ item: item, popover: popover, parentMenuRef: parentMenuRef, delay: delay }) {
    let state = (0, $2JTht$react.useContext)($a8f6e7d095d1cc86$export$24aad8519b95b41b);
    let popoverContext = (0, $c5ccf687772c0422$exports.useSlottedContext)((0, $61e2b7078adb18bc$exports.PopoverContext));
    let ref = (0, $2JTht$reactariautils.useObjectRef)(item.props.ref);
    let rootMenuTriggerState = (0, $2JTht$react.useContext)($a8f6e7d095d1cc86$export$795aec4671cbae19);
    let submenuTriggerState = (0, $2JTht$reactstatelymenu.useSubmenuTriggerState)({
        triggerKey: item.key
    }, rootMenuTriggerState);
    let submenuRef = (0, $2JTht$react.useRef)(null);
    let { submenuTriggerProps: submenuTriggerProps, submenuProps: submenuProps, popoverProps: popoverProps } = (0, $2JTht$reactariamenu.useSubmenuTrigger)({
        node: item,
        parentMenuRef: parentMenuRef,
        submenuRef: submenuRef,
        delay: delay
    }, submenuTriggerState, ref);
    let { menuItemProps: menuItemProps, labelProps: labelProps, descriptionProps: descriptionProps, keyboardShortcutProps: keyboardShortcutProps, ...states } = (0, $2JTht$reactaria.useMenuItem)({
        key: item.key,
        ...submenuTriggerProps
    }, state, ref);
    let props = item.props;
    let { hoverProps: hoverProps, isHovered: isHovered } = (0, $2JTht$reactariainteractions.useHover)({
        isDisabled: states.isDisabled
    });
    let { isFocusVisible: isFocusVisible, focusProps: focusProps } = (0, $2JTht$reactaria.useFocusRing)();
    let renderProps = (0, $c5ccf687772c0422$exports.useRenderProps)({
        ...props,
        id: undefined,
        children: item.rendered,
        defaultClassName: 'react-aria-MenuItem',
        values: {
            ...states,
            isHovered: isHovered,
            isFocusVisible: isFocusVisible,
            selectionMode: state.selectionManager.selectionMode,
            selectionBehavior: state.selectionManager.selectionBehavior,
            hasSubmenu: true,
            isOpen: submenuTriggerState.isOpen
        }
    });
    return /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement((0, $c5ccf687772c0422$exports.Provider), {
        values: [
            [
                (0, $a8a589c28affdc40$exports.TextContext),
                {
                    slots: {
                        label: labelProps,
                        description: descriptionProps
                    }
                }
            ],
            [
                (0, $70c886ca98ebdc2b$exports.KeyboardContext),
                keyboardShortcutProps
            ],
            [
                $a8f6e7d095d1cc86$export$c7e742effb1c51e2,
                submenuProps
            ],
            [
                (0, $2979ab89b336194b$exports.OverlayTriggerStateContext),
                submenuTriggerState
            ],
            [
                (0, $61e2b7078adb18bc$exports.PopoverContext),
                {
                    ref: submenuRef,
                    trigger: 'SubmenuTrigger',
                    triggerRef: ref,
                    placement: 'end top',
                    UNSTABLE_portalContainer: popoverContext.UNSTABLE_portalContainer || undefined,
                    ...popoverProps
                }
            ]
        ]
    }, /*#__PURE__*/ (0, ($parcel$interopDefault($2JTht$react))).createElement("div", {
        ...(0, $2JTht$reactaria.mergeProps)(menuItemProps, focusProps, hoverProps),
        ...renderProps,
        ref: ref,
        "data-disabled": states.isDisabled || undefined,
        "data-hovered": isHovered || undefined,
        "data-focused": states.isFocused || undefined,
        "data-focus-visible": isFocusVisible || undefined,
        "data-pressed": states.isPressed || undefined,
        "data-selected": states.isSelected || undefined,
        "data-selection-mode": state.selectionManager.selectionMode === 'none' ? undefined : state.selectionManager.selectionMode,
        "data-has-submenu": true,
        "data-open": submenuTriggerState.isOpen || undefined
    }, renderProps.children), popover);
}


//# sourceMappingURL=Menu.main.js.map
