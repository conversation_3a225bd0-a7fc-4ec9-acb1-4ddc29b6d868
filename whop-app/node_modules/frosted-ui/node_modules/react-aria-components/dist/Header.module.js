import {useContextProps as $64fa3d84918910a7$export$29f1550f4b0d4415} from "./utils.module.js";
import {useShallowRender as $7135fc7d473fd974$export$aeba0b1fb3dcd8b8} from "./Collection.module.js";
import $i47tY$react, {createContext as $i47tY$createContext, forwardRef as $i47tY$forwardRef} from "react";

/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 


const $72a5793c14baf454$export$e0e4026c12a8bdbb = /*#__PURE__*/ (0, $i47tY$createContext)({});
function $72a5793c14baf454$var$Header(originalProps, originalRef) {
    let [props, ref] = (0, $64fa3d84918910a7$export$29f1550f4b0d4415)(originalProps, originalRef, $72a5793c14baf454$export$e0e4026c12a8bdbb);
    let shallow = (0, $7135fc7d473fd974$export$aeba0b1fb3dcd8b8)('header', originalProps, originalRef);
    if (shallow) return shallow;
    return /*#__PURE__*/ (0, $i47tY$react).createElement("header", {
        className: "react-aria-Header",
        ...props,
        ref: ref
    }, props.children);
}
const $72a5793c14baf454$export$8b251419efc915eb = /*#__PURE__*/ (0, $i47tY$forwardRef)($72a5793c14baf454$var$Header);


export {$72a5793c14baf454$export$e0e4026c12a8bdbb as HeaderContext, $72a5793c14baf454$export$8b251419efc915eb as Header};
//# sourceMappingURL=Header.module.js.map
