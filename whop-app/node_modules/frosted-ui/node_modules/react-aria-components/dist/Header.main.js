var $c5ccf687772c0422$exports = require("./utils.main.js");
var $3114c2382242bdc0$exports = require("./Collection.main.js");
var $gJXsb$react = require("react");


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "HeaderContext", () => $c7efb75a1a3fe2d2$export$e0e4026c12a8bdbb);
$parcel$export(module.exports, "Header", () => $c7efb75a1a3fe2d2$export$8b251419efc915eb);
/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 


const $c7efb75a1a3fe2d2$export$e0e4026c12a8bdbb = /*#__PURE__*/ (0, $gJXsb$react.createContext)({});
function $c7efb75a1a3fe2d2$var$Header(originalProps, originalRef) {
    let [props, ref] = (0, $c5ccf687772c0422$exports.useContextProps)(originalProps, originalRef, $c7efb75a1a3fe2d2$export$e0e4026c12a8bdbb);
    let shallow = (0, $3114c2382242bdc0$exports.useShallowRender)('header', originalProps, originalRef);
    if (shallow) return shallow;
    return /*#__PURE__*/ (0, ($parcel$interopDefault($gJXsb$react))).createElement("header", {
        className: "react-aria-Header",
        ...props,
        ref: ref
    }, props.children);
}
const $c7efb75a1a3fe2d2$export$8b251419efc915eb = /*#__PURE__*/ (0, $gJXsb$react.forwardRef)($c7efb75a1a3fe2d2$var$Header);


//# sourceMappingURL=Header.main.js.map
