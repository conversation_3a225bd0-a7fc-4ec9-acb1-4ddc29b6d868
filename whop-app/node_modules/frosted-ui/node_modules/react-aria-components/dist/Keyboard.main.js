var $c5ccf687772c0422$exports = require("./utils.main.js");
var $jeUoS$react = require("react");


function $parcel$interopDefault(a) {
  return a && a.__esModule ? a.default : a;
}

function $parcel$export(e, n, v, s) {
  Object.defineProperty(e, n, {get: v, set: s, enumerable: true, configurable: true});
}

$parcel$export(module.exports, "KeyboardContext", () => $70c886ca98ebdc2b$export$744d98a3b8a94e1c);
$parcel$export(module.exports, "Keyboard", () => $70c886ca98ebdc2b$export$16e4d70cc375e707);
/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 

const $70c886ca98ebdc2b$export$744d98a3b8a94e1c = /*#__PURE__*/ (0, $jeUoS$react.createContext)({});
function $70c886ca98ebdc2b$var$Keyboard(props, ref) {
    [props, ref] = (0, $c5ccf687772c0422$exports.useContextProps)(props, ref, $70c886ca98ebdc2b$export$744d98a3b8a94e1c);
    return /*#__PURE__*/ (0, ($parcel$interopDefault($jeUoS$react))).createElement("kbd", {
        dir: "ltr",
        ...props,
        ref: ref
    });
}
const $70c886ca98ebdc2b$export$16e4d70cc375e707 = /*#__PURE__*/ (0, $jeUoS$react.forwardRef)($70c886ca98ebdc2b$var$Keyboard);


//# sourceMappingURL=Keyboard.main.js.map
