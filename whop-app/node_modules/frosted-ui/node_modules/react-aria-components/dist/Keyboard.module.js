import {useContextProps as $64fa3d84918910a7$export$29f1550f4b0d4415} from "./utils.module.js";
import $3zqIJ$react, {createContext as $3zqIJ$createContext, forwardRef as $3zqIJ$forwardRef} from "react";

/*
 * Copyright 2022 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 

const $63df2425e2108aa8$export$744d98a3b8a94e1c = /*#__PURE__*/ (0, $3zqIJ$createContext)({});
function $63df2425e2108aa8$var$Keyboard(props, ref) {
    [props, ref] = (0, $64fa3d84918910a7$export$29f1550f4b0d4415)(props, ref, $63df2425e2108aa8$export$744d98a3b8a94e1c);
    return /*#__PURE__*/ (0, $3zqIJ$react).createElement("kbd", {
        dir: "ltr",
        ...props,
        ref: ref
    });
}
const $63df2425e2108aa8$export$16e4d70cc375e707 = /*#__PURE__*/ (0, $3zqIJ$forwardRef)($63df2425e2108aa8$var$Keyboard);


export {$63df2425e2108aa8$export$744d98a3b8a94e1c as KeyboardContext, $63df2425e2108aa8$export$16e4d70cc375e707 as Keyboard};
//# sourceMappingURL=Keyboard.module.js.map
