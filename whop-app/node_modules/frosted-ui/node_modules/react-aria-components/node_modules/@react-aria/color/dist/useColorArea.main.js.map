{"mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;AAsCM,SAAS,0CAAa,KAA2B,EAAE,KAAqB;IAC7E,IAAI,cACF,UAAU,aACV,SAAS,aACT,SAAS,gBACT,YAAY,EACZ,cAAc,SAAS,SACvB,KAAK,SACL,KAAK,EACN,GAAG;IACJ,IAAI,kBAAkB,CAAA,GAAA,gDAA0B,EAAE,CAAA,GAAA,mDAAW,GAAG;IAEhE,IAAI,qBAAC,iBAAiB,wBAAE,oBAAoB,EAAC,GAAG,CAAA,GAAA,wCAAiB;IAEjE,IAAI,aAAC,SAAS,UAAE,MAAM,EAAC,GAAG,CAAA,GAAA,8BAAQ;IAElC,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qBAAO,EAAoB;IACjE,IAAI,aAAa,CAAA,GAAA,wBAAU,EAAE,CAAC,WAAuC,SAAS;QAC5E,IAAI,SAAS,OAAO,EAClB,CAAA,GAAA,2CAAoB,EAAE,SAAS,OAAO;IAE1C,GAAG;QAAC;KAAU;IAEd,CAAA,GAAA,kCAAW,EAAE,WAAW;QAAC,MAAM,MAAM;QAAE,MAAM,MAAM;KAAC,EAAE,CAAC,CAAC,GAAG,EAAE;QAC3D,IAAI,WAAW,MAAM,KAAK,CACvB,gBAAgB,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE,GAC1C,gBAAgB,CAAC,MAAM,QAAQ,CAAC,QAAQ,EAAE;QAC7C,MAAM,QAAQ,CAAC;IACjB;IAEA,IAAI,CAAC,yBAAyB,2BAA2B,GAAG,CAAA,GAAA,qBAAO,EAAE;IACrE,IAAI,CAAC,iCAAiC,mCAAmC,GAAG,CAAA,GAAA,qBAAO,EAAE;IACrF,IAAI,YAAC,QAAQ,YAAE,QAAQ,YAAE,QAAQ,EAAC,GAAG,MAAM,QAAQ;IACnD,IAAI,eAAe,MAAM,YAAY;IACrC,IAAI,eAAe,MAAM,YAAY;IAErC,IAAI,kBAAkB,CAAA,GAAA,mBAAK,EAAiC;IAE5D,IAAI,iBAAC,aAAa,EAAC,GAAG,CAAA,GAAA,wCAAU,EAAE;QAChC,WAAU,CAAC;YACT,kDAAkD;YAClD,IAAI,CAAC,+BAA+B,IAAI,CAAC,EAAE,GAAG,GAAG;gBAC/C,EAAE,mBAAmB;gBACrB;YACF;YACA,4FAA4F;YAC5F,EAAE,cAAc;YAChB,iEAAiE;YACjE,MAAM,WAAW,CAAC;YAClB,2BAA2B;YAC3B,IAAI;YACJ,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,MAAM,UAAU,CAAC,MAAM,gBAAgB;oBACvC,MAAM;oBACN;gBACF,KAAK;oBACH,MAAM,UAAU,CAAC,MAAM,gBAAgB;oBACvC,MAAM;oBACN;gBACF,KAAK;oBACH,cAAc,QAAQ,MAAM,UAAU,CAAC,MAAM,gBAAgB,IAAI,MAAM,UAAU,CAAC,MAAM,gBAAgB;oBACxG,MAAM;oBACN;gBACF,KAAK;oBACH,cAAc,QAAQ,MAAM,UAAU,CAAC,MAAM,gBAAgB,IAAI,MAAM,UAAU,CAAC,MAAM,gBAAgB;oBACxG,MAAM;oBACN;YACJ;YACA,MAAM,WAAW,CAAC;YAClB,IAAI,KAAK;gBACP,IAAI,QAAQ,QAAQ,MAAM,YAAY;gBACtC,WAAW;gBACX,gBAAgB;YAClB;QACF;IACF;IAEA,IAAI,cAAc;QAChB;YACE,gBAAgB,OAAO,GAAG;YAC1B,MAAM,WAAW,CAAC;QACpB;QACA,QAAO,UAAC,MAAM,UAAE,MAAM,eAAE,WAAW,YAAE,QAAQ,EAAC;gBAgBtB;YAftB,IAAI,cACF,UAAU,cACV,UAAU,cACV,UAAU,cACV,UAAU,oBACV,gBAAgB,gBAChB,YAAY,oBACZ,gBAAgB,gBAChB,YAAY,oBACZ,gBAAgB,qBAChB,iBAAiB,EAClB,GAAG;YACJ,IAAI,gBAAgB,OAAO,IAAI,MAC7B,gBAAgB,OAAO,GAAG;YAE5B,IAAI,SAAC,KAAK,UAAE,MAAM,EAAC,GAAG,EAAA,wBAAA,aAAa,OAAO,cAApB,4CAAA,sBAAsB,qBAAqB,OAAM;gBAAC,OAAO;gBAAG,QAAQ;YAAC;YAC3F,IAAI,eAAe,WAAW,KAAK,WAAW;YAC9C,IAAI,gBAAgB,YAAY;gBAC9B,IAAI,cAAc,YAAY,mBAAmB,eAAe,mBAAmB;gBACnF,IAAI,cAAc,YAAY,mBAAmB,eAAe,mBAAmB;gBACnF,IAAI,AAAC,SAAS,KAAK,cAAc,SAAW,SAAS,KAAK,cAAc,OACtE,WAAW;qBACN,IAAI,AAAC,SAAS,KAAK,cAAc,SAAW,SAAS,KAAK,cAAc,OAC7E,WAAW;qBACN,IAAI,SAAS,GAClB,WAAW;qBACN,IAAI,SAAS,GAClB,WAAW;gBAEb,2BAA2B;gBAC3B,kEAAkE;gBAClE,eAAe,gBAAgB,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,MAAM;gBAC3E,gBAAgB;YAClB,OAAO;gBACL,gBAAgB,OAAO,CAAC,CAAC,IAAI,AAAC,CAAA,cAAc,QAAQ,KAAK,CAAA,IAAK,SAAS;gBACvE,gBAAgB,OAAO,CAAC,CAAC,IAAI,SAAS;gBACtC,kBAAkB,gBAAgB,OAAO,CAAC,CAAC,EAAE,gBAAgB,OAAO,CAAC,CAAC;YACxE;QACF;QACA;YACE,cAAc,OAAO,GAAG;YACxB,MAAM,WAAW,CAAC;YAClB,IAAI,QAAQ,iBAAiB,MAAM,YAAY;YAC/C,WAAW;QACb;IACF;IACA,IAAI,EAAC,WAAW,cAAc,EAAC,GAAG,CAAA,GAAA,oCAAM,EAAE;IAE1C,IAAI,oBAAC,gBAAgB,EAAC,GAAG,CAAA,GAAA,2CAAa,EAAE;QACtC,qBAAqB,CAAC;YACpB,IAAI,CAAC,aAAa;gBAChB,2BAA2B;gBAC3B,mCAAmC;YACrC;QACF;IACF;IAEA,IAAI,iBAAiB,CAAA,GAAA,mBAAK,EAA6B;IACvD,IAAI,gBAAgB,CAAA,GAAA,mBAAK,EAAW;IACpC,IAAI,EAAC,WAAW,kBAAkB,EAAC,GAAG,CAAA,GAAA,oCAAM,EAAE;QAC5C;YACE,IAAI,cAAc,OAAO,EACvB,YAAY,WAAW;QAE3B;QACA,QAAO,CAAC;YACN,IAAI,cAAc,OAAO,EACvB,YAAY,MAAM,CAAC;QAEvB;QACA;YACE,IAAI,cAAc,OAAO,EACvB,YAAY,SAAS;QAEzB;IACF;IAEA,IAAI,cAAc,CAAC;QACjB,IAAI,CAAC,MAAM,UAAU,EAAE;YACrB,eAAe,OAAO,GAAG;YACzB,2BAA2B;YAC3B;YACA,MAAM,WAAW,CAAC;YAClB,IAAI,OAAO,iBAAiB,aAC1B,kBAAkB,QAAQ,aAAa,WAAW;iBAC7C;gBACL,kBAAkB,QAAQ,WAAW,WAAW;gBAChD,kBAAkB,QAAQ,YAAY,WAAW;YACnD;QACF;IACF;IAEA,IAAI,YAAY,CAAC;YACS;YAAf;QAAT,IAAI,KAAK,CAAA,eAAA,EAAE,SAAS,cAAX,0BAAA,gBAAe,oBAAA,EAAE,cAAc,cAAhB,wCAAA,iBAAkB,CAAC,EAAE,CAAC,UAAU;QACxD,IAAI,OAAO,eAAe,OAAO,EAAE;YACjC,2BAA2B;YAC3B;YACA,MAAM,WAAW,CAAC;YAClB,eAAe,OAAO,GAAG;YACzB,cAAc,OAAO,GAAG;YAExB,IAAI,OAAO,iBAAiB,aAC1B,qBAAqB,QAAQ,aAAa,WAAW;iBAChD;gBACL,qBAAqB,QAAQ,WAAW,WAAW;gBACnD,qBAAqB,QAAQ,YAAY,WAAW;YACtD;QACF;IACF;IAEA,IAAI,kBAAkB,CAAC,WAAoB,IAA+B,SAAiB;QACzF,IAAI,OAAO,UAAU,qBAAqB;QAC1C,IAAI,SAAC,KAAK,UAAE,MAAM,EAAC,GAAG;QACtB,IAAI,IAAI,AAAC,CAAA,UAAU,KAAK,CAAC,AAAD,IAAK;QAC7B,IAAI,IAAI,AAAC,CAAA,UAAU,KAAK,CAAC,AAAD,IAAK;QAC7B,IAAI,cAAc,OAChB,IAAI,IAAI;QAEV,IAAI,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,KAAK,CAAC,MAAM,UAAU,IAAI,eAAe,OAAO,KAAK,WAAW;YACrG,cAAc,OAAO,GAAG;YACxB,2BAA2B;YAC3B,eAAe,OAAO,GAAG;YACzB,MAAM,iBAAiB,CAAC,GAAG;YAE3B;YACA,MAAM,WAAW,CAAC;YAElB,IAAI,OAAO,iBAAiB,aAC1B,kBAAkB,QAAQ,aAAa,eAAe;iBACjD;gBACL,kBAAkB,QAAQ,WAAW,eAAe;gBACpD,kBAAkB,QAAQ,YAAY,eAAe;YACvD;QACF;IACF;IAEA,IAAI,gBAAgB,CAAC;YACK;YAAf;QAAT,IAAI,KAAK,CAAA,eAAA,EAAE,SAAS,cAAX,0BAAA,gBAAe,oBAAA,EAAE,cAAc,cAAhB,wCAAA,iBAAkB,CAAC,EAAE,CAAC,UAAU;QACxD,IAAI,cAAc,OAAO,IAAI,OAAO,eAAe,OAAO,EAAE;YAC1D,cAAc,OAAO,GAAG;YACxB,2BAA2B;YAC3B,eAAe,OAAO,GAAG;YACzB,MAAM,WAAW,CAAC;YAClB;YAEA,IAAI,OAAO,iBAAiB,aAC1B,qBAAqB,QAAQ,aAAa,eAAe;iBACpD;gBACL,qBAAqB,QAAQ,WAAW,eAAe;gBACvD,qBAAqB,QAAQ,YAAY,eAAe;YAC1D;QACF;IACF;IAEA,IAAI,wBAAwB,aAAa,CAAC,IAAI,CAAA,GAAA,gCAAS,EAAE;QACvD,GAAI,OAAO,iBAAiB,cAAc;YACxC,eAAe,CAAC;gBACd,IAAI,EAAE,WAAW,KAAK,WAAY,CAAA,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,AAAD,GACnF;gBAEF,gBAAgB,EAAE,aAAa,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO;YACpE;QAAC,IAAI;YACH,aAAa,CAAC;gBACZ,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EACtD;gBAEF,gBAAgB,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,OAAO;YAClE;YACA,cAAc,CAAC;gBACb,gBAAgB,EAAE,aAAa,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO;YAC3H;QACF,CAAC;IACL,GAAG;IAEH,IAAI,oBAAoB,aAAa,CAAC,IAAI,CAAA,GAAA,gCAAS,EAAE;QACnD,GAAI,OAAO,iBAAiB,cAAc;YACxC,eAAe,CAAC;gBACd,IAAI,EAAE,WAAW,KAAK,WAAY,CAAA,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,AAAD,GACnF;gBAEF,YAAY,EAAE,SAAS;YACzB;QAAC,IAAI;YACH,aAAa,CAAC;gBACZ,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EACtD;gBAEF,YAAY;YACd;YACA,cAAc,CAAC;gBACb,YAAY,EAAE,cAAc,CAAC,EAAE,CAAC,UAAU;YAC5C;QACF,CAAC;IACL,GAAG,kBAAkB,eAAe;IAEpC,IAAI,EAAC,YAAY,gBAAgB,EAAC,GAAG,CAAA,GAAA,qCAAO,EAAE;QAC5C,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,IAAI,EAAC,YAAY,gBAAgB,EAAC,GAAG,CAAA,GAAA,qCAAO,EAAE;QAC5C,SAAS;YACP,gBAAgB;QAClB;IACF;IAEA,MAAM,WAAW,CAAC;QAChB,MAAM,UAAC,MAAM,EAAC,GAAG;QACjB,mCAAmC;QACnC,IAAI,WAAW,UAAU,OAAO,EAC9B,MAAM,SAAS,CAAC,WAAW,OAAO,KAAK;aAClC,IAAI,WAAW,UAAU,OAAO,EACrC,MAAM,SAAS,CAAC,WAAW,OAAO,KAAK;IAE3C;IAEA,IAAI,WAAW,CAAA,GAAA,2BAAI,OAAO,CAAA,GAAA,+BAAQ;IAElC,IAAI,QAAQ,MAAM,eAAe;IACjC,MAAM,6BAA6B,CAAA,GAAA,wBAAU,EAAE,CAAC;QAC9C,MAAM,eAAe,mCAAmC;QACxD,OAAO,CAAC,EACN,eACA,gBAAgB,MAAM,CAAC,qBAAqB;YAAC,MAAM,MAAM,cAAc,CAAC,SAAS;YAAS,OAAO,MAAM,kBAAkB,CAAC,SAAS;QAAO,KAE1I;YACE,gBAAgB,MAAM,CAAC,qBAAqB;gBAAC,MAAM,MAAM,cAAc,CAAC,SAAS;gBAAS,OAAO,MAAM,kBAAkB,CAAC,SAAS;YAAO;YAC1I,gBAAgB,MAAM,CAAC,qBAAqB;gBAAC,MAAM,MAAM,cAAc,CAAC,YAAY,WAAW,WAAW,UAAU;gBAAS,OAAO,MAAM,kBAAkB,CAAC,YAAY,WAAW,WAAW,UAAU;YAAO;YAChN,gBAAgB,MAAM,CAAC,qBAAqB;gBAAC,MAAM,MAAM,cAAc,CAAC,UAAU;gBAAS,OAAO,MAAM,kBAAkB,CAAC,UAAU;YAAO;SAC7I,CAAC,IAAI,CAAC,MACR,EAAE,EAAE,MAAM,YAAY,CAAC,QAAQ,CAAC;IACnC,GAAG;QAAC;QAAQ;QAAO;QAAiB;QAAiC;QAAyB;QAAU;QAAU;KAAS;IAE3H,IAAI,mBAAmB,gBAAgB,MAAM,CAAC;IAE9C,IAAI,uBAAuB,CAAA,GAAA,+BAAQ,EAAE;QACnC,GAAG,KAAK;QACR,cAAc,YAAY,gBAAgB,MAAM,CAAC,mBAAmB;YAAC,OAAO;YAAW,cAAc;QAAgB,KAAK;IAC5H;IAEA,IAAI,uBAAuB,CAAA,GAAA,+BAAQ,EAAE;QACnC,GAAG,KAAK;QACR,cAAc,YAAY,gBAAgB,MAAM,CAAC,mBAAmB;YAAC,OAAO;YAAW,cAAc;QAAgB,KAAK;IAC5H;IAEA,IAAI,0BAA0B,CAAA,GAAA,+BAAQ,EACpC;QACE,GAAG,KAAK;QACR,cAAc,YAAY,CAAC,EAAE,UAAU,EAAE,EAAE,iBAAiB,CAAC,GAAG;IAClE,GACA,WAAW,mBAAmB;IAGhC,IAAI,sBAAsB,gBAAgB,MAAM,CAAC;IAEjD,IAAI,uBAAC,mBAAmB,EAAC,GAAG,CAAA,GAAA,gDAAgB,EAAE;QAAC,OAAO;YACpD,SAAS;YACT,OAAO;YACP,QAAQ;YACR,eAAe;QACjB;IAAC;IAED,IAAI,uBACF,mBAAmB,mBACnB,eAAe,EAChB,GAAG,CAAA,GAAA,8CAAmB,EAAE;mBACvB;eACA;kBACA;kBACA;kBACA;IACF;IAEA,OAAO;QACL,gBAAgB;YACd,GAAG,uBAAuB;YAC1B,GAAG,qBAAqB;YACxB,GAAG,mBAAmB;YACtB,MAAM;QACR;QACA,YAAY;YACV,GAAG,iBAAiB;YACpB,GAAG,eAAe;YAClB,MAAM;QACR;QACA,aAAa;YACX,GAAG,oBAAoB;YACvB,GAAG,mBAAmB;YACtB,GAAG,gBAAgB;YACnB,MAAM;YACN,KAAK,MAAM,KAAK,CAAC,eAAe,CAAC,UAAU,QAAQ;YACnD,KAAK,MAAM,KAAK,CAAC,eAAe,CAAC,UAAU,QAAQ;YACnD,MAAM;YACN,wBAAwB;YACxB,kBAAkB,2BAA2B;YAC7C,oBAAoB;YACpB,oBAAoB,KAAK,CAAC,mBAAmB;YAC7C,gBAAgB,KAAK,CAAC,eAAe;YACrC,UAAU;YACV,OAAO,MAAM,KAAK,CAAC,eAAe,CAAC;YACnC,MAAM;YACN,UAAW,YAAY,CAAC,gBAAgB,iBAAiB,MAAM,YAAY;YAC3E;;;;MAIA,GACA,eAAgB,YAAY,CAAC,gBAAgB,iBAAiB,OAAO,0BAA0B,YAAY;sBAC3G;QACF;QACA,aAAa;YACX,GAAG,oBAAoB;YACvB,GAAG,mBAAmB;YACtB,GAAG,gBAAgB;YACnB,MAAM;YACN,KAAK,MAAM,KAAK,CAAC,eAAe,CAAC,UAAU,QAAQ;YACnD,KAAK,MAAM,KAAK,CAAC,eAAe,CAAC,UAAU,QAAQ;YACnD,MAAM;YACN,wBAAwB;YACxB,kBAAkB,2BAA2B;YAC7C,oBAAoB;YACpB,oBAAoB,KAAK,CAAC,mBAAmB;YAC7C,gBAAgB,KAAK,CAAC,eAAe;YACrC,UAAU;YACV,OAAO,MAAM,KAAK,CAAC,eAAe,CAAC;YACnC,MAAM;YACN,UAAW,YAAY,iBAAiB,MAAM,YAAY;YAC1D;;;;MAIA,GACA,eAAgB,YAAY,iBAAiB,OAAO,0BAA0B,YAAY;sBAC1F;QACF;IACF;AACF", "sources": ["packages/@react-aria/color/src/useColorArea.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaColorAreaProps, ColorChannel} from '@react-types/color';\nimport {ColorAreaState} from '@react-stately/color';\nimport {DOMAttributes} from '@react-types/shared';\nimport {focusWithoutScrolling, isAndroid, isIOS, mergeProps, useFormReset, useGlobalListeners, useLabels} from '@react-aria/utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport React, {ChangeEvent, InputHTMLAttributes, RefObject, useCallback, useRef, useState} from 'react';\nimport {useColorAreaGradient} from './useColorAreaGradient';\nimport {useFocus, useFocusWithin, useKeyboard, useMove} from '@react-aria/interactions';\nimport {useLocale, useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useVisuallyHidden} from '@react-aria/visually-hidden';\n\nexport interface ColorAreaAria {\n  /** Props for the color area container element. */\n  colorAreaProps: DOMAttributes,\n  /** Props for the thumb element. */\n  thumbProps: DOMAttributes,\n  /** Props for the visually hidden horizontal range input element. */\n  xInputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Props for the visually hidden vertical range input element. */\n  yInputProps: InputHTMLAttributes<HTMLInputElement>\n}\n\nexport interface AriaColorAreaOptions extends AriaColorAreaProps {\n  /** A ref to the input that represents the x axis of the color area. */\n  inputXRef: RefObject<HTMLInputElement>,\n  /** A ref to the input that represents the y axis of the color area. */\n  inputYRef: RefObject<HTMLInputElement>,\n  /** A ref to the color area containing element. */\n  containerRef: RefObject<Element>\n}\n\n/**\n * Provides the behavior and accessibility implementation for a color area component.\n * Color area allows users to adjust two channels of an RGB, HSL or HSB color value against a two-dimensional gradient background.\n */\nexport function useColorArea(props: AriaColorAreaOptions, state: ColorAreaState): ColorAreaAria {\n  let {\n    isDisabled,\n    inputXRef,\n    inputYRef,\n    containerRef,\n    'aria-label': ariaLabel,\n    xName,\n    yName\n  } = props;\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/color');\n\n  let {addGlobalListener, removeGlobalListener} = useGlobalListeners();\n\n  let {direction, locale} = useLocale();\n\n  let [focusedInput, setFocusedInput] = useState<'x' | 'y' | null>(null);\n  let focusInput = useCallback((inputRef:RefObject<HTMLInputElement> = inputXRef) => {\n    if (inputRef.current) {\n      focusWithoutScrolling(inputRef.current);\n    }\n  }, [inputXRef]);\n\n  useFormReset(inputXRef, [state.xValue, state.yValue], ([x, y]) => {\n    let newColor = state.value\n      .withChannelValue(state.channels.xChannel, x)\n      .withChannelValue(state.channels.yChannel, y);\n    state.setValue(newColor);\n  });\n\n  let [valueChangedViaKeyboard, setValueChangedViaKeyboard] = useState(false);\n  let [valueChangedViaInputChangeEvent, setValueChangedViaInputChangeEvent] = useState(false);\n  let {xChannel, yChannel, zChannel} = state.channels;\n  let xChannelStep = state.xChannelStep;\n  let yChannelStep = state.yChannelStep;\n\n  let currentPosition = useRef<{x: number, y: number} | null>(null);\n\n  let {keyboardProps} = useKeyboard({\n    onKeyDown(e) {\n      // these are the cases that useMove doesn't handle\n      if (!/^(PageUp|PageDown|Home|End)$/.test(e.key)) {\n        e.continuePropagation();\n        return;\n      }\n      // same handling as useMove, don't need to stop propagation, useKeyboard will do that for us\n      e.preventDefault();\n      // remember to set this and unset it so that onChangeEnd is fired\n      state.setDragging(true);\n      setValueChangedViaKeyboard(true);\n      let dir;\n      switch (e.key) {\n        case 'PageUp':\n          state.incrementY(state.yChannelPageStep);\n          dir = 'y';\n          break;\n        case 'PageDown':\n          state.decrementY(state.yChannelPageStep);\n          dir = 'y';\n          break;\n        case 'Home':\n          direction === 'rtl' ? state.incrementX(state.xChannelPageStep) : state.decrementX(state.xChannelPageStep);\n          dir = 'x';\n          break;\n        case 'End':\n          direction === 'rtl' ? state.decrementX(state.xChannelPageStep) : state.incrementX(state.xChannelPageStep);\n          dir = 'x';\n          break;\n      }\n      state.setDragging(false);\n      if (dir) {\n        let input = dir === 'x' ? inputXRef : inputYRef;\n        focusInput(input);\n        setFocusedInput(dir);\n      }\n    }\n  });\n\n  let moveHandler = {\n    onMoveStart() {\n      currentPosition.current = null;\n      state.setDragging(true);\n    },\n    onMove({deltaX, deltaY, pointerType, shiftKey}) {\n      let {\n        incrementX,\n        decrementX,\n        incrementY,\n        decrementY,\n        xChannelPageStep,\n        xChannelStep,\n        yChannelPageStep,\n        yChannelStep,\n        getThumbPosition,\n        setColorFromPoint\n      } = state;\n      if (currentPosition.current == null) {\n        currentPosition.current = getThumbPosition();\n      }\n      let {width, height} = containerRef.current?.getBoundingClientRect() || {width: 0, height: 0};\n      let valueChanged = deltaX !== 0 || deltaY !== 0;\n      if (pointerType === 'keyboard') {\n        let deltaXValue = shiftKey && xChannelPageStep > xChannelStep ? xChannelPageStep : xChannelStep;\n        let deltaYValue = shiftKey && yChannelPageStep > yChannelStep ? yChannelPageStep : yChannelStep;\n        if ((deltaX > 0 && direction === 'ltr') || (deltaX < 0 && direction === 'rtl')) {\n          incrementX(deltaXValue);\n        } else if ((deltaX < 0 && direction === 'ltr') || (deltaX > 0 && direction === 'rtl')) {\n          decrementX(deltaXValue);\n        } else if (deltaY > 0) {\n          decrementY(deltaYValue);\n        } else if (deltaY < 0) {\n          incrementY(deltaYValue);\n        }\n        setValueChangedViaKeyboard(valueChanged);\n        // set the focused input based on which axis has the greater delta\n        focusedInput = valueChanged && Math.abs(deltaY) > Math.abs(deltaX) ? 'y' : 'x';\n        setFocusedInput(focusedInput);\n      } else {\n        currentPosition.current.x += (direction === 'rtl' ? -1 : 1) * deltaX / width ;\n        currentPosition.current.y += deltaY / height;\n        setColorFromPoint(currentPosition.current.x, currentPosition.current.y);\n      }\n    },\n    onMoveEnd() {\n      isOnColorArea.current = false;\n      state.setDragging(false);\n      let input = focusedInput === 'x' ? inputXRef : inputYRef;\n      focusInput(input);\n    }\n  };\n  let {moveProps: movePropsThumb} = useMove(moveHandler);\n\n  let {focusWithinProps} = useFocusWithin({\n    onFocusWithinChange: (focusWithin:boolean) => {\n      if (!focusWithin) {\n        setValueChangedViaKeyboard(false);\n        setValueChangedViaInputChangeEvent(false);\n      }\n    }\n  });\n\n  let currentPointer = useRef<number | null | undefined>(undefined);\n  let isOnColorArea = useRef<boolean>(false);\n  let {moveProps: movePropsContainer} = useMove({\n    onMoveStart() {\n      if (isOnColorArea.current) {\n        moveHandler.onMoveStart();\n      }\n    },\n    onMove(e) {\n      if (isOnColorArea.current) {\n        moveHandler.onMove(e);\n      }\n    },\n    onMoveEnd() {\n      if (isOnColorArea.current) {\n        moveHandler.onMoveEnd();\n      }\n    }\n  });\n\n  let onThumbDown = (id: number | null | undefined) => {\n    if (!state.isDragging) {\n      currentPointer.current = id;\n      setValueChangedViaKeyboard(false);\n      focusInput();\n      state.setDragging(true);\n      if (typeof PointerEvent !== 'undefined') {\n        addGlobalListener(window, 'pointerup', onThumbUp, false);\n      } else {\n        addGlobalListener(window, 'mouseup', onThumbUp, false);\n        addGlobalListener(window, 'touchend', onThumbUp, false);\n      }\n    }\n  };\n\n  let onThumbUp = (e) => {\n    let id = e.pointerId ?? e.changedTouches?.[0].identifier;\n    if (id === currentPointer.current) {\n      setValueChangedViaKeyboard(false);\n      focusInput();\n      state.setDragging(false);\n      currentPointer.current = undefined;\n      isOnColorArea.current = false;\n\n      if (typeof PointerEvent !== 'undefined') {\n        removeGlobalListener(window, 'pointerup', onThumbUp, false);\n      } else {\n        removeGlobalListener(window, 'mouseup', onThumbUp, false);\n        removeGlobalListener(window, 'touchend', onThumbUp, false);\n      }\n    }\n  };\n\n  let onColorAreaDown = (colorArea: Element, id: number | null | undefined, clientX: number, clientY: number) => {\n    let rect = colorArea.getBoundingClientRect();\n    let {width, height} = rect;\n    let x = (clientX - rect.x) / width;\n    let y = (clientY - rect.y) / height;\n    if (direction === 'rtl') {\n      x = 1 - x;\n    }\n    if (x >= 0 && x <= 1 && y >= 0 && y <= 1 && !state.isDragging && currentPointer.current === undefined) {\n      isOnColorArea.current = true;\n      setValueChangedViaKeyboard(false);\n      currentPointer.current = id;\n      state.setColorFromPoint(x, y);\n\n      focusInput();\n      state.setDragging(true);\n\n      if (typeof PointerEvent !== 'undefined') {\n        addGlobalListener(window, 'pointerup', onColorAreaUp, false);\n      } else {\n        addGlobalListener(window, 'mouseup', onColorAreaUp, false);\n        addGlobalListener(window, 'touchend', onColorAreaUp, false);\n      }\n    }\n  };\n\n  let onColorAreaUp = (e) => {\n    let id = e.pointerId ?? e.changedTouches?.[0].identifier;\n    if (isOnColorArea.current && id === currentPointer.current) {\n      isOnColorArea.current = false;\n      setValueChangedViaKeyboard(false);\n      currentPointer.current = undefined;\n      state.setDragging(false);\n      focusInput();\n\n      if (typeof PointerEvent !== 'undefined') {\n        removeGlobalListener(window, 'pointerup', onColorAreaUp, false);\n      } else {\n        removeGlobalListener(window, 'mouseup', onColorAreaUp, false);\n        removeGlobalListener(window, 'touchend', onColorAreaUp, false);\n      }\n    }\n  };\n\n  let colorAreaInteractions = isDisabled ? {} : mergeProps({\n    ...(typeof PointerEvent !== 'undefined' ? {\n      onPointerDown: (e: React.PointerEvent) => {\n        if (e.pointerType === 'mouse' && (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey)) {\n          return;\n        }\n        onColorAreaDown(e.currentTarget, e.pointerId, e.clientX, e.clientY);\n      }} : {\n        onMouseDown: (e: React.MouseEvent) => {\n          if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) {\n            return;\n          }\n          onColorAreaDown(e.currentTarget, undefined, e.clientX, e.clientY);\n        },\n        onTouchStart: (e: React.TouchEvent) => {\n          onColorAreaDown(e.currentTarget, e.changedTouches[0].identifier, e.changedTouches[0].clientX, e.changedTouches[0].clientY);\n        }\n      })\n  }, movePropsContainer);\n\n  let thumbInteractions = isDisabled ? {} : mergeProps({\n    ...(typeof PointerEvent !== 'undefined' ? {\n      onPointerDown: (e: React.PointerEvent) => {\n        if (e.pointerType === 'mouse' && (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey)) {\n          return;\n        }\n        onThumbDown(e.pointerId);\n      }} : {\n        onMouseDown: (e: React.MouseEvent) => {\n          if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) {\n            return;\n          }\n          onThumbDown(undefined);\n        },\n        onTouchStart: (e: React.TouchEvent) => {\n          onThumbDown(e.changedTouches[0].identifier);\n        }\n      })\n  }, focusWithinProps, keyboardProps, movePropsThumb);\n\n  let {focusProps: xInputFocusProps} = useFocus({\n    onFocus: () => {\n      setFocusedInput('x');\n    }\n  });\n\n  let {focusProps: yInputFocusProps} = useFocus({\n    onFocus: () => {\n      setFocusedInput('y');\n    }\n  });\n\n  const onChange = (e: ChangeEvent<HTMLInputElement>) => {\n    const {target} = e;\n    setValueChangedViaInputChangeEvent(true);\n    if (target === inputXRef.current) {\n      state.setXValue(parseFloat(target.value));\n    } else if (target === inputYRef.current) {\n      state.setYValue(parseFloat(target.value));\n    }\n  };\n\n  let isMobile = isIOS() || isAndroid();\n\n  let value = state.getDisplayColor();\n  const getAriaValueTextForChannel = useCallback((channel:ColorChannel) => {\n    const isAfterInput = valueChangedViaInputChangeEvent || valueChangedViaKeyboard;\n    return `${\n      isAfterInput ?\n      stringFormatter.format('colorNameAndValue', {name: value.getChannelName(channel, locale), value: value.formatChannelValue(channel, locale)})\n      :\n      [\n        stringFormatter.format('colorNameAndValue', {name: value.getChannelName(channel, locale), value: value.formatChannelValue(channel, locale)}),\n        stringFormatter.format('colorNameAndValue', {name: value.getChannelName(channel === yChannel ? xChannel : yChannel, locale), value: value.formatChannelValue(channel === yChannel ? xChannel : yChannel, locale)}),\n        stringFormatter.format('colorNameAndValue', {name: value.getChannelName(zChannel, locale), value: value.formatChannelValue(zChannel, locale)})\n      ].join(', ')\n    }, ${value.getColorName(locale)}`;\n  }, [locale, value, stringFormatter, valueChangedViaInputChangeEvent, valueChangedViaKeyboard, xChannel, yChannel, zChannel]);\n\n  let colorPickerLabel = stringFormatter.format('colorPicker');\n\n  let xInputLabellingProps = useLabels({\n    ...props,\n    'aria-label': ariaLabel ? stringFormatter.format('colorInputLabel', {label: ariaLabel, channelLabel: colorPickerLabel}) : colorPickerLabel\n  });\n\n  let yInputLabellingProps = useLabels({\n    ...props,\n    'aria-label': ariaLabel ? stringFormatter.format('colorInputLabel', {label: ariaLabel, channelLabel: colorPickerLabel}) : colorPickerLabel\n  });\n\n  let colorAreaLabellingProps = useLabels(\n    {\n      ...props,\n      'aria-label': ariaLabel ? `${ariaLabel}, ${colorPickerLabel}` : undefined\n    },\n    isMobile ? colorPickerLabel : undefined\n  );\n\n  let ariaRoleDescription = stringFormatter.format('twoDimensionalSlider');\n\n  let {visuallyHiddenProps} = useVisuallyHidden({style: {\n    opacity: '0.0001',\n    width: '100%',\n    height: '100%',\n    pointerEvents: 'none'\n  }});\n\n  let {\n    colorAreaStyleProps,\n    thumbStyleProps\n  } = useColorAreaGradient({\n    direction,\n    state,\n    xChannel,\n    yChannel,\n    zChannel\n  });\n\n  return {\n    colorAreaProps: {\n      ...colorAreaLabellingProps,\n      ...colorAreaInteractions,\n      ...colorAreaStyleProps,\n      role: 'group'\n    },\n    thumbProps: {\n      ...thumbInteractions,\n      ...thumbStyleProps,\n      role: 'presentation'\n    },\n    xInputProps: {\n      ...xInputLabellingProps,\n      ...visuallyHiddenProps,\n      ...xInputFocusProps,\n      type: 'range',\n      min: state.value.getChannelRange(xChannel).minValue,\n      max: state.value.getChannelRange(xChannel).maxValue,\n      step: xChannelStep,\n      'aria-roledescription': ariaRoleDescription,\n      'aria-valuetext': getAriaValueTextForChannel(xChannel),\n      'aria-orientation': 'horizontal',\n      'aria-describedby': props['aria-describedby'],\n      'aria-details': props['aria-details'],\n      disabled: isDisabled,\n      value: state.value.getChannelValue(xChannel),\n      name: xName,\n      tabIndex: (isMobile || !focusedInput || focusedInput === 'x' ? undefined : -1),\n      /*\n        So that only a single \"2d slider\" control shows up when listing form elements for screen readers,\n        add aria-hidden=\"true\" to the unfocused control when the value has not changed via the keyboard,\n        but remove aria-hidden to reveal the input for each channel when the value has changed with the keyboard.\n      */\n      'aria-hidden': (isMobile || !focusedInput || focusedInput === 'x' || valueChangedViaKeyboard ? undefined : 'true'),\n      onChange\n    },\n    yInputProps: {\n      ...yInputLabellingProps,\n      ...visuallyHiddenProps,\n      ...yInputFocusProps,\n      type: 'range',\n      min: state.value.getChannelRange(yChannel).minValue,\n      max: state.value.getChannelRange(yChannel).maxValue,\n      step: yChannelStep,\n      'aria-roledescription': ariaRoleDescription,\n      'aria-valuetext': getAriaValueTextForChannel(yChannel),\n      'aria-orientation': 'vertical',\n      'aria-describedby': props['aria-describedby'],\n      'aria-details': props['aria-details'],\n      disabled: isDisabled,\n      value: state.value.getChannelValue(yChannel),\n      name: yName,\n      tabIndex: (isMobile || focusedInput === 'y' ? undefined : -1),\n      /*\n        So that only a single \"2d slider\" control shows up when listing form elements for screen readers,\n        add aria-hidden=\"true\" to the unfocused input when the value has not changed via the keyboard,\n        but remove aria-hidden to reveal the input for each channel when the value has changed with the keyboard.\n      */\n      'aria-hidden': (isMobile || focusedInput === 'y' || valueChangedViaKeyboard ? undefined : 'true'),\n      onChange\n    }\n  };\n}\n"], "names": [], "version": 3, "file": "useColorArea.main.js.map"}