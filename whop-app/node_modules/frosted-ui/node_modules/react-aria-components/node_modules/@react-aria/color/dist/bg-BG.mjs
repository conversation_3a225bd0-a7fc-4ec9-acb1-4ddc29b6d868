var $bf2b4507594e3d45$exports = {};
$bf2b4507594e3d45$exports = {
    "colorInputLabel": (args)=>`${args.label}, ${args.channelLabel}`,
    "colorNameAndValue": (args)=>`${args.name}: ${args.value}`,
    "colorPicker": `\u{421}\u{440}\u{435}\u{434}\u{441}\u{442}\u{432}\u{43E} \u{437}\u{430} \u{438}\u{437}\u{431}\u{438}\u{440}\u{430}\u{43D}\u{435} \u{43D}\u{430} \u{446}\u{432}\u{44F}\u{442}`,
    "colorSwatch": `\u{446}\u{432}\u{435}\u{442}\u{43D}\u{430} \u{43C}\u{43E}\u{441}\u{442}\u{440}\u{430}`,
    "transparent": `\u{43F}\u{440}\u{43E}\u{437}\u{440}\u{430}\u{447}\u{435}\u{43D}`,
    "twoDimensionalSlider": `2D \u{43F}\u{43B}\u{44A}\u{437}\u{433}\u{430}\u{447}`
};


export {$bf2b4507594e3d45$exports as default};
//# sourceMappingURL=bg-BG.module.js.map
