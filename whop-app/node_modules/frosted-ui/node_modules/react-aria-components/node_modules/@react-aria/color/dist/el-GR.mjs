var $18e4d1d5b500a9ee$exports = {};
$18e4d1d5b500a9ee$exports = {
    "colorInputLabel": (args)=>`${args.label}, ${args.channelLabel}`,
    "colorNameAndValue": (args)=>`${args.name}: ${args.value}`,
    "colorPicker": `\u{395}\u{3C0}\u{3B9}\u{3BB}\u{3BF}\u{3B3}\u{3AD}\u{3B1}\u{3C2} \u{3C7}\u{3C1}\u{3C9}\u{3BC}\u{3AC}\u{3C4}\u{3C9}\u{3BD}`,
    "colorSwatch": `\u{3C7}\u{3C1}\u{3C9}\u{3BC}\u{3B1}\u{3C4}\u{3B9}\u{3BA}\u{3CC} \u{3B4}\u{3B5}\u{3AF}\u{3B3}\u{3BC}\u{3B1}`,
    "transparent": `\u{3B4}\u{3B9}\u{3B1}\u{3C6}\u{3B1}\u{3BD}\u{3AD}\u{3C2}`,
    "twoDimensionalSlider": `\u{3A1}\u{3C5}\u{3B8}\u{3BC}\u{3B9}\u{3C3}\u{3C4}\u{3B9}\u{3BA}\u{3CC} 2D`
};


export {$18e4d1d5b500a9ee$exports as default};
//# sourceMappingURL=el-GR.module.js.map
