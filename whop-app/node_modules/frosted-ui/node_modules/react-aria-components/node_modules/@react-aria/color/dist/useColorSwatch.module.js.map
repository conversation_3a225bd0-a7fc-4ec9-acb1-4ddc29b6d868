{"mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;AAkCM,SAAS,0CAAe,KAA2B;IACxD,IAAI,EAAC,OAAO,KAAK,aAAE,SAAS,EAAC,GAAG;IAChC,IAAI,eAAe,SAAS;IAC5B,IAAI,QAAQ,CAAA,GAAA,cAAM,EAAE,IAAM,OAAO,iBAAiB,WAAW,CAAA,GAAA,iBAAS,EAAE,gBAAgB,cAAc;QAAC;KAAa;IACpH,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,gBAAQ;IACvB,IAAI,WAAW,CAAA,GAAA,qBAAa,EAAE,OAAO;QAAC,WAAW;IAAI;IACrD,IAAI,kBAAkB,CAAA,GAAA,kCAA0B,EAAE,CAAA,GAAA,oDAAW,GAAG;IAChE,IAAI,KAAK,CAAA,GAAA,YAAI,EAAE,MAAM,EAAE;IAEvB,IAAI,CAAC,WACH,YAAY,MAAM,eAAe,CAAC,aAAa,IAAI,gBAAgB,MAAM,CAAC,iBAAiB,MAAM,YAAY,CAAC;IAGhH,OAAO;QACL,kBAAkB;YAChB,GAAG,QAAQ;YACX,MAAM;YACN,wBAAwB,gBAAgB,MAAM,CAAC;YAC/C,cAAc;gBAAC;gBAAW,KAAK,CAAC,aAAa,IAAI;aAAG,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;YAC1E,mBAAmB,KAAK,CAAC,kBAAkB,GAAG,CAAC,EAAE,GAAG,CAAC,EAAE,KAAK,CAAC,kBAAkB,CAAC,CAAC,GAAG;gBACpF;YACA,OAAO;gBACL,iBAAiB,MAAM,QAAQ,CAAC;gBAChC,aAAa;gBACb,mBAAmB;YACrB;QACF;QACA,OAAO,SAAS;IAClB;AACF", "sources": ["packages/@react-aria/color/src/useColorSwatch.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\nimport {Color} from '@react-types/color';\nimport {filterDOMProps, useId} from '@react-aria/utils';\nimport {HTMLAttributes, useMemo} from 'react';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {parseColor} from '@react-stately/color';\nimport {useLocale, useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaColorSwatchProps extends AriaLabelingProps, DOMProps {\n  /** The color value to display in the swatch. */\n  color?: string | Color | null,\n  /** \n   * A localized accessible name for the color.\n   * By default, a description is generated from the color value,\n   * but this can be overridden if you have a more specific color\n   * name (e.g. Pantone colors).\n   */\n  colorName?: string\n}\n\nexport interface ColorSwatchAria {\n  /** Props for the color swatch element. */\n  colorSwatchProps: HTMLAttributes<HTMLElement>,\n  /** The parsed color value of the swatch. */\n  color: Color\n}\n\n/**\n * Provides the accessibility implementation for a color swatch component.\n * A color swatch displays a preview of a selected color.\n */\nexport function useColorSwatch(props: AriaColorSwatchProps): ColorSwatchAria {\n  let {color: value, colorName} = props;\n  let nonNullValue = value || '#fff0';\n  let color = useMemo(() => typeof nonNullValue === 'string' ? parseColor(nonNullValue) : nonNullValue, [nonNullValue]);\n  let {locale} = useLocale();\n  let DOMProps = filterDOMProps(props, {labelable: true});\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/color');\n  let id = useId(props.id);\n\n  if (!colorName) {\n    colorName = color.getChannelValue('alpha') === 0 ? stringFormatter.format('transparent') : color.getColorName(locale);\n  }\n\n  return {\n    colorSwatchProps: {\n      ...DOMProps,\n      role: 'img',\n      'aria-roledescription': stringFormatter.format('colorSwatch'),\n      'aria-label': [colorName, props['aria-label'] || ''].filter(Boolean).join(', '),\n      'aria-labelledby': props['aria-labelledby'] ? `${id} ${props['aria-labelledby']}` : undefined,\n      id,\n      style: {\n        backgroundColor: color.toString('css'),\n        // @ts-ignore\n        forcedColorAdjust: 'none'\n      }\n    },\n    color: color || null\n  };\n}\n"], "names": [], "version": 3, "file": "useColorSwatch.module.js.map"}