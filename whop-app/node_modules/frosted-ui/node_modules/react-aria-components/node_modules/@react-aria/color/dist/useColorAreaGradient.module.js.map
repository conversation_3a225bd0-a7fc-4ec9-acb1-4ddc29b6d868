{"mappings": ";;;AAAA;;;;;;;;;;AAUA;;AAMA,MAAM,4BAAM,CAAC,QAAiB;QAAC;QAAG;QAAI;QAAK;QAAK;QAAK;QAAK;KAAI,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM,gBAAgB,CAAC,OAAO,KAAK,QAAQ,CAAC,QAAQ,IAAI,CAAC;AACnI,MAAM,mCAAa,CAAC,QAAiB,CAAC,EAAE,MAAM,gBAAgB,CAAC,cAAc,GAAG,aAAa,CAAC;AAE9F,MAAM,oCAAc;SAClB;gBACA;IACA,WAAW,IAAM;AACnB;AAEA,MAAM,oCAAc;SAClB;gBACA;IACA,YAAY,IAAM;AACpB;AAWO,SAAS,0CAAqB,aAAC,SAAS,SAAE,KAAK,YAAE,QAAQ,YAAE,QAAQ,YAAE,QAAQ,EAAC;IACnF,IAAI,YAAY,CAAA,GAAA,cAAM,EAAa;QACjC,IAAI,MAAM,cAAc,QAAQ,SAAS;QACzC,IAAI,kBAAkB,CAAC;QACvB,IAAI,SAAS,MAAM,KAAK,CAAC,eAAe,CAAC;QAEzC,OAAQ,MAAM,KAAK,CAAC,aAAa;YAC/B,KAAK;gBAAO;oBACV,IAAI,MAAM,CAAA,GAAA,iBAAS,EAAE;oBACrB,kBAAkB;wBAChB,YAAY;4BACV,4FAA4F;4BAC5F,6FAA6F;4BAC7F,6DAA6D;4BAC7D,CAAC,mBAAmB,EAAE,IAAI,EAAE,EAAE,IAAI,gBAAgB,CAAC,UAAU,GAAG,EAAE,EAAE,IAAI,gBAAgB,CAAC,UAAU,KAAK,CAAC,CAAC;4BAC1G,CAAC,wBAAwB,EAAE,IAAI,gBAAgB,CAAC,UAAU,GAAG,EAAE,EAAE,IAAI,gBAAgB,CAAC,UAAU,KAAK,CAAC,CAAC;4BACvG,IAAI,gBAAgB,CAAC,UAAU;yBAChC,CAAC,IAAI,CAAC;wBACP,qBAAqB;oBACvB;oBACA;gBACF;YACA,KAAK;gBAAO;oBACV,IAAI,WAAW,MAAM,KAAK,CAAC,gBAAgB;oBAC3C,IAAI,QAAQ,CAAA,GAAA,iBAAS,EAAE,qBAAqB,gBAAgB,CAAC,UAAU;oBAEvE,IAAI,KAAK,SACN,MAAM,CAAC,CAAA,IAAK,MAAM,UAClB,GAAG,CAAC,CAAA,IAAK,CAAC,mBAAmB,EAAE,MAAM,WAAW,MAAM,MAAM,EAAE,EAAE,iCAAW,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,EACxF,OAAO;oBACV,IAAI,aAAa,OACf,GAAG,IAAI,CAAC,MAAM,QAAQ,CAAC;oBAGzB,kBAAkB;wBAChB,YAAY,GAAG,IAAI,CAAC;oBACtB;oBACA;gBACF;YACA,KAAK;gBAAO;oBACV,IAAI,WAAW,MAAM,KAAK,CAAC,gBAAgB;oBAC3C,IAAI,QAAQ,CAAA,GAAA,iBAAS,EAAE,sBAAsB,gBAAgB,CAAC,UAAU;oBAExE,IAAI,KAAK,SACN,MAAM,CAAC,CAAA,IAAK,MAAM,UAClB,GAAG,CAAC,CAAA,IAAK,CAAC,mBAAmB,EAAE,MAAM,WAAW,MAAM,MAAM,EAAE,EAAE,iCAAW,CAAC,EAAE,CAAC,OAAO,CAAC,CAAC,EACxF,OAAO;oBACV,IAAI,aAAa,OACf,GAAG,IAAI,CAAC,MAAM,QAAQ,CAAC;oBAGzB,kBAAkB;wBAChB,YAAY,GAAG,IAAI,CAAC;oBACtB;oBACA;gBACF;QACF;QAEA,IAAI,KAAC,CAAC,KAAE,CAAC,EAAC,GAAG,MAAM,gBAAgB;QAEnC,IAAI,cAAc,OAChB,IAAI,IAAI;QAGV,IAAI,6BAA6B;YAAC,mBAAmB;QAAM;QAE3D,OAAO;YACL,qBAAqB;gBACnB,OAAO;oBACL,UAAU;oBACV,aAAa;oBACb,GAAG,0BAA0B;oBAC7B,GAAG,eAAe;gBACpB;YACF;YACA,iBAAiB;gBACf,OAAO;oBACL,UAAU;oBACV,MAAM,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;oBACnB,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,CAAC;oBAClB,WAAW;oBACX,aAAa;oBACb,GAAG,0BAA0B;gBAC/B;YACF;QACF;IACF,GAAG;QAAC;QAAW;QAAO;QAAU;QAAU;KAAS;IAEnD,OAAO;AACT", "sources": ["packages/@react-aria/color/src/useColorAreaGradient.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n*/\n\nimport {Color} from '@react-types/color';\nimport {CSSProperties, useMemo} from 'react';\nimport {parseColor} from '@react-stately/color';\n\nconst hue = (color: Color) => [0, 60, 120, 180, 240, 300, 360].map(hue => color.withChannelValue('hue', hue).toString('css')).join(', ');\nconst saturation = (color: Color) => `${color.withChannelValue('saturation', 0)}, transparent`;\n\nconst hslChannels = {\n  hue,\n  saturation,\n  lightness: () => 'black, transparent, white'\n};\n\nconst hsbChannels = {\n  hue,\n  saturation,\n  brightness: () => 'black, transparent'\n};\n\ninterface Gradients {\n  colorAreaStyleProps: {\n    style: CSSProperties\n  },\n  thumbStyleProps: {\n    style: CSSProperties\n  }\n}\n\nexport function useColorAreaGradient({direction, state, zChannel, xChannel, yChannel}): Gradients {\n  let returnVal = useMemo<Gradients>(() => {\n    let end = direction === 'rtl' ? 'left' : 'right';\n    let colorAreaStyles = {};\n    let zValue = state.value.getChannelValue(zChannel);\n\n    switch (state.value.getColorSpace()) {\n      case 'rgb': {\n        let rgb = parseColor('rgb(0, 0, 0)');\n        colorAreaStyles = {\n          background: [\n            // The screen blend mode multiplies the inverse of each channel, e.g. 1 - (1 - a) * (1 - b).\n            // Create a layer for each channel, with the other channels as 0. After blending, this should\n            // result in the gradients being combined channel by channel.\n            `linear-gradient(to ${end}, ${rgb.withChannelValue(xChannel, 0)}, ${rgb.withChannelValue(xChannel, 255)})`,\n            `linear-gradient(to top, ${rgb.withChannelValue(yChannel, 0)}, ${rgb.withChannelValue(yChannel, 255)})`,\n            rgb.withChannelValue(zChannel, zValue)\n          ].join(','),\n          backgroundBlendMode: 'screen'\n        };\n        break;\n      }\n      case 'hsl': {\n        let channels = state.value.getColorChannels();\n        let value = parseColor('hsl(0, 100%, 50%)').withChannelValue(zChannel, zValue);\n\n        let bg = channels\n          .filter(c => c !== zChannel)\n          .map(c => `linear-gradient(to ${c === xChannel ? end : 'top'}, ${hslChannels[c](value)})`)\n          .reverse();\n        if (zChannel === 'hue') {\n          bg.push(value.toString('css'));\n        }\n    \n        colorAreaStyles = {\n          background: bg.join(', ')\n        };\n        break;\n      }\n      case 'hsb': {\n        let channels = state.value.getColorChannels();\n        let value = parseColor('hsb(0, 100%, 100%)').withChannelValue(zChannel, zValue);\n\n        let bg = channels\n          .filter(c => c !== zChannel)\n          .map(c => `linear-gradient(to ${c === xChannel ? end : 'top'}, ${hsbChannels[c](value)})`)\n          .reverse();\n        if (zChannel === 'hue') {\n          bg.push(value.toString('css'));\n        }\n    \n        colorAreaStyles = {\n          background: bg.join(', ')\n        };\n        break;\n      }\n    }\n\n    let {x, y} = state.getThumbPosition();\n\n    if (direction === 'rtl') {\n      x = 1 - x;\n    }\n\n    let forcedColorAdjustNoneStyle = {forcedColorAdjust: 'none'};\n\n    return {\n      colorAreaStyleProps: {\n        style: {\n          position: 'relative',\n          touchAction: 'none',\n          ...forcedColorAdjustNoneStyle,\n          ...colorAreaStyles\n        }\n      },\n      thumbStyleProps: {\n        style: {\n          position: 'absolute',\n          left: `${x * 100}%`,\n          top: `${y * 100}%`,\n          transform: 'translate(-50%, -50%)',\n          touchAction: 'none',\n          ...forcedColorAdjustNoneStyle\n        }\n      }\n    };\n  }, [direction, state, zChannel, xChannel, yChannel]);\n\n  return returnVal;\n}\n\n"], "names": [], "version": 3, "file": "useColorAreaGradient.module.js.map"}