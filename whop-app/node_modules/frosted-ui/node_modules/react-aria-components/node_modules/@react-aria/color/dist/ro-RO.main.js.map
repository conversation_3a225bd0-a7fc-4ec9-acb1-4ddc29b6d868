{"mappings": "AAAA,iBAAiB;IAAG,mBAAmB,CAAC,OAAS,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,YAAY,CAAC,CAAC;IACpF,qBAAqB,CAAC,OAAS,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,CAAC;IAC5D,eAAe,CAAC,kBAAkB,CAAC;IACnC,eAAe,CAAC,mBAAmB,CAAC;IACpC,eAAe,CAAC,WAAW,CAAC;IAC5B,wBAAwB,CAAC,SAAS,CAAC;AACrC", "sources": ["packages/@react-aria/color/intl/ro-RO.json"], "sourcesContent": ["{\n  \"colorInputLabel\": \"{label}, {channelLabel}\",\n  \"colorNameAndValue\": \"{name}: {value}\",\n  \"colorPicker\": \"Selector de culori\",\n  \"colorSwatch\": \"specimen de culoare\",\n  \"transparent\": \"transparent\",\n  \"twoDimensionalSlider\": \"Glisor 2D\"\n}\n"], "names": [], "version": 3, "file": "ro-RO.main.js.map"}