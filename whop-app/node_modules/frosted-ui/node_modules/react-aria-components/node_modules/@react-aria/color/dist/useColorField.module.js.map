{"mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AAiCM,SAAS,0CACd,KAA0B,EAC1B,KAAsB,EACtB,GAAgC;IAEhC,IAAI,cACF,UAAU,cACV,UAAU,cACV,UAAU,mBACV,eAAe,sBACf,qBAAqB,QACtB,GAAG;IAEJ,IAAI,cACF,UAAU,cACV,UAAU,aACV,SAAS,aACT,SAAS,kBACT,cAAc,kBACd,cAAc,UACd,MAAM,EACP,GAAG;IAEJ,IAAI,UAAU,CAAA,GAAA,YAAI;IAClB,IAAI,mBAAC,eAAe,EAAC,GAAG,CAAA,GAAA,oBAAY,EAClC;oBACE;oBACA;oBACA;QACA,UAAU;QACV,UAAU;QACV,aAAa;QACb,kBAAkB;QAClB,aAAa;QACb,kBAAkB;QAClB,OAAO,aAAa,WAAW,QAAQ,KAAK;QAC5C,WAAW,aAAa,WAAW,QAAQ,CAAC,SAAS;IACvD;IAGF,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,eAAO,EAAE;IAC7C,IAAI,oBAAC,gBAAgB,EAAC,GAAG,CAAA,GAAA,qBAAa,EAAE;oBAAC;QAAY,qBAAqB;IAAc;IAExF,IAAI,UAAU,CAAA,GAAA,kBAAU,EAAE,CAAC;QACzB,IAAI,KAAK,GAAG,CAAC,EAAE,MAAM,KAAK,KAAK,GAAG,CAAC,EAAE,MAAM,GACzC;QAEF,IAAI,EAAE,MAAM,GAAG,GACb;aACK,IAAI,EAAE,MAAM,GAAG,GACpB;IAEJ,GAAG;QAAC;QAAW;KAAU;IACzB,mEAAmE;IACnE,IAAI,oBAAoB,mBAAmB,cAAc,cAAc,CAAC;IACxE,CAAA,GAAA,qBAAa,EAAE;QAAC,UAAU;QAAS,YAAY;IAAiB,GAAG;IAEnE,IAAI,WAAW,CAAA;QACb,IAAI,MAAM,QAAQ,CAAC,QACjB,MAAM,aAAa,CAAC;IAExB;IAEA,IAAI,cAAC,UAAU,EAAE,GAAG,YAAW,GAAG,CAAA,GAAA,4BAAoB,EAAE;QACtD,GAAG,KAAK;QACR,IAAI;QACJ,OAAO;QACP,cAAc;QACd,UAAU;QACV,CAAC,CAAA,GAAA,iCAAyB,EAAE,EAAE;QAC9B,MAAM;QACN,cAAc;kBACd;IACF,GAAG,OAAO;IAEV,aAAa,CAAA,GAAA,iBAAS,EAAE,YAAY,iBAAiB,kBAAkB;QACrE,MAAM;QACN,iBAAiB;QACjB,iBAAiB;QACjB,iBAAiB;QACjB,kBAAkB;QAClB,aAAa;QACb,YAAY;QACZ,QAAQ;IACV;IAEA,IAAI,uBAAuB,UACzB,UAAU,CAAC,gBAAgB,GAAG;IAGhC,OAAO;oBACL;QACA,GAAG,UAAU;IACf;AACF", "sources": ["packages/@react-aria/color/src/useColorField.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaColorFieldProps} from '@react-types/color';\nimport {ColorFieldState} from '@react-stately/color';\nimport {DOMAttributes, ValidationResult} from '@react-types/shared';\nimport {\n  InputHTMLAttributes,\n  LabelHTMLAttributes,\n  RefObject,\n  useCallback,\n  useState\n} from 'react';\nimport {mergeProps, useId} from '@react-aria/utils';\nimport {privateValidationStateProp} from '@react-stately/form';\nimport {useFocusWithin, useScrollWheel} from '@react-aria/interactions';\nimport {useFormattedTextField} from '@react-aria/textfield';\nimport {useSpinButton} from '@react-aria/spinbutton';\n\nexport interface ColorFieldAria extends ValidationResult {\n  /** Props for the label element. */\n  labelProps: LabelHTMLAttributes<HTMLLabelElement>,\n  /** Props for the input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Props for the text field's description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the text field's error message element, if any. */\n  errorMessageProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a color field component.\n * Color fields allow users to enter and adjust a hex color value.\n */\nexport function useColorField(\n  props: AriaColorFieldProps,\n  state: ColorFieldState,\n  ref: RefObject<HTMLInputElement>\n): ColorFieldAria {\n  let {\n    isDisabled,\n    isReadOnly,\n    isRequired,\n    isWheelDisabled,\n    validationBehavior = 'aria'\n  } = props;\n\n  let {\n    colorValue,\n    inputValue,\n    increment,\n    decrement,\n    incrementToMax,\n    decrementToMin,\n    commit\n  } = state;\n\n  let inputId = useId();\n  let {spinButtonProps} = useSpinButton(\n    {\n      isDisabled,\n      isReadOnly,\n      isRequired,\n      maxValue: 0xFFFFFF,\n      minValue: 0,\n      onIncrement: increment,\n      onIncrementToMax: incrementToMax,\n      onDecrement: decrement,\n      onDecrementToMin: decrementToMin,\n      value: colorValue ? colorValue.toHexInt() : undefined,\n      textValue: colorValue ? colorValue.toString('hex') : undefined\n    }\n  );\n\n  let [focusWithin, setFocusWithin] = useState(false);\n  let {focusWithinProps} = useFocusWithin({isDisabled, onFocusWithinChange: setFocusWithin});\n\n  let onWheel = useCallback((e) => {\n    if (Math.abs(e.deltaY) <= Math.abs(e.deltaX)) {\n      return;\n    }\n    if (e.deltaY > 0) {\n      increment();\n    } else if (e.deltaY < 0) {\n      decrement();\n    }\n  }, [decrement, increment]);\n  // If the input isn't supposed to receive input, disable scrolling.\n  let scrollingDisabled = isWheelDisabled || isDisabled || isReadOnly || !focusWithin;\n  useScrollWheel({onScroll: onWheel, isDisabled: scrollingDisabled}, ref);\n\n  let onChange = value => {\n    if (state.validate(value)) {\n      state.setInputValue(value);\n    }\n  };\n\n  let {inputProps, ...otherProps} = useFormattedTextField({\n    ...props,\n    id: inputId,\n    value: inputValue,\n    defaultValue: undefined,\n    validate: undefined,\n    [privateValidationStateProp]: state,\n    type: 'text',\n    autoComplete: 'off',\n    onChange\n  }, state, ref);\n\n  inputProps = mergeProps(inputProps, spinButtonProps, focusWithinProps, {\n    role: 'textbox',\n    'aria-valuemax': null,\n    'aria-valuemin': null,\n    'aria-valuenow': null,\n    'aria-valuetext': null,\n    autoCorrect: 'off',\n    spellCheck: 'false',\n    onBlur: commit\n  });\n\n  if (validationBehavior === 'native') {\n    inputProps['aria-required'] = undefined;\n  }\n\n  return {\n    inputProps,\n    ...otherProps\n  };\n}\n"], "names": [], "version": 3, "file": "useColorField.module.js.map"}