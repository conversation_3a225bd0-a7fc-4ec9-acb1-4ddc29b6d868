{"mappings": ";AAAA,4BAAiB;IAAG,mBAAmB,CAAC,OAAS,CAAC,EAAE,KAAK,KAAK,CAAC,EAAE,EAAE,KAAK,YAAY,CAAC,CAAC;IACpF,qBAAqB,CAAC,OAAS,CAAC,EAAE,KAAK,IAAI,CAAC,EAAE,EAAE,KAAK,KAAK,CAAC,CAAC;IAC5D,eAAe,CAAC,gBAAgB,CAAC;IACjC,eAAe,CAAC,gBAAgB,CAAC;IACjC,eAAe,CAAC,YAAY,CAAC;IAC7B,wBAAwB,CAAC,sBAAsB,CAAC;AAClD", "sources": ["packages/@react-aria/color/intl/pt-BR.json"], "sourcesContent": ["{\n  \"colorInputLabel\": \"{label}, {channelLabel}\",\n  \"colorNameAndValue\": \"{name}: {value}\",\n  \"colorPicker\": \"Seletor de cores\",\n  \"colorSwatch\": \"amostra de cores\",\n  \"transparent\": \"transparente\",\n  \"twoDimensionalSlider\": \"Controle deslizante 2D\"\n}\n"], "names": [], "version": 3, "file": "pt-BR.module.js.map"}