{"mappings": ";;;;;AAAA;;;;;;;;;;CAUC;;;;AAmCM,SAAS,0CAAe,KAA6B,EAAE,KAAuB;IACnF,IAAI,YAAC,QAAQ,YAAE,QAAQ,eAAE,WAAW,WAAE,OAAO,EAAE,cAAc,SAAS,QAAE,IAAI,EAAC,GAAG;IAEhF,IAAI,UAAC,MAAM,aAAE,SAAS,EAAC,GAAG,CAAA,GAAA,gBAAQ;IAElC,oEAAoE;IACpE,IAAI,CAAC,MAAM,KAAK,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC,kBAAkB,EACzD,YAAY,MAAM,KAAK,CAAC,cAAc,CAAC,SAAS;IAGlD,gDAAgD;IAChD,IAAI,cAAC,UAAU,cAAE,UAAU,cAAE,UAAU,eAAE,WAAW,EAAC,GAAG,CAAA,GAAA,gBAAQ,EAAE;QAAC,GAAG,KAAK;QAAE,cAAc;IAAS,GAAG,OAAO;IAC9G,IAAI,cAAC,UAAU,cAAE,UAAU,EAAC,GAAG,CAAA,GAAA,qBAAa,EAAE;QAC5C,OAAO;qBACP;QACA,YAAY,MAAM,UAAU;cAC5B;kBACA;kBACA;IACF,GAAG;IAEH,IAAI,QAAQ,MAAM,eAAe;IACjC,IAAI,qBAAqB;QACvB,IAAI;QACJ,IAAI,gBAAgB,YAClB,KAAK;aACA,IAAI,cAAc,OACvB,KAAK;aAEL,KAAK;QAEP,OAAQ;YACN,KAAK;gBAAO;oBACV,IAAI,QAAQ;wBAAC;wBAAG;wBAAI;wBAAK;wBAAK;wBAAK;wBAAK;qBAAI,CAAC,GAAG,CAAC,CAAA,MAAO,MAAM,gBAAgB,CAAC,OAAO,KAAK,QAAQ,CAAC,QAAQ,IAAI,CAAC;oBACjH,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,EAAE,MAAM,CAAC,CAAC;gBAC9C;YACA,KAAK;gBAAa;oBAChB,oFAAoF;oBACpF,mDAAmD;oBACnD,IAAI,MAAM,MAAM,gBAAgB,CAAC;oBACjC,IAAI,MAAM,MAAM,gBAAgB,CAAC;oBACjC,IAAI,QAAQ,MAAM,gBAAgB,CAAC,SAAS,KAAK,QAAQ,CAAC;oBAC1D,IAAI,SAAS,MAAM,gBAAgB,CAAC,SAAS,AAAC,CAAA,MAAM,GAAE,IAAK,GAAG,QAAQ,CAAC;oBACvE,IAAI,MAAM,MAAM,gBAAgB,CAAC,SAAS,KAAK,QAAQ,CAAC;oBACxD,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,OAAO,EAAE,EAAE,IAAI,CAAC,CAAC;gBACjE;YACA,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBAAS;oBACZ,IAAI,QAAQ,MAAM,gBAAgB,CAAC,SAAS,MAAM,gBAAgB,CAAC,IAAI,QAAQ,CAAC;oBAChF,IAAI,MAAM,MAAM,gBAAgB,CAAC,SAAS,MAAM,gBAAgB,CAAC,IAAI,QAAQ,CAAC;oBAC9E,OAAO,CAAC,mBAAmB,EAAE,GAAG,EAAE,EAAE,MAAM,EAAE,EAAE,IAAI,CAAC,CAAC;gBACtD;YACA;gBACE,MAAM,IAAI,MAAM,4BAA4B;QAChD;IACF;IAEA,IAAI,6BAA6B;QAAC,mBAAmB;IAAM;IAE3D,IAAI,YAAY,OACd,UAAU,CAAC,iBAAiB,IAAI,CAAC,EAAE,EAAE,MAAM,UAAU,CAAC,QAAQ,CAAC;SAC1D,IAAI,YAAY,SACrB,UAAU,CAAC,iBAAiB,IAAI,CAAC,EAAE,EAAE,MAAM,YAAY,CAAC,QAAQ,CAAC;IAGnE,IAAI,uBAAC,mBAAmB,EAAC,GAAG,CAAA,GAAA,wBAAgB,EAAE;QAC5C,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,YAAY;YACV,GAAG,CAAA,GAAA,iBAAS,EAAE,YAAY,WAAW;YACrC,OAAO;gBACL,GAAG,WAAW,KAAK;gBACnB,GAAG,0BAA0B;gBAC7B,YAAY;YACd;QACF;QACA,YAAY;YACV,GAAG,UAAU;YACb,OAAO;gBACL,GAAG,WAAW,KAAK;gBACnB,GAAG,oBAAoB,KAAK;YAC9B;QACF;QACA,YAAY;YACV,GAAG,UAAU;YACb,OAAO;gBACL,GAAG,WAAW,KAAK;gBACnB,GAAG,0BAA0B;YAC/B;QACF;oBACA;qBACA;IACF;AACF", "sources": ["packages/@react-aria/color/src/useColorSlider.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaColorSliderProps} from '@react-types/color';\nimport {ColorSliderState} from '@react-stately/color';\nimport {DOMAttributes} from '@react-types/shared';\nimport {InputHTMLAttributes, RefObject} from 'react';\nimport {mergeProps} from '@react-aria/utils';\nimport {useLocale} from '@react-aria/i18n';\nimport {useSlider, useSliderThumb} from '@react-aria/slider';\nimport {useVisuallyHidden} from '@react-aria/visually-hidden';\n\nexport interface AriaColorSliderOptions extends AriaColorSliderProps {\n  /** A ref for the track element. */\n  trackRef: RefObject<Element>,\n  /** A ref for the input element. */\n  inputRef: RefObject<HTMLInputElement>\n}\n\nexport interface ColorSliderAria {\n  /** Props for the label element. */\n  labelProps: DOMAttributes,\n  /** Props for the track element. */\n  trackProps: DOMAttributes,\n  /** Props for the thumb element. */\n  thumbProps: DOMAttributes,\n  /** Props for the visually hidden range input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>,\n  /** Props for the output element, displaying the value of the color slider. */\n  outputProps: DOMAttributes\n}\n\n/**\n * Provides the behavior and accessibility implementation for a color slider component.\n * Color sliders allow users to adjust an individual channel of a color value.\n */\nexport function useColorSlider(props: AriaColorSliderOptions, state: ColorSliderState): ColorSliderAria {\n  let {trackRef, inputRef, orientation, channel, 'aria-label': ariaLabel, name} = props;\n\n  let {locale, direction} = useLocale();\n\n  // Provide a default aria-label if there is no other label provided.\n  if (!props.label && !ariaLabel && !props['aria-labelledby']) {\n    ariaLabel = state.value.getChannelName(channel, locale);\n  }\n\n  // @ts-ignore - ignore unused incompatible props\n  let {groupProps, trackProps, labelProps, outputProps} = useSlider({...props, 'aria-label': ariaLabel}, state, trackRef);\n  let {inputProps, thumbProps} = useSliderThumb({\n    index: 0,\n    orientation,\n    isDisabled: props.isDisabled,\n    name,\n    trackRef,\n    inputRef\n  }, state);\n\n  let value = state.getDisplayColor();\n  let generateBackground = () => {\n    let to: string;\n    if (orientation === 'vertical') {\n      to = 'top';\n    } else if (direction === 'ltr') {\n      to = 'right';\n    } else {\n      to = 'left';\n    }\n    switch (channel) {\n      case 'hue': {\n        let stops = [0, 60, 120, 180, 240, 300, 360].map(hue => value.withChannelValue('hue', hue).toString('css')).join(', ');\n        return `linear-gradient(to ${to}, ${stops})`;\n      }\n      case 'lightness': {\n        // We have to add an extra color stop in the middle so that the hue shows up at all.\n        // Otherwise it will always just be black to white.\n        let min = state.getThumbMinValue(0);\n        let max = state.getThumbMaxValue(0);\n        let start = value.withChannelValue(channel, min).toString('css');\n        let middle = value.withChannelValue(channel, (max - min) / 2).toString('css');\n        let end = value.withChannelValue(channel, max).toString('css');\n        return `linear-gradient(to ${to}, ${start}, ${middle}, ${end})`;\n      }\n      case 'saturation':\n      case 'brightness':\n      case 'red':\n      case 'green':\n      case 'blue':\n      case 'alpha': {\n        let start = value.withChannelValue(channel, state.getThumbMinValue(0)).toString('css');\n        let end = value.withChannelValue(channel, state.getThumbMaxValue(0)).toString('css');\n        return `linear-gradient(to ${to}, ${start}, ${end})`;\n      }\n      default:\n        throw new Error('Unknown color channel: ' + channel);\n    }\n  };\n\n  let forcedColorAdjustNoneStyle = {forcedColorAdjust: 'none'};\n\n  if (channel === 'hue') {\n    inputProps['aria-valuetext'] += `, ${value.getHueName(locale)}`;\n  } else if (channel !== 'alpha') {\n    inputProps['aria-valuetext'] += `, ${value.getColorName(locale)}`;\n  }\n\n  let {visuallyHiddenProps} = useVisuallyHidden({\n    style: {\n      opacity: '0.0001',\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none'\n    }\n  });\n\n  return {\n    trackProps: {\n      ...mergeProps(groupProps, trackProps),\n      style: {\n        ...trackProps.style,\n        ...forcedColorAdjustNoneStyle,\n        background: generateBackground()\n      }\n    },\n    inputProps: {\n      ...inputProps,\n      style: {\n        ...inputProps.style,\n        ...visuallyHiddenProps.style\n      }\n    },\n    thumbProps: {\n      ...thumbProps,\n      style: {\n        ...thumbProps.style,\n        ...forcedColorAdjustNoneStyle\n      }\n    },\n    labelProps,\n    outputProps\n  };\n}\n"], "names": [], "version": 3, "file": "useColorSlider.module.js.map"}