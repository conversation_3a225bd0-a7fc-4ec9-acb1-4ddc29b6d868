{"mappings": ";;;;;;AAAA;;;;;;;;;;CAUC;;;;;AA+BM,SAAS,0CAAc,KAA4B,EAAE,KAAsB,EAAE,QAAqC;IACvH,IAAI,cACF,UAAU,eACV,WAAW,eACX,WAAW,EACX,cAAc,SAAS,QACvB,IAAI,EACL,GAAG;IAEJ,IAAI,qBAAC,iBAAiB,wBAAE,oBAAoB,EAAC,GAAG,CAAA,GAAA,yBAAiB;IAEjE,IAAI,cAAc,AAAC,CAAA,cAAc,WAAU,IAAK;IAEhD,IAAI,aAAa,CAAA,GAAA,kBAAU,EAAE;QAC3B,IAAI,SAAS,OAAO,EAClB,CAAA,GAAA,4BAAoB,EAAE,SAAS,OAAO;IAE1C,GAAG;QAAC;KAAS;IAEb,CAAA,GAAA,mBAAW,EAAE,UAAU,MAAM,GAAG,EAAE,MAAM,MAAM;IAE9C,IAAI,kBAAkB,CAAA,GAAA,aAAK,EAAiC;IAE5D,IAAI,iBAAC,aAAa,EAAC,GAAG,CAAA,GAAA,kBAAU,EAAE;QAChC,WAAU,CAAC;YACT,kDAAkD;YAClD,IAAI,CAAC,sBAAsB,IAAI,CAAC,EAAE,GAAG,GAAG;gBACtC,EAAE,mBAAmB;gBACrB;YACF;YACA,4FAA4F;YAC5F,EAAE,cAAc;YAChB,iEAAiE;YACjE,MAAM,WAAW,CAAC;YAClB,OAAQ,EAAE,GAAG;gBACX,KAAK;oBACH,EAAE,cAAc;oBAChB,MAAM,SAAS,CAAC,MAAM,QAAQ;oBAC9B;gBACF,KAAK;oBACH,EAAE,cAAc;oBAChB,MAAM,SAAS,CAAC,MAAM,QAAQ;oBAC9B;YACJ;YACA,MAAM,WAAW,CAAC;QACpB;IACF;IAEA,IAAI,cAAc;QAChB;YACE,gBAAgB,OAAO,GAAG;YAC1B,MAAM,WAAW,CAAC;QACpB;QACA,QAAO,UAAC,MAAM,UAAE,MAAM,eAAE,WAAW,YAAE,QAAQ,EAAC;YAC5C,IAAI,gBAAgB,OAAO,IAAI,MAC7B,gBAAgB,OAAO,GAAG,MAAM,gBAAgB,CAAC;YAEnD,gBAAgB,OAAO,CAAC,CAAC,IAAI;YAC7B,gBAAgB,OAAO,CAAC,CAAC,IAAI;YAC7B,IAAI,gBAAgB,YAAY;gBAC9B,IAAI,SAAS,KAAK,SAAS,GACzB,MAAM,SAAS,CAAC,WAAW,MAAM,QAAQ,GAAG,MAAM,IAAI;qBACjD,IAAI,SAAS,KAAK,SAAS,GAChC,MAAM,SAAS,CAAC,WAAW,MAAM,QAAQ,GAAG,MAAM,IAAI;YAE1D,OACE,MAAM,eAAe,CAAC,gBAAgB,OAAO,CAAC,CAAC,EAAE,gBAAgB,OAAO,CAAC,CAAC,EAAE;QAEhF;QACA;YACE,UAAU,OAAO,GAAG;YACpB,MAAM,WAAW,CAAC;YAClB;QACF;IACF;IACA,IAAI,EAAC,WAAW,cAAc,EAAC,GAAG,CAAA,GAAA,cAAM,EAAE;IAE1C,IAAI,iBAAiB,CAAA,GAAA,aAAK,EAA6B;IACvD,IAAI,YAAY,CAAA,GAAA,aAAK,EAAW;IAChC,IAAI,EAAC,WAAW,kBAAkB,EAAC,GAAG,CAAA,GAAA,cAAM,EAAE;QAC5C;YACE,IAAI,UAAU,OAAO,EACnB,YAAY,WAAW;QAE3B;QACA,QAAO,CAAC;YACN,IAAI,UAAU,OAAO,EACnB,YAAY,MAAM,CAAC;QAEvB;QACA;YACE,IAAI,UAAU,OAAO,EACnB,YAAY,SAAS;QAEzB;IACF;IAEA,IAAI,cAAc,CAAC;QACjB,IAAI,CAAC,MAAM,UAAU,EAAE;YACrB,eAAe,OAAO,GAAG;YACzB;YACA,MAAM,WAAW,CAAC;YAElB,IAAI,OAAO,iBAAiB,aAC1B,kBAAkB,QAAQ,aAAa,WAAW;iBAC7C;gBACL,kBAAkB,QAAQ,WAAW,WAAW;gBAChD,kBAAkB,QAAQ,YAAY,WAAW;YACnD;QACF;IACF;IAEA,IAAI,YAAY,CAAC;YACS;YAAf;QAAT,IAAI,KAAK,CAAA,eAAA,EAAE,SAAS,cAAX,0BAAA,gBAAe,oBAAA,EAAE,cAAc,cAAhB,wCAAA,iBAAkB,CAAC,EAAE,CAAC,UAAU;QACxD,IAAI,OAAO,eAAe,OAAO,EAAE;YACjC;YACA,MAAM,WAAW,CAAC;YAClB,eAAe,OAAO,GAAG;YACzB,UAAU,OAAO,GAAG;YAEpB,IAAI,OAAO,iBAAiB,aAC1B,qBAAqB,QAAQ,aAAa,WAAW;iBAChD;gBACL,qBAAqB,QAAQ,WAAW,WAAW;gBACnD,qBAAqB,QAAQ,YAAY,WAAW;YACtD;QACF;IACF;IAEA,IAAI,cAAc,CAAC,OAAgB,IAA+B,OAAe;QAC/E,IAAI,OAAO,MAAM,qBAAqB;QACtC,IAAI,IAAI,QAAQ,KAAK,CAAC,GAAG,KAAK,KAAK,GAAG;QACtC,IAAI,IAAI,QAAQ,KAAK,CAAC,GAAG,KAAK,MAAM,GAAG;QACvC,IAAI,SAAS,KAAK,IAAI,CAAC,IAAI,IAAI,IAAI;QACnC,IAAI,cAAc,UAAU,SAAS,eAAe,CAAC,MAAM,UAAU,IAAI,eAAe,OAAO,KAAK,WAAW;YAC7G,UAAU,OAAO,GAAG;YACpB,eAAe,OAAO,GAAG;YACzB,MAAM,eAAe,CAAC,GAAG,GAAG;YAE5B;YACA,MAAM,WAAW,CAAC;YAElB,IAAI,OAAO,iBAAiB,aAC1B,kBAAkB,QAAQ,aAAa,WAAW;iBAC7C;gBACL,kBAAkB,QAAQ,WAAW,WAAW;gBAChD,kBAAkB,QAAQ,YAAY,WAAW;YACnD;QACF;IACF;IAEA,IAAI,YAAY,CAAC;YACS;YAAf;QAAT,IAAI,KAAK,CAAA,eAAA,EAAE,SAAS,cAAX,0BAAA,gBAAe,oBAAA,EAAE,cAAc,cAAhB,wCAAA,iBAAkB,CAAC,EAAE,CAAC,UAAU;QACxD,IAAI,UAAU,OAAO,IAAI,OAAO,eAAe,OAAO,EAAE;YACtD,UAAU,OAAO,GAAG;YACpB,eAAe,OAAO,GAAG;YACzB,MAAM,WAAW,CAAC;YAClB;YAGA,IAAI,OAAO,iBAAiB,aAC1B,qBAAqB,QAAQ,aAAa,WAAW;iBAChD;gBACL,qBAAqB,QAAQ,WAAW,WAAW;gBACnD,qBAAqB,QAAQ,YAAY,WAAW;YACtD;QACF;IACF;IAEA,IAAI,oBAAoB,aAAa,CAAC,IAAI,CAAA,GAAA,iBAAS,EAAE;QACnD,GAAI,OAAO,iBAAiB,cAAc;YACxC,eAAe,CAAC;gBACd,IAAI,EAAE,WAAW,KAAK,WAAY,CAAA,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,AAAD,GACnF;gBAEF,YAAY,EAAE,aAAa,EAAE,EAAE,SAAS,EAAE,EAAE,OAAO,EAAE,EAAE,OAAO;YAChE;QAAC,IAAI;YACH,aAAa,CAAC;gBACZ,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EACtD;gBAEF,YAAY,EAAE,aAAa,EAAE,WAAW,EAAE,OAAO,EAAE,EAAE,OAAO;YAC9D;YACA,cAAc,CAAC;gBACb,YAAY,EAAE,aAAa,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,UAAU,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,EAAE,cAAc,CAAC,EAAE,CAAC,OAAO;YACvH;QACF,CAAC;IACL,GAAG;IAEH,IAAI,oBAAoB,aAAa,CAAC,IAAI,CAAA,GAAA,iBAAS,EAAE;QACnD,aAAa,CAAC;YACZ,IAAI,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,EACtD;YAEF,YAAY;QACd;QACA,eAAe,CAAC;YACd,IAAI,EAAE,WAAW,KAAK,WAAY,CAAA,EAAE,MAAM,KAAK,KAAK,EAAE,MAAM,IAAI,EAAE,OAAO,IAAI,EAAE,OAAO,AAAD,GACnF;YAEF,YAAY,EAAE,SAAS;QACzB;QACA,cAAc,CAAC;YACb,YAAY,EAAE,cAAc,CAAC,EAAE,CAAC,UAAU;QAC5C;IACF,GAAG,eAAe;IAClB,IAAI,KAAC,CAAC,KAAE,CAAC,EAAC,GAAG,MAAM,gBAAgB,CAAC;IAEpC,gDAAgD;IAChD,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,gBAAQ;IACvB,IAAI,aAAa,QAAQ,KAAK,CAAC,kBAAkB,IAAI,MACnD,YAAY,MAAM,KAAK,CAAC,cAAc,CAAC,OAAO;IAGhD,IAAI,sBAAsB,CAAA,GAAA,gBAAQ,EAAE;QAClC,GAAG,KAAK;QACR,cAAc;IAChB;IAEA,IAAI,YAAC,QAAQ,YAAE,QAAQ,QAAE,IAAI,EAAC,GAAG,MAAM,KAAK,CAAC,eAAe,CAAC;IAE7D,IAAI,6BAA6B;QAC/B,mBAAmB;IACrB;IAEA,IAAI,uBAAC,mBAAmB,EAAC,GAAG,CAAA,GAAA,wBAAgB,EAAE;QAC5C,OAAO;YACL,SAAS;YACT,OAAO;YACP,QAAQ;YACR,eAAe;QACjB;IACF;IAEA,OAAO;QACL,YAAY;YACV,GAAG,iBAAiB;YACpB,OAAO;gBACL,UAAU;gBACV,aAAa;gBACb,OAAO,cAAc;gBACrB,QAAQ,cAAc;gBACtB,YAAY,CAAC;;;;;;;;;;;;;;;;;QAiBb,CAAC;gBACD,UAAU,CAAC,eAAe,EAAE,iCAAW,aAAa,aAAa,aAAa,CAAC,EAAE,iCAAW,aAAa,aAAa,aAAa,EAAE,CAAC;gBACtI,GAAG,0BAA0B;YAC/B;QACF;QACA,YAAY;YACV,GAAG,iBAAiB;YACpB,OAAO;gBACL,UAAU;gBACV,MAAM,cAAc;gBACpB,KAAK,cAAc;gBACnB,WAAW;gBACX,aAAa;gBACb,GAAG,0BAA0B;YAC/B;QACF;QACA,YAAY,CAAA,GAAA,iBAAS,EACnB,qBACA;YACE,MAAM;YACN,KAAK,OAAO;YACZ,KAAK,OAAO;YACZ,MAAM,OAAO;YACb,kBAAkB,CAAC,EAAE,MAAM,KAAK,CAAC,kBAAkB,CAAC,OAAO,QAAQ,EAAE,EAAE,MAAM,KAAK,CAAC,UAAU,CAAC,QAAQ,CAAC;YACvG,UAAU;YACV,OAAO,CAAC,EAAE,MAAM,KAAK,CAAC,eAAe,CAAC,OAAO,CAAC;kBAC9C;YACA,UAAU,CAAC;gBACT,MAAM,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,KAAK;YACxC;YACA,OAAO,oBAAoB,KAAK;YAChC,qBAAqB,KAAK,CAAC,oBAAoB;YAC/C,oBAAoB,KAAK,CAAC,mBAAmB;YAC7C,gBAAgB,KAAK,CAAC,eAAe;QACvC;IAEJ;AACF;AAEA,2CAA2C;AAC3C,SAAS,iCAAW,EAAU,EAAE,EAAU,EAAE,CAAS;IACnD,OAAO,CAAC,EAAE,EAAE,GAAG,EAAE,EAAE,GAAG,GAAG,EAAE,CAAC,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,WAAW,EAAE,IAAI,EAAE,MAAM,EAAE,EAAE,EAAE,EAAE,EAAE,UAAU,EAAE,CAAC,IAAI,EAAE,GAAG,CAAC;AAC1G", "sources": ["packages/@react-aria/color/src/useColorWheel.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaColorWheelProps} from '@react-types/color';\nimport {ColorWheelState} from '@react-stately/color';\nimport {DOMAttributes} from '@react-types/shared';\nimport {focusWithoutScrolling, mergeProps, useFormReset, useGlobalListeners, useLabels} from '@react-aria/utils';\nimport React, {ChangeEvent, InputHTMLAttributes, RefObject, useCallback, useRef} from 'react';\nimport {useKeyboard, useMove} from '@react-aria/interactions';\nimport {useLocale} from '@react-aria/i18n';\nimport {useVisuallyHidden} from '@react-aria/visually-hidden';\n\nexport interface AriaColorWheelOptions extends AriaColorWheelProps {\n  /** The outer radius of the color wheel. */\n  outerRadius: number,\n  /** The inner radius of the color wheel. */\n  innerRadius: number\n}\n\nexport interface ColorWheelAria {\n  /** Props for the track element. */\n  trackProps: DOMAttributes,\n  /** Props for the thumb element. */\n  thumbProps: DOMAttributes,\n  /** Props for the visually hidden range input element. */\n  inputProps: InputHTMLAttributes<HTMLInputElement>\n}\n\n/**\n * Provides the behavior and accessibility implementation for a color wheel component.\n * Color wheels allow users to adjust the hue of an HSL or HSB color value on a circular track.\n */\nexport function useColorWheel(props: AriaColorWheelOptions, state: ColorWheelState, inputRef: RefObject<HTMLInputElement>): ColorWheelAria {\n  let {\n    isDisabled,\n    innerRadius,\n    outerRadius,\n    'aria-label': ariaLabel,\n    name\n  } = props;\n\n  let {addGlobalListener, removeGlobalListener} = useGlobalListeners();\n\n  let thumbRadius = (innerRadius + outerRadius) / 2;\n\n  let focusInput = useCallback(() => {\n    if (inputRef.current) {\n      focusWithoutScrolling(inputRef.current);\n    }\n  }, [inputRef]);\n\n  useFormReset(inputRef, state.hue, state.setHue);\n\n  let currentPosition = useRef<{x: number, y: number} | null>(null);\n\n  let {keyboardProps} = useKeyboard({\n    onKeyDown(e) {\n      // these are the cases that useMove doesn't handle\n      if (!/^(PageUp|PageDown)$/.test(e.key)) {\n        e.continuePropagation();\n        return;\n      }\n      // same handling as useMove, don't need to stop propagation, useKeyboard will do that for us\n      e.preventDefault();\n      // remember to set this and unset it so that onChangeEnd is fired\n      state.setDragging(true);\n      switch (e.key) {\n        case 'PageUp':\n          e.preventDefault();\n          state.increment(state.pageStep);\n          break;\n        case 'PageDown':\n          e.preventDefault();\n          state.decrement(state.pageStep);\n          break;\n      }\n      state.setDragging(false);\n    }\n  });\n\n  let moveHandler = {\n    onMoveStart() {\n      currentPosition.current = null;\n      state.setDragging(true);\n    },\n    onMove({deltaX, deltaY, pointerType, shiftKey}) {\n      if (currentPosition.current == null) {\n        currentPosition.current = state.getThumbPosition(thumbRadius);\n      }\n      currentPosition.current.x += deltaX;\n      currentPosition.current.y += deltaY;\n      if (pointerType === 'keyboard') {\n        if (deltaX > 0 || deltaY < 0) {\n          state.increment(shiftKey ? state.pageStep : state.step);\n        } else if (deltaX < 0 || deltaY > 0) {\n          state.decrement(shiftKey ? state.pageStep : state.step);\n        }\n      } else {\n        state.setHueFromPoint(currentPosition.current.x, currentPosition.current.y, thumbRadius);\n      }\n    },\n    onMoveEnd() {\n      isOnTrack.current = false;\n      state.setDragging(false);\n      focusInput();\n    }\n  };\n  let {moveProps: movePropsThumb} = useMove(moveHandler);\n\n  let currentPointer = useRef<number | null | undefined>(undefined);\n  let isOnTrack = useRef<boolean>(false);\n  let {moveProps: movePropsContainer} = useMove({\n    onMoveStart() {\n      if (isOnTrack.current) {\n        moveHandler.onMoveStart();\n      }\n    },\n    onMove(e) {\n      if (isOnTrack.current) {\n        moveHandler.onMove(e);\n      }\n    },\n    onMoveEnd() {\n      if (isOnTrack.current) {\n        moveHandler.onMoveEnd();\n      }\n    }\n  });\n\n  let onThumbDown = (id: number | null | undefined) => {\n    if (!state.isDragging) {\n      currentPointer.current = id;\n      focusInput();\n      state.setDragging(true);\n\n      if (typeof PointerEvent !== 'undefined') {\n        addGlobalListener(window, 'pointerup', onThumbUp, false);\n      } else {\n        addGlobalListener(window, 'mouseup', onThumbUp, false);\n        addGlobalListener(window, 'touchend', onThumbUp, false);\n      }\n    }\n  };\n\n  let onThumbUp = (e) => {\n    let id = e.pointerId ?? e.changedTouches?.[0].identifier;\n    if (id === currentPointer.current) {\n      focusInput();\n      state.setDragging(false);\n      currentPointer.current = undefined;\n      isOnTrack.current = false;\n\n      if (typeof PointerEvent !== 'undefined') {\n        removeGlobalListener(window, 'pointerup', onThumbUp, false);\n      } else {\n        removeGlobalListener(window, 'mouseup', onThumbUp, false);\n        removeGlobalListener(window, 'touchend', onThumbUp, false);\n      }\n    }\n  };\n\n  let onTrackDown = (track: Element, id: number | null | undefined, pageX: number, pageY: number) => {\n    let rect = track.getBoundingClientRect();\n    let x = pageX - rect.x - rect.width / 2;\n    let y = pageY - rect.y - rect.height / 2;\n    let radius = Math.sqrt(x * x + y * y);\n    if (innerRadius < radius && radius < outerRadius && !state.isDragging && currentPointer.current === undefined) {\n      isOnTrack.current = true;\n      currentPointer.current = id;\n      state.setHueFromPoint(x, y, radius);\n\n      focusInput();\n      state.setDragging(true);\n\n      if (typeof PointerEvent !== 'undefined') {\n        addGlobalListener(window, 'pointerup', onTrackUp, false);\n      } else {\n        addGlobalListener(window, 'mouseup', onTrackUp, false);\n        addGlobalListener(window, 'touchend', onTrackUp, false);\n      }\n    }\n  };\n\n  let onTrackUp = (e) => {\n    let id = e.pointerId ?? e.changedTouches?.[0].identifier;\n    if (isOnTrack.current && id === currentPointer.current) {\n      isOnTrack.current = false;\n      currentPointer.current = undefined;\n      state.setDragging(false);\n      focusInput();\n\n\n      if (typeof PointerEvent !== 'undefined') {\n        removeGlobalListener(window, 'pointerup', onTrackUp, false);\n      } else {\n        removeGlobalListener(window, 'mouseup', onTrackUp, false);\n        removeGlobalListener(window, 'touchend', onTrackUp, false);\n      }\n    }\n  };\n\n  let trackInteractions = isDisabled ? {} : mergeProps({\n    ...(typeof PointerEvent !== 'undefined' ? {\n      onPointerDown: (e: React.PointerEvent) => {\n        if (e.pointerType === 'mouse' && (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey)) {\n          return;\n        }\n        onTrackDown(e.currentTarget, e.pointerId, e.clientX, e.clientY);\n      }} : {\n        onMouseDown: (e: React.MouseEvent) => {\n          if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) {\n            return;\n          }\n          onTrackDown(e.currentTarget, undefined, e.clientX, e.clientY);\n        },\n        onTouchStart: (e: React.TouchEvent) => {\n          onTrackDown(e.currentTarget, e.changedTouches[0].identifier, e.changedTouches[0].clientX, e.changedTouches[0].clientY);\n        }\n      })\n  }, movePropsContainer);\n\n  let thumbInteractions = isDisabled ? {} : mergeProps({\n    onMouseDown: (e: React.MouseEvent) => {\n      if (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey) {\n        return;\n      }\n      onThumbDown(undefined);\n    },\n    onPointerDown: (e: React.PointerEvent) => {\n      if (e.pointerType === 'mouse' && (e.button !== 0 || e.altKey || e.ctrlKey || e.metaKey)) {\n        return;\n      }\n      onThumbDown(e.pointerId);\n    },\n    onTouchStart: (e: React.TouchEvent) => {\n      onThumbDown(e.changedTouches[0].identifier);\n    }\n  }, keyboardProps, movePropsThumb);\n  let {x, y} = state.getThumbPosition(thumbRadius);\n\n  // Provide a default aria-label if none is given\n  let {locale} = useLocale();\n  if (ariaLabel == null && props['aria-labelledby'] == null) {\n    ariaLabel = state.value.getChannelName('hue', locale);\n  }\n\n  let inputLabellingProps = useLabels({\n    ...props,\n    'aria-label': ariaLabel\n  });\n\n  let {minValue, maxValue, step} = state.value.getChannelRange('hue');\n\n  let forcedColorAdjustNoneStyle = {\n    forcedColorAdjust: 'none'\n  };\n\n  let {visuallyHiddenProps} = useVisuallyHidden({\n    style: {\n      opacity: '0.0001',\n      width: '100%',\n      height: '100%',\n      pointerEvents: 'none'\n    }\n  });\n\n  return {\n    trackProps: {\n      ...trackInteractions,\n      style: {\n        position: 'relative',\n        touchAction: 'none',\n        width: outerRadius * 2,\n        height: outerRadius * 2,\n        background: `\n          conic-gradient(\n            from 90deg,\n            hsl(0, 100%, 50%),\n            hsl(30, 100%, 50%),\n            hsl(60, 100%, 50%),\n            hsl(90, 100%, 50%),\n            hsl(120, 100%, 50%),\n            hsl(150, 100%, 50%),\n            hsl(180, 100%, 50%),\n            hsl(210, 100%, 50%),\n            hsl(240, 100%, 50%),\n            hsl(270, 100%, 50%),\n            hsl(300, 100%, 50%),\n            hsl(330, 100%, 50%),\n            hsl(360, 100%, 50%)\n          )\n        `,\n        clipPath: `path(evenodd, \"${circlePath(outerRadius, outerRadius, outerRadius)} ${circlePath(outerRadius, outerRadius, innerRadius)}\")`,\n        ...forcedColorAdjustNoneStyle\n      }\n    },\n    thumbProps: {\n      ...thumbInteractions,\n      style: {\n        position: 'absolute',\n        left: outerRadius + x,\n        top: outerRadius + y,\n        transform: 'translate(-50%, -50%)',\n        touchAction: 'none',\n        ...forcedColorAdjustNoneStyle\n      }\n    },\n    inputProps: mergeProps(\n      inputLabellingProps,\n      {\n        type: 'range',\n        min: String(minValue),\n        max: String(maxValue),\n        step: String(step),\n        'aria-valuetext': `${state.value.formatChannelValue('hue', locale)}, ${state.value.getHueName(locale)}`,\n        disabled: isDisabled,\n        value: `${state.value.getChannelValue('hue')}`,\n        name,\n        onChange: (e: ChangeEvent<HTMLInputElement>) => {\n          state.setHue(parseFloat(e.target.value));\n        },\n        style: visuallyHiddenProps.style,\n        'aria-errormessage': props['aria-errormessage'],\n        'aria-describedby': props['aria-describedby'],\n        'aria-details': props['aria-details']\n      }\n    )\n  };\n}\n\n// Creates an SVG path string for a circle.\nfunction circlePath(cx: number, cy: number, r: number) {\n  return `M ${cx}, ${cy} m ${-r}, 0 a ${r}, ${r}, 0, 1, 0, ${r * 2}, 0 a ${r}, ${r}, 0, 1, 0 ${-r * 2}, 0`;\n}\n"], "names": [], "version": 3, "file": "useColorWheel.module.js.map"}