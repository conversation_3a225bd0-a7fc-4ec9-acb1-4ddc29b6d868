{"mappings": ";;;AAAA;;;;;;;;;;CAUC;;AAeM,SAAS,0CAAqB,KAAiC,EAAE,KAA6B,EAAE,QAAqC;IAC1I,IAAI,UAAC,MAAM,EAAC,GAAG,CAAA,GAAA,gBAAQ;IACvB,OAAO,CAAA,GAAA,qBAAa,EAAE;QACpB,GAAG,KAAK;QACR,OAAO;QACP,cAAc;QACd,UAAU;QACV,UAAU;QACV,8DAA8D;QAC9D,cAAc,KAAK,CAAC,aAAa,IAAK,CAAA,MAAM,KAAK,IAAI,KAAK,CAAC,kBAAkB,GAAG,YAAY,MAAM,UAAU,CAAC,cAAc,CAAC,MAAM,OAAO,EAAE,OAAM;IACnJ,GAAG,OAAO;AACZ", "sources": ["packages/@react-aria/color/src/useColorChannelField.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps} from '@react-types/shared';\nimport {ColorChannelFieldProps, ColorChannelFieldState} from '@react-stately/color';\nimport {NumberFieldAria, useNumberField} from '@react-aria/numberfield';\nimport {RefObject} from 'react';\nimport {useLocale} from '@react-aria/i18n';\n\nexport interface AriaColorChannelFieldProps extends ColorChannelFieldProps, AriaLabelingProps {}\nexport interface ColorChannelFieldAria extends NumberFieldAria {}\n\n/**\n * Provides the behavior and accessibility implementation for a color channel field, allowing users to edit the\n * value of an individual color channel.\n */\nexport function useColorChannelField(props: AriaColorChannelFieldProps, state: ColorChannelFieldState, inputRef: RefObject<HTMLInputElement>): ColorChannelFieldAria {\n  let {locale} = useLocale();\n  return useNumberField({\n    ...props,\n    value: undefined,\n    defaultValue: undefined,\n    onChange: undefined,\n    validate: undefined,\n    // Provide a default aria-label if no other label is provided.\n    'aria-label': props['aria-label'] || (props.label || props['aria-labelledby'] ? undefined : state.colorValue.getChannelName(props.channel, locale))\n  }, state, inputRef);\n}\n"], "names": [], "version": 3, "file": "useColorChannelField.module.js.map"}