import {useGridList as $2nLyh$useGridList} from "@react-aria/gridlist";

/*
 * Copyright 2024 Adobe. All rights reserved.
 * This file is licensed to you under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License. You may obtain a copy
 * of the License at http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software distributed under
 * the License is distributed on an "AS IS" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS
 * OF ANY KIND, either express or implied. See the License for the specific language
 * governing permissions and limitations under the License.
 */ 
function $1642482277341dec$export$abb2211d46906a63(props, state, ref) {
    let { gridProps: gridProps } = (0, $2nLyh$useGridList)(props, state, ref);
    gridProps.role = 'treegrid';
    return {
        gridProps: gridProps
    };
}


export {$1642482277341dec$export$abb2211d46906a63 as useTreeGridList};
//# sourceMappingURL=useTreeGridList.module.js.map
