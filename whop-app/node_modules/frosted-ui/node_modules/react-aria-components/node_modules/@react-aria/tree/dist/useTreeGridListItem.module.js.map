{"mappings": ";;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;AAkCM,SAAS,0CAAuB,KAAkC,EAAE,KAAmB,EAAE,GAAgC;IAC9H,IAAI,QAAC,IAAI,EAAC,GAAG;IACb,IAAI,eAAe,CAAA,GAAA,sBAAc,EAAE,OAAO,OAAO;IACjD,IAAI,aAAa,aAAa,QAAQ,CAAC,gBAAgB,KAAK;IAC5D,IAAI,kBAAkB,CAAA,GAAA,kCAA0B,EAAE,CAAA,GAAA,oDAAW,GAAG;IAEhE,IAAI,oBAAoB;QACtB,SAAS;YACP,IAAI,CAAC,aAAa,UAAU,EAAE;gBAC5B,MAAM,SAAS,CAAC,KAAK,GAAG;gBACxB,MAAM,gBAAgB,CAAC,UAAU,CAAC;gBAClC,MAAM,gBAAgB,CAAC,aAAa,CAAC,KAAK,GAAG;YAC/C;QACF;QACA,cAAc,aAAa,gBAAgB,MAAM,CAAC,cAAc,gBAAgB,MAAM,CAAC;QACvF,UAAU,CAAA,GAAA,gBAAQ,MAAM,KAAK;QAC7B,iCAAiC;IACnC;IAEA,+FAA+F;IAC/F,OAAO;QACL,GAAG,YAAY;2BACf;IACF;AACF", "sources": ["packages/@react-aria/tree/src/useTreeGridListItem.ts"], "sourcesContent": ["/*\n * Copyright 2024 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaButtonProps} from '@react-types/button';\nimport {AriaGridListItemOptions, GridListItemAria, useGridListItem} from '@react-aria/gridlist';\nimport {DOMAttributes, FocusableElement, Node} from '@react-types/shared';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {isAndroid} from '@react-aria/utils';\nimport {RefObject} from 'react';\nimport {TreeState} from '@react-stately/tree';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\n\nexport interface AriaTreeGridListItemOptions extends Omit<AriaGridListItemOptions, 'isVirtualized'> {\n  /** An object representing the treegrid item. Contains all the relevant information that makes up the treegrid row. */\n  node: Node<unknown>\n}\n\nexport interface TreeGridListItemAria extends GridListItemAria {\n  /** Props for the tree grid row element. */\n  rowProps: DOMAttributes,\n  /** Props for the tree grid cell element within the tree grid list row. */\n  gridCellProps: DOMAttributes,\n  /** Props for the tree grid row description element, if any. */\n  descriptionProps: DOMAttributes,\n  /** Props for the tree grid row expand button. */\n  expandButtonProps: AriaButtonProps\n}\n\n/**\n * Provides the behavior and accessibility implementation for a row in a tree grid list.\n * @param props - Props for the row.\n * @param state - State of the parent list, as returned by `useTreeState`.\n * @param ref - The ref attached to the row element.\n */\nexport function useTreeGridListItem<T>(props: AriaTreeGridListItemOptions, state: TreeState<T>, ref: RefObject<FocusableElement>): TreeGridListItemAria {\n  let {node} = props;\n  let gridListAria = useGridListItem(props, state, ref);\n  let isExpanded = gridListAria.rowProps['aria-expanded'] === true;\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/tree');\n\n  let expandButtonProps = {\n    onPress: () => {\n      if (!gridListAria.isDisabled) {\n        state.toggleKey(node.key);\n        state.selectionManager.setFocused(true);\n        state.selectionManager.setFocusedKey(node.key);\n      }\n    },\n    'aria-label': isExpanded ? stringFormatter.format('collapse') : stringFormatter.format('expand'),\n    tabIndex: isAndroid() ? -1 : null,\n    'data-react-aria-prevent-focus': true\n  };\n\n  // TODO: should it return a state specifically for isExpanded? Or is aria attribute sufficient?\n  return {\n    ...gridListAria,\n    expandButtonProps\n  };\n}\n"], "names": [], "version": 3, "file": "useTreeGridListItem.module.js.map"}