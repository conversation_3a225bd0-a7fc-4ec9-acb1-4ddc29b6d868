module.exports={"@react-aria/breadcrumbs":{breadcrumbs:"Caminho detalhado"},"@react-aria/calendar":{dateRange:e=>`${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selecionado`,finishRangeSelectionPrompt:`Clique para concluir a sele\xe7\xe3o do intervalo de datas`,maximumDate:`\xdaltima data dispon\xedvel`,minimumDate:`Primeira data dispon\xedvel`,next:`Pr\xf3ximo`,previous:"Anterior",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.dateRange}`,startRangeSelectionPrompt:`Clique para iniciar a sele\xe7\xe3o do intervalo de datas`,todayDate:e=>`Hoje, ${e.date}`,todayDateSelected:e=>`Ho<PERSON>, ${e.date} selecionado`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:"Seletor de cores",colorSwatch:"amostra de cores",transparent:"transparente",twoDimensionalSlider:"Controle deslizante 2D"},"@react-aria/combobox":{buttonLabel:`Mostrar sugest\xf5es`,countAnnouncement:(e,a)=>`${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} op\xe7\xe3o`,other:()=>`${a.number(e.optionCount)} op\xe7\xf5es`})} dispon\xedvel.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Grupo inserido ${e.groupTitle}, com ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} op\xe7\xe3o`,other:()=>`${a.number(e.groupCount)} op\xe7\xf5es`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:", selecionado",other:""},e.isSelected)}`,listboxLabel:`Sugest\xf5es`,selectedAnnouncement:e=>`${e.optionText}, selecionado`},"@react-aria/datepicker":{calendar:`Calend\xe1rio`,day:"dia",dayPeriod:"AM/PM",endDate:"Data final",era:"era",hour:"hora",minute:"minuto",month:`m\xeas`,second:"segundo",selectedDateDescription:e=>`Data selecionada: ${e.date}`,selectedRangeDescription:e=>`Intervalo selecionado: ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Hora selecionada: ${e.time}`,startDate:"Data inicial",timeZoneName:`fuso hor\xe1rio`,weekday:"dia da semana",year:"ano"},"@react-aria/dnd":{dragDescriptionKeyboard:`Pressione Enter para come\xe7ar a arrastar.`,dragDescriptionKeyboardAlt:`Pressione Alt + Enter para come\xe7ar a arrastar.`,dragDescriptionLongPress:`Pressione e segure para come\xe7ar a arrastar.`,dragDescriptionTouch:`Toque duas vezes para come\xe7ar a arrastar.`,dragDescriptionVirtual:`Clique para come\xe7ar a arrastar.`,dragItem:e=>`Arrastar ${e.itemText}`,dragSelectedItems:(e,a)=>`Arrastar ${a.plural(e.count,{one:()=>`${a.number(e.count)} item selecionado`,other:()=>`${a.number(e.count)} itens selecionados`})}`,dragSelectedKeyboard:(e,a)=>`Pressione Enter para arrastar ${a.plural(e.count,{one:()=>`${a.number(e.count)} o item selecionado`,other:()=>`${a.number(e.count)} os itens selecionados`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Pressione Alt + Enter para arrastar ${a.plural(e.count,{one:()=>`${a.number(e.count)} o item selecionado`,other:()=>`${a.number(e.count)} os itens selecionados`})}.`,dragSelectedLongPress:(e,a)=>`Pressione e segure para arrastar ${a.plural(e.count,{one:()=>`${a.number(e.count)} o item selecionado`,other:()=>`${a.number(e.count)} os itens selecionados`})}.`,dragStartedKeyboard:`Comece a arrastar. Pressione Tab para navegar at\xe9 um alvo e, em seguida, pressione Enter para soltar ou pressione Escape para cancelar.`,dragStartedTouch:`Comece a arrastar. Navegue at\xe9 um alvo e toque duas vezes para soltar.`,dragStartedVirtual:`Comece a arrastar. Navegue at\xe9 um alvo e clique ou pressione Enter para soltar.`,dropCanceled:`Libera\xe7\xe3o cancelada.`,dropComplete:`Libera\xe7\xe3o conclu\xedda.`,dropDescriptionKeyboard:"Pressione Enter para soltar. Pressione Escape para cancelar.",dropDescriptionTouch:"Toque duas vezes para soltar.",dropDescriptionVirtual:"Clique para soltar.",dropIndicator:`indicador de libera\xe7\xe3o`,dropOnItem:e=>`Soltar em ${e.itemText}`,dropOnRoot:"Soltar",endDragKeyboard:"Arrastando. Pressione Enter para cancelar.",endDragTouch:"Arrastando. Toque duas vezes para cancelar.",endDragVirtual:"Arrastando. Clique para cancelar.",insertAfter:e=>`Inserir ap\xf3s ${e.itemText}`,insertBefore:e=>`Inserir antes de ${e.itemText}`,insertBetween:e=>`Inserir entre ${e.beforeItemText} e ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} n\xe3o selecionado.`,longPressToSelect:`Mantenha pressionado para entrar no modo de sele\xe7\xe3o.`,select:"Selecionar",selectedAll:"Todos os itens selecionados.",selectedCount:(e,a)=>`${a.plural(e.count,{"=0":"Nenhum item selecionado",one:()=>`${a.number(e.count)} item selecionado`,other:()=>`${a.number(e.count)} itens selecionados`})}.`,selectedItem:e=>`${e.item} selecionado.`},"@react-aria/gridlist":{hasActionAnnouncement:`linha tem uma a\xe7\xe3o`,hasLinkAnnouncement:e=>`linha tem o link: ${e.link}`},"@react-aria/menu":{longPressMessage:"Pressione e segure ou pressione Alt + Seta para baixo para abrir o menu"},"@react-aria/numberfield":{decrease:e=>`Diminuir ${e.fieldLabel}`,increase:e=>`Aumentar ${e.fieldLabel}`,numberField:`Campo de n\xfamero`},"@react-aria/overlays":{dismiss:"Descartar"},"@react-aria/pagination":{next:`Pr\xf3ximo`,previous:"Anterior"},"@react-aria/searchfield":{"Clear search":"Limpar pesquisa"},"@react-aria/spinbutton":{Empty:"Vazio"},"@react-aria/steplist":{steplist:"Lista de etapas"},"@react-aria/table":{ascending:"crescente",ascendingSort:e=>`classificado pela coluna ${e.columnName} em ordem crescente`,columnSize:e=>`${e.value} pixels`,descending:"decrescente",descendingSort:e=>`classificado pela coluna ${e.columnName} em ordem decrescente`,resizerDescription:`Pressione Enter para come\xe7ar a redimensionar`,select:"Selecionar",selectAll:"Selecionar tudo",sortable:`coluna classific\xe1vel`},"@react-aria/tag":{removeButtonLabel:"Remover",removeDescription:"Pressione Delete para remover a tag."},"@react-aria/toast":{close:"Fechar",notifications:`Notifica\xe7\xf5es`},"@react-aria/tree":{collapse:"Recolher",expand:"Expandir"},"@react-stately/color":{alpha:"Alfa",black:"preto",blue:"Azul","blue purple":"roxo azulado",brightness:"Brilho",brown:"marrom","brown yellow":"marrom amarelado",colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:"ciano","cyan blue":"azul-ciano",dark:"escuro",gray:"cinza",grayish:"acinzentado",green:"Verde","green cyan":"verde-ciano",hue:"Matiz",light:"claro",lightness:"Luminosidade",magenta:"magenta","magenta pink":"rosa-magenta",orange:"laranja","orange yellow":"amarelo alaranjado",pale:`p\xe1lido`,pink:"rosa","pink red":"rosa avermelhado",purple:"roxo","purple magenta":"roxo-magenta",red:"Vermelho","red orange":"laranja avermelhado",saturation:`Satura\xe7\xe3o`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} transparente`,"very dark":"muito escuro","very light":"muito claro",vibrant:"vibrante",white:"branco",yellow:"amarelo","yellow green":"verde amarelado"},"@react-stately/datepicker":{rangeOverflow:e=>`O valor deve ser ${e.maxValue} ou anterior.`,rangeReversed:`A data inicial deve ser anterior \xe0 data final.`,rangeUnderflow:e=>`O valor deve ser ${e.minValue} ou posterior.`,unavailableDate:`Data selecionada indispon\xedvel.`},"react-aria-components":{colorSwatchPicker:"Amostras de cores",dropzoneLabel:"DropZone",selectPlaceholder:"Selecione um item",tableResizer:"Redimensionador"}};