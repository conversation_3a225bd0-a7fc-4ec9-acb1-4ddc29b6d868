module.exports={"@react-aria/breadcrumbs":{breadcrumbs:"Broodkruimels"},"@react-aria/calendar":{dateRange:e=>`${e.startDate} tot ${e.endDate}`,dateSelected:e=>`${e.date} geselecteerd`,finishRangeSelectionPrompt:"Klik om de selectie van het datumbereik te voltooien",maximumDate:"Laatste beschikbare datum",minimumDate:"Eerste beschikbare datum",next:"Volgende",previous:"Vorige",selectedDateDescription:e=>`Geselecteerde datum: ${e.date}`,selectedRangeDescription:e=>`Geselecteerd bereik: ${e.dateRange}`,startRangeSelectionPrompt:"Klik om het datumbereik te selecteren",todayDate:e=>`Vandaag, ${e.date}`,todayDateSelected:e=>`Vandaag, ${e.date} geselecteerd`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:"Kleurkiezer",colorSwatch:"kleurstaal",transparent:"transparant",twoDimensionalSlider:"2D-schuifregelaar"},"@react-aria/combobox":{buttonLabel:"Suggesties weergeven",countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} optie`,other:()=>`${t.number(e.optionCount)} opties`})} beschikbaar.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`Groep ${e.groupTitle} ingevoerd met ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} optie`,other:()=>`${t.number(e.groupCount)} opties`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:", geselecteerd",other:""},e.isSelected)}`,listboxLabel:"Suggesties",selectedAnnouncement:e=>`${e.optionText}, geselecteerd`},"@react-aria/datepicker":{calendar:"Kalender",day:"dag",dayPeriod:"a.m./p.m.",endDate:"Einddatum",era:"tijdperk",hour:"uur",minute:"minuut",month:"maand",second:"seconde",selectedDateDescription:e=>`Geselecteerde datum: ${e.date}`,selectedRangeDescription:e=>`Geselecteerd bereik: ${e.startDate} tot ${e.endDate}`,selectedTimeDescription:e=>`Geselecteerde tijd: ${e.time}`,startDate:"Startdatum",timeZoneName:"tijdzone",weekday:"dag van de week",year:"jaar"},"@react-aria/dnd":{dragDescriptionKeyboard:"Druk op Enter om te slepen.",dragDescriptionKeyboardAlt:"Druk op Alt + Enter om te slepen.",dragDescriptionLongPress:"Houd lang ingedrukt om te slepen.",dragDescriptionTouch:"Dubbeltik om te slepen.",dragDescriptionVirtual:"Klik om met slepen te starten.",dragItem:e=>`${e.itemText} slepen`,dragSelectedItems:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)} geselecteerd item`,other:()=>`${t.number(e.count)} geselecteerde items`})} slepen`,dragSelectedKeyboard:(e,t)=>`Druk op Enter om ${t.plural(e.count,{one:()=>`${t.number(e.count)} geselecteerd item`,other:()=>`${t.number(e.count)} geselecteerde items`})} te slepen.`,dragSelectedKeyboardAlt:(e,t)=>`Druk op Alt + Enter om ${t.plural(e.count,{one:()=>`${t.number(e.count)} geselecteerd item`,other:()=>`${t.number(e.count)} geselecteerde items`})} te slepen.`,dragSelectedLongPress:(e,t)=>`Houd lang ingedrukt om ${t.plural(e.count,{one:()=>`${t.number(e.count)} geselecteerd item`,other:()=>`${t.number(e.count)} geselecteerde items`})} te slepen.`,dragStartedKeyboard:"Begonnen met slepen. Druk op Tab om naar een locatie te gaan. Druk dan op Enter om neer te zetten, of op Esc om te annuleren.",dragStartedTouch:"Begonnen met slepen. Ga naar de gewenste locatie en dubbeltik om neer te zetten.",dragStartedVirtual:"Begonnen met slepen. Ga naar de gewenste locatie en klik of druk op Enter om neer te zetten.",dropCanceled:"Neerzetten geannuleerd.",dropComplete:"Neerzetten voltooid.",dropDescriptionKeyboard:"Druk op Enter om neer te zetten. Druk op Esc om het slepen te annuleren.",dropDescriptionTouch:"Dubbeltik om neer te zetten.",dropDescriptionVirtual:"Klik om neer te zetten.",dropIndicator:"aanwijzer voor neerzetten",dropOnItem:e=>`Neerzetten op ${e.itemText}`,dropOnRoot:"Neerzetten op",endDragKeyboard:"Bezig met slepen. Druk op Enter om te annuleren.",endDragTouch:"Bezig met slepen. Dubbeltik om te annuleren.",endDragVirtual:"Bezig met slepen. Klik om te annuleren.",insertAfter:e=>`Plaatsen na ${e.itemText}`,insertBefore:e=>`Plaatsen v\xf3\xf3r ${e.itemText}`,insertBetween:e=>`Plaatsen tussen ${e.beforeItemText} en ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} niet geselecteerd.`,longPressToSelect:"Druk lang om de selectiemodus te openen.",select:"Selecteren",selectedAll:"Alle items geselecteerd.",selectedCount:(e,t)=>`${t.plural(e.count,{"=0":"Geen items geselecteerd",one:()=>`${t.number(e.count)} item geselecteerd`,other:()=>`${t.number(e.count)} items geselecteerd`})}.`,selectedItem:e=>`${e.item} geselecteerd.`},"@react-aria/gridlist":{hasActionAnnouncement:"rij heeft actie",hasLinkAnnouncement:e=>`rij heeft koppeling: ${e.link}`},"@react-aria/menu":{longPressMessage:"Druk lang op Alt + pijl-omlaag of druk op Alt om het menu te openen"},"@react-aria/numberfield":{decrease:e=>`${e.fieldLabel} verlagen`,increase:e=>`${e.fieldLabel} verhogen`,numberField:"Getalveld"},"@react-aria/overlays":{dismiss:"Negeren"},"@react-aria/pagination":{next:"Volgende",previous:"Vorige"},"@react-aria/searchfield":{"Clear search":"Zoekactie wissen"},"@react-aria/spinbutton":{Empty:"Leeg"},"@react-aria/steplist":{steplist:"Stappenlijst"},"@react-aria/table":{ascending:"oplopend",ascendingSort:e=>`gesorteerd in oplopende volgorde in kolom ${e.columnName}`,columnSize:e=>`${e.value} pixels`,descending:"aflopend",descendingSort:e=>`gesorteerd in aflopende volgorde in kolom ${e.columnName}`,resizerDescription:"Druk op Enter om het formaat te wijzigen",select:"Selecteren",selectAll:"Alles selecteren",sortable:"sorteerbare kolom"},"@react-aria/tag":{removeButtonLabel:"Verwijderen",removeDescription:"Druk op Verwijderen om de tag te verwijderen."},"@react-aria/toast":{close:"Sluiten",notifications:"Meldingen"},"@react-aria/tree":{collapse:"Samenvouwen",expand:"Uitvouwen"},"@react-stately/color":{alpha:"Alfa",black:"zwart",blue:"Blauw","blue purple":"paarsblauw",brightness:"Helderheid",brown:"bruin","brown yellow":"bruingeel",colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:"cyaan","cyan blue":"cyaanblauw",dark:"donker",gray:"grijs",grayish:"grijsachtig",green:"Groen","green cyan":"cyaangroen",hue:"Kleurtoon",light:"licht",lightness:"Lichtsterkte",magenta:"magenta","magenta pink":"magentaroze",orange:"oranje","orange yellow":"oranjegeel",pale:"bleek",pink:"roze","pink red":"rozerood",purple:"paars","purple magenta":"magentapaars",red:"Rood","red orange":"roodoranje",saturation:"Verzadiging",transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} transparant`,"very dark":"heel donker","very light":"heel licht",vibrant:"levendig",white:"wit",yellow:"geel","yellow green":"geelgroen"},"@react-stately/datepicker":{rangeOverflow:e=>`Waarde moet ${e.maxValue} of eerder zijn.`,rangeReversed:"De startdatum moet voor de einddatum liggen.",rangeUnderflow:e=>`Waarde moet ${e.minValue} of later zijn.`,unavailableDate:"Geselecteerde datum niet beschikbaar."},"react-aria-components":{colorSwatchPicker:"kleurstalen",dropzoneLabel:"DropZone",selectPlaceholder:"Selecteer een item",tableResizer:"Resizer"}};