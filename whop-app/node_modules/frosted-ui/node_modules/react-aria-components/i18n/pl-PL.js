module.exports={"@react-aria/breadcrumbs":{breadcrumbs:"Struktura nawigacyjna"},"@react-aria/calendar":{dateRange:e=>`${e.startDate} do ${e.endDate}`,dateSelected:e=>`Wybrano ${e.date}`,finishRangeSelectionPrompt:`<PERSON><PERSON><PERSON>j, aby zak<PERSON> wyb\xf3r zakresu dat`,maximumDate:`Ostatnia dostępna data`,minimumDate:`Pier<PERSON>za dostępna data`,next:"Dalej",previous:"<PERSON>ste<PERSON>",selectedDateDescription:e=>`Wybrana data: ${e.date}`,selectedRangeDescription:e=>`Wybrany zakres: ${e.dateRange}`,startRangeSelectionPrompt:`<PERSON>liknij, aby r<PERSON> wyb\xf3r zakresu dat`,todayDate:e=>`Dzisiaj, ${e.date}`,todayDateSelected:e=>`<PERSON><PERSON><PERSON><PERSON> wybrano ${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`Pr\xf3bnik kolor\xf3w`,colorSwatch:`pr\xf3bka koloru`,transparent:"przezroczysty",twoDimensionalSlider:"Suwak 2D"},"@react-aria/combobox":{buttonLabel:`Wyświetlaj sugestie`,countAnnouncement:(e,a)=>`dostępna/dostępne(-nych) ${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} opcja`,other:()=>`${a.number(e.optionCount)} opcje(-i)`})}.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Dołączono do grupy ${e.groupTitle}, z ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} opcją`,other:()=>`${a.number(e.groupCount)} opcjami`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:", wybrano",other:""},e.isSelected)}`,listboxLabel:"Sugestie",selectedAnnouncement:e=>`${e.optionText}, wybrano`},"@react-aria/datepicker":{calendar:"Kalendarz",day:`dzień`,dayPeriod:`rano / po południu / wieczorem`,endDate:`Data końcowa`,era:"era",hour:"godzina",minute:"minuta",month:`miesiąc`,second:"sekunda",selectedDateDescription:e=>`Wybrana data: ${e.date}`,selectedRangeDescription:e=>`Wybrany zakres: ${e.startDate} do ${e.endDate}`,selectedTimeDescription:e=>`Wybrany czas: ${e.time}`,startDate:`Data początkowa`,timeZoneName:"strefa czasowa",weekday:`dzień tygodnia`,year:"rok"},"@react-aria/dnd":{dragDescriptionKeyboard:`Naciśnij Enter, aby rozpocząć przeciąganie.`,dragDescriptionKeyboardAlt:`Naciśnij Alt + Enter, aby rozpocząć przeciąganie.`,dragDescriptionLongPress:`Naciśnij i przytrzymaj, aby rozpocząć przeciąganie.`,dragDescriptionTouch:`Dotknij dwukrotnie, aby rozpocząć przeciąganie.`,dragDescriptionVirtual:`Kliknij, aby rozpocząć przeciąganie.`,dragItem:e=>`Przeciągnij ${e.itemText}`,dragSelectedItems:(e,a)=>`Przeciągnij ${a.plural(e.count,{one:()=>`${a.number(e.count)} wybrany element`,other:()=>`${a.number(e.count)} wybranych element\xf3w`})}`,dragSelectedKeyboard:(e,a)=>`Naciśnij Enter, aby przeciągnąć ${a.plural(e.count,{one:()=>`${a.number(e.count)} wybrany element`,other:()=>`${a.number(e.count)} wybrane(-ych) elementy(-\xf3w)`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Naciśnij Alt + Enter, aby przeciągnąć ${a.plural(e.count,{one:()=>`${a.number(e.count)} wybrany element`,other:()=>`${a.number(e.count)} wybrane(-ych) elementy(-\xf3w)`})}.`,dragSelectedLongPress:(e,a)=>`Naciśnij i przytrzymaj, aby przeciągnąć ${a.plural(e.count,{one:()=>`${a.number(e.count)} wybrany element`,other:()=>`${a.number(e.count)} wybrane(-ych) elementy(-\xf3w)`})}.`,dragStartedKeyboard:`Rozpoczęto przeciąganie. Naciśnij Tab, aby wybrać miejsce docelowe, a następnie naciśnij Enter, aby upuścić, lub Escape, aby anulować.`,dragStartedTouch:`Rozpoczęto przeciąganie. Wybierz miejsce, w kt\xf3rym chcesz upuścić element, a następnie dotknij dwukrotnie, aby upuścić.F`,dragStartedVirtual:`Rozpoczęto przeciąganie. Wybierz miejsce, w kt\xf3rym chcesz upuścić element, a następnie kliknij lub naciśnij Enter, aby upuścić.`,dropCanceled:"Anulowano upuszczenie.",dropComplete:`Zakończono upuszczanie.`,dropDescriptionKeyboard:`Naciśnij Enter, aby upuścić. Naciśnij Escape, aby anulować przeciągnięcie.`,dropDescriptionTouch:`Dotknij dwukrotnie, aby upuścić.`,dropDescriptionVirtual:`Kliknij, aby upuścić.`,dropIndicator:`wskaźnik upuszczenia`,dropOnItem:e=>`Upuść na ${e.itemText}`,dropOnRoot:`Upuść`,endDragKeyboard:`Przeciąganie. Naciśnij Enter, aby anulować przeciągnięcie.`,endDragTouch:`Przeciąganie. Kliknij dwukrotnie, aby anulować przeciągnięcie.`,endDragVirtual:`Przeciąganie. Kliknij, aby anulować przeciąganie.`,insertAfter:e=>`Umieść za ${e.itemText}`,insertBefore:e=>`Umieść przed ${e.itemText}`,insertBetween:e=>`Umieść między ${e.beforeItemText} i ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`Nie zaznaczono ${e.item}.`,longPressToSelect:`Naciśnij i przytrzymaj, aby wejść do trybu wyboru.`,select:"Zaznacz",selectedAll:"Wszystkie zaznaczone elementy.",selectedCount:(e,a)=>`${a.plural(e.count,{"=0":`Nie zaznaczono żadnych element\xf3w`,one:()=>`${a.number(e.count)} zaznaczony element`,other:()=>`${a.number(e.count)} zaznaczonych element\xf3w`})}.`,selectedItem:e=>`Zaznaczono ${e.item}.`},"@react-aria/gridlist":{hasActionAnnouncement:`wiersz zawiera działanie`,hasLinkAnnouncement:e=>`wiersz zawiera link: ${e.link}`},"@react-aria/menu":{longPressMessage:`Naciśnij i przytrzymaj lub naciśnij klawisze Alt + Strzałka w d\xf3ł, aby otworzyć menu`},"@react-aria/numberfield":{decrease:e=>`Zmniejsz ${e.fieldLabel}`,increase:e=>`Zwiększ ${e.fieldLabel}`,numberField:"Pole numeru"},"@react-aria/overlays":{dismiss:"Zignoruj"},"@react-aria/pagination":{next:"Dalej",previous:"Wstecz"},"@react-aria/searchfield":{"Clear search":`Wyczyść zawartość wyszukiwania`},"@react-aria/spinbutton":{Empty:"Pusty"},"@react-aria/steplist":{steplist:`Lista krok\xf3w`},"@react-aria/table":{ascending:`rosnąco`,ascendingSort:e=>`posortowano według kolumny ${e.columnName} w porządku rosnącym`,columnSize:e=>`Liczba pikseli: ${e.value}`,descending:`malejąco`,descendingSort:e=>`posortowano według kolumny ${e.columnName} w porządku malejącym`,resizerDescription:`Naciśnij Enter, aby rozpocząć zmienianie rozmiaru`,select:"Zaznacz",selectAll:"Zaznacz wszystko",sortable:`kolumna z możliwością sortowania`},"@react-aria/tag":{removeButtonLabel:`Usuń`,removeDescription:`Naciśnij Usuń, aby usunąć znacznik.`},"@react-aria/toast":{close:"Zamknij",notifications:"Powiadomienia"},"@react-aria/tree":{collapse:`Zwiń`,expand:`Rozwiń`},"@react-stately/color":{alpha:"Alfa",black:"czarny",blue:"Niebieski","blue purple":"niebiesko-fioletowy",brightness:`Jasność`,brown:`brązowy`,"brown yellow":`brązowo-ż\xf3łty`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:"cyjanowy","cyan blue":"cyjanowo-niebieski",dark:"ciemny",gray:"szary",grayish:"szarawy",green:"Zielony","green cyan":"zielono-cyjanowy",hue:`Odcień`,light:"jasny",lightness:`Jaskrawość`,magenta:"purpurowy","magenta pink":`purpurowo-r\xf3żowy`,orange:`pomarańczowy`,"orange yellow":`pomarańczowo-ż\xf3łty`,pale:"blady",pink:`r\xf3żowy`,"pink red":`r\xf3żowo-czerwony`,purple:"fioletowy","purple magenta":"fioletowo-purpurowy",red:"Czerwony","red orange":`czerwono-pomarańczowy`,saturation:"Nasycenie",transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} przezroczystości`,"very dark":"bardzo ciemny","very light":"bardzo jasny",vibrant:"intensywny",white:`biały`,yellow:`ż\xf3łty`,"yellow green":`ż\xf3łto-zielony`},"@react-stately/datepicker":{rangeOverflow:e=>`Wartość musi mieć wartość ${e.maxValue} lub wcześniejszą.`,rangeReversed:`Data rozpoczęcia musi być wcześniejsza niż data zakończenia.`,rangeUnderflow:e=>`Wartość musi mieć wartość ${e.minValue} lub p\xf3źniejszą.`,unavailableDate:`Wybrana data jest niedostępna.`},"react-aria-components":{colorSwatchPicker:`Pr\xf3bki kolor\xf3w`,dropzoneLabel:"Strefa upuszczania",selectPlaceholder:"Wybierz element",tableResizer:"Zmiana rozmiaru"}};