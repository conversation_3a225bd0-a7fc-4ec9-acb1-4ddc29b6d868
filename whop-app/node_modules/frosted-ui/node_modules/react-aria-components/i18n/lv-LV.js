module.exports={"@react-aria/breadcrumbs":{breadcrumbs:`<PERSON><PERSON><PERSON>i`},"@react-aria/calendar":{dateRange:e=>`No ${e.startDate} līdz ${e.endDate}`,dateSelected:e=>`Atlasīts: ${e.date}`,finishRangeSelectionPrompt:`Noklikšķiniet, lai pabeigtu datumu diapazona atlasi`,maximumDate:`Pēdējais pieejamais datums`,minimumDate:"Pirmais pieejamais datums",next:`<PERSON><PERSON><PERSON><PERSON>k`,previous:`Atpakaļ`,selectedDateDescription:e=>`Atlasītais datums: ${e.date}`,selectedRangeDescription:e=>`Atlasītais diapazons: ${e.dateRange}`,startRangeSelectionPrompt:`Noklikšķiniet, lai sāktu datumu diapazona atlasi`,todayDate:e=>`Šodien, ${e.date}`,todayDateSelected:e=>`<PERSON><PERSON><PERSON>, ${e.date}`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`Krāsu atlasītājs`,colorSwatch:`krāsu paraugs`,transparent:`caurspīdīgs`,twoDimensionalSlider:`2D slīdnis`},"@react-aria/combobox":{buttonLabel:`Rādīt ieteikumus`,countAnnouncement:(e,a)=>`Pieejamo opciju skaits: ${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} opcija`,other:()=>`${a.number(e.optionCount)} opcijas`})}.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Ievadīta grupa ${e.groupTitle}, ar ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} opciju`,other:()=>`${a.number(e.groupCount)} opcijām`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:`, atlasīta`,other:""},e.isSelected)}`,listboxLabel:"Ieteikumi",selectedAnnouncement:e=>`${e.optionText}, atlasīta`},"@react-aria/datepicker":{calendar:`Kalendārs`,day:"diena",dayPeriod:`priekšpusdienā/pēcpusdienā`,endDate:"Beigu datums",era:`ēra`,hour:"stundas",minute:`minūtes`,month:`mēnesis`,second:"sekundes",selectedDateDescription:e=>`Atlasītais datums: ${e.date}`,selectedRangeDescription:e=>`Atlasītais diapazons: no ${e.startDate} līdz ${e.endDate}`,selectedTimeDescription:e=>`Atlasītais laiks: ${e.time}`,startDate:`Sākuma datums`,timeZoneName:"laika josla",weekday:`nedēļas diena`,year:"gads"},"@react-aria/dnd":{dragDescriptionKeyboard:`Nospiediet Enter, lai sāktu vilkšanu.`,dragDescriptionKeyboardAlt:`Nospiediet taustiņu kombināciju Alt+Enter, lai sāktu vilkšanu.`,dragDescriptionLongPress:`Turiet nospiestu, lai sāktu vilkšanu.`,dragDescriptionTouch:`Veiciet dubultskārienu, lai sāktu vilkšanu.`,dragDescriptionVirtual:`Noklikšķiniet, lai sāktu vilkšanu.`,dragItem:e=>`Velciet ${e.itemText}`,dragSelectedItems:(e,a)=>`Velciet ${a.plural(e.count,{one:()=>`${a.number(e.count)} atlasīto vienumu`,other:()=>`${a.number(e.count)} atlasītos vienumus`})}`,dragSelectedKeyboard:(e,a)=>`Nospiediet taustiņu Enter, lai vilktu ${a.plural(e.count,{one:()=>`${a.number(e.count)} atlasīto vienumu`,other:()=>`${a.number(e.count)} atlasītos vienumus`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Nospiediet taustiņu kombināciju Alt+Enter, lai vilktu ${a.plural(e.count,{one:()=>`${a.number(e.count)} atlasīto vienumu`,other:()=>`${a.number(e.count)} atlasītos vienumus`})}.`,dragSelectedLongPress:(e,a)=>`Turiet nospiestu, lai vilktu ${a.plural(e.count,{one:()=>`${a.number(e.count)} atlasīto vienumu`,other:()=>`${a.number(e.count)} atlasītos vienumus`})}.`,dragStartedKeyboard:`Uzsākta vilkšana. Nospiediet taustiņu Tab, lai pārietu uz nomešanas mērķi, pēc tam nospiediet Enter, lai nomestu, vai nospiediet Escape, lai atceltu.`,dragStartedTouch:`Uzsākta vilkšana. Pārejiet uz nomešanas mērķi, pēc tam veiciet dubultskārienu, lai nomestu.`,dragStartedVirtual:`Uzsākta vilkšana. Pārejiet uz nomešanas mērķi, pēc tam nospiediet Enter, lai nomestu.`,dropCanceled:`Nomešana atcelta.`,dropComplete:`Nomešana pabeigta.`,dropDescriptionKeyboard:`Nospiediet Enter, lai nomestu. Nospiediet Escape, lai atceltu vilkšanu.`,dropDescriptionTouch:`Veiciet dubultskārienu, lai nomestu.`,dropDescriptionVirtual:`Noklikšķiniet, lai nomestu.`,dropIndicator:`nomešanas indikators`,dropOnItem:e=>`Nometiet uz ${e.itemText}`,dropOnRoot:"Nometiet uz",endDragKeyboard:`Notiek vilkšana. Nospiediet Enter, lai atceltu vilkšanu.`,endDragTouch:`Notiek vilkšana. Veiciet dubultskārienu, lai atceltu vilkšanu.`,endDragVirtual:`Notiek vilkšana. Noklikšķiniet, lai atceltu vilkšanu.`,insertAfter:e=>`Ievietojiet pēc ${e.itemText}`,insertBefore:e=>`Ievietojiet pirms ${e.itemText}`,insertBetween:e=>`Ievietojiet starp ${e.beforeItemText} un ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`Vienums ${e.item} nav atlasīts.`,longPressToSelect:`Ilgi turiet nospiestu. lai ieslēgtu atlases režīmu.`,select:`Atlasīt`,selectedAll:`Atlasīti visi vienumi.`,selectedCount:(e,a)=>`${a.plural(e.count,{"=0":`Nav atlasīts neviens vienums`,one:()=>`Atlasīto vienumu skaits: ${a.number(e.count)}`,other:()=>`Atlasīto vienumu skaits: ${a.number(e.count)}`})}.`,selectedItem:e=>`Atlasīts vienums ${e.item}.`},"@react-aria/gridlist":{hasActionAnnouncement:`rindai ir darbība`,hasLinkAnnouncement:e=>`rindai ir saite: ${e.link}`},"@react-aria/menu":{longPressMessage:`Lai atvērtu izvēlni, turiet nospiestu vai nospiediet taustiņu kombināciju Alt + lejupvērstā bultiņa`},"@react-aria/numberfield":{decrease:e=>`Samazināšana ${e.fieldLabel}`,increase:e=>`Palielināšana ${e.fieldLabel}`,numberField:`Skaitļu lauks`},"@react-aria/overlays":{dismiss:`Nerādīt`},"@react-aria/pagination":{next:`Tālāk`,previous:`Atpakaļ`},"@react-aria/searchfield":{"Clear search":`Notīrīt meklēšanu`},"@react-aria/spinbutton":{Empty:`Tukšs`},"@react-aria/steplist":{steplist:`Darbību saraksts`},"@react-aria/table":{ascending:`augošā secībā`,ascendingSort:e=>`kārtots pēc kolonnas ${e.columnName} augošā secībā`,columnSize:e=>`${e.value} pikseļi`,descending:`dilstošā secībā`,descendingSort:e=>`kārtots pēc kolonnas ${e.columnName} dilstošā secībā`,resizerDescription:`Nospiediet Enter, lai sāktu izmēru mainīšanu`,select:`Atlasīt`,selectAll:`Atlasīt visu`,sortable:`kārtojamā kolonna`},"@react-aria/tag":{removeButtonLabel:`Noņemt`,removeDescription:`Nospiediet Delete [Dzēst], lai noņemtu tagu.`},"@react-aria/toast":{close:`Aizvērt`,notifications:`Paziņojumi`},"@react-aria/tree":{collapse:`Sakļaut`,expand:`Izvērst`},"@react-stately/color":{alpha:"Alfa",black:"melns",blue:"Zila","blue purple":"zili violets",brightness:"Spilgtums",brown:`brūns`,"brown yellow":`brūni dzeltens`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`ciāns`,"cyan blue":`ciāna zils`,dark:`tumšs`,gray:`pelēks`,grayish:`pelēcīgs`,green:`Zaļa`,"green cyan":`zaļš ciāns`,hue:`Nokrāsa`,light:`gaišs`,lightness:`Gaišums`,magenta:"fuksiju","magenta pink":`fuksiju rozā`,orange:`oranžs`,"orange yellow":`oranži dzeltens`,pale:`bāls`,pink:`rozā`,"pink red":`rozīgi sarkans`,purple:"violets","purple magenta":"violets fuksiju",red:"Sarkana","red orange":`sarkanīgi oranžs`,saturation:`Piesātinājums`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} caurspīdīgs`,"very dark":`ļoti tumšs`,"very light":`ļoti gaišs`,vibrant:`košs`,white:"balts",yellow:"dzeltens","yellow green":`dzelteni zaļš`},"@react-stately/datepicker":{rangeOverflow:e=>`Vērtībai ir jābūt ${e.maxValue} vai agrākai.`,rangeReversed:`Sākuma datumam ir jābūt pirms beigu datuma.`,rangeUnderflow:e=>`Vērtībai ir jābūt ${e.minValue} vai vēlākai.`,unavailableDate:`Atlasītais datums nav pieejams.`},"react-aria-components":{colorSwatchPicker:`Krāsu paraugi`,dropzoneLabel:"DropZone",selectPlaceholder:`Izvēlēties vienumu`,tableResizer:`Izmēra mainītājs`}};