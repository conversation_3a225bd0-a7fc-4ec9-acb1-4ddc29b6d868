module.exports={"@react-aria/breadcrumbs":{breadcrumbs:`パンくずリスト`},"@react-aria/calendar":{dateRange:e=>`${e.startDate} から ${e.endDate}`,dateSelected:e=>`${e.date} を選択`,finishRangeSelectionPrompt:`クリックして日付範囲の選択を終了`,maximumDate:`最終利用可能日`,minimumDate:`最初の利用可能日`,next:`次へ`,previous:`前へ`,selectedDateDescription:e=>`選択した日付 : ${e.date}`,selectedRangeDescription:e=>`選択範囲 : ${e.dateRange}`,startRangeSelectionPrompt:`クリックして日付範囲の選択を開始`,todayDate:e=>`本日、${e.date}`,todayDateSelected:e=>`本日、${e.date} を選択`},"@react-aria/color":{colorInputLabel:e=>`${e.label}、${e.channelLabel}`,colorNameAndValue:e=>`${e.name} : ${e.value}`,colorPicker:`カラーピッカー`,colorSwatch:`カラースウォッチ`,transparent:`透明`,twoDimensionalSlider:`2D スライダー`},"@react-aria/combobox":{buttonLabel:`候補を表示`,countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)} 個のオプション`,other:()=>`${t.number(e.optionCount)} 個のオプション`})}を利用できます。`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`入力されたグループ ${e.groupTitle}、${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)} 個のオプション`,other:()=>`${t.number(e.groupCount)} 個のオプション`})}を含む。`,other:""},e.isGroupChange)}${e.optionText}${t.select({true:`、選択済み`,other:""},e.isSelected)}`,listboxLabel:`候補`,selectedAnnouncement:e=>`${e.optionText}、選択済み`},"@react-aria/datepicker":{calendar:`カレンダー`,day:`日`,dayPeriod:`午前/午後`,endDate:`終了日`,era:`時代`,hour:`時`,minute:`分`,month:`月`,second:`秒`,selectedDateDescription:e=>`選択した日付 : ${e.date}`,selectedRangeDescription:e=>`選択範囲 : ${e.startDate} から ${e.endDate}`,selectedTimeDescription:e=>`選択した時間 : ${e.time}`,startDate:`開始日`,timeZoneName:`タイムゾーン`,weekday:`曜日`,year:`年`},"@react-aria/dnd":{dragDescriptionKeyboard:`Enter キーを押してドラッグを開始してください。`,dragDescriptionKeyboardAlt:`Alt+Enter キーを押してドラッグを開始します。`,dragDescriptionLongPress:`長押ししてドラッグを開始します。`,dragDescriptionTouch:`ダブルタップしてドラッグを開始します。`,dragDescriptionVirtual:`クリックしてドラッグを開始します。`,dragItem:e=>`${e.itemText} をドラッグ`,dragSelectedItems:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)} 個の選択項目`,other:()=>`${t.number(e.count)} 個の選択項目`})} をドラッグ`,dragSelectedKeyboard:(e,t)=>`Enter キーを押して、${t.plural(e.count,{one:()=>`${t.number(e.count)} 選択した項目`,other:()=>`${t.number(e.count)} 選択した項目`})}をドラッグします。`,dragSelectedKeyboardAlt:(e,t)=>`Alt+Enter キーを押して、${t.plural(e.count,{one:()=>`${t.number(e.count)} 選択した項目`,other:()=>`${t.number(e.count)} 選択した項目`})}をドラッグします。`,dragSelectedLongPress:(e,t)=>`長押しして、${t.plural(e.count,{one:()=>`${t.number(e.count)} 選択した項目`,other:()=>`${t.number(e.count)} 選択した項目`})}をドラッグします。`,dragStartedKeyboard:`ドラッグを開始します。Tab キーを押してドロップターゲットにいどうし、Enter キーを押してドロップするか、Esc キーを押してキャンセルします。`,dragStartedTouch:`ドラッグを開始しました。ドロップのターゲットに移動し、ダブルタップしてドロップします。`,dragStartedVirtual:`ドラッグを開始しました。ドロップのターゲットに移動し、クリックまたは Enter キーを押してドロップします。`,dropCanceled:`ドロップがキャンセルされました。`,dropComplete:`ドロップが完了しました。`,dropDescriptionKeyboard:`Enter キーを押してドロップします。Esc キーを押してドラッグをキャンセルします。`,dropDescriptionTouch:`ダブルタップしてドロップします。`,dropDescriptionVirtual:`クリックしてドロップします。`,dropIndicator:`ドロップインジケーター`,dropOnItem:e=>`${e.itemText} にドロップ`,dropOnRoot:`ドロップ場所`,endDragKeyboard:`ドラッグしています。Enter キーを押してドラッグをキャンセルします。`,endDragTouch:`ドラッグしています。ダブルタップしてドラッグをキャンセルします。`,endDragVirtual:`ドラッグしています。クリックしてドラッグをキャンセルします。`,insertAfter:e=>`${e.itemText} の後に挿入`,insertBefore:e=>`${e.itemText} の前に挿入`,insertBetween:e=>`${e.beforeItemText} と ${e.afterItemText} の間に挿入`},"@react-aria/grid":{deselectedItem:e=>`${e.item} が選択されていません。`,longPressToSelect:`長押しして選択モードを開きます。`,select:`選択`,selectedAll:`すべての項目を選択しました。`,selectedCount:(e,t)=>`${t.plural(e.count,{"=0":`項目が選択されていません`,one:()=>`${t.number(e.count)} 項目を選択しました`,other:()=>`${t.number(e.count)} 項目を選択しました`})}。`,selectedItem:e=>`${e.item} を選択しました。`},"@react-aria/gridlist":{hasActionAnnouncement:`行にはアクションがあります`,hasLinkAnnouncement:e=>`行にリンクがあります : ${e.link}`},"@react-aria/menu":{longPressMessage:`長押しまたは Alt+下矢印キーでメニューを開く`},"@react-aria/numberfield":{decrease:e=>`${e.fieldLabel}を縮小`,increase:e=>`${e.fieldLabel}を拡大`,numberField:`数値フィールド`},"@react-aria/overlays":{dismiss:`閉じる`},"@react-aria/pagination":{next:`次へ`,previous:`前へ`},"@react-aria/searchfield":{"Clear search":`検索をクリア`},"@react-aria/spinbutton":{Empty:`空`},"@react-aria/steplist":{steplist:`手順リスト`},"@react-aria/table":{ascending:`昇順`,ascendingSort:e=>`列 ${e.columnName} を昇順で並べ替え`,columnSize:e=>`${e.value} ピクセル`,descending:`降順`,descendingSort:e=>`列 ${e.columnName} を降順で並べ替え`,resizerDescription:`Enter キーを押してサイズ変更を開始`,select:`選択`,selectAll:`すべて選択`,sortable:`並べ替え可能な列`},"@react-aria/tag":{removeButtonLabel:`削除`,removeDescription:`タグを削除するには、Delete キーを押します。`},"@react-aria/toast":{close:`閉じる`,notifications:`通知`},"@react-aria/tree":{collapse:`折りたたむ`,expand:`展開`},"@react-stately/color":{alpha:`アルファ`,black:`ブラック`,blue:`青`,"blue purple":`ブルーパープル`,brightness:`明るさ`,brown:`ブラウン`,"brown yellow":`ブラウンイエロー`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`シアン`,"cyan blue":`シアンブルー`,dark:`ダーク`,gray:`グレー`,grayish:`グレイッシュ`,green:`緑`,"green cyan":`グリーンシアン`,hue:`色相`,light:`ライト`,lightness:`明度`,magenta:`マゼンタ`,"magenta pink":`マゼンタピンク`,orange:`オレンジ`,"orange yellow":`オレンジイエロー`,pale:`ペール`,pink:`ピンク`,"pink red":`ピンクレッド`,purple:`パープル`,"purple magenta":`パープルマゼンタ`,red:`赤`,"red orange":`レッドオレンジ`,saturation:`彩度`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} 透明`,"very dark":`最も暗い`,"very light":`ベリーライト`,vibrant:`鮮やか`,white:`ホワイト`,yellow:`イエロー`,"yellow green":`イエローグリーン`},"@react-stately/datepicker":{rangeOverflow:e=>`値は ${e.maxValue} 以下にする必要があります。`,rangeReversed:`開始日は終了日より前にする必要があります。`,rangeUnderflow:e=>`値は ${e.minValue} 以上にする必要があります。`,unavailableDate:`選択した日付は使用できません。`},"react-aria-components":{colorSwatchPicker:`カラースウォッチ`,dropzoneLabel:`ドロップゾーン`,selectPlaceholder:`項目を選択`,tableResizer:`サイズ変更ツール`}};