module.exports={"@react-aria/breadcrumbs":{breadcrumbs:"Navigasjonsstier"},"@react-aria/calendar":{dateRange:e=>`${e.startDate} til ${e.endDate}`,dateSelected:e=>`${e.date} valgt`,finishRangeSelectionPrompt:`Klikk for \xe5 fullf\xf8re valg av datoomr\xe5de`,maximumDate:"Siste tilgjengelige dato",minimumDate:`F\xf8rste tilgjengelige dato`,next:"Neste",previous:"Forrige",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt omr\xe5de: ${e.dateRange}`,startRangeSelectionPrompt:`Klikk for \xe5 starte valg av datoomr\xe5de`,todayDate:e=>`I dag, ${e.date}`,todayDateSelected:e=>`I dag, ${e.date} valgt`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:"Fargevelger",colorSwatch:"fargekart",transparent:"gjennomsiktig",twoDimensionalSlider:"2D-glidebryter"},"@react-aria/combobox":{buttonLabel:"Vis forslag",countAnnouncement:(e,r)=>`${r.plural(e.optionCount,{one:()=>`${r.number(e.optionCount)} alternativ`,other:()=>`${r.number(e.optionCount)} alternativer`})} finnes.`,focusAnnouncement:(e,r)=>`${r.select({true:()=>`Angitt gruppe ${e.groupTitle}, med ${r.plural(e.groupCount,{one:()=>`${r.number(e.groupCount)} alternativ`,other:()=>`${r.number(e.groupCount)} alternativer`})}. `,other:""},e.isGroupChange)}${e.optionText}${r.select({true:", valgt",other:""},e.isSelected)}`,listboxLabel:"Forslag",selectedAnnouncement:e=>`${e.optionText}, valgt`},"@react-aria/datepicker":{calendar:"Kalender",day:"dag",dayPeriod:"a.m./p.m.",endDate:"Sluttdato",era:"tidsalder",hour:"time",minute:"minutt",month:`m\xe5ned`,second:"sekund",selectedDateDescription:e=>`Valgt dato: ${e.date}`,selectedRangeDescription:e=>`Valgt omr\xe5de: ${e.startDate} til ${e.endDate}`,selectedTimeDescription:e=>`Valgt tid: ${e.time}`,startDate:"Startdato",timeZoneName:"tidssone",weekday:"ukedag",year:`\xe5r`},"@react-aria/dnd":{dragDescriptionKeyboard:`Trykk p\xe5 Enter for \xe5 begynne \xe5 dra.`,dragDescriptionKeyboardAlt:`Trykk p\xe5 Alt + Enter for \xe5 begynne \xe5 dra.`,dragDescriptionLongPress:`Trykk lenge for \xe5 begynne \xe5 dra.`,dragDescriptionTouch:`Dobbelttrykk for \xe5 begynne \xe5 dra.`,dragDescriptionVirtual:`Klikk for \xe5 begynne \xe5 dra.`,dragItem:e=>`Dra ${e.itemText}`,dragSelectedItems:(e,r)=>`Dra ${r.plural(e.count,{one:()=>`${r.number(e.count)} merket element`,other:()=>`${r.number(e.count)} merkede elementer`})}`,dragSelectedKeyboard:(e,r)=>`Trykk Enter for \xe5 dra ${r.plural(e.count,{one:()=>`${r.number(e.count)} valgt element`,other:()=>`${r.number(e.count)} valgte elementer`})}.`,dragSelectedKeyboardAlt:(e,r)=>`Trykk p\xe5 Alt + Enter for \xe5 dra ${r.plural(e.count,{one:()=>`${r.number(e.count)} valgt element`,other:()=>`${r.number(e.count)} valgte elementer`})}.`,dragSelectedLongPress:(e,r)=>`Trykk lenge for \xe5 dra ${r.plural(e.count,{one:()=>`${r.number(e.count)} valgt element`,other:()=>`${r.number(e.count)} valgte elementer`})}.`,dragStartedKeyboard:`Begynte \xe5 dra. Trykk p\xe5 Tab for \xe5 navigere til et m\xe5l, og trykk deretter p\xe5 Enter for \xe5 slippe eller p\xe5 Esc for \xe5 avbryte.`,dragStartedTouch:`Begynte \xe5 dra. Naviger til et m\xe5l, og dobbelttrykk for \xe5 slippe.`,dragStartedVirtual:`Begynte \xe5 dra. Naviger til et m\xe5l, og klikk eller trykk p\xe5 Enter for \xe5 slippe.`,dropCanceled:`Avbr\xf8t slipping.`,dropComplete:`Slippingen er fullf\xf8rt.`,dropDescriptionKeyboard:`Trykk p\xe5 Enter for \xe5 slippe. Trykk p\xe5 Esc hvis du vil avbryte draingen.`,dropDescriptionTouch:`Dobbelttrykk for \xe5 slippe.`,dropDescriptionVirtual:`Klikk for \xe5 slippe.`,dropIndicator:"slippeindikator",dropOnItem:e=>`Slipp p\xe5 ${e.itemText}`,dropOnRoot:`Slipp p\xe5`,endDragKeyboard:`Drar. Trykk p\xe5 Enter hvis du vil avbryte.`,endDragTouch:"Drar. Dobbelttrykk hvis du vil avbryte.",endDragVirtual:"Drar. Klikk hvis du vil avbryte.",insertAfter:e=>`Sett inn etter ${e.itemText}`,insertBefore:e=>`Sett inn f\xf8r ${e.itemText}`,insertBetween:e=>`Sett inn mellom ${e.beforeItemText} og ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} er ikke valgt.`,longPressToSelect:`Bruk et langt trykk for \xe5 g\xe5 inn i valgmodus.`,select:"Velg",selectedAll:"Alle elementer er valgt.",selectedCount:(e,r)=>`${r.plural(e.count,{"=0":"Ingen elementer er valgt",one:()=>`${r.number(e.count)} element er valgt`,other:()=>`${r.number(e.count)} elementer er valgt`})}.`,selectedItem:e=>`${e.item} er valgt.`},"@react-aria/gridlist":{hasActionAnnouncement:"rad har handling",hasLinkAnnouncement:e=>`rad har kobling: ${e.link}`},"@react-aria/menu":{longPressMessage:`Langt trykk eller trykk Alt + PilNed for \xe5 \xe5pne menyen`},"@react-aria/numberfield":{decrease:e=>`Reduser ${e.fieldLabel}`,increase:e=>`\xd8k ${e.fieldLabel}`,numberField:"Tallfelt"},"@react-aria/overlays":{dismiss:"Lukk"},"@react-aria/pagination":{next:"Neste",previous:"Forrige"},"@react-aria/searchfield":{"Clear search":`T\xf8m s\xf8k`},"@react-aria/spinbutton":{Empty:"Tom"},"@react-aria/steplist":{steplist:"Trinnliste"},"@react-aria/table":{ascending:"stigende",ascendingSort:e=>`sortert etter kolonne ${e.columnName} i stigende rekkef\xf8lge`,columnSize:e=>`${e.value} piksler`,descending:"synkende",descendingSort:e=>`sortert etter kolonne ${e.columnName} i synkende rekkef\xf8lge`,resizerDescription:`Trykk p\xe5 Enter for \xe5 starte st\xf8rrelsesendring`,select:"Velg",selectAll:"Velg alle",sortable:"kolonne som kan sorteres"},"@react-aria/tag":{removeButtonLabel:"Fjern",removeDescription:`Trykk p\xe5 Slett for \xe5 fjerne taggen.`},"@react-aria/toast":{close:"Lukk",notifications:"Varsler"},"@react-aria/tree":{collapse:"Skjul",expand:"Utvid"},"@react-stately/color":{alpha:"Alfa",black:"svart",blue:`Bl\xe5`,"blue purple":`bl\xe5lilla`,brightness:"Lysstyrke",brown:"brun","brown yellow":"brungul",colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:"cyan","cyan blue":`cyanbl\xe5`,dark:`m\xf8rk`,gray:`gr\xe5`,grayish:`gr\xe5aktig`,green:`Gr\xf8nn`,"green cyan":`gr\xf8nncyan`,hue:"Fargetone",light:"lys",lightness:"Lyshet",magenta:"magenta","magenta pink":"magentarosa",orange:"oransje","orange yellow":"oransjegul",pale:"blek",pink:"rosa","pink red":`rosar\xf8d`,purple:"lilla","purple magenta":"lillamagenta",red:`R\xf8d`,"red orange":`r\xf8doransje`,saturation:"Metning",transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} gjennomsiktig`,"very dark":`sv\xe6rt m\xf8rk`,"very light":`sv\xe6rt lys`,vibrant:"levende",white:"hvit",yellow:"gul","yellow green":`gulgr\xf8nn`},"@react-stately/datepicker":{rangeOverflow:e=>`Verdien m\xe5 v\xe6re ${e.maxValue} eller tidligere.`,rangeReversed:`Startdatoen m\xe5 v\xe6re f\xf8r sluttdatoen.`,rangeUnderflow:e=>`Verdien m\xe5 v\xe6re ${e.minValue} eller senere.`,unavailableDate:"Valgt dato utilgjengelig."},"react-aria-components":{colorSwatchPicker:"Fargekart",dropzoneLabel:"Droppsone",selectPlaceholder:"Velg et element",tableResizer:`St\xf8rrelsesendrer`}};