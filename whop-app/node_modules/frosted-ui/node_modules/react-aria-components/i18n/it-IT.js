module.exports={"@react-aria/breadcrumbs":{breadcrumbs:"Breadcrumb"},"@react-aria/calendar":{dateRange:e=>`Da ${e.startDate} a ${e.endDate}`,dateSelected:e=>`${e.date} selezionata`,finishRangeSelectionPrompt:`Fai clic per completare la selezione dell’intervallo di date`,maximumDate:"Ultima data disponibile",minimumDate:"Prima data disponibile",next:"Successivo",previous:"Precedente",selectedDateDescription:e=>`Data selezionata: ${e.date}`,selectedRangeDescription:e=>`Intervallo selezionato: ${e.dateRange}`,startRangeSelectionPrompt:`Fai clic per selezionare l’intervallo di date`,todayDate:e=>`Oggi, ${e.date}`,todayDateSelected:e=>`Oggi, ${e.date} selezionata`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:"Selettore colore",colorSwatch:"campione di colore",transparent:"trasparente",twoDimensionalSlider:"Cursore 2D"},"@react-aria/combobox":{buttonLabel:"Mostra suggerimenti",countAnnouncement:(e,a)=>`${a.plural(e.optionCount,{one:()=>`${a.number(e.optionCount)} opzione disponibile`,other:()=>`${a.number(e.optionCount)} opzioni disponibili`})}.`,focusAnnouncement:(e,a)=>`${a.select({true:()=>`Ingresso nel gruppo ${e.groupTitle}, con ${a.plural(e.groupCount,{one:()=>`${a.number(e.groupCount)} opzione`,other:()=>`${a.number(e.groupCount)} opzioni`})}. `,other:""},e.isGroupChange)}${e.optionText}${a.select({true:", selezionato",other:""},e.isSelected)}`,listboxLabel:"Suggerimenti",selectedAnnouncement:e=>`${e.optionText}, selezionato`},"@react-aria/datepicker":{calendar:"Calendario",day:"giorno",dayPeriod:"AM/PM",endDate:"Data finale",era:"era",hour:"ora",minute:"minuto",month:"mese",second:"secondo",selectedDateDescription:e=>`Data selezionata: ${e.date}`,selectedRangeDescription:e=>`Intervallo selezionato: da ${e.startDate} a ${e.endDate}`,selectedTimeDescription:e=>`Ora selezionata: ${e.time}`,startDate:"Data iniziale",timeZoneName:"fuso orario",weekday:"giorno della settimana",year:"anno"},"@react-aria/dnd":{dragDescriptionKeyboard:"Premi Invio per iniziare a trascinare.",dragDescriptionKeyboardAlt:"Premi Alt + Invio per iniziare a trascinare.",dragDescriptionLongPress:"Premi a lungo per iniziare a trascinare.",dragDescriptionTouch:"Tocca due volte per iniziare a trascinare.",dragDescriptionVirtual:"Fai clic per iniziare a trascinare.",dragItem:e=>`Trascina ${e.itemText}`,dragSelectedItems:(e,a)=>`Trascina ${a.plural(e.count,{one:()=>`${a.number(e.count)} altro elemento selezionato`,other:()=>`${a.number(e.count)} altri elementi selezionati`})}`,dragSelectedKeyboard:(e,a)=>`Premi Invio per trascinare ${a.plural(e.count,{one:()=>`${a.number(e.count)} elemento selezionato`,other:()=>`${a.number(e.count)} elementi selezionati`})}.`,dragSelectedKeyboardAlt:(e,a)=>`Premi Alt + Invio per trascinare ${a.plural(e.count,{one:()=>`${a.number(e.count)} elemento selezionato`,other:()=>`${a.number(e.count)} elementi selezionati`})}.`,dragSelectedLongPress:(e,a)=>`Premi a lungo per trascinare ${a.plural(e.count,{one:()=>`${a.number(e.count)} elemento selezionato`,other:()=>`${a.number(e.count)} elementi selezionati`})}.`,dragStartedKeyboard:`Hai iniziato a trascinare. Premi Tab per arrivare sull’area di destinazione, quindi premi Invio per rilasciare o Esc per annullare.`,dragStartedTouch:`Hai iniziato a trascinare. Arriva sull’area di destinazione, quindi tocca due volte per rilasciare.`,dragStartedVirtual:`Hai iniziato a trascinare. Arriva sull’area di destinazione, quindi fai clic o premi Invio per rilasciare.`,dropCanceled:"Rilascio annullato.",dropComplete:"Rilascio completato.",dropDescriptionKeyboard:"Premi Invio per rilasciare. Premi Esc per annullare.",dropDescriptionTouch:"Tocca due volte per rilasciare.",dropDescriptionVirtual:"Fai clic per rilasciare.",dropIndicator:"indicatore di rilascio",dropOnItem:e=>`Rilascia su ${e.itemText}`,dropOnRoot:"Rilascia su",endDragKeyboard:"Trascinamento. Premi Invio per annullare.",endDragTouch:"Trascinamento. Tocca due volte per annullare.",endDragVirtual:"Trascinamento. Fai clic per annullare.",insertAfter:e=>`Inserisci dopo ${e.itemText}`,insertBefore:e=>`Inserisci prima di ${e.itemText}`,insertBetween:e=>`Inserisci tra ${e.beforeItemText} e ${e.afterItemText}`},"@react-aria/grid":{deselectedItem:e=>`${e.item} non selezionato.`,longPressToSelect:`Premi a lungo per passare alla modalit\xe0 di selezione.`,select:"Seleziona",selectedAll:"Tutti gli elementi selezionati.",selectedCount:(e,a)=>`${a.plural(e.count,{"=0":"Nessun elemento selezionato",one:()=>`${a.number(e.count)} elemento selezionato`,other:()=>`${a.number(e.count)} elementi selezionati`})}.`,selectedItem:e=>`${e.item} selezionato.`},"@react-aria/gridlist":{hasActionAnnouncement:`la riga ha un’azione`,hasLinkAnnouncement:e=>`la riga ha un collegamento: ${e.link}`},"@react-aria/menu":{longPressMessage:`Premere a lungo o premere Alt + Freccia gi\xf9 per aprire il menu`},"@react-aria/numberfield":{decrease:e=>`Riduci ${e.fieldLabel}`,increase:e=>`Aumenta ${e.fieldLabel}`,numberField:"Campo numero"},"@react-aria/overlays":{dismiss:"Ignora"},"@react-aria/pagination":{next:"Successivo",previous:"Precedente"},"@react-aria/searchfield":{"Clear search":"Cancella ricerca"},"@react-aria/spinbutton":{Empty:"Vuoto"},"@react-aria/steplist":{steplist:"Elenco dei passaggi"},"@react-aria/table":{ascending:"crescente",ascendingSort:e=>`in ordine crescente in base alla colonna ${e.columnName}`,columnSize:e=>`${e.value} pixel`,descending:"decrescente",descendingSort:e=>`in ordine decrescente in base alla colonna ${e.columnName}`,resizerDescription:"Premi Invio per iniziare a ridimensionare",select:"Seleziona",selectAll:"Seleziona tutto",sortable:"colonna ordinabile"},"@react-aria/tag":{removeButtonLabel:"Rimuovi",removeDescription:"Premi Elimina per rimuovere il tag."},"@react-aria/toast":{close:"Chiudi",notifications:"Notifiche"},"@react-aria/tree":{collapse:"Comprimi",expand:"Espandi"},"@react-stately/color":{alpha:"Alfa",black:"nero",blue:"Blu","blue purple":"blu viola",brightness:`Luminosit\xe0`,brown:"marrone","brown yellow":"giallo bruno",colorName:e=>`${e.hue} ${e.chroma} ${e.lightness}`,cyan:"ciano","cyan blue":"blu ciano",dark:"scuro",gray:"grigio",grayish:"grigiastro",green:"Verde","green cyan":"verde ciano",hue:`Tonalit\xe0`,light:"chiaro",lightness:`Luminosit\xe0`,magenta:"magenta","magenta pink":"rosa magenta",orange:"arancio","orange yellow":"giallo arancio",pale:"tenue",pink:"rosa","pink red":"rosa rosso",purple:"viola","purple magenta":"viola magenta",red:"Rosso","red orange":"rosso arancio",saturation:"Saturazione",transparentColorName:e=>`${e.hue} ${e.chroma} ${e.lightness}, trasparenza ${e.percentTransparent}`,"very dark":"molto scuro","very light":"molto chiaro",vibrant:"vivace",white:"bianco",yellow:"giallo","yellow green":"giallo verde"},"@react-stately/datepicker":{rangeOverflow:e=>`Il valore deve essere ${e.maxValue} o precedente.`,rangeReversed:"La data di inizio deve essere antecedente alla data di fine.",rangeUnderflow:e=>`Il valore deve essere ${e.minValue} o successivo.`,unavailableDate:"Data selezionata non disponibile."},"react-aria-components":{colorSwatchPicker:"Campioni di colore",dropzoneLabel:"Zona di rilascio",selectPlaceholder:"Seleziona un elemento",tableResizer:"Ridimensionamento"}};