module.exports={"@react-aria/breadcrumbs":{breadcrumbs:`탐색 표시`},"@react-aria/calendar":{dateRange:e=>`${e.startDate} ~ ${e.endDate}`,dateSelected:e=>`${e.date} 선택됨`,finishRangeSelectionPrompt:`날짜 범위 선택을 완료하려면 클릭하십시오.`,maximumDate:`마지막으로 사용 가능한 일자`,minimumDate:`처음으로 사용 가능한 일자`,next:`다음`,previous:`이전`,selectedDateDescription:e=>`선택 일자: ${e.date}`,selectedRangeDescription:e=>`선택 범위: ${e.dateRange}`,startRangeSelectionPrompt:`날짜 범위 선택을 시작하려면 클릭하십시오.`,todayDate:e=>`오늘, ${e.date}`,todayDateSelected:e=>`오늘, ${e.date} 선택됨`},"@react-aria/color":{colorInputLabel:e=>`${e.label}, ${e.channelLabel}`,colorNameAndValue:e=>`${e.name}: ${e.value}`,colorPicker:`색상 피커`,colorSwatch:`색상 견본`,transparent:`투명도`,twoDimensionalSlider:`2D 슬라이더`},"@react-aria/combobox":{buttonLabel:`제안 사항 표시`,countAnnouncement:(e,t)=>`${t.plural(e.optionCount,{one:()=>`${t.number(e.optionCount)}개 옵션`,other:()=>`${t.number(e.optionCount)}개 옵션`})}을 사용할 수 있습니다.`,focusAnnouncement:(e,t)=>`${t.select({true:()=>`입력한 그룹 ${e.groupTitle}, ${t.plural(e.groupCount,{one:()=>`${t.number(e.groupCount)}개 옵션`,other:()=>`${t.number(e.groupCount)}개 옵션`})}. `,other:""},e.isGroupChange)}${e.optionText}${t.select({true:`, 선택됨`,other:""},e.isSelected)}`,listboxLabel:`제안`,selectedAnnouncement:e=>`${e.optionText}, 선택됨`},"@react-aria/datepicker":{calendar:`달력`,day:`일`,dayPeriod:`오전/오후`,endDate:`종료일`,era:`연호`,hour:`시`,minute:`분`,month:`월`,second:`초`,selectedDateDescription:e=>`선택 일자: ${e.date}`,selectedRangeDescription:e=>`선택 범위: ${e.startDate} ~ ${e.endDate}`,selectedTimeDescription:e=>`선택 시간: ${e.time}`,startDate:`시작일`,timeZoneName:`시간대`,weekday:`요일`,year:`년`},"@react-aria/dnd":{dragDescriptionKeyboard:`드래그를 시작하려면 Enter를 누르세요.`,dragDescriptionKeyboardAlt:`드래그를 시작하려면 Alt + Enter를 누르십시오.`,dragDescriptionLongPress:`드래그를 시작하려면 길게 누르십시오.`,dragDescriptionTouch:`드래그를 시작하려면 더블 탭하세요.`,dragDescriptionVirtual:`드래그를 시작하려면 클릭하세요.`,dragItem:e=>`${e.itemText} 드래그`,dragSelectedItems:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)}개 선택 항목`,other:()=>`${t.number(e.count)}개 선택 항목`})} 드래그`,dragSelectedKeyboard:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)}개 선택 항목`,other:()=>`${t.number(e.count)}개 선택 항목`})}을 드래그하려면 Enter를 누르십시오.`,dragSelectedKeyboardAlt:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)}개 선택 항목`,other:()=>`${t.number(e.count)}개 선택 항목`})}을 드래그하려면 Alt + Enter를 누르십시오.`,dragSelectedLongPress:(e,t)=>`${t.plural(e.count,{one:()=>`${t.number(e.count)}개 선택 항목`,other:()=>`${t.number(e.count)}개 선택 항목`})}을 드래그하려면 길게 누르십시오.`,dragStartedKeyboard:`드래그가 시작되었습니다. Tab을 눌러 드롭 대상으로 이동한 다음 Enter를 눌러 드롭하거나 Esc를 눌러 취소하세요.`,dragStartedTouch:`드래그가 시작되었습니다. 드롭 대상으로 이동한 다음 더블 탭하여 드롭하세요.`,dragStartedVirtual:`드래그가 시작되었습니다. 드롭 대상으로 이동한 다음 클릭하거나 Enter를 눌러 드롭하세요.`,dropCanceled:`드롭이 취소되었습니다.`,dropComplete:`드롭이 완료되었습니다.`,dropDescriptionKeyboard:`드롭하려면 Enter를 누르세요. 드래그를 취소하려면 Esc를 누르세요.`,dropDescriptionTouch:`더블 탭하여 드롭하세요.`,dropDescriptionVirtual:`드롭하려면 클릭하세요.`,dropIndicator:`드롭 표시기`,dropOnItem:e=>`${e.itemText}에 드롭`,dropOnRoot:`드롭 대상`,endDragKeyboard:`드래그 중입니다. 드래그를 취소하려면 Enter를 누르세요.`,endDragTouch:`드래그 중입니다. 드래그를 취소하려면 더블 탭하세요.`,endDragVirtual:`드래그 중입니다. 드래그를 취소하려면 클릭하세요.`,insertAfter:e=>`${e.itemText} 이후에 삽입`,insertBefore:e=>`${e.itemText} 이전에 삽입`,insertBetween:e=>`${e.beforeItemText} 및 ${e.afterItemText} 사이에 삽입`},"@react-aria/grid":{deselectedItem:e=>`${e.item}이(가) 선택되지 않았습니다.`,longPressToSelect:`선택 모드로 들어가려면 길게 누르십시오.`,select:`선택`,selectedAll:`모든 항목이 선택되었습니다.`,selectedCount:(e,t)=>`${t.plural(e.count,{"=0":`선택된 항목이 없습니다`,one:()=>`${t.number(e.count)}개 항목이 선택되었습니다`,other:()=>`${t.number(e.count)}개 항목이 선택되었습니다`})}.`,selectedItem:e=>`${e.item}이(가) 선택되었습니다.`},"@react-aria/gridlist":{hasActionAnnouncement:`행에 액션이 있음`,hasLinkAnnouncement:e=>`행에 링크가 있음: ${e.link}`},"@react-aria/menu":{longPressMessage:`길게 누르거나 Alt + 아래쪽 화살표를 눌러 메뉴 열기`},"@react-aria/numberfield":{decrease:e=>`${e.fieldLabel} 감소`,increase:e=>`${e.fieldLabel} 증가`,numberField:`번호 필드`},"@react-aria/overlays":{dismiss:`무시`},"@react-aria/pagination":{next:`다음`,previous:`이전`},"@react-aria/searchfield":{"Clear search":`검색 지우기`},"@react-aria/spinbutton":{Empty:`비어 있음`},"@react-aria/steplist":{steplist:`단계 목록`},"@react-aria/table":{ascending:`오름차순`,ascendingSort:e=>`${e.columnName} 열을 기준으로 오름차순으로 정렬됨`,columnSize:e=>`${e.value} 픽셀`,descending:`내림차순`,descendingSort:e=>`${e.columnName} 열을 기준으로 내림차순으로 정렬됨`,resizerDescription:`크기 조정을 시작하려면 Enter를 누르세요.`,select:`선택`,selectAll:`모두 선택`,sortable:`정렬 가능한 열`},"@react-aria/tag":{removeButtonLabel:`제거`,removeDescription:`태그를 제거하려면 Delete 키를 누르십시오.`},"@react-aria/toast":{close:`닫기`,notifications:`알림`},"@react-aria/tree":{collapse:`접기`,expand:`펼치기`},"@react-stately/color":{alpha:`알파`,black:`검은색`,blue:`파랑`,"blue purple":`청자색`,brightness:`명도`,brown:`갈색`,"brown yellow":`황갈색`,colorName:e=>`${e.lightness} ${e.chroma} ${e.hue}`,cyan:`청록색`,"cyan blue":`청록색`,dark:`다크`,gray:`회색`,grayish:`회갈색`,green:`초록`,"green cyan":`청록색`,hue:`색조`,light:`라이트`,lightness:`밝기`,magenta:`자홍색`,"magenta pink":`마젠타 핑크`,orange:`주황색`,"orange yellow":`불그스름한 노랑`,pale:`흙색`,pink:`분홍색`,"pink red":`핑크 레드`,purple:`자주색`,"purple magenta":`보라빛 자홍색`,red:`빨강`,"red orange":`붉은 주황색`,saturation:`채도`,transparentColorName:e=>`${e.lightness} ${e.chroma} ${e.hue}, ${e.percentTransparent} 투명도`,"very dark":`매우 어두운`,"very light":`매우 연함`,vibrant:`강렬한`,white:`흰색`,yellow:`노란색`,"yellow green":`연두색`},"@react-stately/datepicker":{rangeOverflow:e=>`값은 ${e.maxValue} 이전이어야 합니다.`,rangeReversed:`시작일은 종료일 이전이어야 합니다.`,rangeUnderflow:e=>`값은 ${e.minValue} 이상이어야 합니다.`,unavailableDate:`선택한 날짜를 사용할 수 없습니다.`},"react-aria-components":{colorSwatchPicker:`색상 견본`,dropzoneLabel:`드롭 영역`,selectPlaceholder:`항목 선택`,tableResizer:`크기 조정기`}};