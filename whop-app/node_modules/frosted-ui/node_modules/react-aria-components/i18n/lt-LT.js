module.exports={"@react-aria/breadcrumbs":{breadcrumbs:`<PERSON><PERSON><PERSON><PERSON><PERSON>`},"@react-aria/calendar":{dateRange:a=>`Nuo ${a.startDate} iki ${a.endDate}`,dateSelected:a=>`Pasirinkta ${a.date}`,finishRangeSelectionPrompt:`Spustelėkite, kad baigtumėte pasirinkti datų intervalą`,maximumDate:`Paskutinė galima data`,minimumDate:"Pirmoji galima data",next:"Paskesnis",previous:"Ankstesnis",selectedDateDescription:a=>`Pasirinkta data: ${a.date}`,selectedRangeDescription:a=>`Pasirinktas intervalas: ${a.dateRange}`,startRangeSelectionPrompt:`Spustelėkite, kad pradėtumėte pasirinkti datų intervalą`,todayDate:a=>`Šiandien, ${a.date}`,todayDateSelected:a=>`Šiandien, pasirinkta ${a.date}`},"@react-aria/color":{colorInputLabel:a=>`${a.label}, ${a.channelLabel}`,colorNameAndValue:a=>`${a.name}: ${a.value}`,colorPicker:`Spalvų parinkiklis`,colorSwatch:`spalvų pavyzdys`,transparent:"skaidrus",twoDimensionalSlider:"2D slankiklis"},"@react-aria/combobox":{buttonLabel:`Rodyti pasiūlymus`,countAnnouncement:(a,e)=>`Yra ${e.plural(a.optionCount,{one:()=>`${e.number(a.optionCount)} parinktis`,other:()=>`${e.number(a.optionCount)} parinktys (-ių)`})}.`,focusAnnouncement:(a,e)=>`${e.select({true:()=>`Įvesta grupė ${a.groupTitle}, su ${e.plural(a.groupCount,{one:()=>`${e.number(a.groupCount)} parinktimi`,other:()=>`${e.number(a.groupCount)} parinktimis (-ių)`})}. `,other:""},a.isGroupChange)}${a.optionText}${e.select({true:", pasirinkta",other:""},a.isSelected)}`,listboxLabel:`Pasiūlymai`,selectedAnnouncement:a=>`${a.optionText}, pasirinkta`},"@react-aria/datepicker":{calendar:"Kalendorius",day:"diena",dayPeriod:`iki pietų / po pietų`,endDate:"Pabaigos data",era:"era",hour:"valanda",minute:`minutė`,month:`mėnuo`,second:`sekundė`,selectedDateDescription:a=>`Pasirinkta data: ${a.date}`,selectedRangeDescription:a=>`Pasirinktas intervalas: nuo ${a.startDate} iki ${a.endDate}`,selectedTimeDescription:a=>`Pasirinktas laikas: ${a.time}`,startDate:`Pradžios data`,timeZoneName:"laiko juosta",weekday:`savaitės diena`,year:"metai"},"@react-aria/dnd":{dragDescriptionKeyboard:`Paspauskite „Enter“, kad pradėtumėte vilkti.`,dragDescriptionKeyboardAlt:`Paspauskite „Alt + Enter“, kad pradėtumėte vilkti.`,dragDescriptionLongPress:`Palaikykite nuspaudę, kad pradėtumėte vilkti.`,dragDescriptionTouch:`Palieskite dukart, kad pradėtumėte vilkti.`,dragDescriptionVirtual:`Spustelėkite, kad pradėtumėte vilkti.`,dragItem:a=>`Vilkti ${a.itemText}`,dragSelectedItems:(a,e)=>`Vilkti ${e.plural(a.count,{one:()=>`${e.number(a.count)} pasirinktą elementą`,other:()=>`${e.number(a.count)} pasirinktus elementus`})}`,dragSelectedKeyboard:(a,e)=>`Paspauskite „Enter“, jei norite nuvilkti ${e.plural(a.count,{one:()=>`${e.number(a.count)} pasirinktą elementą`,other:()=>`${e.number(a.count)} pasirinktus elementus`})}.`,dragSelectedKeyboardAlt:(a,e)=>`Paspauskite „Alt + Enter“, kad nuvilktumėte ${e.plural(a.count,{one:()=>`${e.number(a.count)} pasirinktą elementą`,other:()=>`${e.number(a.count)} pasirinktus elementus`})}.`,dragSelectedLongPress:(a,e)=>`Nuspaudę palaikykite, kad nuvilktumėte ${e.plural(a.count,{one:()=>`${e.number(a.count)} pasirinktą elementą`,other:()=>`${e.number(a.count)} pasirinktus elementus`})}.`,dragStartedKeyboard:`Pradėta vilkti. Paspauskite „Tab“, kad pereitumėte į tiesioginę paskirties vietą, tada paspauskite „Enter“, kad numestumėte, arba „Escape“, kad atšauktumėte.`,dragStartedTouch:`Pradėta vilkti. Eikite į tiesioginę paskirties vietą, tada palieskite dukart, kad numestumėte.`,dragStartedVirtual:`Pradėta vilkti. Eikite į tiesioginę paskirties vietą ir spustelėkite arba paspauskite „Enter“, kad numestumėte.`,dropCanceled:`Numetimas atšauktas.`,dropComplete:"Numesta.",dropDescriptionKeyboard:`Paspauskite „Enter“, kad numestumėte. Paspauskite „Escape“, kad atšauktumėte vilkimą.`,dropDescriptionTouch:`Palieskite dukart, kad numestumėte.`,dropDescriptionVirtual:`Spustelėkite, kad numestumėte.`,dropIndicator:"numetimo indikatorius",dropOnItem:a=>`Numesti ant ${a.itemText}`,dropOnRoot:"Numesti ant",endDragKeyboard:`Velkama. Paspauskite „Enter“, kad atšauktumėte vilkimą.`,endDragTouch:`Velkama. Spustelėkite dukart, kad atšauktumėte vilkimą.`,endDragVirtual:`Velkama. Spustelėkite, kad atšauktumėte vilkimą.`,insertAfter:a=>`Įterpti po ${a.itemText}`,insertBefore:a=>`Įterpti prieš ${a.itemText}`,insertBetween:a=>`Įterpti tarp ${a.beforeItemText} ir ${a.afterItemText}`},"@react-aria/grid":{deselectedItem:a=>`${a.item} nepasirinkta.`,longPressToSelect:`Norėdami įjungti pasirinkimo režimą, paspauskite ir palaikykite.`,select:"Pasirinkti",selectedAll:"Pasirinkti visi elementai.",selectedCount:(a,e)=>`${e.plural(a.count,{"=0":`Nepasirinktas nė vienas elementas`,one:()=>`Pasirinktas ${e.number(a.count)} elementas`,other:()=>`Pasirinkta elementų: ${e.number(a.count)}`})}.`,selectedItem:a=>`Pasirinkta: ${a.item}.`},"@react-aria/gridlist":{hasActionAnnouncement:`eilutėje yra veiksmas`,hasLinkAnnouncement:a=>`eilutėje yra nuoroda: ${a.link}`},"@react-aria/menu":{longPressMessage:`Norėdami atidaryti meniu, nuspaudę palaikykite arba paspauskite „Alt + ArrowDown“.`},"@react-aria/numberfield":{decrease:a=>`Sumažinti ${a.fieldLabel}`,increase:a=>`Padidinti ${a.fieldLabel}`,numberField:"Numerio laukas"},"@react-aria/overlays":{dismiss:"Atmesti"},"@react-aria/pagination":{next:"Paskesnis",previous:"Ankstesnis"},"@react-aria/searchfield":{"Clear search":`Išvalyti iešką`},"@react-aria/spinbutton":{Empty:`Tuščias`},"@react-aria/steplist":{steplist:`Žingsnių sąrašas`},"@react-aria/table":{ascending:`didėjančia tvarka`,ascendingSort:a=>`surikiuota pagal stulpelį ${a.columnName} didėjančia tvarka`,columnSize:a=>`${a.value} piks.`,descending:`mažėjančia tvarka`,descendingSort:a=>`surikiuota pagal stulpelį ${a.columnName} mažėjančia tvarka`,resizerDescription:`Paspauskite „Enter“, kad pradėtumėte keisti dydį`,select:"Pasirinkti",selectAll:`Pasirinkti viską`,sortable:"rikiuojamas stulpelis"},"@react-aria/tag":{removeButtonLabel:`Pašalinti`,removeDescription:`Norėdami pašalinti žymą, paspauskite „Delete“ klavišą.`},"@react-aria/toast":{close:`Uždaryti`,notifications:`Pranešimai`},"@react-aria/tree":{collapse:"Sutraukti",expand:`Išskleisti`},"@react-stately/color":{alpha:"Alfa",black:"juoda",blue:`Mėlyna`,"blue purple":`melsvai violetinė`,brightness:`Ryškumas`,brown:"ruda","brown yellow":"rusvai geltona",colorName:a=>`${a.lightness} ${a.chroma} ${a.hue}`,cyan:`žalsvai mėlyna`,"cyan blue":`žalsvai mėlyna`,dark:"tamsi",gray:"pilka",grayish:`pilkšva`,green:`Žalia`,"green cyan":`žalsvai mėlyna`,hue:"Atspalvis",light:`šviesi`,lightness:`Šviesumas`,magenta:"rausvai raudona","magenta pink":`purpurinė`,orange:`oranžinė`,"orange yellow":`oranžinio atspalvio geltona`,pale:`blyški`,pink:`rožinė`,"pink red":`rožinė raudona`,purple:`violetinė`,"purple magenta":`purpurinė rausvai raudona`,red:"Raudona","red orange":`rausvai oranžinė`,saturation:`Įsotinimas`,transparentColorName:a=>`${a.lightness} ${a.chroma} ${a.hue}, ${a.percentTransparent} skaidri`,"very dark":"labai tamsi","very light":`labai šviesi`,vibrant:`ryški`,white:"balta",yellow:"geltona","yellow green":`gelsvai žalia`},"@react-stately/datepicker":{rangeOverflow:a=>`Reikšmė turi būti ${a.maxValue} arba ankstesnė.`,rangeReversed:`Pradžios data turi būti ankstesnė nei pabaigos data.`,rangeUnderflow:a=>`Reikšmė turi būti ${a.minValue} arba naujesnė.`,unavailableDate:"Pasirinkta data nepasiekiama."},"react-aria-components":{colorSwatchPicker:`Spalvų pavyzdžiai`,dropzoneLabel:`„DropZone“`,selectPlaceholder:`Pasirinkite elementą`,tableResizer:`Dydžio keitiklis`}};