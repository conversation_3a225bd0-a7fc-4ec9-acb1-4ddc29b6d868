"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.BaseButton = void 0;
const tslib_1 = require("tslib");
const classnames_1 = tslib_1.__importDefault(require("classnames"));
const radix_ui_1 = require("radix-ui");
const React = tslib_1.__importStar(require("react"));
const base_button_props_1 = require("./base-button.props");
const map_prop_values_1 = require("../../helpers/map-prop-values");
const spinner_1 = require("../spinner");
const visually_hidden_1 = require("../visually-hidden");
const BaseButton = (props) => {
    const { children, loading, disabled = props.loading, className, asChild = false, size = base_button_props_1.baseButtonPropDefs.size.default, variant = base_button_props_1.baseButtonPropDefs.variant.default, color = base_button_props_1.baseButtonPropDefs.color.default, highContrast = base_button_props_1.baseButtonPropDefs.highContrast.default, ...baseButtonProps } = props;
    const Comp = asChild ? radix_ui_1.Slot.Root : 'button';
    return (React.createElement(Comp, { "data-accent-color": color || (variant === 'surface' ? 'gray' : color), ...baseButtonProps, className: (0, classnames_1.default)('fui-reset', 'fui-BaseButton', className, `fui-r-size-${size}`, `fui-variant-${variant}`, {
            'fui-high-contrast': highContrast,
        }), "aria-busy": loading || undefined, "data-disabled": disabled || undefined, disabled: disabled }, props.loading ? (React.createElement(React.Fragment, null,
        React.createElement("span", { style: { display: 'contents', visibility: 'hidden' }, "aria-hidden": true }, children),
        React.createElement(visually_hidden_1.VisuallyHidden.Root, null, children),
        React.createElement("span", { style: {
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                inset: '0',
            } },
            React.createElement(spinner_1.Spinner, { size: (0, map_prop_values_1.mapButtonSizeToSpinnerSize)(size) })))) : (children)));
};
exports.BaseButton = BaseButton;
BaseButton.displayName = 'BaseButton';
//# sourceMappingURL=base-button.js.map