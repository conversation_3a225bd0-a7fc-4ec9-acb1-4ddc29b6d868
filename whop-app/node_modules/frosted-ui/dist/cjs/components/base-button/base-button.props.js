"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.baseButtonPropDefs = void 0;
const helpers_1 = require("../../helpers");
const sizes = ['1', '2', '3', '4'];
const variants = ['classic', 'solid', 'soft', 'surface', 'ghost'];
const baseButtonPropDefs = {
    size: { type: 'enum', values: sizes, default: '2' },
    variant: { type: 'enum', values: variants, default: 'surface' },
    color: helpers_1.colorProp,
    highContrast: helpers_1.highContrastProp,
};
exports.baseButtonPropDefs = baseButtonPropDefs;
//# sourceMappingURL=base-button.props.js.map