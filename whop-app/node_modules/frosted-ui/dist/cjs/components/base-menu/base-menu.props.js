"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.baseMenuItemPropDefs = exports.baseMenuContentPropDefs = exports.baseMenuCheckboxItemPropDefs = void 0;
const helpers_1 = require("../../helpers");
const contentSizes = ['1', '2', '3'];
const variants = ['solid', 'translucent'];
const baseMenuContentPropDefs = {
    size: { type: 'enum', values: contentSizes, default: '2' },
    color: helpers_1.colorProp,
    variant: { type: 'enum', values: variants, default: 'translucent' },
};
exports.baseMenuContentPropDefs = baseMenuContentPropDefs;
const baseMenuItemPropDefs = {
    color: helpers_1.colorProp,
    shortcut: { type: 'string', default: undefined },
};
exports.baseMenuItemPropDefs = baseMenuItemPropDefs;
const baseMenuCheckboxItemPropDefs = {
    shortcut: { type: 'string', default: undefined },
};
exports.baseMenuCheckboxItemPropDefs = baseMenuCheckboxItemPropDefs;
//# sourceMappingURL=base-menu.props.js.map