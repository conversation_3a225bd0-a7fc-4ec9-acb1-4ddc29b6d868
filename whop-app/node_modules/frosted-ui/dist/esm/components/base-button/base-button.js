import classNames from 'classnames';
import { Slot } from 'radix-ui';
import * as React from 'react';
import { baseButtonPropDefs } from './base-button.props';
import { mapButtonSizeToSpinnerSize } from '../../helpers/map-prop-values';
import { Spinner } from '../spinner';
import { VisuallyHidden } from '../visually-hidden';
const BaseButton = (props) => {
    const { children, loading, disabled = props.loading, className, asChild = false, size = baseButtonPropDefs.size.default, variant = baseButtonPropDefs.variant.default, color = baseButtonPropDefs.color.default, highContrast = baseButtonPropDefs.highContrast.default, ...baseButtonProps } = props;
    const Comp = asChild ? Slot.Root : 'button';
    return (React.createElement(Comp, { "data-accent-color": color || (variant === 'surface' ? 'gray' : color), ...baseButtonProps, className: classNames('fui-reset', 'fui-BaseButton', className, `fui-r-size-${size}`, `fui-variant-${variant}`, {
            'fui-high-contrast': highContrast,
        }), "aria-busy": loading || undefined, "data-disabled": disabled || undefined, disabled: disabled }, props.loading ? (React.createElement(React.Fragment, null,
        React.createElement("span", { style: { display: 'contents', visibility: 'hidden' }, "aria-hidden": true }, children),
        React.createElement(VisuallyHidden.Root, null, children),
        React.createElement("span", { style: {
                position: 'absolute',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                inset: '0',
            } },
            React.createElement(Spinner, { size: mapButtonSizeToSpinnerSize(size) })))) : (children)));
};
BaseButton.displayName = 'BaseButton';
export { BaseButton };
//# sourceMappingURL=base-button.js.map