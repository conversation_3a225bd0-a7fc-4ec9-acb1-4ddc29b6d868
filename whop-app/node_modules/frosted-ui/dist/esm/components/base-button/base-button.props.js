import { colorProp, highContrastProp } from '../../helpers';
const sizes = ['1', '2', '3', '4'];
const variants = ['classic', 'solid', 'soft', 'surface', 'ghost'];
const baseButtonPropDefs = {
    size: { type: 'enum', values: sizes, default: '2' },
    variant: { type: 'enum', values: variants, default: 'surface' },
    color: colorProp,
    highContrast: highContrastProp,
};
export { baseButtonPropDefs };
//# sourceMappingURL=base-button.props.js.map