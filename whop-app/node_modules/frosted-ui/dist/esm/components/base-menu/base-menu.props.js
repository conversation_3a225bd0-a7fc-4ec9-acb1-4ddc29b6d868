import { colorProp } from '../../helpers';
const contentSizes = ['1', '2', '3'];
const variants = ['solid', 'translucent'];
const baseMenuContentPropDefs = {
    size: { type: 'enum', values: contentSizes, default: '2' },
    color: colorProp,
    variant: { type: 'enum', values: variants, default: 'translucent' },
};
const baseMenuItemPropDefs = {
    color: colorProp,
    shortcut: { type: 'string', default: undefined },
};
const baseMenuCheckboxItemPropDefs = {
    shortcut: { type: 'string', default: undefined },
};
export { baseMenuCheckboxItemPropDefs, baseMenuContentPropDefs, baseMenuItemPropDefs };
//# sourceMappingURL=base-menu.props.js.map