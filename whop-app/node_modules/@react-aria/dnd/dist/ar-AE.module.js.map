{"mappings": ";AAAA,4BAAiB;IAAG,2BAA2B,CAAC,oGAAsB,CAAC;IACrE,8BAA8B,CAAC,gIAAgC,CAAC;IAChE,4BAA4B,CAAC,uJAAyB,CAAC;IACvD,wBAAwB,CAAC,kIAAsB,CAAC;IAChD,0BAA0B,CAAC,8FAAgB,CAAC;IAC5C,YAAY,CAAC,OAAS,CAAC,6BAAK,EAAE,KAAK,QAAQ,EAAE;IAC7C,qBAAqB,CAAC,MAAM,YAAc,CAAC,6BAAK,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,0DAAU,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,wEAAY,CAAC;QAAA,IAAI;IACtM,wBAAwB,CAAC,MAAM,YAAc,CAAC,6FAAqB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,CAAC,gIAAoB,CAAC;YAAE,OAAO,CAAC,gIAAoB,CAAC;QAAA,GAAG,CAAC,CAAC;IAClK,2BAA2B,CAAC,MAAM,YAAc,CAAC,8IAAkC,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,CAAC,gIAAoB,CAAC;YAAE,OAAO,CAAC,gIAAoB,CAAC;QAAA,GAAG,CAAC,CAAC;IAClL,yBAAyB,CAAC,MAAM,YAAc,CAAC,0HAAoB,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,CAAC,gIAAoB,CAAC;YAAE,OAAO,CAAC,gIAAoB,CAAC;QAAA,GAAG,CAAC,CAAC;IAClK,uBAAuB,CAAC,idAA6F,CAAC;IACtH,oBAAoB,CAAC,6UAAyD,CAAC;IAC/E,sBAAsB,CAAC,2VAAiE,CAAC;IACzF,gBAAgB,CAAC,qGAAiB,CAAC;IACnC,gBAAgB,CAAC,sFAAc,CAAC;IAChC,2BAA2B,CAAC,yMAA6C,CAAC;IAC1E,wBAAwB,CAAC,mHAAmB,CAAC;IAC7C,0BAA0B,CAAC,+EAAa,CAAC;IACzC,iBAAiB,CAAC,8EAAY,CAAC;IAC/B,cAAc,CAAC,OAAS,CAAC,oCAAM,EAAE,KAAK,QAAQ,EAAE;IAChD,cAAc,CAAC,iDAAO,CAAC;IACvB,mBAAmB,CAAC,uJAA+B,CAAC;IACpD,gBAAgB,CAAC,qLAA+B,CAAC;IACjD,kBAAkB,CAAC,iJAAyB,CAAC;IAC7C,eAAe,CAAC,OAAS,CAAC,mDAAS,EAAE,KAAK,QAAQ,EAAE;IACpD,gBAAgB,CAAC,OAAS,CAAC,mDAAS,EAAE,KAAK,QAAQ,EAAE;IACrD,iBAAiB,CAAC,OAAS,CAAC,mDAAS,EAAE,KAAK,cAAc,CAAC,SAAG,EAAE,KAAK,aAAa,EAAE;AACtF", "sources": ["packages/@react-aria/dnd/intl/ar-AE.json"], "sourcesContent": ["{\n  \"dragDescriptionKeyboard\": \"اضغط Enter لبدء السحب.\",\n  \"dragDescriptionKeyboardAlt\": \"اضغط على Alt + Enter لبدء السحب.\",\n  \"dragDescriptionLongPress\": \"اضغط باستمرار لبدء السحب.\",\n  \"dragDescriptionTouch\": \"اضغط مرتين لبدء السحب.\",\n  \"dragDescriptionVirtual\": \"انقر لبدء السحب.\",\n  \"dragItem\": \"اسحب {itemText}\",\n  \"dragSelectedItems\": \"اسحب {count, plural, one {# عنصر محدد} other {# عناصر محددة}}\",\n  \"dragSelectedKeyboard\": \"اضغط على Enter للسحب {count, plural, one {عدد العناصر المختارة} other {عدد العناصر المختارة}}.\",\n  \"dragSelectedKeyboardAlt\": \"اضغط على مفتاحي Alt + Enter للسحب {count, plural, one {عدد العناصر المختارة} other {عدد العناصر المختارة}}.\",\n  \"dragSelectedLongPress\": \"اضغط باستمرار للسحب {count, plural, one {عدد العناصر المختارة} other {عدد العناصر المختارة}}.\",\n  \"dragStartedKeyboard\": \"بدأ السحب. اضغط Tab للانتقال إلى موضع الإفلات، ثم اضغط Enter للإفلات، أو اضغط Escape للإلغاء.\",\n  \"dragStartedTouch\": \"بدأ السحب. انتقل إلى موضع الإفلات، ثم اضغط مرتين للإفلات.\",\n  \"dragStartedVirtual\": \"بدأ السحب. انتقل إلى مكان الإفلات، ثم انقر أو اضغط Enter للإفلات.\",\n  \"dropCanceled\": \"تم إلغاء الإفلات.\",\n  \"dropComplete\": \"اكتمل الإفلات.\",\n  \"dropDescriptionKeyboard\": \"اضغط Enter للإفلات. اضغط Escape لإلغاء السحب.\",\n  \"dropDescriptionTouch\": \"اضغط مرتين للإفلات.\",\n  \"dropDescriptionVirtual\": \"انقر للإفلات.\",\n  \"dropIndicator\": \"مؤشر الإفلات\",\n  \"dropOnItem\": \"إفلات {itemText}\",\n  \"dropOnRoot\": \"الإفلات\",\n  \"endDragKeyboard\": \"السحب. اضغط Enter لإلغاء السحب.\",\n  \"endDragTouch\": \"السحب. اضغط مرتين لإلغاء السحب.\",\n  \"endDragVirtual\": \"السحب. انقر لإلغاء السحب.\",\n  \"insertAfter\": \"أدخل بعد {itemText}\",\n  \"insertBefore\": \"أدخل قبل {itemText}\",\n  \"insertBetween\": \"أدخل بين {beforeItemText} و {afterItemText}\"\n}\n"], "names": [], "version": 3, "file": "ar-AE.module.js.map"}