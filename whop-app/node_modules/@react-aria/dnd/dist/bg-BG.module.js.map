{"mappings": ";AAAA,4BAAiB;IAAG,2BAA2B,CAAC,6PAA+C,CAAC;IAC9F,8BAA8B,CAAC,mPAAmD,CAAC;IACnF,4BAA4B,CAAC,mUAAqD,CAAC;IACnF,wBAAwB,CAAC,uSAAiD,CAAC;IAC3E,0BAA0B,CAAC,gOAAsC,CAAC;IAClE,YAAY,CAAC,OAAS,CAAC,2CAAO,EAAE,KAAK,QAAQ,EAAE;IAC/C,qBAAqB,CAAC,MAAM,YAAc,CAAC,2CAAO,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,2GAAiB,CAAC;QAAA,IAAI;IAClN,wBAAwB,CAAC,MAAM,YAAc,CAAC,8JAAgC,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,2GAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IAC/O,2BAA2B,CAAC,MAAM,YAAc,CAAC,0KAAsC,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,2GAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IACxP,yBAAyB,CAAC,MAAM,YAAc,CAAC,oPAAwC,EAAE,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,6FAAe,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,2GAAiB,CAAC;QAAA,GAAG,CAAC,CAAC;IACxP,uBAAuB,CAAC,wuBAA0I,CAAC;IACnK,oBAAoB,CAAC,wgBAAwF,CAAC;IAC9G,sBAAsB,CAAC,6hBAA+F,CAAC;IACvH,gBAAgB,CAAC,iIAAqB,CAAC;IACvC,gBAAgB,CAAC,wIAAsB,CAAC;IACxC,2BAA2B,CAAC,+XAAyE,CAAC;IACtG,wBAAwB,CAAC,iMAA+B,CAAC;IACzD,0BAA0B,CAAC,0HAAoB,CAAC;IAChD,iBAAiB,CAAC,gIAAoB,CAAC;IACvC,cAAc,CAAC,OAAS,CAAC,wEAAY,EAAE,KAAK,QAAQ,EAAE;IACtD,cAAc,CAAC,uEAAW,CAAC;IAC3B,mBAAmB,CAAC,gSAAoD,CAAC;IACzE,gBAAgB,CAAC,0UAAsD,CAAC;IACxE,kBAAkB,CAAC,6KAA6B,CAAC;IACjD,eAAe,CAAC,OAAS,CAAC,wEAAY,EAAE,KAAK,QAAQ,EAAE;IACvD,gBAAgB,CAAC,OAAS,CAAC,+EAAa,EAAE,KAAK,QAAQ,EAAE;IACzD,iBAAiB,CAAC,OAAS,CAAC,+EAAa,EAAE,KAAK,cAAc,CAAC,SAAG,EAAE,KAAK,aAAa,EAAE;AAC1F", "sources": ["packages/@react-aria/dnd/intl/bg-BG.json"], "sourcesContent": ["{\n  \"dragDescriptionKeyboard\": \"Натиснете „Enter“, за да започнете да плъзгате.\",\n  \"dragDescriptionKeyboardAlt\": \"Натиснете Alt + Enter, за да започнете да плъзгате.\",\n  \"dragDescriptionLongPress\": \"Натиснете продължително, за да започнете да плъзгате.\",\n  \"dragDescriptionTouch\": \"Натиснете двукратно, за да започнете да плъзгате.\",\n  \"dragDescriptionVirtual\": \"Щракнете, за да започнете да плъзгате.\",\n  \"dragItem\": \"Плъзни {itemText}\",\n  \"dragSelectedItems\": \"Плъзни {count, plural, one {# избран елемент} other {# избрани елемента}}\",\n  \"dragSelectedKeyboard\": \"Натиснете Enter, за да плъзнете {count, plural, one {# избран елемент} other {# избрани елементи}}.\",\n  \"dragSelectedKeyboardAlt\": \"Натиснете Alt и Enter, за да плъзнете {count, plural, one {# избран елемент} other {# избрани елементи}}.\",\n  \"dragSelectedLongPress\": \"Натиснете продължително, за да плъзнете {count, plural, one {# избран елемент} other {# избрани елементи}}.\",\n  \"dragStartedKeyboard\": \"Започна плъзгане. Натиснете „Tab“, за да се придвижите до целта, след което натиснете „Enter“ за пускане или натиснете „Escape“ за отмяна.\",\n  \"dragStartedTouch\": \"Започна плъзгане. Придвижете се до целта, след което натиснете двукратно, за да пуснете.\",\n  \"dragStartedVirtual\": \"Започна плъзгане. Придвижете се до целта, след което щракнете или натиснете „Enter“ за пускане.\",\n  \"dropCanceled\": \"Пускането е отменено.\",\n  \"dropComplete\": \"Пускането е завършено.\",\n  \"dropDescriptionKeyboard\": \"Натиснете „Enter“ за пускане. Натиснете „Escape“ за отмяна на плъзгането.\",\n  \"dropDescriptionTouch\": \"Натиснете двукратно за пускане.\",\n  \"dropDescriptionVirtual\": \"Щракнете за пускане.\",\n  \"dropIndicator\": \"индикатор за пускане\",\n  \"dropOnItem\": \"Пусни върху {itemText}\",\n  \"dropOnRoot\": \"Пусни върху\",\n  \"endDragKeyboard\": \"Плъзгане. Натиснете „Enter“ за отмяна на плъзгането.\",\n  \"endDragTouch\": \"Плъзгане. Натиснете двукратно за отмяна на плъзгането.\",\n  \"endDragVirtual\": \"Плъзгане. Щракнете за отмяна.\",\n  \"insertAfter\": \"Вмъкни след {itemText}\",\n  \"insertBefore\": \"Вмъкни преди {itemText}\",\n  \"insertBetween\": \"Вмъкни между {beforeItemText} и {afterItemText}\"\n}\n"], "names": [], "version": 3, "file": "bg-BG.module.js.map"}