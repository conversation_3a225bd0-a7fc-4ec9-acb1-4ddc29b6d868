{"mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;AA2BM,SAAS,0CAAgB,KAAuD,EAAE,KAAyC;IAChI,IAAI,kBAAkB,CAAA,GAAA,kCAA0B,EAAE,CAAA,GAAA,oDAAW,GAAG;IAChE,IAAI,WAAW,CAAA,GAAA,qBAAa,EAAE;IAE9B,IAAI,QAAQ,CAAA,GAAA,wCAAyB,EAAE,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM,YAAY,CAAC,GAAG,EAAE,MAAM,QAAQ,EAAE;IACzG,IAAI,0BAA0B,CAAA,GAAA,wCAAyB,EAAE,MAAM,YAAY,CAAC,KAAK,EAAE,MAAM,YAAY,CAAC,GAAG,EAAE,MAAM,QAAQ,EAAE;IAE3H,+CAA+C;IAC/C,CAAA,GAAA,sBAAc,EAAE;QACd,iDAAiD;QACjD,IAAI,CAAC,MAAM,SAAS,EAClB,CAAA,GAAA,eAAO,EAAE;IAEb,GAAG;QAAC;KAAwB;IAE5B,2CAA2C;IAC3C,IAAI,0BAA0B,CAAA,GAAA,yCAAyB,EAAE;IACzD,CAAA,GAAA,sBAAc,EAAE;QACd,IAAI,yBACF,CAAA,GAAA,eAAO,EAAE,yBAAyB,UAAU;IAE9C,yGAAyG;IAC3G,GAAG;QAAC;KAAwB;IAE5B,IAAI,iBAAiB,CAAA,GAAA,gBAAQ,EAAE;QAAC,QAAQ,MAAM,YAAY;QAAG,MAAM,SAAS;QAAE,MAAM,eAAe;KAAC;IAEpG,6CAA6C;IAC7C,CAAA,GAAA,yCAAO,EAAE,GAAG,CAAC,OAAO;QAClB,WAAW,KAAK,CAAC,aAAa;QAC9B,gBAAgB,KAAK,CAAC,kBAAkB;wBACxC;iCACA;IACF;IAEA,2GAA2G;IAC3G,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,eAAO,EAAE;IAC7C,IAAI,eAAe,MAAM,UAAU,IAAI,MAAM,yBAAyB;IACtE,IAAI,gBAAgB,aAAa;QAC/B,eAAe;QACf,MAAM,UAAU,CAAC;IACnB;IAEA,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,eAAO,EAAE;IACrD,IAAI,mBAAmB,MAAM,UAAU,IAAI,MAAM,6BAA6B;IAC9E,IAAI,oBAAoB,iBAAiB;QACvC,mBAAmB;QACnB,MAAM,UAAU,CAAC;IACnB;IAEA,IAAI,aAAa,CAAA,GAAA,gBAAQ,EAAE;QACzB,IAAI,KAAK,CAAC,KAAK;QACf,cAAc;YAAC,KAAK,CAAC,aAAa;YAAE;SAAwB,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC;QAClF,mBAAmB,KAAK,CAAC,kBAAkB;IAC7C;IAEA,OAAO;QACL,eAAe,CAAA,GAAA,iBAAS,EAAE,UAAU,YAAY;YAC9C,MAAM;YACN,gBAAgB,KAAK,CAAC,eAAe,IAAI;YACzC,oBAAoB,KAAK,CAAC,mBAAmB,IAAI;QACnD;QACA,iBAAiB;YACf,SAAS,IAAM,MAAM,aAAa;YAClC,cAAc,gBAAgB,MAAM,CAAC;YACrC,YAAY;YACZ,eAAe;QACjB;QACA,iBAAiB;YACf,SAAS,IAAM,MAAM,iBAAiB;YACtC,cAAc,gBAAgB,MAAM,CAAC;YACrC,YAAY;YACZ,eAAe;QACjB;QACA,mBAAmB;YACjB,IAAI;QACN;eACA;IACF;AACF", "sources": ["packages/@react-aria/calendar/src/useCalendarBase.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {announce} from '@react-aria/live-announcer';\nimport {AriaButtonProps} from '@react-types/button';\nimport {AriaLabelingProps, DOMAttributes, DOMProps} from '@react-types/shared';\nimport {CalendarPropsBase} from '@react-types/calendar';\nimport {CalendarState, RangeCalendarState} from '@react-stately/calendar';\nimport {filterDOMProps, mergeProps, useLabels, useSlotId, useUpdateEffect} from '@react-aria/utils';\nimport {hookData, useSelectedDateDescription, useVisibleRangeDescription} from './utils';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useState} from 'react';\n\nexport interface CalendarAria {\n  /** Props for the calendar grouping element. */\n  calendarProps: DOMAttributes,\n  /** Props for the next button. */\n  nextButtonProps: AriaButtonProps,\n  /** Props for the previous button. */\n  prevButtonProps: AriaButtonProps,\n  /** Props for the error message element, if any. */\n  errorMessageProps: DOMAttributes,\n  /** A description of the visible date range, for use in the calendar title. */\n  title: string\n}\n\nexport function useCalendarBase(props: CalendarPropsBase & DOMProps & AriaLabelingProps, state: CalendarState | RangeCalendarState): CalendarAria {\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/calendar');\n  let domProps = filterDOMProps(props);\n\n  let title = useVisibleRangeDescription(state.visibleRange.start, state.visibleRange.end, state.timeZone, false);\n  let visibleRangeDescription = useVisibleRangeDescription(state.visibleRange.start, state.visibleRange.end, state.timeZone, true);\n\n  // Announce when the visible date range changes\n  useUpdateEffect(() => {\n    // only when pressing the Previous or Next button\n    if (!state.isFocused) {\n      announce(visibleRangeDescription);\n    }\n  }, [visibleRangeDescription]);\n\n  // Announce when the selected value changes\n  let selectedDateDescription = useSelectedDateDescription(state);\n  useUpdateEffect(() => {\n    if (selectedDateDescription) {\n      announce(selectedDateDescription, 'polite', 4000);\n    }\n    // handle an update to the caption that describes the currently selected range, to announce the new value\n  }, [selectedDateDescription]);\n\n  let errorMessageId = useSlotId([Boolean(props.errorMessage), props.isInvalid, props.validationState]);\n\n  // Pass the label to the child grid elements.\n  hookData.set(state, {\n    ariaLabel: props['aria-label'],\n    ariaLabelledBy: props['aria-labelledby'],\n    errorMessageId,\n    selectedDateDescription\n  });\n\n  // If the next or previous buttons become disabled while they are focused, move focus to the calendar body.\n  let [nextFocused, setNextFocused] = useState(false);\n  let nextDisabled = props.isDisabled || state.isNextVisibleRangeInvalid();\n  if (nextDisabled && nextFocused) {\n    setNextFocused(false);\n    state.setFocused(true);\n  }\n\n  let [previousFocused, setPreviousFocused] = useState(false);\n  let previousDisabled = props.isDisabled || state.isPreviousVisibleRangeInvalid();\n  if (previousDisabled && previousFocused) {\n    setPreviousFocused(false);\n    state.setFocused(true);\n  }\n\n  let labelProps = useLabels({\n    id: props['id'],\n    'aria-label': [props['aria-label'], visibleRangeDescription].filter(Boolean).join(', '),\n    'aria-labelledby': props['aria-labelledby']\n  });\n\n  return {\n    calendarProps: mergeProps(domProps, labelProps, {\n      role: 'application',\n      'aria-details': props['aria-details'] || undefined,\n      'aria-describedby': props['aria-describedby'] || undefined\n    }),\n    nextButtonProps: {\n      onPress: () => state.focusNextPage(),\n      'aria-label': stringFormatter.format('next'),\n      isDisabled: nextDisabled,\n      onFocusChange: setNextFocused\n    },\n    prevButtonProps: {\n      onPress: () => state.focusPreviousPage(),\n      'aria-label': stringFormatter.format('previous'),\n      isDisabled: previousDisabled,\n      onFocusChange: setPreviousFocused\n    },\n    errorMessageProps: {\n      id: errorMessageId\n    },\n    title\n  };\n}\n"], "names": [], "version": 3, "file": "useCalendarBase.module.js.map"}