{"mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC;;;;;;;AAqEM,SAAS,0CAAgB,KAA4B,EAAE,KAAyC,EAAE,GAAkC;IACzI,IAAI,QAAC,IAAI,cAAE,UAAU,EAAC,GAAG;IACzB,IAAI,kBAAC,cAAc,2BAAE,uBAAuB,EAAC,GAAG,CAAA,GAAA,kCAAO,EAAE,GAAG,CAAC;IAC7D,IAAI,kBAAkB,CAAA,GAAA,gDAA0B,EAAE,CAAA,GAAA,mDAAW,GAAG;IAChE,IAAI,gBAAgB,CAAA,GAAA,qCAAe,EAAE;QACnC,SAAS;QACT,KAAK;QACL,OAAO;QACP,MAAM;QACN,KAAK,CAAA,GAAA,sCAAW,EAAE;QAClB,UAAU,MAAM,QAAQ;IAC1B;IACA,IAAI,aAAa,MAAM,UAAU,CAAC;IAClC,IAAI,YAAY,MAAM,aAAa,CAAC,SAAS,CAAC,MAAM,cAAc;IAClE,aAAa,cAAc,MAAM,cAAc,CAAC;IAChD,IAAI,gBAAgB,MAAM,iBAAiB,CAAC;IAC5C,IAAI,eAAe,CAAC,cAAc,CAAC;IACnC,IAAI,YAAY,MAAM,cAAc,IAAI,QACtC,sBAAsB,QAClB,CAAC,MAAM,UAAU,IAAI,MAAM,gBAAgB,IAAI,KAAK,OAAO,CAAC,MAAM,gBAAgB,CAAC,KAAK,KAAK,KAAK,KAAK,OAAO,CAAC,MAAM,gBAAgB,CAAC,GAAG,KAAK,IAC9I,MAAM,KAAK,IAAI,CAAA,GAAA,sCAAQ,EAAE,MAAM,KAAK,EAAE;IAG5C,IAAI,WACF,aAAa;IAGf,0FAA0F;IAC1F,uDAAuD;IACvD,OAAO,CAAA,GAAA,iCAAU,EAAgB,MAAM,CAAA,GAAA,uCAAS;IAChD,IAAI,aAAa,CAAA,GAAA,oBAAM,EAAE,IAAM,KAAK,MAAM,CAAC,MAAM,QAAQ,GAAG;QAAC;QAAM,MAAM,QAAQ;KAAC;IAElF,+EAA+E;IAC/E,IAAI,cAAc,CAAA,GAAA,oCAAM,EAAE,MAAM,MAAM,QAAQ;IAC9C,IAAI,QAAQ,CAAA,GAAA,oBAAM,EAAE;QAClB,IAAI,QAAQ;QAEZ,4EAA4E;QAC5E,uCAAuC;QACvC,IACE,sBAAsB,SACtB,MAAM,KAAK,IACX,CAAC,MAAM,UAAU,IAChB,CAAA,CAAA,GAAA,sCAAQ,EAAE,MAAM,MAAM,KAAK,CAAC,KAAK,KAAK,CAAA,GAAA,sCAAQ,EAAE,MAAM,MAAM,KAAK,CAAC,GAAG,CAAA,GAEtE,QAAQ,0BAA0B;QAGpC,SAAS,cAAc,MAAM,CAAC;QAC9B,IAAI,aACF,wEAAwE;QACxE,QAAQ,gBAAgB,MAAM,CAAC,aAAa,sBAAsB,aAAa;YAC7E,MAAM;QACR;aACK,IAAI,YACT,qCAAqC;QACrC,QAAQ,gBAAgB,MAAM,CAAC,gBAAgB;YAC7C,MAAM;QACR;QAGF,IAAI,MAAM,QAAQ,IAAI,CAAA,GAAA,sCAAQ,EAAE,MAAM,MAAM,QAAQ,GAClD,SAAS,OAAO,gBAAgB,MAAM,CAAC;aAClC,IAAI,MAAM,QAAQ,IAAI,CAAA,GAAA,sCAAQ,EAAE,MAAM,MAAM,QAAQ,GACzD,SAAS,OAAO,gBAAgB,MAAM,CAAC;QAGzC,OAAO;IACT,GAAG;QAAC;QAAe;QAAY;QAAiB;QAAY;QAAa;QAAM;QAAO;KAAwB;IAE9G,4EAA4E;IAC5E,mEAAmE;IACnE,IAAI,uBAAuB;IAC3B,IAAI,gBAAgB,SAAS,aAAa,CAAC,MAAM,UAAU,IAAI;QAC7D,iEAAiE;QACjE,IAAI,MAAM,UAAU,EAClB,uBAAuB,gBAAgB,MAAM,CAAC;aAG9C,uBAAuB,gBAAgB,MAAM,CAAC;;IAIlD,IAAI,mBAAmB,CAAA,GAAA,oCAAa,EAAE;IAEtC,IAAI,kBAAkB,CAAA,GAAA,mBAAK,EAAE;IAC7B,IAAI,yBAAyB,CAAA,GAAA,mBAAK,EAAE;IACpC,IAAI,oBAAoB,CAAA,GAAA,mBAAK,EAA6C;IAC1E,IAAI,cAAC,UAAU,aAAE,SAAS,EAAC,GAAG,CAAA,GAAA,qCAAO,EAAE;QACrC,mFAAmF;QACnF,oFAAoF;QACpF,2BAA2B,gBAAgB,SAAS,CAAC,CAAC,MAAM,UAAU;QACtE,qBAAqB;QACrB,YAAY,CAAC,gBAAgB,MAAM,UAAU;QAC7C,cAAa,CAAC;YACZ,IAAI,MAAM,UAAU,EAAE;gBACpB,MAAM,cAAc,CAAC;gBACrB;YACF;YAEA,IAAI,sBAAsB,SAAS,CAAC,MAAM,UAAU,IAAK,CAAA,EAAE,WAAW,KAAK,WAAW,EAAE,WAAW,KAAK,OAAM,GAAI;gBAChH,+DAA+D;gBAC/D,wCAAwC;gBACxC,wFAAwF;gBACxF,6FAA6F;gBAC7F,IAAI,MAAM,gBAAgB,IAAI,CAAC,WAAW;oBACxC,IAAI,CAAA,GAAA,sCAAQ,EAAE,MAAM,MAAM,gBAAgB,CAAC,KAAK,GAAG;wBACjD,MAAM,aAAa,CAAC,MAAM,gBAAgB,CAAC,GAAG;wBAC9C,MAAM,cAAc,CAAC;wBACrB,MAAM,WAAW,CAAC;wBAClB,uBAAuB,OAAO,GAAG;wBACjC;oBACF,OAAO,IAAI,CAAA,GAAA,sCAAQ,EAAE,MAAM,MAAM,gBAAgB,CAAC,GAAG,GAAG;wBACtD,MAAM,aAAa,CAAC,MAAM,gBAAgB,CAAC,KAAK;wBAChD,MAAM,cAAc,CAAC;wBACrB,MAAM,WAAW,CAAC;wBAClB,uBAAuB,OAAO,GAAG;wBACjC;oBACF;gBACF;gBAEA,IAAI,gBAAgB;oBAClB,MAAM,WAAW,CAAC;oBAClB,kBAAkB,OAAO,GAAG;oBAE5B,MAAM,UAAU,CAAC;oBACjB,MAAM,cAAc,CAAC;oBACrB,gBAAgB,OAAO,GAAG;gBAC5B;gBAEA,2EAA2E;gBAC3E,4EAA4E;gBAC5E,IAAI,EAAE,WAAW,KAAK,SACpB,kBAAkB,OAAO,GAAG,WAAW,eAAe;qBAEtD;YAEJ;QACF;QACA;YACE,uBAAuB,OAAO,GAAG;YACjC,gBAAgB,OAAO,GAAG;YAC1B,aAAa,kBAAkB,OAAO;YACtC,kBAAkB,OAAO,GAAG;QAC9B;QACA;YACE,sDAAsD;YACtD,IAAI,CAAE,CAAA,gBAAgB,KAAI,KAAM,CAAC,MAAM,UAAU,EAAE;gBACjD,MAAM,UAAU,CAAC;gBACjB,MAAM,cAAc,CAAC;YACvB;QACF;QACA,WAAU,CAAC;YACT,IAAI,MAAM,UAAU,EAClB;YAGF,qEAAqE;YACrE,8EAA8E;YAC9E,kCAAkC;YAClC,IAAI,gBAAgB,SAAS,kBAAkB,OAAO,EAAE;gBACtD,MAAM,UAAU,CAAC;gBACjB,MAAM,cAAc,CAAC;YACvB;YAEA,IAAI,gBAAgB,OAAO;gBACzB,IAAI,uBAAuB,OAAO,EAChC,uEAAuE;gBACvE,uEAAuE;gBACvE,6BAA6B;gBAC7B,MAAM,aAAa,CAAC;qBACf,IAAI,MAAM,UAAU,IAAI,CAAC,gBAAgB,OAAO,EAAE;oBACvD,wEAAwE;oBACxE,MAAM,UAAU,CAAC;oBACjB,MAAM,cAAc,CAAC;gBACvB,OAAO,IAAI,EAAE,WAAW,KAAK,cAAc,CAAC,MAAM,UAAU,EAAE;oBAC5D,+EAA+E;oBAC/E,oFAAoF;oBACpF,qGAAqG;oBACrG,8EAA8E;oBAC9E,MAAM,UAAU,CAAC;oBACjB,IAAI,UAAU,KAAK,GAAG,CAAC;wBAAC,MAAM;oBAAC;oBAC/B,IAAI,MAAM,SAAS,CAAC,UAClB,UAAU,KAAK,QAAQ,CAAC;wBAAC,MAAM;oBAAC;oBAElC,IAAI,CAAC,MAAM,SAAS,CAAC,UACnB,MAAM,cAAc,CAAC;gBAEzB,OAAO,IAAI,EAAE,WAAW,KAAK,WAAW;oBACtC,qDAAqD;oBACrD,MAAM,UAAU,CAAC;oBACjB,MAAM,cAAc,CAAC;gBACvB;YACF;QACF;IACF;IAEA,IAAI,WAA+B;IACnC,IAAI,CAAC,YACH,WAAW,CAAA,GAAA,sCAAQ,EAAE,MAAM,MAAM,WAAW,IAAI,IAAI;IAGtD,sDAAsD;IACtD,CAAA,GAAA,sBAAQ,EAAE;QACR,IAAI,aAAa,IAAI,OAAO,EAAE;YAC5B,CAAA,GAAA,2CAAoB,EAAE,IAAI,OAAO;YAEjC,4DAA4D;YAC5D,2DAA2D;YAC3D,gEAAgE;YAChE,6DAA6D;YAC7D,+DAA+D;YAC/D,mEAAmE;YACnE,uDAAuD;YACvD,IAAI,CAAA,GAAA,mDAAqB,QAAQ,aAAa,SAAS,aAAa,KAAK,IAAI,OAAO,EAClF,CAAA,GAAA,wCAAiB,EAAE,IAAI,OAAO,EAAE;gBAAC,mBAAmB,CAAA,GAAA,qCAAc,EAAE,IAAI,OAAO;YAAC;QAEpF;IACF,GAAG;QAAC;QAAW;KAAI;IAEnB,IAAI,oBAAoB,CAAA,GAAA,qCAAe,EAAE;QACvC,KAAK;QACL,UAAU,MAAM,QAAQ;QACxB,UAAU,KAAK,QAAQ,CAAC,UAAU;IACpC;IAEA,IAAI,gBAAgB,CAAA,GAAA,oBAAM,EAAE,IAAM,kBAAkB,aAAa,CAAC,YAAY,IAAI,CAAC,CAAA,OAAQ,KAAK,IAAI,KAAK,OAAQ,KAAK,EAAE;QAAC;QAAmB;KAAW;IAEvJ,OAAO;QACL,WAAW;YACT,MAAM;YACN,iBAAiB,CAAC,gBAAgB;YAClC,iBAAiB,cAAc;YAC/B,gBAAgB,aAAa;QAC/B;QACA,aAAa,CAAA,GAAA,gCAAS,EAAE,YAAY;YAClC;gBACE,IAAI,CAAC,YACH,MAAM,cAAc,CAAC;YAEzB;sBACA;YACA,MAAM;YACN,iBAAiB,CAAC,gBAAgB;YAClC,cAAc;YACd,gBAAgB,aAAa;YAC7B,oBAAoB;gBAClB,YAAY,iBAAiB;gBAC7B,gBAAgB,CAAC,mBAAmB;aACrC,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;YAC/B,gBAAe,CAAC;gBACd,0EAA0E;gBAC1E,IAAI,mBAAmB,SAAU,CAAA,EAAE,WAAW,KAAK,WAAW,MAAM,UAAU,AAAD,KAAM,cACjF,MAAM,aAAa,CAAC;YAExB;YACA,eAAc,CAAC;gBACb,uDAAuD;gBACvD,wCAAwC;gBACxC,gCAAgC;gBAChC,IAAI,2BAA2B,EAAE,MAAM,EACrC,EAAE,MAAM,CAAC,qBAAqB,CAAC,EAAE,SAAS;YAE9C;YACA,eAAc,CAAC;gBACb,sCAAsC;gBACtC,EAAE,cAAc;YAClB;QACF;mBACA;mBACA;oBACA;oBACA;uBACA;QACA,uBAAuB,KAAK,OAAO,CAAC,MAAM,YAAY,CAAC,KAAK,IAAI,KAAK,KAAK,OAAO,CAAC,MAAM,YAAY,CAAC,GAAG,IAAI;mBAC5G;uBACA;IACF;AACF", "sources": ["packages/@react-aria/calendar/src/useCalendarCell.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {CalendarDate, isEqualDay, isSameDay, isToday} from '@internationalized/date';\nimport {CalendarState, RangeCalendarState} from '@react-stately/calendar';\nimport {DOMAttributes, RefObject} from '@react-types/shared';\nimport {focusWithoutScrolling, getScrollParent, mergeProps, scrollIntoViewport, useDeepMemo, useDescription} from '@react-aria/utils';\nimport {getEraFormat, hookData} from './utils';\nimport {getInteractionModality, usePress} from '@react-aria/interactions';\n// @ts-ignore\nimport intlMessages from '../intl/*.json';\nimport {useDateFormatter, useLocalizedStringFormatter} from '@react-aria/i18n';\nimport {useEffect, useMemo, useRef} from 'react';\n\nexport interface AriaCalendarCellProps {\n  /** The date that this cell represents. */\n  date: CalendarDate,\n  /**\n   * Whether the cell is disabled. By default, this is determined by the\n   * Calendar's `minValue`, `maxValue`, and `isDisabled` props.\n   */\n  isDisabled?: boolean,\n\n  /**\n   * Whether the cell is outside of the current month.\n   */\n  isOutsideMonth?: boolean\n}\n\nexport interface CalendarCellAria {\n  /** Props for the grid cell element (e.g. `<td>`). */\n  cellProps: DOMAttributes,\n  /** Props for the button element within the cell. */\n  buttonProps: DOMAttributes,\n  /** Whether the cell is currently being pressed. */\n  isPressed: boolean,\n  /** Whether the cell is selected. */\n  isSelected: boolean,\n  /** Whether the cell is focused. */\n  isFocused: boolean,\n  /**\n   * Whether the cell is disabled, according to the calendar's `minValue`, `maxValue`, and `isDisabled` props.\n   * Disabled dates are not focusable, and cannot be selected by the user. They are typically\n   * displayed with a dimmed appearance.\n   */\n  isDisabled: boolean,\n  /**\n   * Whether the cell is unavailable, according to the calendar's `isDateUnavailable` prop. Unavailable dates remain\n   * focusable, but cannot be selected by the user. They should be displayed with a visual affordance to indicate they\n   * are unavailable, such as a different color or a strikethrough.\n   *\n   * Note that because they are focusable, unavailable dates must meet a 4.5:1 color contrast ratio,\n   * [as defined by WCAG](https://www.w3.org/WAI/WCAG21/Understanding/contrast-minimum.html).\n   */\n  isUnavailable: boolean,\n  /**\n   * Whether the cell is outside the visible range of the calendar.\n   * For example, dates before the first day of a month in the same week.\n   */\n  isOutsideVisibleRange: boolean,\n  /** Whether the cell is part of an invalid selection. */\n  isInvalid: boolean,\n  /** The day number formatted according to the current locale. */\n  formattedDate: string\n}\n\n/**\n * Provides the behavior and accessibility implementation for a calendar cell component.\n * A calendar cell displays a date cell within a calendar grid which can be selected by the user.\n */\nexport function useCalendarCell(props: AriaCalendarCellProps, state: CalendarState | RangeCalendarState, ref: RefObject<HTMLElement | null>): CalendarCellAria {\n  let {date, isDisabled} = props;\n  let {errorMessageId, selectedDateDescription} = hookData.get(state)!;\n  let stringFormatter = useLocalizedStringFormatter(intlMessages, '@react-aria/calendar');\n  let dateFormatter = useDateFormatter({\n    weekday: 'long',\n    day: 'numeric',\n    month: 'long',\n    year: 'numeric',\n    era: getEraFormat(date),\n    timeZone: state.timeZone\n  });\n  let isSelected = state.isSelected(date);\n  let isFocused = state.isCellFocused(date) && !props.isOutsideMonth;\n  isDisabled = isDisabled || state.isCellDisabled(date);\n  let isUnavailable = state.isCellUnavailable(date);\n  let isSelectable = !isDisabled && !isUnavailable;\n  let isInvalid = state.isValueInvalid && Boolean(\n    'highlightedRange' in state\n      ? !state.anchorDate && state.highlightedRange && date.compare(state.highlightedRange.start) >= 0 && date.compare(state.highlightedRange.end) <= 0\n      : state.value && isSameDay(state.value, date)\n  );\n\n  if (isInvalid) {\n    isSelected = true;\n  }\n\n  // For performance, reuse the same date object as before if the new date prop is the same.\n  // This allows subsequent useMemo results to be reused.\n  date = useDeepMemo<CalendarDate>(date, isEqualDay);\n  let nativeDate = useMemo(() => date.toDate(state.timeZone), [date, state.timeZone]);\n\n  // aria-label should be localize Day of week, Month, Day and Year without Time.\n  let isDateToday = isToday(date, state.timeZone);\n  let label = useMemo(() => {\n    let label = '';\n\n    // If this is a range calendar, add a description of the full selected range\n    // to the first and last selected date.\n    if (\n      'highlightedRange' in state &&\n      state.value &&\n      !state.anchorDate &&\n      (isSameDay(date, state.value.start) || isSameDay(date, state.value.end))\n    ) {\n      label = selectedDateDescription + ', ';\n    }\n\n    label += dateFormatter.format(nativeDate);\n    if (isDateToday) {\n      // If date is today, set appropriate string depending on selected state:\n      label = stringFormatter.format(isSelected ? 'todayDateSelected' : 'todayDate', {\n        date: label\n      });\n    } else if (isSelected) {\n      // If date is selected but not today:\n      label = stringFormatter.format('dateSelected', {\n        date: label\n      });\n    }\n\n    if (state.minValue && isSameDay(date, state.minValue)) {\n      label += ', ' + stringFormatter.format('minimumDate');\n    } else if (state.maxValue && isSameDay(date, state.maxValue)) {\n      label += ', ' + stringFormatter.format('maximumDate');\n    }\n\n    return label;\n  }, [dateFormatter, nativeDate, stringFormatter, isSelected, isDateToday, date, state, selectedDateDescription]);\n\n  // When a cell is focused and this is a range calendar, add a prompt to help\n  // screenreader users know that they are in a range selection mode.\n  let rangeSelectionPrompt = '';\n  if ('anchorDate' in state && isFocused && !state.isReadOnly && isSelectable) {\n    // If selection has started add \"click to finish selecting range\"\n    if (state.anchorDate) {\n      rangeSelectionPrompt = stringFormatter.format('finishRangeSelectionPrompt');\n    // Otherwise, add \"click to start selecting range\" prompt\n    } else {\n      rangeSelectionPrompt = stringFormatter.format('startRangeSelectionPrompt');\n    }\n  }\n\n  let descriptionProps = useDescription(rangeSelectionPrompt);\n\n  let isAnchorPressed = useRef(false);\n  let isRangeBoundaryPressed = useRef(false);\n  let touchDragTimerRef = useRef<ReturnType<typeof setTimeout> | undefined>(undefined);\n  let {pressProps, isPressed} = usePress({\n    // When dragging to select a range, we don't want dragging over the original anchor\n    // again to trigger onPressStart. Cancel presses immediately when the pointer exits.\n    shouldCancelOnPointerExit: 'anchorDate' in state && !!state.anchorDate,\n    preventFocusOnPress: true,\n    isDisabled: !isSelectable || state.isReadOnly,\n    onPressStart(e) {\n      if (state.isReadOnly) {\n        state.setFocusedDate(date);\n        return;\n      }\n\n      if ('highlightedRange' in state && !state.anchorDate && (e.pointerType === 'mouse' || e.pointerType === 'touch')) {\n        // Allow dragging the start or end date of a range to modify it\n        // rather than starting a new selection.\n        // Don't allow dragging when invalid, or weird jumping behavior may occur as date ranges\n        // are constrained to available dates. The user will need to select a new range in this case.\n        if (state.highlightedRange && !isInvalid) {\n          if (isSameDay(date, state.highlightedRange.start)) {\n            state.setAnchorDate(state.highlightedRange.end);\n            state.setFocusedDate(date);\n            state.setDragging(true);\n            isRangeBoundaryPressed.current = true;\n            return;\n          } else if (isSameDay(date, state.highlightedRange.end)) {\n            state.setAnchorDate(state.highlightedRange.start);\n            state.setFocusedDate(date);\n            state.setDragging(true);\n            isRangeBoundaryPressed.current = true;\n            return;\n          }\n        }\n\n        let startDragging = () => {\n          state.setDragging(true);\n          touchDragTimerRef.current = undefined;\n\n          state.selectDate(date);\n          state.setFocusedDate(date);\n          isAnchorPressed.current = true;\n        };\n\n        // Start selection on mouse/touch down so users can drag to select a range.\n        // On touch, delay dragging to determine if the user really meant to scroll.\n        if (e.pointerType === 'touch') {\n          touchDragTimerRef.current = setTimeout(startDragging, 200);\n        } else {\n          startDragging();\n        }\n      }\n    },\n    onPressEnd() {\n      isRangeBoundaryPressed.current = false;\n      isAnchorPressed.current = false;\n      clearTimeout(touchDragTimerRef.current);\n      touchDragTimerRef.current = undefined;\n    },\n    onPress() {\n      // For non-range selection, always select on press up.\n      if (!('anchorDate' in state) && !state.isReadOnly) {\n        state.selectDate(date);\n        state.setFocusedDate(date);\n      }\n    },\n    onPressUp(e) {\n      if (state.isReadOnly) {\n        return;\n      }\n\n      // If the user tapped quickly, the date won't be selected yet and the\n      // timer will still be in progress. In this case, select the date on touch up.\n      // Timer is cleared in onPressEnd.\n      if ('anchorDate' in state && touchDragTimerRef.current) {\n        state.selectDate(date);\n        state.setFocusedDate(date);\n      }\n\n      if ('anchorDate' in state) {\n        if (isRangeBoundaryPressed.current) {\n          // When clicking on the start or end date of an already selected range,\n          // start a new selection on press up to also allow dragging the date to\n          // change the existing range.\n          state.setAnchorDate(date);\n        } else if (state.anchorDate && !isAnchorPressed.current) {\n          // When releasing a drag or pressing the end date of a range, select it.\n          state.selectDate(date);\n          state.setFocusedDate(date);\n        } else if (e.pointerType === 'keyboard' && !state.anchorDate) {\n          // For range selection, auto-advance the focused date by one if using keyboard.\n          // This gives an indication that you're selecting a range rather than a single date.\n          // For mouse, this is unnecessary because users will see the indication on hover. For screen readers,\n          // there will be an announcement to \"click to finish selecting range\" (above).\n          state.selectDate(date);\n          let nextDay = date.add({days: 1});\n          if (state.isInvalid(nextDay)) {\n            nextDay = date.subtract({days: 1});\n          }\n          if (!state.isInvalid(nextDay)) {\n            state.setFocusedDate(nextDay);\n          }\n        } else if (e.pointerType === 'virtual') {\n          // For screen readers, just select the date on click.\n          state.selectDate(date);\n          state.setFocusedDate(date);\n        }\n      }\n    }\n  });\n\n  let tabIndex: number | undefined = undefined;\n  if (!isDisabled) {\n    tabIndex = isSameDay(date, state.focusedDate) ? 0 : -1;\n  }\n\n  // Focus the button in the DOM when the state updates.\n  useEffect(() => {\n    if (isFocused && ref.current) {\n      focusWithoutScrolling(ref.current);\n\n      // Scroll into view if navigating with a keyboard, otherwise\n      // try not to shift the view under the user's mouse/finger.\n      // If in a overlay, scrollIntoViewport will only cause scrolling\n      // up to the overlay scroll body to prevent overlay shifting.\n      // Also only scroll into view if the cell actually got focused.\n      // There are some cases where the cell might be disabled or inside,\n      // an inert container and we don't want to scroll then.\n      if (getInteractionModality() !== 'pointer' && document.activeElement === ref.current) {\n        scrollIntoViewport(ref.current, {containingElement: getScrollParent(ref.current)});\n      }\n    }\n  }, [isFocused, ref]);\n\n  let cellDateFormatter = useDateFormatter({\n    day: 'numeric',\n    timeZone: state.timeZone,\n    calendar: date.calendar.identifier\n  });\n\n  let formattedDate = useMemo(() => cellDateFormatter.formatToParts(nativeDate).find(part => part.type === 'day')!.value, [cellDateFormatter, nativeDate]);\n\n  return {\n    cellProps: {\n      role: 'gridcell',\n      'aria-disabled': !isSelectable || undefined,\n      'aria-selected': isSelected || undefined,\n      'aria-invalid': isInvalid || undefined\n    },\n    buttonProps: mergeProps(pressProps, {\n      onFocus() {\n        if (!isDisabled) {\n          state.setFocusedDate(date);\n        }\n      },\n      tabIndex,\n      role: 'button',\n      'aria-disabled': !isSelectable || undefined,\n      'aria-label': label,\n      'aria-invalid': isInvalid || undefined,\n      'aria-describedby': [\n        isInvalid ? errorMessageId : undefined,\n        descriptionProps['aria-describedby']\n      ].filter(Boolean).join(' ') || undefined,\n      onPointerEnter(e) {\n        // Highlight the date on hover or drag over a date when selecting a range.\n        if ('highlightDate' in state && (e.pointerType !== 'touch' || state.isDragging) && isSelectable) {\n          state.highlightDate(date);\n        }\n      },\n      onPointerDown(e) {\n        // This is necessary on touch devices to allow dragging\n        // outside the original pressed element.\n        // (JSDOM does not support this)\n        if ('releasePointerCapture' in e.target) {\n          e.target.releasePointerCapture(e.pointerId);\n        }\n      },\n      onContextMenu(e) {\n        // Prevent context menu on long press.\n        e.preventDefault();\n      }\n    }),\n    isPressed,\n    isFocused,\n    isSelected,\n    isDisabled,\n    isUnavailable,\n    isOutsideVisibleRange: date.compare(state.visibleRange.start) < 0 || date.compare(state.visibleRange.end) > 0,\n    isInvalid,\n    formattedDate\n  };\n}\n"], "names": [], "version": 3, "file": "useCalendarCell.main.js.map"}