{"name": "@react-aria/form", "version": "3.1.1", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "types": "dist/types.d.ts", "exports": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "source": "src/index.ts", "files": ["dist", "src"], "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@react-aria/interactions": "^3.25.5", "@react-aria/utils": "^3.30.1", "@react-stately/form": "^3.2.1", "@react-types/shared": "^3.32.0", "@swc/helpers": "^0.5.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1", "react-dom": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}, "publishConfig": {"access": "public"}, "gitHead": "2c58ed3ddd9be9100af9b1d0cfd137fcdc95ba2d"}