{"mappings": ";AAAA,4BAAiB;IAAG,eAAe,CAAC,gCAAI,CAAC;IACvC,qBAAqB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,WAAW,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,WAAW,EAAE,iBAAG,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,WAAW,EAAE,iBAAG,CAAC;QAAA,GAAG,yBAAI,CAAC;IACvM,qBAAqB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC;YAAC,MAAM,IAAM,CAAC,yCAAM,EAAE,KAAK,UAAU,CAAC,WAAI,EAAE,UAAU,MAAM,CAAC,KAAK,UAAU,EAAE;oBAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,UAAU,EAAE,iBAAG,CAAC;oBAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,UAAU,EAAE,iBAAG,CAAC;gBAAA,GAAG,EAAE,CAAC;YAAE,OAAO,EAAE;QAAA,GAAG,KAAK,aAAa,IAAI,KAAK,UAAU,GAAG,UAAU,MAAM,CAAC;YAAC,MAAM,CAAC,0BAAK,CAAC;YAAE,OAAO,EAAE;QAAA,GAAG,KAAK,UAAU,GAAG;IACpX,gBAAgB,CAAC,gBAAE,CAAC;IACpB,wBAAwB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,0BAAK,CAAC;AAC7D", "sources": ["packages/@react-aria/combobox/intl/zh-TW.json"], "sourcesContent": ["{\n  \"buttonLabel\": \"顯示建議\",\n  \"countAnnouncement\": \"{optionCount, plural, one {# 選項} other {# 選項}} 可用。\",\n  \"focusAnnouncement\": \"{isGroupChange, select, true {輸入的群組 {groupTitle}, 有 {groupCount, plural, one {# 選項} other {# 選項}}. } other {}}{optionText}{isSelected, select, true {, 已選取} other {}}\",\n  \"listboxLabel\": \"建議\",\n  \"selectedAnnouncement\": \"{optionText}, 已選取\"\n}\n"], "names": [], "version": 3, "file": "zh-TW.module.js.map"}