{"mappings": ";AAAA,4BAAiB;IAAG,eAAe,CAAC,gCAAI,CAAC;IACvC,qBAAqB,CAAC,MAAM,YAAc,CAAC,SAAE,EAAE,UAAU,MAAM,CAAC,KAAK,WAAW,EAAE;YAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,WAAW,EAAE,yBAAI,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,WAAW,EAAE,yBAAI,CAAC;QAAA,GAAG,wBAAG,CAAC;IAC1M,qBAAqB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC;YAAC,MAAM,IAAM,CAAC,yBAAI,EAAE,KAAK,UAAU,CAAC,0CAAO,EAAE,UAAU,MAAM,CAAC,KAAK,UAAU,EAAE;oBAAC,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,UAAU,EAAE,yBAAI,CAAC;oBAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,UAAU,EAAE,yBAAI,CAAC;gBAAA,GAAG,EAAE,CAAC;YAAE,OAAO,EAAE;QAAA,GAAG,KAAK,aAAa,IAAI,KAAK,UAAU,GAAG,UAAU,MAAM,CAAC;YAAC,MAAM,CAAC,0BAAK,CAAC;YAAE,OAAO,EAAE;QAAA,GAAG,KAAK,UAAU,GAAG;IACvX,gBAAgB,CAAC,gBAAE,CAAC;IACpB,wBAAwB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,0BAAK,CAAC;AAC7D", "sources": ["packages/@react-aria/combobox/intl/zh-CN.json"], "sourcesContent": ["{\n  \"buttonLabel\": \"显示建议\",\n  \"countAnnouncement\": \"有 {optionCount, plural, one {# 个选项} other {# 个选项}}可用。\",\n  \"focusAnnouncement\": \"{isGroupChange, select, true {进入了 {groupTitle} 组，其中有 {groupCount, plural, one {# 个选项} other {# 个选项}}. } other {}}{optionText}{isSelected, select, true {, 已选择} other {}}\",\n  \"listboxLabel\": \"建议\",\n  \"selectedAnnouncement\": \"{optionText}, 已选择\"\n}\n"], "names": [], "version": 3, "file": "zh-CN.module.js.map"}