{"mappings": ";;;;;;AEyBA,+BAAgC,SAAQ,SAAS;IAC/C,4GAA4G;IAC5G,cAAc,CAAC,EAAE,cAAc,CAAC;IAChC,gDAAgD;IAChD,MAAM,CAAC,EAAE,gBAAgB,CAAA;CAC1B;AAED;IACE,aAAa,CAAC,GAAG,EAAE,GAAG,GAAG,oBAAoB,CAAC;IAC9C,cAAc,IAAI,IAAI,CAAC;IACvB,WAAW,EAAE,qBAAqB,CAAA;CACnC;AAED;IACE,IAAI,EAAE,IAAI,CAAA;CACX;AAED;IACE,WAAW,EAAE,IAAI,CAAA;CAClB;AAED;;;;;;;GAOG;AACH,yBAAyB,CAAC,EAAE,KAAK,EAAE,cAAc,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,WAAW,GAAG,IAAI,CAAC,GAAG,QAAQ,CAoExI;AClGD,4CAA4C,CAAC;IAC3C,2KAA2K;IAC3K,IAAI,EAAE,SAAS,CAAC,CAAC,CAAC;IAClB,wHAAwH;IACxH,aAAa,CAAC,EAAE,OAAO,CAAA;CACxB;AAED;IACE,+FAA+F;IAC/F,iBAAiB,EAAE,aAAa,CAAA;CACjC;AAED;;;;;GAKG;AACH,qCAAqC,CAAC,EAAE,KAAK,EAAE,2BAA2B,CAAC,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,qBAAqB,CA8DlK;ACrED;;;;GAIG;AACH,4BAA4B,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,GAAG,cAAc,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,WAAW,CA6CpJ;ACpED;IACE,sCAAsC;IACtC,QAAQ,EAAE,aAAa,CAAA;CACxB;AAED;;;;GAIG;AAEH,kCAAkC,CAAC,EAAE,KAAK,EAAE,aAAa,CAAC,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,OAAO,GAAG,IAAI,CAAC,GAAG,kBAAkB,CAarI;ACvBD;IACE,iHAAiH;IACjH,IAAI,EAAE,SAAS,OAAO,CAAC,CAAC;IACxB,2DAA2D;IAC3D,aAAa,CAAC,EAAE,OAAO,CAAC;IACxB,wEAAwE;IACxE,qBAAqB,CAAC,EAAE,OAAO,CAAC;IAChC;;;;QAII;IACJ,QAAQ,CAAC,EAAE,MAAM,IAAI,CAAA;CACtB;AAED;IACE,wCAAwC;IACxC,aAAa,EAAE,aAAa,CAAC;IAC7B,wDAAwD;IACxD,SAAS,EAAE,OAAO,CAAA;CACnB;AAED;;;;;GAKG;AACH,6BAA6B,CAAC,EAAE,KAAK,EAAE,kBAAkB,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,aAAa,CAYvI;ACrCD;IACE,qCAAqC;IACrC,GAAG,EAAE,GAAG,CAAA;CACT;AAED;IACE,oDAAoD;IACpD,aAAa,EAAE,iBAAiB,CAAA;CACjC;AAED;IACE,iDAAiD;IACjD,aAAa,EAAE,iBAAiB,CAAA;CACjC;AAED;;;;GAIG;AACH,0CAA0C,CAAC,EAAE,KAAK,EAAE,+BAA+B,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,GAAG,0BAA0B,CAUrI;AAED;;;;GAIG;AACH,0CAA0C,CAAC,EAAE,KAAK,EAAE,WAAW,CAAC,CAAC,GAAG,0BAA0B,CAa7F;AC9CD;IACE,mDAAmD;IACnD,UAAU,EAAE,aAAa,CAAC;IAC1B,qCAAqC;IACrC,YAAY,EAAE,aAAa,CAAC;IAC5B,sDAAsD;IACtD,UAAU,EAAE,OAAO,CAAA;CACpB;AAED,4CAA4C,CAAC;IAC3C,2KAA2K;IAC3K,MAAM,EAAE,SAAS,CAAC,CAAC,CAAC;IACpB,gEAAgE;IAChE,YAAY,EAAE,MAAM,CAAC;IACrB;;;;SAIK;IACL,UAAU,CAAC,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,CAAC;IAChD,+BAA+B;IAC/B,UAAU,CAAC,EAAE,OAAO,CAAC;IACrB,mCAAmC;IACnC,aAAa,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;IACvD,sEAAsE;IACtE,QAAQ,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,IAAI,CAAC;IAClD,iCAAiC;IACjC,WAAW,CAAC,EAAE,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,EAAE,UAAU,CAAC,KAAK,IAAI,CAAA;CACrD;AAED;;;;;GAKG;AACH,qCAAqC,CAAC,EAAE,KAAK,EAAE,2BAA2B,CAAC,CAAC,EAAE,KAAK,EAAE,uBAAuB,CAAC,CAAC,EAAE,GAAG,EAAE,UAAU,gBAAgB,GAAG,IAAI,CAAC,GAAG,qBAAqB,CAqN9K;AC3PD,oCAAoC,gBAAgB,CAEnD;AAGD,YAAY,EAAC,QAAQ,EAAE,WAAW,EAAE,YAAY,EAAC,MAAM,kBAAkB,CAAC", "sources": ["packages/@react-aria/table/src/packages/@react-aria/table/src/utils.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/TableKeyboardDelegate.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/useTable.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/useTableColumnHeader.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/useTableRow.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/useTableHeaderRow.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/useTableCell.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/useTableSelectionCheckbox.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/useTableColumnResize.ts", "packages/@react-aria/table/src/packages/@react-aria/table/src/index.ts", "packages/@react-aria/table/src/index.ts"], "sourcesContent": [null, null, null, null, null, null, null, null, null, null, "/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport {useTable} from './useTable';\nexport {useTableColumnHeader} from './useTableColumnHeader';\nexport {useTableRow} from './useTableRow';\nexport {useTableHeaderRow} from './useTableHeaderRow';\nexport {useTableCell} from './useTableCell';\nexport {useTableSelectionCheckbox, useTableSelectAllCheckbox} from './useTableSelectionCheckbox';\nexport {useTableColumnResize} from './useTableColumnResize';\n\n// Workaround for a Parcel bug where re-exports don't work in the CommonJS output format...\n// export {useGridRowGroup as useTableRowGroup} from '@react-aria/grid';\nimport {GridRowGroupAria, useGridRowGroup} from '@react-aria/grid';\nexport function useTableRowGroup(): GridRowGroupAria {\n  return useGridRowGroup();\n}\n\nexport type {AriaTableProps} from './useTable';\nexport type {GridAria, GridRowAria, GridRowProps} from '@react-aria/grid';\nexport type {AriaTableColumnHeaderProps, TableColumnHeaderAria} from './useTableColumnHeader';\nexport type {AriaTableCellProps, TableCellAria} from './useTableCell';\nexport type {TableHeaderRowAria} from './useTableHeaderRow';\nexport type {AriaTableSelectionCheckboxProps, TableSelectionCheckboxAria, TableSelectAllCheckboxAria} from './useTableSelectionCheckbox';\nexport type {AriaTableColumnResizeProps, TableColumnResizeAria} from './useTableColumnResize';\n"], "names": [], "version": 3, "file": "types.d.ts.map"}