{"mappings": "AAAA,iBAAiB;IAAG,aAAa,CAAC,kBAAY,CAAC;IAC7C,iBAAiB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,gDAAiC,CAAC;IAChF,cAAc,CAAC,OAAS,GAAG,KAAK,KAAK,CAAC,OAAO,CAAC;IAC9C,cAAc,CAAC,mBAAa,CAAC;IAC7B,kBAAkB,CAAC,OAAS,GAAG,KAAK,UAAU,CAAC,iDAAkC,CAAC;IAClF,sBAAsB,CAAC,wDAAyC,CAAC;IACjE,UAAU,CAAC,MAAG,CAAC;IACf,aAAa,CAAC,sBAAU,CAAC;IACzB,YAAY,CAAC,4BAAmB,CAAC;AACnC", "sources": ["packages/@react-aria/table/intl/tr-TR.json"], "sourcesContent": ["{\n  \"ascending\": \"artan sırada\",\n  \"ascendingSort\": \"{columnName} sütuna göre artan düzende sırala\",\n  \"columnSize\": \"{value} piksel\",\n  \"descending\": \"azalan sırada\",\n  \"descendingSort\": \"{columnName} sütuna göre azalan düzende sırala\",\n  \"resizerDescription\": \"Yeniden boyutlandırmak için Enter'a basın\",\n  \"select\": \"Seç\",\n  \"selectAll\": \"Tü<PERSON>ün<PERSON> Seç\",\n  \"sortable\": \"Sıralanabilir sütun\"\n}\n"], "names": [], "version": 3, "file": "tr-TR.main.js.map"}