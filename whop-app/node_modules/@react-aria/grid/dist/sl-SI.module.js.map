{"mappings": ";AAAA,4BAAiB;IAAG,kBAAkB,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC;IAC/E,qBAAqB,CAAC,kEAAgD,CAAC;IACvE,UAAU,CAAC,QAAQ,CAAC;IACpB,eAAe,CAAC,wBAAwB,CAAC;IACzC,iBAAiB,CAAC,MAAM,YAAc,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE;YAAC,MAAM,CAAC,uBAAuB,CAAC;YAAE,KAAK,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,kBAAkB,CAAC;YAAE,OAAO,IAAM,GAAG,UAAU,MAAM,CAAC,KAAK,KAAK,EAAE,sBAAsB,CAAC;QAAA,GAAG,CAAC,CAAC;IACjP,gBAAgB,CAAC,OAAS,CAAC,QAAQ,EAAE,KAAK,IAAI,CAAC,WAAW,CAAC;AAC7D", "sources": ["packages/@react-aria/grid/intl/sl-SI.json"], "sourcesContent": ["{\n  \"deselectedItem\": \"Element {item} ni izbran.\",\n  \"longPressToSelect\": \"Za izbirni način pritisnite in dlje časa držite.\",\n  \"select\": \"Izberite\",\n  \"selectedAll\": \"Vsi elementi so izbrani.\",\n  \"selectedCount\": \"{count, plural, =0 {Noben element ni izbran} one {# element je izbran} other {# elementov je izbranih}}.\",\n  \"selectedItem\": \"Element {item} je izbran.\"\n}\n"], "names": [], "version": 3, "file": "sl-SI.module.js.map"}