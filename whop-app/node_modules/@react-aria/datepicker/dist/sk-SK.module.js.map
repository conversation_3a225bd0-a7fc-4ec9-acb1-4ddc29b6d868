{"mappings": ";AAAA,4BAAiB;IAAG,YAAY,CAAC,WAAQ,CAAC;IACxC,OAAO,CAAC,SAAG,CAAC;IACZ,aAAa,CAAC,KAAK,CAAC;IACpB,WAAW,CAAC,wBAAe,CAAC;IAC5B,OAAO,CAAC,eAAS,CAAC;IAClB,QAAQ,CAAC,MAAM,CAAC;IAChB,UAAU,CAAC,SAAM,CAAC;IAClB,SAAS,CAAC,MAAM,CAAC;IACjB,UAAU,CAAC,OAAO,CAAC;IACnB,2BAA2B,CAAC,OAAS,CAAC,qBAAe,EAAE,KAAK,IAAI,EAAE;IAClE,4BAA4B,CAAC,OAAS,CAAC,sBAAmB,EAAE,KAAK,SAAS,CAAC,IAAI,EAAE,KAAK,OAAO,EAAE;IAC/F,2BAA2B,CAAC,OAAS,CAAC,sBAAa,EAAE,KAAK,IAAI,EAAE;IAChE,aAAa,CAAC,sBAAa,CAAC;IAC5B,gBAAgB,CAAC,wBAAY,CAAC;IAC9B,WAAW,CAAC,+BAAU,CAAC;IACvB,QAAQ,CAAC,GAAG,CAAC;AACf", "sources": ["packages/@react-aria/datepicker/intl/sk-SK.json"], "sourcesContent": ["{\n  \"calendar\": \"<PERSON>len<PERSON><PERSON><PERSON>\",\n  \"day\": \"deň\",\n  \"dayPeriod\": \"AM/PM\",\n  \"endDate\": \"Dátum ukončenia\",\n  \"era\": \"letopočet\",\n  \"hour\": \"hodina\",\n  \"minute\": \"minúta\",\n  \"month\": \"mesiac\",\n  \"second\": \"sekunda\",\n  \"selectedDateDescription\": \"Vybratý dátum: {date}\",\n  \"selectedRangeDescription\": \"Vybratý rozsah: od {startDate} do {endDate}\",\n  \"selectedTimeDescription\": \"Vybratý čas: {time}\",\n  \"startDate\": \"Dátum začatia\",\n  \"timeZoneName\": \"časové pásmo\",\n  \"weekday\": \"deň týždňa\",\n  \"year\": \"rok\"\n}\n"], "names": [], "version": 3, "file": "sk-SK.module.js.map"}