{"version": 3, "sources": ["../../../src/server/normalizers/underscore-normalizer.ts"], "sourcesContent": ["import type { Normalizer } from './normalizer'\n\n/**\n * UnderscoreNormalizer replaces all instances of %5F with _.\n */\nexport class UnderscoreNormalizer implements Normalizer {\n  public normalize(pathname: string): string {\n    return pathname.replace(/%5F/g, '_')\n  }\n}\n"], "names": ["UnderscoreNormalizer", "normalize", "pathname", "replace"], "mappings": "AAEA;;CAEC,GACD,OAAO,MAAMA;IACJC,UAAUC,QAAgB,EAAU;QACzC,OAAOA,SAASC,OAAO,CAAC,QAAQ;IAClC;AACF"}