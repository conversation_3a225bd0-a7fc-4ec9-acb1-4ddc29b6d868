{"version": 3, "file": "errors.d.ts", "sourceRoot": "", "sources": ["src/errors.ts"], "names": [], "mappings": "AAEA,KAAK,SAAS,GACV,cAAc,GACd,cAAc,GACd,oBAAoB,GACpB,eAAe,GACf,YAAY,GACZ,oBAAoB,GACpB,mBAAmB,GACnB,kBAAkB,GAClB,cAAc,GACd,iBAAiB,GACjB,gBAAgB,GAChB,uBAAuB,GACvB,kBAAkB,GAClB,eAAe,GACf,uBAAuB,GACvB,oBAAoB,GACpB,wBAAwB,GACxB,mBAAmB,GACnB,kBAAkB,GAClB,kBAAkB,GAClB,cAAc,GACd,eAAe,GACf,qBAAqB,GACrB,iBAAiB,GACjB,eAAe,GACf,cAAc,GACd,aAAa,GACb,kBAAkB,GAClB,wBAAwB,GACxB,6BAA6B,GAC7B,2BAA2B,GAC3B,+BAA+B,CAAA;AAEnC;;;;;GAKG;AACH,qBAAa,SAAU,SAAQ,KAAK;IAClC,IAAI,EAAE,SAAS,CAAA;IAQf,KAAK,CAAC,EAAE,MAAM,CAAC,MAAM,EAAE,OAAO,CAAC,GAAG;QAAE,GAAG,CAAC,EAAE,KAAK,CAAA;KAAE,CAAA;CA6BlD;AAED;;;GAGG;AACH,qBAAa,WAAY,SAAQ,SAAS;CAGzC;AAED;;;;;;;;;;;;;GAaG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;GAIG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAuCG;AACH,qBAAa,kBAAmB,SAAQ,SAAS;IAC/C,MAAM,CAAC,IAAI,SAAuB;CACnC;AAED;;;;;;;;GAQG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;;;;;GAQG;AACH,qBAAa,UAAW,SAAQ,SAAS;IACvC,MAAM,CAAC,IAAI,SAAe;CAC3B;AAED;;;;;;;;;;GAUG;AACH,qBAAa,kBAAmB,SAAQ,SAAS;IAC/C,MAAM,CAAC,IAAI,SAAuB;CACnC;AAED;;;;;;GAMG;AACH,qBAAa,iBAAkB,SAAQ,WAAW;IAChD,MAAM,CAAC,IAAI,SAAsB;IACjC;;;;;;;;;;OAUG;IACH,IAAI,EAAE,MAAM,CAAgB;CAC7B;AAED;;;;;;GAMG;AACH,qBAAa,gBAAiB,SAAQ,SAAS;IAC7C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;;GAMG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;;;;;;;;GAWG;AACH,qBAAa,eAAgB,SAAQ,SAAS;IAC5C,MAAM,CAAC,IAAI,SAAoB;CAChC;AAED;;;;;;;GAOG;AACH,qBAAa,cAAe,SAAQ,SAAS;IAC3C,MAAM,CAAC,IAAI,SAAmB;CAC/B;AAED;;;;;;;GAOG;AACH,qBAAa,qBAAsB,SAAQ,SAAS;IAClD,MAAM,CAAC,IAAI,SAA0B;CACtC;AAED;;;;;;GAMG;AACH,qBAAa,gBAAiB,SAAQ,SAAS;IAC7C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;;;;;;;;;GAaG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;;;;;;;;;GAYG;AACH,qBAAa,qBAAsB,SAAQ,WAAW;IACpD,MAAM,CAAC,IAAI,SAA0B;CACtC;AAED;;;;;;GAMG;AACH,qBAAa,kBAAmB,SAAQ,WAAW;IACjD,MAAM,CAAC,IAAI,SAAuB;CACnC;AAED;;;;;GAKG;AACH,qBAAa,sBAAuB,SAAQ,SAAS;IACnD,MAAM,CAAC,IAAI,SAA2B;CACvC;AAED;;;;;;;GAOG;AACH,qBAAa,iBAAkB,SAAQ,SAAS;IAC9C,MAAM,CAAC,IAAI,SAAsB;CAClC;AAED;;;;;;;;;;;;;;;GAeG;AACH,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;;;;;;GAUG;AACH,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;;;;;;;GASG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;;GAKG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;;GAKG;AACH,qBAAa,mBAAoB,SAAQ,SAAS;IAChD,MAAM,CAAC,IAAI,SAAwB;CACpC;AAED;;;GAGG;AACH,qBAAa,eAAgB,SAAQ,SAAS;IAC5C,MAAM,CAAC,IAAI,SAAoB;CAChC;AAED;;;;;;;;;;;GAWG;AACH,qBAAa,aAAc,SAAQ,SAAS;IAC1C,MAAM,CAAC,IAAI,SAAkB;CAC9B;AAED;;;;;GAKG;AACH,qBAAa,YAAa,SAAQ,SAAS;IACzC,MAAM,CAAC,IAAI,SAAiB;CAC7B;AAED;;;;;;;;;;GAUG;AACH,qBAAa,WAAY,SAAQ,WAAW;IAC1C,MAAM,CAAC,IAAI,SAAgB;CAC5B;AAuBD;;;;GAIG;AACH,qBAAa,sBAAuB,SAAQ,SAAS;IACnD,MAAM,CAAC,IAAI,SAA2B;CACvC;AAED;;;;;GAKG;AACH,qBAAa,2BAA4B,SAAQ,SAAS;IACxD,MAAM,CAAC,IAAI,SAAgC;CAC5C;AAED;;;GAGG;AACH,qBAAa,yBAA0B,SAAQ,SAAS;IACtD,MAAM,CAAC,IAAI,SAA8B;CAC1C;AAED;;;;;;GAMG;AACH,qBAAa,gBAAiB,SAAQ,WAAW;IAC/C,MAAM,CAAC,IAAI,SAAqB;CACjC;AAED;;;GAGG;AACH,qBAAa,6BAA8B,SAAQ,SAAS;IAC1D,MAAM,CAAC,IAAI,SAAkC;CAC9C"}