{"name": "react-stately", "version": "3.41.0", "description": "Spectrum UI components in React", "license": "Apache-2.0", "main": "dist/main.js", "module": "dist/module.js", "exports": {"source": "./src/index.ts", "types": ["./dist/types.d.ts", "./src/index.ts"], "import": "./dist/import.mjs", "require": "./dist/main.js"}, "types": "dist/types.d.ts", "source": "src/index.ts", "files": ["dist"], "sideEffects": false, "scripts": {"prepublishOnly": "mkdir -p dist; cp src/index.ts dist/types.d.ts; grep -v '^export type' src/index.ts > dist/module.js; babel --root-mode upward src/index.ts -o dist/main.js"}, "repository": {"type": "git", "url": "https://github.com/adobe/react-spectrum"}, "dependencies": {"@react-stately/calendar": "^3.8.4", "@react-stately/checkbox": "^3.7.1", "@react-stately/collections": "^3.12.7", "@react-stately/color": "^3.9.1", "@react-stately/combobox": "^3.11.1", "@react-stately/data": "^3.14.0", "@react-stately/datepicker": "^3.15.1", "@react-stately/disclosure": "^3.0.7", "@react-stately/dnd": "^3.7.0", "@react-stately/form": "^3.2.1", "@react-stately/list": "^3.13.0", "@react-stately/menu": "^3.9.7", "@react-stately/numberfield": "^3.10.1", "@react-stately/overlays": "^3.6.19", "@react-stately/radio": "^3.11.1", "@react-stately/searchfield": "^3.5.15", "@react-stately/select": "^3.7.1", "@react-stately/selection": "^3.20.5", "@react-stately/slider": "^3.7.1", "@react-stately/table": "^3.15.0", "@react-stately/tabs": "^3.8.5", "@react-stately/toast": "^3.1.2", "@react-stately/toggle": "^3.9.1", "@react-stately/tooltip": "^3.5.7", "@react-stately/tree": "^3.9.2", "@react-types/shared": "^3.32.0"}, "peerDependencies": {"react": "^16.8.0 || ^17.0.0-rc.1 || ^18.0.0 || ^19.0.0-rc.1"}, "devDependencies": {"@babel/cli": "^7.24.1", "@babel/core": "^7.24.3"}, "publishConfig": {"access": "public"}, "gitHead": "2c58ed3ddd9be9100af9b1d0cfd137fcdc95ba2d"}