{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.12 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, frosted_ui, components, utilities;\n@layer theme {\n  :root, :host {\n    --spacing: 0.25rem;\n    --container-sm: 24rem;\n    --container-2xl: 42rem;\n    --container-4xl: 56rem;\n    --container-5xl: 64rem;\n    --container-6xl: 72rem;\n    --radius-lg: 0.5rem;\n    --radius-xl: 0.75rem;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--default-font-family);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji');\n    font-feature-settings: normal;\n    font-variation-settings: normal;\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  ::-webkit-calendar-picker-indicator {\n    line-height: 1;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type='button'], [type='reset'], [type='submit']), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden='until-found'])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .absolute {\n    position: absolute;\n  }\n  .relative {\n    position: relative;\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .mb-0\\.5 {\n    margin-bottom: calc(var(--spacing) * 0.5);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-10 {\n    margin-bottom: calc(var(--spacing) * 10);\n  }\n  .mb-16 {\n    margin-bottom: calc(var(--spacing) * 16);\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .\\!h-12 {\n    height: calc(var(--spacing) * 12) !important;\n  }\n  .h-0\\.5 {\n    height: calc(var(--spacing) * 0.5);\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-1\\.5 {\n    height: calc(var(--spacing) * 1.5);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-2\\.5 {\n    height: calc(var(--spacing) * 2.5);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-px {\n    height: 1px;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .\\!w-full {\n    width: 100% !important;\n  }\n  .w-0\\.5 {\n    width: calc(var(--spacing) * 0.5);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-1\\.5 {\n    width: calc(var(--spacing) * 1.5);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-7 {\n    width: calc(var(--spacing) * 7);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-5xl {\n    max-width: var(--container-5xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .grow {\n    flex-grow: 1;\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .grid-cols-4 {\n    grid-template-columns: repeat(4, minmax(0, 1fr));\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .divide-y {\n    :where(& > :not(:last-child)) {\n      --tw-divide-y-reverse: 0;\n      border-bottom-style: var(--tw-border-style);\n      border-top-style: var(--tw-border-style);\n      border-top-width: calc(1px * var(--tw-divide-y-reverse));\n      border-bottom-width: calc(1px * calc(1 - var(--tw-divide-y-reverse)));\n    }\n  }\n  .divide-blue-9\\/10 {\n    :where(& > :not(:last-child)) {\n      border-color: var(--blue-9);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--blue-9) 10%, transparent);\n      }\n    }\n  }\n  .divide-gray-4 {\n    :where(& > :not(:last-child)) {\n      border-color: var(--gray-4);\n    }\n  }\n  .divide-gray-8 {\n    :where(& > :not(:last-child)) {\n      border-color: var(--gray-8);\n    }\n  }\n  .divide-gray-10 {\n    :where(& > :not(:last-child)) {\n      border-color: var(--gray-10);\n    }\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius-lg);\n  }\n  .rounded-xl {\n    border-radius: var(--radius-xl);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .\\!border-gray-8 {\n    border-color: var(--gray-8) !important;\n  }\n  .border-blue-8\\/30 {\n    border-color: var(--blue-8);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--blue-8) 30%, transparent);\n    }\n  }\n  .border-blue-9\\/20 {\n    border-color: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--blue-9) 20%, transparent);\n    }\n  }\n  .border-blue-9\\/30 {\n    border-color: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--blue-9) 30%, transparent);\n    }\n  }\n  .border-blue-9\\/40 {\n    border-color: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--blue-9) 40%, transparent);\n    }\n  }\n  .border-gray-4 {\n    border-color: var(--gray-4);\n  }\n  .border-gray-8 {\n    border-color: var(--gray-8);\n  }\n  .border-gray-8\\/30 {\n    border-color: var(--gray-8);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--gray-8) 30%, transparent);\n    }\n  }\n  .border-gray-8\\/50 {\n    border-color: var(--gray-8);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--gray-8) 50%, transparent);\n    }\n  }\n  .border-gray-10 {\n    border-color: var(--gray-10);\n  }\n  .border-gray-11 {\n    border-color: var(--gray-11);\n  }\n  .border-green-3 {\n    border-color: var(--green-3);\n  }\n  .border-green-4 {\n    border-color: var(--green-4);\n  }\n  .border-green-6 {\n    border-color: var(--green-6);\n  }\n  .border-green-9\\/30 {\n    border-color: var(--green-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--green-9) 30%, transparent);\n    }\n  }\n  .border-red-6 {\n    border-color: var(--red-6);\n  }\n  .border-red-9\\/30 {\n    border-color: var(--red-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--red-9) 30%, transparent);\n    }\n  }\n  .\\!bg-gray-11 {\n    background-color: var(--gray-11) !important;\n  }\n  .bg-black {\n    background-color: #000000;\n  }\n  .bg-blue-3 {\n    background-color: var(--blue-3);\n  }\n  .bg-blue-8 {\n    background-color: var(--blue-8);\n  }\n  .bg-blue-8\\/20 {\n    background-color: var(--blue-8);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--blue-8) 20%, transparent);\n    }\n  }\n  .bg-blue-9 {\n    background-color: var(--blue-9);\n  }\n  .bg-blue-9\\/10 {\n    background-color: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--blue-9) 10%, transparent);\n    }\n  }\n  .bg-blue-9\\/20 {\n    background-color: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--blue-9) 20%, transparent);\n    }\n  }\n  .bg-gray-1 {\n    background-color: var(--gray-1);\n  }\n  .bg-gray-2 {\n    background-color: var(--gray-2);\n  }\n  .bg-gray-3 {\n    background-color: var(--gray-3);\n  }\n  .bg-gray-6 {\n    background-color: var(--gray-6);\n  }\n  .bg-gray-8 {\n    background-color: var(--gray-8);\n  }\n  .bg-gray-8\\/20 {\n    background-color: var(--gray-8);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--gray-8) 20%, transparent);\n    }\n  }\n  .bg-gray-9 {\n    background-color: var(--gray-9);\n  }\n  .bg-gray-11 {\n    background-color: var(--gray-11);\n  }\n  .bg-gray-11\\/50 {\n    background-color: var(--gray-11);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--gray-11) 50%, transparent);\n    }\n  }\n  .bg-gray-12 {\n    background-color: var(--gray-12);\n  }\n  .bg-green-3 {\n    background-color: var(--green-3);\n  }\n  .bg-green-9 {\n    background-color: var(--green-9);\n  }\n  .bg-green-9\\/20 {\n    background-color: var(--green-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--green-9) 20%, transparent);\n    }\n  }\n  .bg-orange-3 {\n    background-color: var(--orange-3);\n  }\n  .bg-orange-9 {\n    background-color: var(--orange-9);\n  }\n  .bg-red-3 {\n    background-color: var(--red-3);\n  }\n  .bg-red-9\\/20 {\n    background-color: var(--red-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--red-9) 20%, transparent);\n    }\n  }\n  .bg-white {\n    background-color: #FFFFFF;\n  }\n  .bg-white\\/80 {\n    background-color: color-mix(in oklab, #FFFFFF 80%, transparent);\n  }\n  .bg-yellow-3 {\n    background-color: var(--yellow-3);\n  }\n  .bg-yellow-9 {\n    background-color: var(--yellow-9);\n  }\n  .bg-gradient-to-b {\n    --tw-gradient-position: to bottom in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-blue-9\\/10 {\n    --tw-gradient-from: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--blue-9) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-9\\/20 {\n    --tw-gradient-from: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--blue-9) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-9\\/30 {\n    --tw-gradient-from: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--blue-9) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-1 {\n    --tw-gradient-from: var(--gray-1);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-9 {\n    --tw-gradient-from: var(--gray-9);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-gray-12 {\n    --tw-gradient-from: var(--gray-12);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-1 {\n    --tw-gradient-from: var(--green-1);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-2 {\n    --tw-gradient-from: var(--green-2);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-3 {\n    --tw-gradient-from: var(--green-3);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-9\\/20 {\n    --tw-gradient-from: var(--green-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--green-9) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-9 {\n    --tw-gradient-from: var(--orange-9);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-9 {\n    --tw-gradient-from: var(--yellow-9);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-blue-9\\/10 {\n    --tw-gradient-via: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--blue-9) 10%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-black {\n    --tw-gradient-to: #000000;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-11\\/20 {\n    --tw-gradient-to: var(--blue-11);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--blue-11) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-11\\/30 {\n    --tw-gradient-to: var(--blue-11);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--blue-11) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-10 {\n    --tw-gradient-to: var(--gray-10);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-12\\/80 {\n    --tw-gradient-to: var(--gray-12);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--gray-12) 80%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-1 {\n    --tw-gradient-to: var(--green-1);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-2 {\n    --tw-gradient-to: var(--green-2);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-3 {\n    --tw-gradient-to: var(--green-3);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-4 {\n    --tw-gradient-to: var(--green-4);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-10\\/10 {\n    --tw-gradient-to: var(--green-10);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--green-10) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-10 {\n    --tw-gradient-to: var(--orange-10);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-yellow-10 {\n    --tw-gradient-to: var(--yellow-10);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-5 {\n    padding: calc(var(--spacing) * 5);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-right {\n    text-align: right;\n  }\n  .leading-tight {\n    --tw-leading: 1.25;\n    line-height: 1.25;\n  }\n  .\\!font-bold {\n    --tw-font-weight: var(--font-weight-bold) !important;\n    font-weight: var(--font-weight-bold) !important;\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-semibold {\n    --tw-font-weight: 600;\n    font-weight: 600;\n  }\n  .\\!text-white {\n    color: #FFFFFF !important;\n  }\n  .text-black {\n    color: #000000;\n  }\n  .text-blue-8 {\n    color: var(--blue-8);\n  }\n  .text-blue-9 {\n    color: var(--blue-9);\n  }\n  .text-blue-11 {\n    color: var(--blue-11);\n  }\n  .text-gray-1 {\n    color: var(--gray-1);\n  }\n  .text-gray-3 {\n    color: var(--gray-3);\n  }\n  .text-gray-4 {\n    color: var(--gray-4);\n  }\n  .text-gray-8 {\n    color: var(--gray-8);\n  }\n  .text-gray-9 {\n    color: var(--gray-9);\n  }\n  .text-gray-10 {\n    color: var(--gray-10);\n  }\n  .text-gray-11 {\n    color: var(--gray-11);\n  }\n  .text-gray-12 {\n    color: var(--gray-12);\n  }\n  .text-green-3 {\n    color: var(--green-3);\n  }\n  .text-green-9 {\n    color: var(--green-9);\n  }\n  .text-green-10 {\n    color: var(--green-10);\n  }\n  .text-green-11 {\n    color: var(--green-11);\n  }\n  .text-green-12 {\n    color: var(--green-12);\n  }\n  .text-orange-1 {\n    color: var(--orange-1);\n  }\n  .text-orange-9 {\n    color: var(--orange-9);\n  }\n  .text-orange-11 {\n    color: var(--orange-11);\n  }\n  .text-red-9 {\n    color: var(--red-9);\n  }\n  .text-red-11 {\n    color: var(--red-11);\n  }\n  .text-white {\n    color: #FFFFFF;\n  }\n  .text-yellow-1 {\n    color: var(--yellow-1);\n  }\n  .text-yellow-9 {\n    color: var(--yellow-9);\n  }\n  .text-yellow-11 {\n    color: var(--yellow-11);\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-blue-9\\/20 {\n    --tw-shadow-color: var(--blue-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--blue-9) 20%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-gray-8\\/30 {\n    --tw-shadow-color: var(--gray-8);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--gray-8) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-green-9\\/30 {\n    --tw-shadow-color: var(--green-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--green-9) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-green-9\\/50 {\n    --tw-shadow-color: var(--green-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--green-9) 50%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-orange-9\\/30 {\n    --tw-shadow-color: var(--orange-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--orange-9) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-yellow-9\\/30 {\n    --tw-shadow-color: var(--yellow-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--yellow-9) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-yellow-9\\/50 {\n    --tw-shadow-color: var(--yellow-9);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--yellow-9) 50%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-shadow {\n    transition-property: box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .hover\\:bg-blue-9\\/5 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--blue-9);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--blue-9) 5%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--gray-10);\n      }\n    }\n  }\n  .hover\\:bg-gray-11 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--gray-11);\n      }\n    }\n  }\n  .hover\\:bg-gray-12 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--gray-12);\n      }\n    }\n  }\n  .hover\\:bg-green-1 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--green-1);\n      }\n    }\n  }\n  .hover\\:bg-green-10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--green-10);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-sm {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 1024px) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 1024px) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 1024px) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 1280px) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .xl\\:grid-cols-4 {\n    @media (width >= 1640px) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n}\n@layer frosted_ui {\n  @keyframes fui-fade-in {\n    from {\n      opacity: 0;\n    }\n    to {\n      opacity: 1;\n    }\n  }\n  @keyframes fui-fade-out {\n    from {\n      opacity: 1;\n    }\n    to {\n      opacity: 0;\n    }\n  }\n  @keyframes fui-slide-from-top {\n    from {\n      transform: translateY(4px) scale(0.97);\n    }\n    to {\n      transform: translateY(0) scale(1);\n    }\n  }\n  @keyframes fui-slide-to-top {\n    from {\n      transform: translateY(0) scale(1);\n    }\n    to {\n      transform: translateY(4px) scale(0.97);\n    }\n  }\n  @keyframes fui-slide-from-bottom {\n    from {\n      transform: translateY(-4px) scale(0.97);\n    }\n    to {\n      transform: translateY(0) scale(1);\n    }\n  }\n  @keyframes fui-slide-to-bottom {\n    from {\n      transform: translateY(0) scale(1);\n    }\n    to {\n      transform: translateY(-4px) scale(0.97);\n    }\n  }\n  @keyframes fui-slide-from-left {\n    from {\n      transform: translateX(4px) scale(0.97);\n    }\n    to {\n      transform: translateX(0) scale(1);\n    }\n  }\n  @keyframes fui-slide-to-left {\n    from {\n      transform: translateX(0) scale(1);\n    }\n    to {\n      transform: translateX(4px) scale(0.97);\n    }\n  }\n  @keyframes fui-slide-from-right {\n    from {\n      transform: translateX(-4px) scale(0.97);\n    }\n    to {\n      transform: translateX(0) scale(1);\n    }\n  }\n  @keyframes fui-slide-to-right {\n    from {\n      transform: translateX(0) scale(1);\n    }\n    to {\n      transform: translateX(-4px) scale(0.97);\n    }\n  }\n  @media (prefers-reduced-motion: no-preference) {\n    .fui-PopperContent {\n      animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);\n    }\n    .fui-PopperContent:where([data-state='open']) {\n      animation-duration: 300ms;\n    }\n    .fui-PopperContent:where([data-state='open']):where([data-side='top']) {\n      animation-name: fui-slide-from-top, fui-fade-in;\n    }\n    .fui-PopperContent:where([data-state='open']):where([data-side='bottom']) {\n      animation-name: fui-slide-from-bottom, fui-fade-in;\n    }\n    .fui-PopperContent:where([data-state='open']):where([data-side='left']) {\n      animation-name: fui-slide-from-left, fui-fade-in;\n    }\n    .fui-PopperContent:where([data-state='open']):where([data-side='right']) {\n      animation-name: fui-slide-from-right, fui-fade-in;\n    }\n    .fui-PopperContent:where([data-state='closed']) {\n      animation-duration: 150ms;\n    }\n    .fui-PopperContent:where([data-state='closed']):where([data-side='top']) {\n      animation-name: fui-slide-to-top, fui-fade-out;\n    }\n    .fui-PopperContent:where([data-state='closed']):where([data-side='bottom']) {\n      animation-name: fui-slide-to-bottom, fui-fade-out;\n    }\n    .fui-PopperContent:where([data-state='closed']):where([data-side='left']) {\n      animation-name: fui-slide-to-left, fui-fade-out;\n    }\n    .fui-PopperContent:where([data-state='closed']):where([data-side='right']) {\n      animation-name: fui-slide-to-right, fui-fade-out;\n    }\n  }\n  .frosted-ui:where([data-is-root-theme='true']) {\n    position: relative;\n    z-index: 0;\n  }\n  .fui-reset:where(a) {\n    cursor: var(--cursor-link);\n    text-decoration: none;\n    color: inherit;\n    outline: none;\n  }\n  .fui-reset:where(button) {\n    -webkit-appearance: none;\n    appearance: none;\n    cursor: var(--cursor-button);\n    background-color: transparent;\n    border: none;\n    font-size: inherit;\n    font-family: inherit;\n    line-height: inherit;\n    letter-spacing: inherit;\n    outline: none;\n    color: inherit;\n    padding: 0;\n    margin: 0;\n    text-align: initial;\n    -webkit-tap-highlight-color: transparent;\n  }\n  .fui-reset:where(h1, h2, h3, h4, h5, h6) {\n    font-size: inherit;\n    font-weight: inherit;\n    margin: 0;\n  }\n  .fui-reset:where(ol, ul) {\n    list-style: none;\n    margin: 0;\n    padding: 0;\n  }\n  .fui-reset:where(p) {\n    margin: 0;\n  }\n  .fui-reset:where(pre) {\n    font-family: inherit;\n    margin: 0;\n  }\n  *, ::before, ::after {\n    box-sizing: border-box;\n    border-width: 0;\n    border-style: solid;\n  }\n  blockquote, dl, dd, h1, h2, h3, h4, h5, h6, hr, figure, p, pre {\n    margin: 0;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  ol, ul {\n    list-style: none;\n    margin: 0;\n    padding: 0;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  *::-webkit-scrollbar {\n    width: var(--space-1);\n    height: var(--space-1);\n  }\n  *::-webkit-scrollbar-track {\n    background-color: var(--gray-a3);\n  }\n  *::-webkit-scrollbar-corner {\n    background-color: var(--gray-a3);\n  }\n  *::-webkit-scrollbar-thumb {\n    border-radius: var(--space-1);\n    background-color: var(--gray-a8);\n  }\n  @media (hover: hover) {\n    :hover::-webkit-scrollbar-thumb:hover {\n      background-color: var(--gray-a9);\n    }\n  }\n  :root, .light, .light-theme {\n    --tomato-1: #fffcfc;\n    --tomato-2: #fff8f7;\n    --tomato-3: #feebe7;\n    --tomato-4: #ffdcd3;\n    --tomato-5: #ffcdc2;\n    --tomato-6: #fdbdaf;\n    --tomato-7: #f5a898;\n    --tomato-8: #ec8e7b;\n    --tomato-9: #e54d2e;\n    --tomato-10: #dd4425;\n    --tomato-11: #d13415;\n    --tomato-12: #5c271f;\n    --tomato-a1: #ff000003;\n    --tomato-a2: #ff200008;\n    --tomato-a3: #f52b0018;\n    --tomato-a4: #ff35002c;\n    --tomato-a5: #ff2e003d;\n    --tomato-a6: #f92d0050;\n    --tomato-a7: #e7280067;\n    --tomato-a8: #db250084;\n    --tomato-a9: #df2600d1;\n    --tomato-a10: #d72400da;\n    --tomato-a11: #cd2200ea;\n    --tomato-a12: #460900e0;\n    --red-1: #fffcfc;\n    --red-2: #fff7f7;\n    --red-3: #feebec;\n    --red-4: #ffdbdc;\n    --red-5: #ffcdce;\n    --red-6: #fdbdbe;\n    --red-7: #f4a9aa;\n    --red-8: #eb8e90;\n    --red-9: #e5484d;\n    --red-10: #dc3e42;\n    --red-11: #ce2c31;\n    --red-12: #641723;\n    --red-a1: #ff000003;\n    --red-a2: #ff000008;\n    --red-a3: #f3000d14;\n    --red-a4: #ff000824;\n    --red-a5: #ff000632;\n    --red-a6: #f8000442;\n    --red-a7: #df000356;\n    --red-a8: #d2000571;\n    --red-a9: #db0007b7;\n    --red-a10: #d10005c1;\n    --red-a11: #c40006d3;\n    --red-a12: #55000de8;\n    --ruby-1: #fffcfd;\n    --ruby-2: #fff7f8;\n    --ruby-3: #feeaed;\n    --ruby-4: #ffdce1;\n    --ruby-5: #ffced6;\n    --ruby-6: #f8bfc8;\n    --ruby-7: #efacb8;\n    --ruby-8: #e592a3;\n    --ruby-9: #e54666;\n    --ruby-10: #dc3b5d;\n    --ruby-11: #ca244d;\n    --ruby-12: #64172b;\n    --ruby-a1: #ff005503;\n    --ruby-a2: #ff002008;\n    --ruby-a3: #f3002515;\n    --ruby-a4: #ff002523;\n    --ruby-a5: #ff002a31;\n    --ruby-a6: #e4002440;\n    --ruby-a7: #ce002553;\n    --ruby-a8: #c300286d;\n    --ruby-a9: #db002cb9;\n    --ruby-a10: #d2002cc4;\n    --ruby-a11: #c10030db;\n    --ruby-a12: #550016e8;\n    --crimson-1: #fffcfd;\n    --crimson-2: #fef7f9;\n    --crimson-3: #ffe9f0;\n    --crimson-4: #fedce7;\n    --crimson-5: #facedd;\n    --crimson-6: #f3bed1;\n    --crimson-7: #eaacc3;\n    --crimson-8: #e093b2;\n    --crimson-9: #e93d82;\n    --crimson-10: #df3478;\n    --crimson-11: #cb1d63;\n    --crimson-12: #621639;\n    --crimson-a1: #ff005503;\n    --crimson-a2: #e0004008;\n    --crimson-a3: #ff005216;\n    --crimson-a4: #f8005123;\n    --crimson-a5: #e5004f31;\n    --crimson-a6: #d0004b41;\n    --crimson-a7: #bf004753;\n    --crimson-a8: #b6004a6c;\n    --crimson-a9: #e2005bc2;\n    --crimson-a10: #d70056cb;\n    --crimson-a11: #c4004fe2;\n    --crimson-a12: #530026e9;\n    --pink-1: #fffcfe;\n    --pink-2: #fef7fb;\n    --pink-3: #fee9f5;\n    --pink-4: #fbdcef;\n    --pink-5: #f6cee7;\n    --pink-6: #efbfdd;\n    --pink-7: #e7acd0;\n    --pink-8: #dd93c2;\n    --pink-9: #d6409f;\n    --pink-10: #cf3897;\n    --pink-11: #c2298a;\n    --pink-12: #651249;\n    --pink-a1: #ff00aa03;\n    --pink-a2: #e0008008;\n    --pink-a3: #f4008c16;\n    --pink-a4: #e2008b23;\n    --pink-a5: #d1008331;\n    --pink-a6: #c0007840;\n    --pink-a7: #b6006f53;\n    --pink-a8: #af006f6c;\n    --pink-a9: #c8007fbf;\n    --pink-a10: #c2007ac7;\n    --pink-a11: #b60074d6;\n    --pink-a12: #59003bed;\n    --plum-1: #fefcff;\n    --plum-2: #fdf7fd;\n    --plum-3: #fbebfb;\n    --plum-4: #f7def8;\n    --plum-5: #f2d1f3;\n    --plum-6: #e9c2ec;\n    --plum-7: #deade3;\n    --plum-8: #cf91d8;\n    --plum-9: #ab4aba;\n    --plum-10: #a144af;\n    --plum-11: #953ea3;\n    --plum-12: #53195d;\n    --plum-a1: #aa00ff03;\n    --plum-a2: #c000c008;\n    --plum-a3: #cc00cc14;\n    --plum-a4: #c200c921;\n    --plum-a5: #b700bd2e;\n    --plum-a6: #a400b03d;\n    --plum-a7: #9900a852;\n    --plum-a8: #9000a56e;\n    --plum-a9: #89009eb5;\n    --plum-a10: #7f0092bb;\n    --plum-a11: #730086c1;\n    --plum-a12: #40004be6;\n    --purple-1: #fefcfe;\n    --purple-2: #fbf7fe;\n    --purple-3: #f7edfe;\n    --purple-4: #f2e2fc;\n    --purple-5: #ead5f9;\n    --purple-6: #e0c4f4;\n    --purple-7: #d1afec;\n    --purple-8: #be93e4;\n    --purple-9: #8e4ec6;\n    --purple-10: #8347b9;\n    --purple-11: #8145b5;\n    --purple-12: #402060;\n    --purple-a1: #aa00aa03;\n    --purple-a2: #8000e008;\n    --purple-a3: #8e00f112;\n    --purple-a4: #8d00e51d;\n    --purple-a5: #8000db2a;\n    --purple-a6: #7a01d03b;\n    --purple-a7: #6d00c350;\n    --purple-a8: #6600c06c;\n    --purple-a9: #5c00adb1;\n    --purple-a10: #53009eb8;\n    --purple-a11: #52009aba;\n    --purple-a12: #250049df;\n    --violet-1: #fdfcfe;\n    --violet-2: #faf8ff;\n    --violet-3: #f4f0fe;\n    --violet-4: #ebe4ff;\n    --violet-5: #e1d9ff;\n    --violet-6: #d4cafe;\n    --violet-7: #c2b5f5;\n    --violet-8: #aa99ec;\n    --violet-9: #6e56cf;\n    --violet-10: #654dc4;\n    --violet-11: #6550b9;\n    --violet-12: #2f265f;\n    --violet-a1: #5500aa03;\n    --violet-a2: #4900ff07;\n    --violet-a3: #4400ee0f;\n    --violet-a4: #4300ff1b;\n    --violet-a5: #3600ff26;\n    --violet-a6: #3100fb35;\n    --violet-a7: #2d01dd4a;\n    --violet-a8: #2b00d066;\n    --violet-a9: #2400b7a9;\n    --violet-a10: #2300abb2;\n    --violet-a11: #1f0099af;\n    --violet-a12: #0b0043d9;\n    --iris-1: #fdfdff;\n    --iris-2: #f8f8ff;\n    --iris-3: #f0f1fe;\n    --iris-4: #e6e7ff;\n    --iris-5: #dadcff;\n    --iris-6: #cbcdff;\n    --iris-7: #b8baf8;\n    --iris-8: #9b9ef0;\n    --iris-9: #5b5bd6;\n    --iris-10: #5151cd;\n    --iris-11: #5753c6;\n    --iris-12: #272962;\n    --iris-a1: #0000ff02;\n    --iris-a2: #0000ff07;\n    --iris-a3: #0011ee0f;\n    --iris-a4: #000bff19;\n    --iris-a5: #000eff25;\n    --iris-a6: #000aff34;\n    --iris-a7: #0008e647;\n    --iris-a8: #0008d964;\n    --iris-a9: #0000c0a4;\n    --iris-a10: #0000b6ae;\n    --iris-a11: #0600abac;\n    --iris-a12: #000246d8;\n    --cyan-1: #fafdfe;\n    --cyan-2: #f2fafb;\n    --cyan-3: #def7f9;\n    --cyan-4: #caf1f6;\n    --cyan-5: #b5e9f0;\n    --cyan-6: #9ddde7;\n    --cyan-7: #7dcedc;\n    --cyan-8: #3db9cf;\n    --cyan-9: #00a2c7;\n    --cyan-10: #0797b9;\n    --cyan-11: #107d98;\n    --cyan-12: #0d3c48;\n    --cyan-a1: #0099cc05;\n    --cyan-a2: #009db10d;\n    --cyan-a3: #00c2d121;\n    --cyan-a4: #00bcd435;\n    --cyan-a5: #01b4cc4a;\n    --cyan-a6: #00a7c162;\n    --cyan-a7: #009fbb82;\n    --cyan-a8: #00a3c0c2;\n    --cyan-a9: #00a2c7;\n    --cyan-a10: #0094b7f8;\n    --cyan-a11: #007491ef;\n    --cyan-a12: #00323ef2;\n    --teal-1: #fafefd;\n    --teal-2: #f3fbf9;\n    --teal-3: #e0f8f3;\n    --teal-4: #ccf3ea;\n    --teal-5: #b8eae0;\n    --teal-6: #a1ded2;\n    --teal-7: #83cdc1;\n    --teal-8: #53b9ab;\n    --teal-9: #12a594;\n    --teal-10: #0d9b8a;\n    --teal-11: #008573;\n    --teal-12: #0d3d38;\n    --teal-a1: #00cc9905;\n    --teal-a2: #00aa800c;\n    --teal-a3: #00c69d1f;\n    --teal-a4: #00c39633;\n    --teal-a5: #00b49047;\n    --teal-a6: #00a6855e;\n    --teal-a7: #0099807c;\n    --teal-a8: #009783ac;\n    --teal-a9: #009e8ced;\n    --teal-a10: #009684f2;\n    --teal-a11: #008573;\n    --teal-a12: #00332df2;\n    --jade-1: #fbfefd;\n    --jade-2: #f4fbf7;\n    --jade-3: #e6f7ed;\n    --jade-4: #d6f1e3;\n    --jade-5: #c3e9d7;\n    --jade-6: #acdec8;\n    --jade-7: #8bceb6;\n    --jade-8: #56ba9f;\n    --jade-9: #29a383;\n    --jade-10: #26997b;\n    --jade-11: #208368;\n    --jade-12: #1d3b31;\n    --jade-a1: #00c08004;\n    --jade-a2: #00a3460b;\n    --jade-a3: #00ae4819;\n    --jade-a4: #00a85129;\n    --jade-a5: #00a2553c;\n    --jade-a6: #009a5753;\n    --jade-a7: #00945f74;\n    --jade-a8: #00976ea9;\n    --jade-a9: #00916bd6;\n    --jade-a10: #008764d9;\n    --jade-a11: #007152df;\n    --jade-a12: #002217e2;\n    --green-1: #fbfefc;\n    --green-2: #f4fbf6;\n    --green-3: #e6f6eb;\n    --green-4: #d6f1df;\n    --green-5: #c4e8d1;\n    --green-6: #adddc0;\n    --green-7: #8eceaa;\n    --green-8: #5bb98b;\n    --green-9: #30a46c;\n    --green-10: #2b9a66;\n    --green-11: #218358;\n    --green-12: #193b2d;\n    --green-a1: #00c04004;\n    --green-a2: #00a32f0b;\n    --green-a3: #00a43319;\n    --green-a4: #00a83829;\n    --green-a5: #019c393b;\n    --green-a6: #00963c52;\n    --green-a7: #00914071;\n    --green-a8: #00924ba4;\n    --green-a9: #008f4acf;\n    --green-a10: #008647d4;\n    --green-a11: #00713fde;\n    --green-a12: #002616e6;\n    --grass-1: #fbfefb;\n    --grass-2: #f5fbf5;\n    --grass-3: #e9f6e9;\n    --grass-4: #daf1db;\n    --grass-5: #c9e8ca;\n    --grass-6: #b2ddb5;\n    --grass-7: #94ce9a;\n    --grass-8: #65ba74;\n    --grass-9: #46a758;\n    --grass-10: #3e9b4f;\n    --grass-11: #2a7e3b;\n    --grass-12: #203c25;\n    --grass-a1: #00c00004;\n    --grass-a2: #0099000a;\n    --grass-a3: #00970016;\n    --grass-a4: #009f0725;\n    --grass-a5: #00930536;\n    --grass-a6: #008f0a4d;\n    --grass-a7: #018b0f6b;\n    --grass-a8: #008d199a;\n    --grass-a9: #008619b9;\n    --grass-a10: #007b17c1;\n    --grass-a11: #006514d5;\n    --grass-a12: #002006df;\n    --brown-1: #fefdfc;\n    --brown-2: #fcf9f6;\n    --brown-3: #f6eee7;\n    --brown-4: #f0e4d9;\n    --brown-5: #ebdaca;\n    --brown-6: #e4cdb7;\n    --brown-7: #dcbc9f;\n    --brown-8: #cea37e;\n    --brown-9: #ad7f58;\n    --brown-10: #a07553;\n    --brown-11: #815e46;\n    --brown-12: #3e332e;\n    --brown-a1: #aa550003;\n    --brown-a2: #aa550009;\n    --brown-a3: #a04b0018;\n    --brown-a4: #9b4a0026;\n    --brown-a5: #9f4d0035;\n    --brown-a6: #a04e0048;\n    --brown-a7: #a34e0060;\n    --brown-a8: #9f4a0081;\n    --brown-a9: #823c00a7;\n    --brown-a10: #723300ac;\n    --brown-a11: #522100b9;\n    --brown-a12: #140600d1;\n    --sky-1: #f9feff;\n    --sky-2: #f1fafd;\n    --sky-3: #e1f6fd;\n    --sky-4: #d1f0fa;\n    --sky-5: #bee7f5;\n    --sky-6: #a9daed;\n    --sky-7: #8dcae3;\n    --sky-8: #60b3d7;\n    --sky-9: #7ce2fe;\n    --sky-10: #74daf8;\n    --sky-11: #00749e;\n    --sky-12: #1d3e56;\n    --sky-a1: #00d5ff06;\n    --sky-a2: #00a4db0e;\n    --sky-a3: #00b3ee1e;\n    --sky-a4: #00ace42e;\n    --sky-a5: #00a1d841;\n    --sky-a6: #0092ca56;\n    --sky-a7: #0089c172;\n    --sky-a8: #0085bf9f;\n    --sky-a9: #00c7fe83;\n    --sky-a10: #00bcf38b;\n    --sky-a11: #00749e;\n    --sky-a12: #002540e2;\n    --mint-1: #f9fefd;\n    --mint-2: #f2fbf9;\n    --mint-3: #ddf9f2;\n    --mint-4: #c8f4e9;\n    --mint-5: #b3ecde;\n    --mint-6: #9ce0d0;\n    --mint-7: #7ecfbd;\n    --mint-8: #4cbba5;\n    --mint-9: #86ead4;\n    --mint-10: #7de0cb;\n    --mint-11: #027864;\n    --mint-12: #16433c;\n    --mint-a1: #00d5aa06;\n    --mint-a2: #00b18a0d;\n    --mint-a3: #00d29e22;\n    --mint-a4: #00cc9937;\n    --mint-a5: #00c0914c;\n    --mint-a6: #00b08663;\n    --mint-a7: #00a17d81;\n    --mint-a8: #009e7fb3;\n    --mint-a9: #00d3a579;\n    --mint-a10: #00c39982;\n    --mint-a11: #007763fd;\n    --mint-a12: #00312ae9;\n    --yellow-1: #fdfdf9;\n    --yellow-2: #fefce9;\n    --yellow-3: #fffab8;\n    --yellow-4: #fff394;\n    --yellow-5: #ffe770;\n    --yellow-6: #f3d768;\n    --yellow-7: #e4c767;\n    --yellow-8: #d5ae39;\n    --yellow-9: #ffe629;\n    --yellow-10: #ffdc00;\n    --yellow-11: #9e6c00;\n    --yellow-12: #473b1f;\n    --yellow-a1: #aaaa0006;\n    --yellow-a2: #f4dd0016;\n    --yellow-a3: #ffee0047;\n    --yellow-a4: #ffe3016b;\n    --yellow-a5: #ffd5008f;\n    --yellow-a6: #ebbc0097;\n    --yellow-a7: #d2a10098;\n    --yellow-a8: #c99700c6;\n    --yellow-a9: #ffe100d6;\n    --yellow-a10: #ffdc00;\n    --yellow-a11: #9e6c00;\n    --yellow-a12: #2e2000e0;\n    --amber-1: #fefdfb;\n    --amber-2: #fefbe9;\n    --amber-3: #fff7c2;\n    --amber-4: #ffee9c;\n    --amber-5: #fbe577;\n    --amber-6: #f3d673;\n    --amber-7: #e9c162;\n    --amber-8: #e2a336;\n    --amber-9: #ffc53d;\n    --amber-10: #ffba18;\n    --amber-11: #ab6400;\n    --amber-12: #4f3422;\n    --amber-a1: #c0800004;\n    --amber-a2: #f4d10016;\n    --amber-a3: #ffde003d;\n    --amber-a4: #ffd40063;\n    --amber-a5: #f8cf0088;\n    --amber-a6: #eab5008c;\n    --amber-a7: #dc9b009d;\n    --amber-a8: #da8a00c9;\n    --amber-a9: #ffb300c2;\n    --amber-a10: #ffb300e7;\n    --amber-a11: #ab6400;\n    --amber-a12: #341500dd;\n    --gold-1: #fdfdfc;\n    --gold-2: #faf9f2;\n    --gold-3: #f2f0e7;\n    --gold-4: #eae6db;\n    --gold-5: #e1dccf;\n    --gold-6: #d8d0bf;\n    --gold-7: #cbc0aa;\n    --gold-8: #b9a88d;\n    --gold-9: #978365;\n    --gold-10: #8c7a5e;\n    --gold-11: #71624b;\n    --gold-12: #3b352b;\n    --gold-a1: #55550003;\n    --gold-a2: #9d8a000d;\n    --gold-a3: #75600018;\n    --gold-a4: #6b4e0024;\n    --gold-a5: #60460030;\n    --gold-a6: #64440040;\n    --gold-a7: #63420055;\n    --gold-a8: #633d0072;\n    --gold-a9: #5332009a;\n    --gold-a10: #492d00a1;\n    --gold-a11: #362100b4;\n    --gold-a12: #130c00d4;\n    --bronze-1: #fdfcfc;\n    --bronze-2: #fdf7f5;\n    --bronze-3: #f6edea;\n    --bronze-4: #efe4df;\n    --bronze-5: #e7d9d3;\n    --bronze-6: #dfcdc5;\n    --bronze-7: #d3bcb3;\n    --bronze-8: #c2a499;\n    --bronze-9: #a18072;\n    --bronze-10: #957468;\n    --bronze-11: #7d5e54;\n    --bronze-12: #43302b;\n    --bronze-a1: #55000003;\n    --bronze-a2: #cc33000a;\n    --bronze-a3: #92250015;\n    --bronze-a4: #80280020;\n    --bronze-a5: #7423002c;\n    --bronze-a6: #7324003a;\n    --bronze-a7: #6c1f004c;\n    --bronze-a8: #671c0066;\n    --bronze-a9: #551a008d;\n    --bronze-a10: #4c150097;\n    --bronze-a11: #3d0f00ab;\n    --bronze-a12: #1d0600d4;\n    --gray-1: #fcfcfc;\n    --gray-2: #f9f9f9;\n    --gray-3: #f0f0f0;\n    --gray-4: #e8e8e8;\n    --gray-5: #e0e0e0;\n    --gray-6: #d9d9d9;\n    --gray-7: #cecece;\n    --gray-8: #bbbbbb;\n    --gray-9: #8d8d8d;\n    --gray-10: #838383;\n    --gray-11: #646464;\n    --gray-12: #202020;\n    --gray-a1: #00000003;\n    --gray-a2: #00000006;\n    --gray-a3: #0000000f;\n    --gray-a4: #00000017;\n    --gray-a5: #0000001f;\n    --gray-a6: #00000026;\n    --gray-a7: #00000031;\n    --gray-a8: #00000044;\n    --gray-a9: #00000072;\n    --gray-a10: #0000007c;\n    --gray-a11: #0000009b;\n    --gray-a12: #000000df;\n    --mauve-1: #fdfcfd;\n    --mauve-2: #faf9fb;\n    --mauve-3: #f2eff3;\n    --mauve-4: #eae7ec;\n    --mauve-5: #e3dfe6;\n    --mauve-6: #dbd8e0;\n    --mauve-7: #d0cdd7;\n    --mauve-8: #bcbac7;\n    --mauve-9: #8e8c99;\n    --mauve-10: #84828e;\n    --mauve-11: #65636d;\n    --mauve-12: #211f26;\n    --mauve-a1: #55005503;\n    --mauve-a2: #2b005506;\n    --mauve-a3: #30004010;\n    --mauve-a4: #20003618;\n    --mauve-a5: #20003820;\n    --mauve-a6: #14003527;\n    --mauve-a7: #10003332;\n    --mauve-a8: #08003145;\n    --mauve-a9: #05001d73;\n    --mauve-a10: #0500197d;\n    --mauve-a11: #0400119c;\n    --mauve-a12: #020008e0;\n    --slate-1: #fcfcfd;\n    --slate-2: #f9f9fb;\n    --slate-3: #f0f0f3;\n    --slate-4: #e8e8ec;\n    --slate-5: #e0e1e6;\n    --slate-6: #d9d9e0;\n    --slate-7: #cdced6;\n    --slate-8: #b9bbc6;\n    --slate-9: #8b8d98;\n    --slate-10: #80838d;\n    --slate-11: #60646c;\n    --slate-12: #1c2024;\n    --slate-a1: #00005503;\n    --slate-a2: #00005506;\n    --slate-a3: #0000330f;\n    --slate-a4: #00002d17;\n    --slate-a5: #0009321f;\n    --slate-a6: #00002f26;\n    --slate-a7: #00062e32;\n    --slate-a8: #00083046;\n    --slate-a9: #00051d74;\n    --slate-a10: #00071b7f;\n    --slate-a11: #0007149f;\n    --slate-a12: #000509e3;\n    --sage-1: #fbfdfc;\n    --sage-2: #f7f9f8;\n    --sage-3: #eef1f0;\n    --sage-4: #e6e9e8;\n    --sage-5: #dfe2e0;\n    --sage-6: #d7dad9;\n    --sage-7: #cbcfcd;\n    --sage-8: #b8bcba;\n    --sage-9: #868e8b;\n    --sage-10: #7c8481;\n    --sage-11: #5f6563;\n    --sage-12: #1a211e;\n    --sage-a1: #00804004;\n    --sage-a2: #00402008;\n    --sage-a3: #002d1e11;\n    --sage-a4: #001f1519;\n    --sage-a5: #00180820;\n    --sage-a6: #00140d28;\n    --sage-a7: #00140a34;\n    --sage-a8: #000f0847;\n    --sage-a9: #00110b79;\n    --sage-a10: #00100a83;\n    --sage-a11: #000a07a0;\n    --sage-a12: #000805e5;\n    --olive-1: #fcfdfc;\n    --olive-2: #f8faf8;\n    --olive-3: #eff1ef;\n    --olive-4: #e7e9e7;\n    --olive-5: #dfe2df;\n    --olive-6: #d7dad7;\n    --olive-7: #cccfcc;\n    --olive-8: #b9bcb8;\n    --olive-9: #898e87;\n    --olive-10: #7f847d;\n    --olive-11: #60655f;\n    --olive-12: #1d211c;\n    --olive-a1: #00550003;\n    --olive-a2: #00490007;\n    --olive-a3: #00200010;\n    --olive-a4: #00160018;\n    --olive-a5: #00180020;\n    --olive-a6: #00140028;\n    --olive-a7: #000f0033;\n    --olive-a8: #040f0047;\n    --olive-a9: #050f0078;\n    --olive-a10: #040e0082;\n    --olive-a11: #020a00a0;\n    --olive-a12: #010600e3;\n    --sand-1: #fdfdfc;\n    --sand-2: #f9f9f8;\n    --sand-3: #f1f0ef;\n    --sand-4: #e9e8e6;\n    --sand-5: #e2e1de;\n    --sand-6: #dad9d6;\n    --sand-7: #cfceca;\n    --sand-8: #bcbbb5;\n    --sand-9: #8d8d86;\n    --sand-10: #82827c;\n    --sand-11: #63635e;\n    --sand-12: #21201c;\n    --sand-a1: #55550003;\n    --sand-a2: #25250007;\n    --sand-a3: #20100010;\n    --sand-a4: #1f150019;\n    --sand-a5: #1f180021;\n    --sand-a6: #19130029;\n    --sand-a7: #19140035;\n    --sand-a8: #1915014a;\n    --sand-a9: #0f0f0079;\n    --sand-a10: #0c0c0083;\n    --sand-a11: #080800a1;\n    --sand-a12: #060500e3;\n    --blue-1: #fdfdfe;\n    --blue-2: #f6faff;\n    --blue-3: #ebf2ff;\n    --blue-4: #ddeaff;\n    --blue-5: #cce0ff;\n    --blue-6: #b7d3ff;\n    --blue-7: #a0c0fd;\n    --blue-8: #7ea7f5;\n    --blue-9: #1754d8;\n    --blue-10: #0543c7;\n    --blue-11: #265ccf;\n    --blue-12: #162e5f;\n    --blue-a1: #00008002;\n    --blue-a2: #0072ff09;\n    --blue-a3: #005aff14;\n    --blue-a4: #0062ff22;\n    --blue-a5: #0064ff33;\n    --blue-a6: #0064ff48;\n    --blue-a7: #0056fa5f;\n    --blue-a8: #0052ec81;\n    --blue-a9: #0043d4e8;\n    --blue-a10: #003fc6fa;\n    --blue-a11: #0040c7d9;\n    --blue-a12: #001a50e9;\n    --orange-1: #fffcfb;\n    --orange-2: #fff7f4;\n    --orange-3: #ffeae4;\n    --orange-4: #ffd8cb;\n    --orange-5: #ffc9b8;\n    --orange-6: #ffb8a3;\n    --orange-7: #ffa38d;\n    --orange-8: #f7886e;\n    --orange-9: #fa4616;\n    --orange-10: #ec3400;\n    --orange-11: #dd2400;\n    --orange-12: #5f2518;\n    --orange-a1: #ff400004;\n    --orange-a2: #ff46000b;\n    --orange-a3: #ff39001b;\n    --orange-a4: #ff400034;\n    --orange-a5: #ff3e0047;\n    --orange-a6: #ff3b005c;\n    --orange-a7: #ff320072;\n    --orange-a8: #f12e0091;\n    --orange-a9: #fa3500e9;\n    --orange-a10: #ec3400;\n    --orange-a11: #dd2400;\n    --orange-a12: #4e0e00e7;\n    --lemon-1: #fcfdf9;\n    --lemon-2: #f9fcee;\n    --lemon-3: #f0fbc5;\n    --lemon-4: #e6f6a6;\n    --lemon-5: #dbed8a;\n    --lemon-6: #cdde7b;\n    --lemon-7: #bdcd6d;\n    --lemon-8: #a6b842;\n    --lemon-9: #d7f100;\n    --lemon-10: #cee610;\n    --lemon-11: #6f7d00;\n    --lemon-12: #3a401d;\n    --lemon-a1: #80aa0006;\n    --lemon-a2: #a5d20011;\n    --lemon-a3: #beee003a;\n    --lemon-a4: #b8e60059;\n    --lemon-a5: #b1d80075;\n    --lemon-a6: #9fc00084;\n    --lemon-a7: #8ca80092;\n    --lemon-a8: #879f00bd;\n    --lemon-a9: #d7f100;\n    --lemon-a10: #cbe400ef;\n    --lemon-a11: #6f7d00;\n    --lemon-a12: #212800e2;\n    --indigo-1: #fdfdff;\n    --indigo-2: #f7f8ff;\n    --indigo-3: #f0f0ff;\n    --indigo-4: #e5e5ff;\n    --indigo-5: #dadaff;\n    --indigo-6: #cbcbff;\n    --indigo-7: #b8b5ff;\n    --indigo-8: #9e95ff;\n    --indigo-9: #6318f8;\n    --indigo-10: #5800e6;\n    --indigo-11: #642ef1;\n    --indigo-12: #2f1978;\n    --indigo-a1: #0000ff02;\n    --indigo-a2: #0020ff08;\n    --indigo-a3: #0000ff0f;\n    --indigo-a4: #0000ff1a;\n    --indigo-a5: #0000ff25;\n    --indigo-a6: #0000ff34;\n    --indigo-a7: #0b01ff4a;\n    --indigo-a8: #1601ff6a;\n    --indigo-a9: #5300f7e7;\n    --indigo-a10: #5800e6;\n    --indigo-a11: #4200eed1;\n    --indigo-a12: #180069e6;\n    --lime-1: #fafefa;\n    --lime-2: #f4fcf3;\n    --lime-3: #dffbdc;\n    --lime-4: #caf8c6;\n    --lime-5: #b4f1af;\n    --lime-6: #9be696;\n    --lime-7: #7bd676;\n    --lime-8: #42c340;\n    --lime-9: #06d718;\n    --lime-10: #00cb00;\n    --lime-11: #008600;\n    --lime-12: #194318;\n    --lime-a1: #00cc0005;\n    --lime-a2: #16c0000c;\n    --lime-a3: #16e20023;\n    --lime-a4: #12e00039;\n    --lime-a5: #10d30050;\n    --lime-a6: #0dc30069;\n    --lime-a7: #0ab30089;\n    --lime-a8: #03af00bf;\n    --lime-a9: #00d612f9;\n    --lime-a10: #00cb00;\n    --lime-a11: #008600;\n    --lime-a12: #013000e7;\n    --magenta-1: #fffcfd;\n    --magenta-2: #fff6f9;\n    --magenta-3: #ffe7ef;\n    --magenta-4: #ffd9e6;\n    --magenta-5: #ffcadb;\n    --magenta-6: #fcbacf;\n    --magenta-7: #f4a6bf;\n    --magenta-8: #ec8cad;\n    --magenta-9: #ff008d;\n    --magenta-10: #ef0081;\n    --magenta-11: #d40070;\n    --magenta-12: #6b0037;\n    --magenta-a1: #ff005503;\n    --magenta-a2: #ff005509;\n    --magenta-a3: #ff005518;\n    --magenta-a4: #ff005826;\n    --magenta-a5: #ff005235;\n    --magenta-a6: #f4004e45;\n    --magenta-a7: #e0004859;\n    --magenta-a8: #d5004a73;\n    --magenta-a9: #ff008d;\n    --magenta-a10: #ef0081;\n    --magenta-a11: #d40070;\n    --magenta-a12: #6b0037;\n    --gray-surface: #ffffffcc;\n    --mauve-surface: #ffffffcc;\n    --slate-surface: #ffffffcc;\n    --sage-surface: #ffffffcc;\n    --olive-surface: #ffffffcc;\n    --sand-surface: #ffffffcc;\n    --tomato-surface: #fff6f5cc;\n    --red-surface: #fff5f5cc;\n    --ruby-surface: #fff5f6cc;\n    --crimson-surface: #fef5f8cc;\n    --pink-surface: #fef5facc;\n    --plum-surface: #fdf5fdcc;\n    --purple-surface: #faf5fecc;\n    --violet-surface: #f9f6ffcc;\n    --iris-surface: #f6f6ffcc;\n    --cyan-surface: #eff9facc;\n    --teal-surface: #f0faf8cc;\n    --jade-surface: #f1faf5cc;\n    --green-surface: #f1faf4cc;\n    --grass-surface: #f3faf3cc;\n    --brown-surface: #fbf8f4cc;\n    --bronze-surface: #fdf5f3cc;\n    --gold-surface: #f9f8efcc;\n    --sky-surface: #eef9fdcc;\n    --mint-surface: #effaf8cc;\n    --yellow-surface: #fefbe4cc;\n    --amber-surface: #fefae4cc;\n    --blue-surface: #f4f9ffcc;\n    --orange-surface: #fff5f1cc;\n    --indigo-surface: #f5f6ffcc;\n    --magenta-surface: #fff4f8cc;\n    --lemon-surface: #f8fbeacc;\n    --lime-surface: #f1fbf0cc;\n  }\n  @supports (color: color(display-p3 1 1 1)) {\n    @media (color-gamut: p3) {\n      :root, .light, .light-theme {\n        --tomato-1: color(display-p3 0.998 0.989 0.988);\n        --tomato-2: color(display-p3 0.994 0.974 0.969);\n        --tomato-3: color(display-p3 0.985 0.924 0.909);\n        --tomato-4: color(display-p3 0.996 0.868 0.835);\n        --tomato-5: color(display-p3 0.98 0.812 0.77);\n        --tomato-6: color(display-p3 0.953 0.75 0.698);\n        --tomato-7: color(display-p3 0.917 0.673 0.611);\n        --tomato-8: color(display-p3 0.875 0.575 0.502);\n        --tomato-9: color(display-p3 0.831 0.345 0.231);\n        --tomato-10: color(display-p3 0.802 0.313 0.2);\n        --tomato-11: color(display-p3 0.755 0.259 0.152);\n        --tomato-12: color(display-p3 0.335 0.165 0.132);\n        --tomato-a1: color(display-p3 0.675 0.024 0.024 / 0.012);\n        --tomato-a2: color(display-p3 0.757 0.145 0.02 / 0.032);\n        --tomato-a3: color(display-p3 0.831 0.184 0.012 / 0.091);\n        --tomato-a4: color(display-p3 0.976 0.192 0.004 / 0.165);\n        --tomato-a5: color(display-p3 0.918 0.192 0.004 / 0.232);\n        --tomato-a6: color(display-p3 0.847 0.173 0.004 / 0.302);\n        --tomato-a7: color(display-p3 0.788 0.165 0.004 / 0.389);\n        --tomato-a8: color(display-p3 0.749 0.153 0.004 / 0.499);\n        --tomato-a9: color(display-p3 0.78 0.149 0 / 0.769);\n        --tomato-a10: color(display-p3 0.757 0.141 0 / 0.8);\n        --tomato-a11: color(display-p3 0.755 0.259 0.152);\n        --tomato-a12: color(display-p3 0.335 0.165 0.132);\n        --red-1: color(display-p3 0.998 0.989 0.988);\n        --red-2: color(display-p3 0.995 0.971 0.971);\n        --red-3: color(display-p3 0.985 0.925 0.925);\n        --red-4: color(display-p3 0.999 0.866 0.866);\n        --red-5: color(display-p3 0.984 0.812 0.811);\n        --red-6: color(display-p3 0.955 0.751 0.749);\n        --red-7: color(display-p3 0.915 0.675 0.672);\n        --red-8: color(display-p3 0.872 0.575 0.572);\n        --red-9: color(display-p3 0.83 0.329 0.324);\n        --red-10: color(display-p3 0.798 0.294 0.285);\n        --red-11: color(display-p3 0.744 0.234 0.222);\n        --red-12: color(display-p3 0.36 0.115 0.143);\n        --red-a1: color(display-p3 0.675 0.024 0.024 / 0.012);\n        --red-a2: color(display-p3 0.863 0.024 0.024 / 0.028);\n        --red-a3: color(display-p3 0.792 0.008 0.008 / 0.075);\n        --red-a4: color(display-p3 1 0.008 0.008 / 0.134);\n        --red-a5: color(display-p3 0.918 0.008 0.008 / 0.189);\n        --red-a6: color(display-p3 0.831 0.02 0.004 / 0.251);\n        --red-a7: color(display-p3 0.741 0.016 0.004 / 0.33);\n        --red-a8: color(display-p3 0.698 0.012 0.004 / 0.428);\n        --red-a9: color(display-p3 0.749 0.008 0 / 0.675);\n        --red-a10: color(display-p3 0.714 0.012 0 / 0.714);\n        --red-a11: color(display-p3 0.744 0.234 0.222);\n        --red-a12: color(display-p3 0.36 0.115 0.143);\n        --ruby-1: color(display-p3 0.998 0.989 0.992);\n        --ruby-2: color(display-p3 0.995 0.971 0.974);\n        --ruby-3: color(display-p3 0.983 0.92 0.928);\n        --ruby-4: color(display-p3 0.987 0.869 0.885);\n        --ruby-5: color(display-p3 0.968 0.817 0.839);\n        --ruby-6: color(display-p3 0.937 0.758 0.786);\n        --ruby-7: color(display-p3 0.897 0.685 0.721);\n        --ruby-8: color(display-p3 0.851 0.588 0.639);\n        --ruby-9: color(display-p3 0.83 0.323 0.408);\n        --ruby-10: color(display-p3 0.795 0.286 0.375);\n        --ruby-11: color(display-p3 0.728 0.211 0.311);\n        --ruby-12: color(display-p3 0.36 0.115 0.171);\n        --ruby-a1: color(display-p3 0.675 0.024 0.349 / 0.012);\n        --ruby-a2: color(display-p3 0.863 0.024 0.024 / 0.028);\n        --ruby-a3: color(display-p3 0.804 0.008 0.11 / 0.079);\n        --ruby-a4: color(display-p3 0.91 0.008 0.125 / 0.13);\n        --ruby-a5: color(display-p3 0.831 0.004 0.133 / 0.185);\n        --ruby-a6: color(display-p3 0.745 0.004 0.118 / 0.244);\n        --ruby-a7: color(display-p3 0.678 0.004 0.114 / 0.314);\n        --ruby-a8: color(display-p3 0.639 0.004 0.125 / 0.412);\n        --ruby-a9: color(display-p3 0.753 0 0.129 / 0.679);\n        --ruby-a10: color(display-p3 0.714 0 0.125 / 0.714);\n        --ruby-a11: color(display-p3 0.728 0.211 0.311);\n        --ruby-a12: color(display-p3 0.36 0.115 0.171);\n        --crimson-1: color(display-p3 0.998 0.989 0.992);\n        --crimson-2: color(display-p3 0.991 0.969 0.976);\n        --crimson-3: color(display-p3 0.987 0.917 0.941);\n        --crimson-4: color(display-p3 0.975 0.866 0.904);\n        --crimson-5: color(display-p3 0.953 0.813 0.864);\n        --crimson-6: color(display-p3 0.921 0.755 0.817);\n        --crimson-7: color(display-p3 0.88 0.683 0.761);\n        --crimson-8: color(display-p3 0.834 0.592 0.694);\n        --crimson-9: color(display-p3 0.843 0.298 0.507);\n        --crimson-10: color(display-p3 0.807 0.266 0.468);\n        --crimson-11: color(display-p3 0.731 0.195 0.388);\n        --crimson-12: color(display-p3 0.352 0.111 0.221);\n        --crimson-a1: color(display-p3 0.675 0.024 0.349 / 0.012);\n        --crimson-a2: color(display-p3 0.757 0.02 0.267 / 0.032);\n        --crimson-a3: color(display-p3 0.859 0.008 0.294 / 0.083);\n        --crimson-a4: color(display-p3 0.827 0.008 0.298 / 0.134);\n        --crimson-a5: color(display-p3 0.753 0.008 0.275 / 0.189);\n        --crimson-a6: color(display-p3 0.682 0.004 0.247 / 0.244);\n        --crimson-a7: color(display-p3 0.62 0.004 0.251 / 0.318);\n        --crimson-a8: color(display-p3 0.6 0.004 0.251 / 0.408);\n        --crimson-a9: color(display-p3 0.776 0 0.298 / 0.702);\n        --crimson-a10: color(display-p3 0.737 0 0.275 / 0.734);\n        --crimson-a11: color(display-p3 0.731 0.195 0.388);\n        --crimson-a12: color(display-p3 0.352 0.111 0.221);\n        --pink-1: color(display-p3 0.998 0.989 0.996);\n        --pink-2: color(display-p3 0.992 0.97 0.985);\n        --pink-3: color(display-p3 0.981 0.917 0.96);\n        --pink-4: color(display-p3 0.963 0.867 0.932);\n        --pink-5: color(display-p3 0.939 0.815 0.899);\n        --pink-6: color(display-p3 0.907 0.756 0.859);\n        --pink-7: color(display-p3 0.869 0.683 0.81);\n        --pink-8: color(display-p3 0.825 0.59 0.751);\n        --pink-9: color(display-p3 0.775 0.297 0.61);\n        --pink-10: color(display-p3 0.748 0.27 0.581);\n        --pink-11: color(display-p3 0.698 0.219 0.528);\n        --pink-12: color(display-p3 0.363 0.101 0.279);\n        --pink-a1: color(display-p3 0.675 0.024 0.675 / 0.012);\n        --pink-a2: color(display-p3 0.757 0.02 0.51 / 0.032);\n        --pink-a3: color(display-p3 0.765 0.008 0.529 / 0.083);\n        --pink-a4: color(display-p3 0.737 0.008 0.506 / 0.134);\n        --pink-a5: color(display-p3 0.663 0.004 0.451 / 0.185);\n        --pink-a6: color(display-p3 0.616 0.004 0.424 / 0.244);\n        --pink-a7: color(display-p3 0.596 0.004 0.412 / 0.318);\n        --pink-a8: color(display-p3 0.573 0.004 0.404 / 0.412);\n        --pink-a9: color(display-p3 0.682 0 0.447 / 0.702);\n        --pink-a10: color(display-p3 0.655 0 0.424 / 0.73);\n        --pink-a11: color(display-p3 0.698 0.219 0.528);\n        --pink-a12: color(display-p3 0.363 0.101 0.279);\n        --plum-1: color(display-p3 0.995 0.988 0.999);\n        --plum-2: color(display-p3 0.988 0.971 0.99);\n        --plum-3: color(display-p3 0.973 0.923 0.98);\n        --plum-4: color(display-p3 0.953 0.875 0.966);\n        --plum-5: color(display-p3 0.926 0.825 0.945);\n        --plum-6: color(display-p3 0.89 0.765 0.916);\n        --plum-7: color(display-p3 0.84 0.686 0.877);\n        --plum-8: color(display-p3 0.775 0.58 0.832);\n        --plum-9: color(display-p3 0.624 0.313 0.708);\n        --plum-10: color(display-p3 0.587 0.29 0.667);\n        --plum-11: color(display-p3 0.543 0.263 0.619);\n        --plum-12: color(display-p3 0.299 0.114 0.352);\n        --plum-a1: color(display-p3 0.675 0.024 1 / 0.012);\n        --plum-a2: color(display-p3 0.58 0.024 0.58 / 0.028);\n        --plum-a3: color(display-p3 0.655 0.008 0.753 / 0.079);\n        --plum-a4: color(display-p3 0.627 0.008 0.722 / 0.126);\n        --plum-a5: color(display-p3 0.58 0.004 0.69 / 0.177);\n        --plum-a6: color(display-p3 0.537 0.004 0.655 / 0.236);\n        --plum-a7: color(display-p3 0.49 0.004 0.616 / 0.314);\n        --plum-a8: color(display-p3 0.471 0.004 0.6 / 0.42);\n        --plum-a9: color(display-p3 0.451 0 0.576 / 0.687);\n        --plum-a10: color(display-p3 0.42 0 0.529 / 0.71);\n        --plum-a11: color(display-p3 0.543 0.263 0.619);\n        --plum-a12: color(display-p3 0.299 0.114 0.352);\n        --purple-1: color(display-p3 0.995 0.988 0.996);\n        --purple-2: color(display-p3 0.983 0.971 0.993);\n        --purple-3: color(display-p3 0.963 0.931 0.989);\n        --purple-4: color(display-p3 0.937 0.888 0.981);\n        --purple-5: color(display-p3 0.904 0.837 0.966);\n        --purple-6: color(display-p3 0.86 0.774 0.942);\n        --purple-7: color(display-p3 0.799 0.69 0.91);\n        --purple-8: color(display-p3 0.719 0.583 0.874);\n        --purple-9: color(display-p3 0.523 0.318 0.751);\n        --purple-10: color(display-p3 0.483 0.289 0.7);\n        --purple-11: color(display-p3 0.473 0.281 0.687);\n        --purple-12: color(display-p3 0.234 0.132 0.363);\n        --purple-a1: color(display-p3 0.675 0.024 0.675 / 0.012);\n        --purple-a2: color(display-p3 0.443 0.024 0.722 / 0.028);\n        --purple-a3: color(display-p3 0.506 0.008 0.835 / 0.071);\n        --purple-a4: color(display-p3 0.451 0.004 0.831 / 0.114);\n        --purple-a5: color(display-p3 0.431 0.004 0.788 / 0.165);\n        --purple-a6: color(display-p3 0.384 0.004 0.745 / 0.228);\n        --purple-a7: color(display-p3 0.357 0.004 0.71 / 0.31);\n        --purple-a8: color(display-p3 0.322 0.004 0.702 / 0.416);\n        --purple-a9: color(display-p3 0.298 0 0.639 / 0.683);\n        --purple-a10: color(display-p3 0.271 0 0.58 / 0.71);\n        --purple-a11: color(display-p3 0.473 0.281 0.687);\n        --purple-a12: color(display-p3 0.234 0.132 0.363);\n        --violet-1: color(display-p3 0.991 0.988 0.995);\n        --violet-2: color(display-p3 0.978 0.974 0.998);\n        --violet-3: color(display-p3 0.953 0.943 0.993);\n        --violet-4: color(display-p3 0.916 0.897 1);\n        --violet-5: color(display-p3 0.876 0.851 1);\n        --violet-6: color(display-p3 0.825 0.793 0.981);\n        --violet-7: color(display-p3 0.752 0.712 0.943);\n        --violet-8: color(display-p3 0.654 0.602 0.902);\n        --violet-9: color(display-p3 0.417 0.341 0.784);\n        --violet-10: color(display-p3 0.381 0.306 0.741);\n        --violet-11: color(display-p3 0.383 0.317 0.702);\n        --violet-12: color(display-p3 0.179 0.15 0.359);\n        --violet-a1: color(display-p3 0.349 0.024 0.675 / 0.012);\n        --violet-a2: color(display-p3 0.161 0.024 0.863 / 0.028);\n        --violet-a3: color(display-p3 0.204 0.004 0.871 / 0.059);\n        --violet-a4: color(display-p3 0.196 0.004 1 / 0.102);\n        --violet-a5: color(display-p3 0.165 0.008 1 / 0.15);\n        --violet-a6: color(display-p3 0.153 0.004 0.906 / 0.208);\n        --violet-a7: color(display-p3 0.141 0.004 0.796 / 0.287);\n        --violet-a8: color(display-p3 0.133 0.004 0.753 / 0.397);\n        --violet-a9: color(display-p3 0.114 0 0.675 / 0.659);\n        --violet-a10: color(display-p3 0.11 0 0.627 / 0.695);\n        --violet-a11: color(display-p3 0.383 0.317 0.702);\n        --violet-a12: color(display-p3 0.179 0.15 0.359);\n        --iris-1: color(display-p3 0.992 0.992 0.999);\n        --iris-2: color(display-p3 0.972 0.973 0.998);\n        --iris-3: color(display-p3 0.943 0.945 0.992);\n        --iris-4: color(display-p3 0.902 0.906 1);\n        --iris-5: color(display-p3 0.857 0.861 1);\n        --iris-6: color(display-p3 0.799 0.805 0.987);\n        --iris-7: color(display-p3 0.721 0.727 0.955);\n        --iris-8: color(display-p3 0.61 0.619 0.918);\n        --iris-9: color(display-p3 0.357 0.357 0.81);\n        --iris-10: color(display-p3 0.318 0.318 0.774);\n        --iris-11: color(display-p3 0.337 0.326 0.748);\n        --iris-12: color(display-p3 0.154 0.161 0.371);\n        --iris-a1: color(display-p3 0.02 0.02 1 / 0.008);\n        --iris-a2: color(display-p3 0.024 0.024 0.863 / 0.028);\n        --iris-a3: color(display-p3 0.004 0.071 0.871 / 0.059);\n        --iris-a4: color(display-p3 0.012 0.051 1 / 0.099);\n        --iris-a5: color(display-p3 0.008 0.035 1 / 0.142);\n        --iris-a6: color(display-p3 0 0.02 0.941 / 0.2);\n        --iris-a7: color(display-p3 0.004 0.02 0.847 / 0.279);\n        --iris-a8: color(display-p3 0.004 0.024 0.788 / 0.389);\n        --iris-a9: color(display-p3 0 0 0.706 / 0.644);\n        --iris-a10: color(display-p3 0 0 0.667 / 0.683);\n        --iris-a11: color(display-p3 0.337 0.326 0.748);\n        --iris-a12: color(display-p3 0.154 0.161 0.371);\n        --cyan-1: color(display-p3 0.982 0.992 0.996);\n        --cyan-2: color(display-p3 0.955 0.981 0.984);\n        --cyan-3: color(display-p3 0.888 0.965 0.975);\n        --cyan-4: color(display-p3 0.821 0.941 0.959);\n        --cyan-5: color(display-p3 0.751 0.907 0.935);\n        --cyan-6: color(display-p3 0.671 0.862 0.9);\n        --cyan-7: color(display-p3 0.564 0.8 0.854);\n        --cyan-8: color(display-p3 0.388 0.715 0.798);\n        --cyan-9: color(display-p3 0.282 0.627 0.765);\n        --cyan-10: color(display-p3 0.264 0.583 0.71);\n        --cyan-11: color(display-p3 0.08 0.48 0.63);\n        --cyan-12: color(display-p3 0.108 0.232 0.277);\n        --cyan-a1: color(display-p3 0.02 0.608 0.804 / 0.02);\n        --cyan-a2: color(display-p3 0.02 0.557 0.647 / 0.044);\n        --cyan-a3: color(display-p3 0.004 0.694 0.796 / 0.114);\n        --cyan-a4: color(display-p3 0.004 0.678 0.784 / 0.181);\n        --cyan-a5: color(display-p3 0.004 0.624 0.733 / 0.248);\n        --cyan-a6: color(display-p3 0.004 0.584 0.706 / 0.33);\n        --cyan-a7: color(display-p3 0.004 0.541 0.667 / 0.436);\n        --cyan-a8: color(display-p3 0 0.533 0.667 / 0.612);\n        --cyan-a9: color(display-p3 0 0.482 0.675 / 0.718);\n        --cyan-a10: color(display-p3 0 0.435 0.608 / 0.738);\n        --cyan-a11: color(display-p3 0.08 0.48 0.63);\n        --cyan-a12: color(display-p3 0.108 0.232 0.277);\n        --teal-1: color(display-p3 0.983 0.996 0.992);\n        --teal-2: color(display-p3 0.958 0.983 0.976);\n        --teal-3: color(display-p3 0.895 0.971 0.952);\n        --teal-4: color(display-p3 0.831 0.949 0.92);\n        --teal-5: color(display-p3 0.761 0.914 0.878);\n        --teal-6: color(display-p3 0.682 0.864 0.825);\n        --teal-7: color(display-p3 0.581 0.798 0.756);\n        --teal-8: color(display-p3 0.433 0.716 0.671);\n        --teal-9: color(display-p3 0.297 0.637 0.581);\n        --teal-10: color(display-p3 0.275 0.599 0.542);\n        --teal-11: color(display-p3 0.08 0.5 0.43);\n        --teal-12: color(display-p3 0.11 0.235 0.219);\n        --teal-a1: color(display-p3 0.024 0.757 0.514 / 0.016);\n        --teal-a2: color(display-p3 0.02 0.647 0.467 / 0.044);\n        --teal-a3: color(display-p3 0.004 0.741 0.557 / 0.106);\n        --teal-a4: color(display-p3 0.004 0.702 0.537 / 0.169);\n        --teal-a5: color(display-p3 0.004 0.643 0.494 / 0.24);\n        --teal-a6: color(display-p3 0.004 0.569 0.447 / 0.318);\n        --teal-a7: color(display-p3 0.004 0.518 0.424 / 0.42);\n        --teal-a8: color(display-p3 0 0.506 0.424 / 0.569);\n        --teal-a9: color(display-p3 0 0.482 0.404 / 0.702);\n        --teal-a10: color(display-p3 0 0.451 0.369 / 0.726);\n        --teal-a11: color(display-p3 0.08 0.5 0.43);\n        --teal-a12: color(display-p3 0.11 0.235 0.219);\n        --jade-1: color(display-p3 0.986 0.996 0.992);\n        --jade-2: color(display-p3 0.962 0.983 0.969);\n        --jade-3: color(display-p3 0.912 0.965 0.932);\n        --jade-4: color(display-p3 0.858 0.941 0.893);\n        --jade-5: color(display-p3 0.795 0.909 0.847);\n        --jade-6: color(display-p3 0.715 0.864 0.791);\n        --jade-7: color(display-p3 0.603 0.802 0.718);\n        --jade-8: color(display-p3 0.44 0.72 0.629);\n        --jade-9: color(display-p3 0.319 0.63 0.521);\n        --jade-10: color(display-p3 0.299 0.592 0.488);\n        --jade-11: color(display-p3 0.15 0.5 0.37);\n        --jade-12: color(display-p3 0.142 0.229 0.194);\n        --jade-a1: color(display-p3 0.024 0.757 0.514 / 0.016);\n        --jade-a2: color(display-p3 0.024 0.612 0.22 / 0.04);\n        --jade-a3: color(display-p3 0.012 0.596 0.235 / 0.087);\n        --jade-a4: color(display-p3 0.008 0.588 0.255 / 0.142);\n        --jade-a5: color(display-p3 0.004 0.561 0.251 / 0.204);\n        --jade-a6: color(display-p3 0.004 0.525 0.278 / 0.287);\n        --jade-a7: color(display-p3 0.004 0.506 0.29 / 0.397);\n        --jade-a8: color(display-p3 0 0.506 0.337 / 0.561);\n        --jade-a9: color(display-p3 0 0.459 0.298 / 0.683);\n        --jade-a10: color(display-p3 0 0.42 0.271 / 0.702);\n        --jade-a11: color(display-p3 0.15 0.5 0.37);\n        --jade-a12: color(display-p3 0.142 0.229 0.194);\n        --green-1: color(display-p3 0.986 0.996 0.989);\n        --green-2: color(display-p3 0.963 0.983 0.967);\n        --green-3: color(display-p3 0.913 0.964 0.925);\n        --green-4: color(display-p3 0.859 0.94 0.879);\n        --green-5: color(display-p3 0.796 0.907 0.826);\n        --green-6: color(display-p3 0.718 0.863 0.761);\n        --green-7: color(display-p3 0.61 0.801 0.675);\n        --green-8: color(display-p3 0.451 0.715 0.559);\n        --green-9: color(display-p3 0.332 0.634 0.442);\n        --green-10: color(display-p3 0.308 0.595 0.417);\n        --green-11: color(display-p3 0.19 0.5 0.32);\n        --green-12: color(display-p3 0.132 0.228 0.18);\n        --green-a1: color(display-p3 0.024 0.757 0.267 / 0.016);\n        --green-a2: color(display-p3 0.024 0.565 0.129 / 0.036);\n        --green-a3: color(display-p3 0.012 0.596 0.145 / 0.087);\n        --green-a4: color(display-p3 0.008 0.588 0.145 / 0.142);\n        --green-a5: color(display-p3 0.004 0.541 0.157 / 0.204);\n        --green-a6: color(display-p3 0.004 0.518 0.157 / 0.283);\n        --green-a7: color(display-p3 0.004 0.486 0.165 / 0.389);\n        --green-a8: color(display-p3 0 0.478 0.2 / 0.55);\n        --green-a9: color(display-p3 0 0.455 0.165 / 0.667);\n        --green-a10: color(display-p3 0 0.416 0.153 / 0.691);\n        --green-a11: color(display-p3 0.19 0.5 0.32);\n        --green-a12: color(display-p3 0.132 0.228 0.18);\n        --grass-1: color(display-p3 0.986 0.996 0.985);\n        --grass-2: color(display-p3 0.966 0.983 0.964);\n        --grass-3: color(display-p3 0.923 0.965 0.917);\n        --grass-4: color(display-p3 0.872 0.94 0.865);\n        --grass-5: color(display-p3 0.811 0.908 0.802);\n        --grass-6: color(display-p3 0.733 0.864 0.724);\n        --grass-7: color(display-p3 0.628 0.803 0.622);\n        --grass-8: color(display-p3 0.477 0.72 0.482);\n        --grass-9: color(display-p3 0.38 0.647 0.378);\n        --grass-10: color(display-p3 0.344 0.598 0.342);\n        --grass-11: color(display-p3 0.263 0.488 0.261);\n        --grass-12: color(display-p3 0.151 0.233 0.153);\n        --grass-a1: color(display-p3 0.024 0.757 0.024 / 0.016);\n        --grass-a2: color(display-p3 0.024 0.565 0.024 / 0.036);\n        --grass-a3: color(display-p3 0.059 0.576 0.008 / 0.083);\n        --grass-a4: color(display-p3 0.035 0.565 0.008 / 0.134);\n        --grass-a5: color(display-p3 0.047 0.545 0.008 / 0.197);\n        --grass-a6: color(display-p3 0.031 0.502 0.004 / 0.275);\n        --grass-a7: color(display-p3 0.012 0.482 0.004 / 0.377);\n        --grass-a8: color(display-p3 0 0.467 0.008 / 0.522);\n        --grass-a9: color(display-p3 0.008 0.435 0 / 0.624);\n        --grass-a10: color(display-p3 0.008 0.388 0 / 0.659);\n        --grass-a11: color(display-p3 0.263 0.488 0.261);\n        --grass-a12: color(display-p3 0.151 0.233 0.153);\n        --brown-1: color(display-p3 0.995 0.992 0.989);\n        --brown-2: color(display-p3 0.987 0.976 0.964);\n        --brown-3: color(display-p3 0.959 0.936 0.909);\n        --brown-4: color(display-p3 0.934 0.897 0.855);\n        --brown-5: color(display-p3 0.909 0.856 0.798);\n        --brown-6: color(display-p3 0.88 0.808 0.73);\n        --brown-7: color(display-p3 0.841 0.742 0.639);\n        --brown-8: color(display-p3 0.782 0.647 0.514);\n        --brown-9: color(display-p3 0.651 0.505 0.368);\n        --brown-10: color(display-p3 0.601 0.465 0.344);\n        --brown-11: color(display-p3 0.485 0.374 0.288);\n        --brown-12: color(display-p3 0.236 0.202 0.183);\n        --brown-a1: color(display-p3 0.675 0.349 0.024 / 0.012);\n        --brown-a2: color(display-p3 0.675 0.349 0.024 / 0.036);\n        --brown-a3: color(display-p3 0.573 0.314 0.012 / 0.091);\n        --brown-a4: color(display-p3 0.545 0.302 0.008 / 0.146);\n        --brown-a5: color(display-p3 0.561 0.29 0.004 / 0.204);\n        --brown-a6: color(display-p3 0.553 0.294 0.004 / 0.271);\n        --brown-a7: color(display-p3 0.557 0.286 0.004 / 0.361);\n        --brown-a8: color(display-p3 0.549 0.275 0.004 / 0.487);\n        --brown-a9: color(display-p3 0.447 0.22 0 / 0.632);\n        --brown-a10: color(display-p3 0.388 0.188 0 / 0.655);\n        --brown-a11: color(display-p3 0.485 0.374 0.288);\n        --brown-a12: color(display-p3 0.236 0.202 0.183);\n        --sky-1: color(display-p3 0.98 0.995 0.999);\n        --sky-2: color(display-p3 0.953 0.98 0.99);\n        --sky-3: color(display-p3 0.899 0.963 0.989);\n        --sky-4: color(display-p3 0.842 0.937 0.977);\n        --sky-5: color(display-p3 0.777 0.9 0.954);\n        --sky-6: color(display-p3 0.701 0.851 0.921);\n        --sky-7: color(display-p3 0.604 0.785 0.879);\n        --sky-8: color(display-p3 0.457 0.696 0.829);\n        --sky-9: color(display-p3 0.585 0.877 0.983);\n        --sky-10: color(display-p3 0.555 0.845 0.959);\n        --sky-11: color(display-p3 0.193 0.448 0.605);\n        --sky-12: color(display-p3 0.145 0.241 0.329);\n        --sky-a1: color(display-p3 0.02 0.804 1 / 0.02);\n        --sky-a2: color(display-p3 0.024 0.592 0.757 / 0.048);\n        --sky-a3: color(display-p3 0.004 0.655 0.886 / 0.102);\n        --sky-a4: color(display-p3 0.004 0.604 0.851 / 0.157);\n        --sky-a5: color(display-p3 0.004 0.565 0.792 / 0.224);\n        --sky-a6: color(display-p3 0.004 0.502 0.737 / 0.299);\n        --sky-a7: color(display-p3 0.004 0.459 0.694 / 0.397);\n        --sky-a8: color(display-p3 0 0.435 0.682 / 0.542);\n        --sky-a9: color(display-p3 0.004 0.71 0.965 / 0.416);\n        --sky-a10: color(display-p3 0.004 0.647 0.914 / 0.444);\n        --sky-a11: color(display-p3 0.193 0.448 0.605);\n        --sky-a12: color(display-p3 0.145 0.241 0.329);\n        --mint-1: color(display-p3 0.98 0.995 0.992);\n        --mint-2: color(display-p3 0.957 0.985 0.977);\n        --mint-3: color(display-p3 0.888 0.972 0.95);\n        --mint-4: color(display-p3 0.819 0.951 0.916);\n        --mint-5: color(display-p3 0.747 0.918 0.873);\n        --mint-6: color(display-p3 0.668 0.87 0.818);\n        --mint-7: color(display-p3 0.567 0.805 0.744);\n        --mint-8: color(display-p3 0.42 0.724 0.649);\n        --mint-9: color(display-p3 0.62 0.908 0.834);\n        --mint-10: color(display-p3 0.585 0.871 0.797);\n        --mint-11: color(display-p3 0.203 0.463 0.397);\n        --mint-12: color(display-p3 0.136 0.259 0.236);\n        --mint-a1: color(display-p3 0.02 0.804 0.608 / 0.02);\n        --mint-a2: color(display-p3 0.02 0.647 0.467 / 0.044);\n        --mint-a3: color(display-p3 0.004 0.761 0.553 / 0.114);\n        --mint-a4: color(display-p3 0.004 0.741 0.545 / 0.181);\n        --mint-a5: color(display-p3 0.004 0.678 0.51 / 0.255);\n        --mint-a6: color(display-p3 0.004 0.616 0.463 / 0.334);\n        --mint-a7: color(display-p3 0.004 0.549 0.412 / 0.432);\n        --mint-a8: color(display-p3 0 0.529 0.392 / 0.581);\n        --mint-a9: color(display-p3 0.004 0.765 0.569 / 0.381);\n        --mint-a10: color(display-p3 0.004 0.69 0.51 / 0.416);\n        --mint-a11: color(display-p3 0.203 0.463 0.397);\n        --mint-a12: color(display-p3 0.136 0.259 0.236);\n        --yellow-1: color(display-p3 0.992 0.992 0.978);\n        --yellow-2: color(display-p3 0.995 0.99 0.922);\n        --yellow-3: color(display-p3 0.997 0.982 0.749);\n        --yellow-4: color(display-p3 0.992 0.953 0.627);\n        --yellow-5: color(display-p3 0.984 0.91 0.51);\n        --yellow-6: color(display-p3 0.934 0.847 0.474);\n        --yellow-7: color(display-p3 0.876 0.785 0.46);\n        --yellow-8: color(display-p3 0.811 0.689 0.313);\n        --yellow-9: color(display-p3 1 0.92 0.22);\n        --yellow-10: color(display-p3 0.977 0.868 0.291);\n        --yellow-11: color(display-p3 0.6 0.44 0);\n        --yellow-12: color(display-p3 0.271 0.233 0.137);\n        --yellow-a1: color(display-p3 0.675 0.675 0.024 / 0.024);\n        --yellow-a2: color(display-p3 0.953 0.855 0.008 / 0.079);\n        --yellow-a3: color(display-p3 0.988 0.925 0.004 / 0.251);\n        --yellow-a4: color(display-p3 0.98 0.875 0.004 / 0.373);\n        --yellow-a5: color(display-p3 0.969 0.816 0.004 / 0.491);\n        --yellow-a6: color(display-p3 0.875 0.71 0 / 0.526);\n        --yellow-a7: color(display-p3 0.769 0.604 0 / 0.542);\n        --yellow-a8: color(display-p3 0.725 0.549 0 / 0.687);\n        --yellow-a9: color(display-p3 1 0.898 0 / 0.781);\n        --yellow-a10: color(display-p3 0.969 0.812 0 / 0.71);\n        --yellow-a11: color(display-p3 0.6 0.44 0);\n        --yellow-a12: color(display-p3 0.271 0.233 0.137);\n        --amber-1: color(display-p3 0.995 0.992 0.985);\n        --amber-2: color(display-p3 0.994 0.986 0.921);\n        --amber-3: color(display-p3 0.994 0.969 0.782);\n        --amber-4: color(display-p3 0.989 0.937 0.65);\n        --amber-5: color(display-p3 0.97 0.902 0.527);\n        --amber-6: color(display-p3 0.936 0.844 0.506);\n        --amber-7: color(display-p3 0.89 0.762 0.443);\n        --amber-8: color(display-p3 0.85 0.65 0.3);\n        --amber-9: color(display-p3 1 0.77 0.26);\n        --amber-10: color(display-p3 0.959 0.741 0.274);\n        --amber-11: color(display-p3 0.64 0.4 0);\n        --amber-12: color(display-p3 0.294 0.208 0.145);\n        --amber-a1: color(display-p3 0.757 0.514 0.024 / 0.016);\n        --amber-a2: color(display-p3 0.902 0.804 0.008 / 0.079);\n        --amber-a3: color(display-p3 0.965 0.859 0.004 / 0.22);\n        --amber-a4: color(display-p3 0.969 0.82 0.004 / 0.35);\n        --amber-a5: color(display-p3 0.933 0.796 0.004 / 0.475);\n        --amber-a6: color(display-p3 0.875 0.682 0.004 / 0.495);\n        --amber-a7: color(display-p3 0.804 0.573 0 / 0.557);\n        --amber-a8: color(display-p3 0.788 0.502 0 / 0.699);\n        --amber-a9: color(display-p3 1 0.686 0 / 0.742);\n        --amber-a10: color(display-p3 0.945 0.643 0 / 0.726);\n        --amber-a11: color(display-p3 0.64 0.4 0);\n        --amber-a12: color(display-p3 0.294 0.208 0.145);\n        --gold-1: color(display-p3 0.992 0.992 0.989);\n        --gold-2: color(display-p3 0.98 0.976 0.953);\n        --gold-3: color(display-p3 0.947 0.94 0.909);\n        --gold-4: color(display-p3 0.914 0.904 0.865);\n        --gold-5: color(display-p3 0.88 0.865 0.816);\n        --gold-6: color(display-p3 0.84 0.818 0.756);\n        --gold-7: color(display-p3 0.788 0.753 0.677);\n        --gold-8: color(display-p3 0.715 0.66 0.565);\n        --gold-9: color(display-p3 0.579 0.517 0.41);\n        --gold-10: color(display-p3 0.538 0.479 0.38);\n        --gold-11: color(display-p3 0.433 0.386 0.305);\n        --gold-12: color(display-p3 0.227 0.209 0.173);\n        --gold-a1: color(display-p3 0.349 0.349 0.024 / 0.012);\n        --gold-a2: color(display-p3 0.592 0.514 0.024 / 0.048);\n        --gold-a3: color(display-p3 0.4 0.357 0.012 / 0.091);\n        --gold-a4: color(display-p3 0.357 0.298 0.008 / 0.134);\n        --gold-a5: color(display-p3 0.345 0.282 0.004 / 0.185);\n        --gold-a6: color(display-p3 0.341 0.263 0.004 / 0.244);\n        --gold-a7: color(display-p3 0.345 0.235 0.004 / 0.322);\n        --gold-a8: color(display-p3 0.345 0.22 0.004 / 0.436);\n        --gold-a9: color(display-p3 0.286 0.18 0 / 0.589);\n        --gold-a10: color(display-p3 0.255 0.161 0 / 0.62);\n        --gold-a11: color(display-p3 0.433 0.386 0.305);\n        --gold-a12: color(display-p3 0.227 0.209 0.173);\n        --bronze-1: color(display-p3 0.991 0.988 0.988);\n        --bronze-2: color(display-p3 0.989 0.97 0.961);\n        --bronze-3: color(display-p3 0.958 0.932 0.919);\n        --bronze-4: color(display-p3 0.929 0.894 0.877);\n        --bronze-5: color(display-p3 0.898 0.853 0.832);\n        --bronze-6: color(display-p3 0.861 0.805 0.778);\n        --bronze-7: color(display-p3 0.812 0.739 0.706);\n        --bronze-8: color(display-p3 0.741 0.647 0.606);\n        --bronze-9: color(display-p3 0.611 0.507 0.455);\n        --bronze-10: color(display-p3 0.563 0.461 0.414);\n        --bronze-11: color(display-p3 0.471 0.373 0.336);\n        --bronze-12: color(display-p3 0.251 0.191 0.172);\n        --bronze-a1: color(display-p3 0.349 0.024 0.024 / 0.012);\n        --bronze-a2: color(display-p3 0.71 0.22 0.024 / 0.04);\n        --bronze-a3: color(display-p3 0.482 0.2 0.008 / 0.083);\n        --bronze-a4: color(display-p3 0.424 0.133 0.004 / 0.122);\n        --bronze-a5: color(display-p3 0.4 0.145 0.004 / 0.169);\n        --bronze-a6: color(display-p3 0.388 0.125 0.004 / 0.224);\n        --bronze-a7: color(display-p3 0.365 0.11 0.004 / 0.295);\n        --bronze-a8: color(display-p3 0.341 0.102 0.004 / 0.393);\n        --bronze-a9: color(display-p3 0.29 0.094 0 / 0.546);\n        --bronze-a10: color(display-p3 0.255 0.082 0 / 0.585);\n        --bronze-a11: color(display-p3 0.471 0.373 0.336);\n        --bronze-a12: color(display-p3 0.251 0.191 0.172);\n        --gray-1: color(display-p3 0.988 0.988 0.988);\n        --gray-2: color(display-p3 0.975 0.975 0.975);\n        --gray-3: color(display-p3 0.939 0.939 0.939);\n        --gray-4: color(display-p3 0.908 0.908 0.908);\n        --gray-5: color(display-p3 0.88 0.88 0.88);\n        --gray-6: color(display-p3 0.849 0.849 0.849);\n        --gray-7: color(display-p3 0.807 0.807 0.807);\n        --gray-8: color(display-p3 0.732 0.732 0.732);\n        --gray-9: color(display-p3 0.553 0.553 0.553);\n        --gray-10: color(display-p3 0.512 0.512 0.512);\n        --gray-11: color(display-p3 0.392 0.392 0.392);\n        --gray-12: color(display-p3 0.125 0.125 0.125);\n        --gray-a1: color(display-p3 0 0 0 / 0.012);\n        --gray-a2: color(display-p3 0 0 0 / 0.024);\n        --gray-a3: color(display-p3 0 0 0 / 0.063);\n        --gray-a4: color(display-p3 0 0 0 / 0.09);\n        --gray-a5: color(display-p3 0 0 0 / 0.122);\n        --gray-a6: color(display-p3 0 0 0 / 0.153);\n        --gray-a7: color(display-p3 0 0 0 / 0.192);\n        --gray-a8: color(display-p3 0 0 0 / 0.267);\n        --gray-a9: color(display-p3 0 0 0 / 0.447);\n        --gray-a10: color(display-p3 0 0 0 / 0.486);\n        --gray-a11: color(display-p3 0 0 0 / 0.608);\n        --gray-a12: color(display-p3 0 0 0 / 0.875);\n        --mauve-1: color(display-p3 0.991 0.988 0.992);\n        --mauve-2: color(display-p3 0.98 0.976 0.984);\n        --mauve-3: color(display-p3 0.946 0.938 0.952);\n        --mauve-4: color(display-p3 0.915 0.906 0.925);\n        --mauve-5: color(display-p3 0.886 0.876 0.901);\n        --mauve-6: color(display-p3 0.856 0.846 0.875);\n        --mauve-7: color(display-p3 0.814 0.804 0.84);\n        --mauve-8: color(display-p3 0.735 0.728 0.777);\n        --mauve-9: color(display-p3 0.555 0.549 0.596);\n        --mauve-10: color(display-p3 0.514 0.508 0.552);\n        --mauve-11: color(display-p3 0.395 0.388 0.424);\n        --mauve-12: color(display-p3 0.128 0.122 0.147);\n        --mauve-a1: color(display-p3 0.349 0.024 0.349 / 0.012);\n        --mauve-a2: color(display-p3 0.184 0.024 0.349 / 0.024);\n        --mauve-a3: color(display-p3 0.129 0.008 0.255 / 0.063);\n        --mauve-a4: color(display-p3 0.094 0.012 0.216 / 0.095);\n        --mauve-a5: color(display-p3 0.098 0.008 0.224 / 0.126);\n        --mauve-a6: color(display-p3 0.055 0.004 0.18 / 0.153);\n        --mauve-a7: color(display-p3 0.067 0.008 0.184 / 0.197);\n        --mauve-a8: color(display-p3 0.02 0.004 0.176 / 0.271);\n        --mauve-a9: color(display-p3 0.02 0.004 0.106 / 0.451);\n        --mauve-a10: color(display-p3 0.012 0.004 0.09 / 0.491);\n        --mauve-a11: color(display-p3 0.016 0 0.059 / 0.612);\n        --mauve-a12: color(display-p3 0.008 0 0.027 / 0.879);\n        --slate-1: color(display-p3 0.988 0.988 0.992);\n        --slate-2: color(display-p3 0.976 0.976 0.984);\n        --slate-3: color(display-p3 0.94 0.941 0.953);\n        --slate-4: color(display-p3 0.908 0.909 0.925);\n        --slate-5: color(display-p3 0.88 0.881 0.901);\n        --slate-6: color(display-p3 0.85 0.852 0.876);\n        --slate-7: color(display-p3 0.805 0.808 0.838);\n        --slate-8: color(display-p3 0.727 0.733 0.773);\n        --slate-9: color(display-p3 0.547 0.553 0.592);\n        --slate-10: color(display-p3 0.503 0.512 0.549);\n        --slate-11: color(display-p3 0.379 0.392 0.421);\n        --slate-12: color(display-p3 0.113 0.125 0.14);\n        --slate-a1: color(display-p3 0.024 0.024 0.349 / 0.012);\n        --slate-a2: color(display-p3 0.024 0.024 0.349 / 0.024);\n        --slate-a3: color(display-p3 0.004 0.004 0.204 / 0.059);\n        --slate-a4: color(display-p3 0.012 0.012 0.184 / 0.091);\n        --slate-a5: color(display-p3 0.004 0.039 0.2 / 0.122);\n        --slate-a6: color(display-p3 0.008 0.008 0.165 / 0.15);\n        --slate-a7: color(display-p3 0.008 0.027 0.184 / 0.197);\n        --slate-a8: color(display-p3 0.004 0.031 0.176 / 0.275);\n        --slate-a9: color(display-p3 0.004 0.02 0.106 / 0.455);\n        --slate-a10: color(display-p3 0.004 0.027 0.098 / 0.499);\n        --slate-a11: color(display-p3 0 0.02 0.063 / 0.62);\n        --slate-a12: color(display-p3 0 0.012 0.031 / 0.887);\n        --sage-1: color(display-p3 0.986 0.992 0.988);\n        --sage-2: color(display-p3 0.97 0.977 0.974);\n        --sage-3: color(display-p3 0.935 0.944 0.94);\n        --sage-4: color(display-p3 0.904 0.913 0.909);\n        --sage-5: color(display-p3 0.875 0.885 0.88);\n        --sage-6: color(display-p3 0.844 0.854 0.849);\n        --sage-7: color(display-p3 0.8 0.811 0.806);\n        --sage-8: color(display-p3 0.725 0.738 0.732);\n        --sage-9: color(display-p3 0.531 0.556 0.546);\n        --sage-10: color(display-p3 0.492 0.515 0.506);\n        --sage-11: color(display-p3 0.377 0.395 0.389);\n        --sage-12: color(display-p3 0.107 0.129 0.118);\n        --sage-a1: color(display-p3 0.024 0.514 0.267 / 0.016);\n        --sage-a2: color(display-p3 0.02 0.267 0.145 / 0.032);\n        --sage-a3: color(display-p3 0.008 0.184 0.125 / 0.067);\n        --sage-a4: color(display-p3 0.012 0.094 0.051 / 0.095);\n        --sage-a5: color(display-p3 0.008 0.098 0.035 / 0.126);\n        --sage-a6: color(display-p3 0.004 0.078 0.027 / 0.157);\n        --sage-a7: color(display-p3 0 0.059 0.039 / 0.2);\n        --sage-a8: color(display-p3 0.004 0.047 0.031 / 0.275);\n        --sage-a9: color(display-p3 0.004 0.059 0.035 / 0.471);\n        --sage-a10: color(display-p3 0 0.047 0.031 / 0.51);\n        --sage-a11: color(display-p3 0 0.031 0.02 / 0.624);\n        --sage-a12: color(display-p3 0 0.027 0.012 / 0.895);\n        --olive-1: color(display-p3 0.989 0.992 0.989);\n        --olive-2: color(display-p3 0.974 0.98 0.973);\n        --olive-3: color(display-p3 0.939 0.945 0.937);\n        --olive-4: color(display-p3 0.907 0.914 0.905);\n        --olive-5: color(display-p3 0.878 0.885 0.875);\n        --olive-6: color(display-p3 0.846 0.855 0.843);\n        --olive-7: color(display-p3 0.803 0.812 0.8);\n        --olive-8: color(display-p3 0.727 0.738 0.723);\n        --olive-9: color(display-p3 0.541 0.556 0.532);\n        --olive-10: color(display-p3 0.5 0.515 0.491);\n        --olive-11: color(display-p3 0.38 0.395 0.374);\n        --olive-12: color(display-p3 0.117 0.129 0.111);\n        --olive-a1: color(display-p3 0.024 0.349 0.024 / 0.012);\n        --olive-a2: color(display-p3 0.024 0.302 0.024 / 0.028);\n        --olive-a3: color(display-p3 0.008 0.129 0.008 / 0.063);\n        --olive-a4: color(display-p3 0.012 0.094 0.012 / 0.095);\n        --olive-a5: color(display-p3 0.035 0.098 0.008 / 0.126);\n        --olive-a6: color(display-p3 0.027 0.078 0.004 / 0.157);\n        --olive-a7: color(display-p3 0.02 0.059 0 / 0.2);\n        --olive-a8: color(display-p3 0.02 0.059 0.004 / 0.279);\n        --olive-a9: color(display-p3 0.02 0.051 0.004 / 0.467);\n        --olive-a10: color(display-p3 0.024 0.047 0 / 0.51);\n        --olive-a11: color(display-p3 0.012 0.039 0 / 0.628);\n        --olive-a12: color(display-p3 0.008 0.024 0 / 0.891);\n        --sand-1: color(display-p3 0.992 0.992 0.989);\n        --sand-2: color(display-p3 0.977 0.977 0.973);\n        --sand-3: color(display-p3 0.943 0.942 0.936);\n        --sand-4: color(display-p3 0.913 0.912 0.903);\n        --sand-5: color(display-p3 0.885 0.883 0.873);\n        --sand-6: color(display-p3 0.854 0.852 0.839);\n        --sand-7: color(display-p3 0.813 0.81 0.794);\n        --sand-8: color(display-p3 0.738 0.734 0.713);\n        --sand-9: color(display-p3 0.553 0.553 0.528);\n        --sand-10: color(display-p3 0.511 0.511 0.488);\n        --sand-11: color(display-p3 0.388 0.388 0.37);\n        --sand-12: color(display-p3 0.129 0.126 0.111);\n        --sand-a1: color(display-p3 0.349 0.349 0.024 / 0.012);\n        --sand-a2: color(display-p3 0.161 0.161 0.024 / 0.028);\n        --sand-a3: color(display-p3 0.067 0.067 0.008 / 0.063);\n        --sand-a4: color(display-p3 0.129 0.129 0.012 / 0.099);\n        --sand-a5: color(display-p3 0.098 0.067 0.008 / 0.126);\n        --sand-a6: color(display-p3 0.102 0.075 0.004 / 0.161);\n        --sand-a7: color(display-p3 0.098 0.098 0.004 / 0.208);\n        --sand-a8: color(display-p3 0.086 0.075 0.004 / 0.287);\n        --sand-a9: color(display-p3 0.051 0.051 0.004 / 0.471);\n        --sand-a10: color(display-p3 0.047 0.047 0 / 0.514);\n        --sand-a11: color(display-p3 0.031 0.031 0 / 0.632);\n        --sand-a12: color(display-p3 0.024 0.02 0 / 0.891);\n        --blue-1: color(display-p3 0.9907 0.9925 0.9965);\n        --blue-2: color(display-p3 0.9679 0.9781 0.9997);\n        --blue-3: color(display-p3 0.9281 0.9496 0.9956);\n        --blue-4: color(display-p3 0.8754 0.9169 1);\n        --blue-5: color(display-p3 0.8137 0.8753 1);\n        --blue-6: color(display-p3 0.7387 0.8226 1);\n        --blue-7: color(display-p3 0.6508 0.7498 0.9743);\n        --blue-8: color(display-p3 0.5268 0.6493 0.9375);\n        --blue-9: color(display-p3 0.1632 0.3246 0.8163);\n        --blue-10: color(display-p3 0.108 0.2592 0.7498);\n        --blue-11: color(display-p3 0.2053 0.3558 0.7842);\n        --blue-12: color(display-p3 0.1088 0.1781 0.3608);\n        --blue-a1: color(display-p3 0.0196 0.0196 0.5098 / 0.008);\n        --blue-a2: color(display-p3 0.0196 0.3882 0.8784 / 0.032);\n        --blue-a3: color(display-p3 0.0078 0.3216 0.949 / 0.075);\n        --blue-a4: color(display-p3 0.0078 0.349 0.9412 / 0.126);\n        --blue-a5: color(display-p3 0.0039 0.3255 0.9373 / 0.185);\n        --blue-a6: color(display-p3 0.0039 0.3294 0.9412 / 0.263);\n        --blue-a7: color(display-p3 0.0039 0.2824 0.9216 / 0.35);\n        --blue-a8: color(display-p3 0.0039 0.2667 0.8706 / 0.475);\n        --blue-a9: color(display-p3 0 0.1922 0.7804 / 0.836);\n        --blue-a10: color(display-p3 0 0.1725 0.7216 / 0.895);\n        --blue-a11: color(display-p3 0 0.1922 0.7294 / 0.797);\n        --blue-a12: color(display-p3 0 0.0745 0.2824 / 0.891);\n        --orange-1: color(display-p3 0.9978 0.9885 0.9856);\n        --orange-2: color(display-p3 0.9992 0.9689 0.9596);\n        --orange-3: color(display-p3 1 0.9209 0.8965);\n        --orange-4: color(display-p3 1 0.8524 0.8);\n        --orange-5: color(display-p3 1 0.7925 0.724);\n        --orange-6: color(display-p3 0.9936 0.7338 0.6567);\n        --orange-7: color(display-p3 0.9501 0.6574 0.572);\n        --orange-8: color(display-p3 0.9107 0.5559 0.4555);\n        --orange-9: color(display-p3 0.9049 0.3335 0.1831);\n        --orange-10: color(display-p3 0.8535 0.2731 0.1143);\n        --orange-11: color(display-p3 0.7978 0.2236 0.0507);\n        --orange-12: color(display-p3 0.3453 0.1605 0.1105);\n        --orange-a1: color(display-p3 0.7569 0.2667 0.0235 / 0.016);\n        --orange-a2: color(display-p3 0.9137 0.2902 0.0196 / 0.044);\n        --orange-a3: color(display-p3 0.8471 0.2314 0.0039 / 0.102);\n        --orange-a4: color(display-p3 0.8824 0.2471 0.0078 / 0.197);\n        --orange-a5: color(display-p3 0.8863 0.2392 0.0039 / 0.267);\n        --orange-a6: color(display-p3 0.8745 0.2314 0.0039 / 0.346);\n        --orange-a7: color(display-p3 0.8824 0.1961 0.0039 / 0.428);\n        --orange-a8: color(display-p3 0.8353 0.1882 0 / 0.546);\n        --orange-a9: color(display-p3 0.8863 0.1843 0 / 0.816);\n        --orange-a10: color(display-p3 0.8275 0.1608 0 / 0.863);\n        --orange-a11: color(display-p3 0.7686 0.1176 0 / 0.879);\n        --orange-a12: color(display-p3 0.2627 0.0549 0 / 0.891);\n        --lemon-1: color(display-p3 0.9897 0.9925 0.9788);\n        --lemon-2: color(display-p3 0.9782 0.9885 0.9371);\n        --lemon-3: color(display-p3 0.9489 0.9835 0.7941);\n        --lemon-4: color(display-p3 0.9146 0.9617 0.6859);\n        --lemon-5: color(display-p3 0.8728 0.9274 0.5873);\n        --lemon-6: color(display-p3 0.8164 0.8701 0.5296);\n        --lemon-7: color(display-p3 0.7518 0.8027 0.4756);\n        --lemon-8: color(display-p3 0.6642 0.7184 0.3325);\n        --lemon-9: color(display-p3 0.8632 0.9421 0.2941);\n        --lemon-10: color(display-p3 0.8258 0.9005 0.2994);\n        --lemon-11: color(display-p3 0.4463 0.4898 0.1262);\n        --lemon-12: color(display-p3 0.231 0.2493 0.1314);\n        --lemon-a1: color(display-p3 0.5137 0.6745 0.0235 / 0.024);\n        --lemon-a2: color(display-p3 0.6902 0.8157 0.0078 / 0.063);\n        --lemon-a3: color(display-p3 0.7569 0.9255 0.0039 / 0.208);\n        --lemon-a4: color(display-p3 0.7255 0.8784 0.0039 / 0.314);\n        --lemon-a5: color(display-p3 0.6863 0.8196 0.0039 / 0.412);\n        --lemon-a6: color(display-p3 0.6118 0.7176 0.0039 / 0.471);\n        --lemon-a7: color(display-p3 0.5294 0.6196 0 / 0.526);\n        --lemon-a8: color(display-p3 0.4941 0.5765 0 / 0.667);\n        --lemon-a9: color(display-p3 0.8039 0.9137 0 / 0.695);\n        --lemon-a10: color(display-p3 0.7569 0.8549 0 / 0.702);\n        --lemon-a11: color(display-p3 0.3569 0.4078 0 / 0.859);\n        --lemon-a12: color(display-p3 0.1137 0.1373 0 / 0.867);\n        --indigo-1: color(display-p3 0.9914 0.9917 1);\n        --indigo-2: color(display-p3 0.9707 0.9716 1);\n        --indigo-3: color(display-p3 0.9408 0.9422 1);\n        --indigo-4: color(display-p3 0.8983 0.8995 1);\n        --indigo-5: color(display-p3 0.8524 0.8522 1);\n        --indigo-6: color(display-p3 0.7953 0.7919 1);\n        --indigo-7: color(display-p3 0.7161 0.7042 1);\n        --indigo-8: color(display-p3 0.6117 0.5789 1);\n        --indigo-9: color(display-p3 0.3562 0.1176 0.9345);\n        --indigo-10: color(display-p3 0.309 0 0.8658);\n        --indigo-11: color(display-p3 0.3639 0.1927 0.9095);\n        --indigo-12: color(display-p3 0.1733 0.1009 0.451);\n        --indigo-a1: color(display-p3 0.0196 0.0196 1 / 0.008);\n        --indigo-a2: color(display-p3 0.0196 0.1451 0.8784 / 0.032);\n        --indigo-a3: color(display-p3 0.0039 0.0039 0.9373 / 0.059);\n        --indigo-a4: color(display-p3 0.0039 0.0039 0.9255 / 0.102);\n        --indigo-a5: color(display-p3 0.0078 0.0078 0.9216 / 0.146);\n        --indigo-a6: color(display-p3 0.0039 0.0039 0.9255 / 0.204);\n        --indigo-a7: color(display-p3 0.0314 0.0039 0.9333 / 0.291);\n        --indigo-a8: color(display-p3 0.0706 0.0039 0.9373 / 0.416);\n        --indigo-a9: color(display-p3 0.2706 0 0.9255 / 0.883);\n        --indigo-a10: color(display-p3 0.2824 0 0.8588 / 0.957);\n        --indigo-a11: color(display-p3 0.2157 0 0.8902 / 0.808);\n        --indigo-a12: color(display-p3 0.0784 0 0.3882 / 0.899);\n        --lime-1: color(display-p3 0.9836 0.9961 0.9805);\n        --lime-2: color(display-p3 0.9614 0.9862 0.9553);\n        --lime-3: color(display-p3 0.8945 0.9821 0.8729);\n        --lime-4: color(display-p3 0.8269 0.9657 0.7927);\n        --lime-5: color(display-p3 0.7545 0.9369 0.7099);\n        --lime-6: color(display-p3 0.6723 0.8927 0.6185);\n        --lime-7: color(display-p3 0.5673 0.8305 0.5032);\n        --lime-8: color(display-p3 0.4126 0.7539 0.3277);\n        --lime-9: color(display-p3 0.3843 0.8306 0.2661);\n        --lime-10: color(display-p3 0.3386 0.7852 0.2155);\n        --lime-11: color(display-p3 0.2169 0.5168 0.1352);\n        --lime-12: color(display-p3 0.1424 0.2578 0.1142);\n        --lime-a1: color(display-p3 0.2157 0.8039 0.0196 / 0.02);\n        --lime-a2: color(display-p3 0.1098 0.7333 0.0196 / 0.044);\n        --lime-a3: color(display-p3 0.1882 0.851 0.0078 / 0.13);\n        --lime-a4: color(display-p3 0.1725 0.851 0.0039 / 0.208);\n        --lime-a5: color(display-p3 0.1647 0.7882 0.0039 / 0.291);\n        --lime-a6: color(display-p3 0.149 0.7255 0.0039 / 0.381);\n        --lime-a7: color(display-p3 0.1373 0.6627 0.0039 / 0.499);\n        --lime-a8: color(display-p3 0.1294 0.6353 0 / 0.675);\n        --lime-a9: color(display-p3 0.1608 0.7686 0 / 0.734);\n        --lime-a10: color(display-p3 0.1686 0.7216 0 / 0.769);\n        --lime-a11: color(display-p3 0.102 0.4392 0 / 0.859);\n        --lime-a12: color(display-p3 0.0314 0.1647 0 / 0.887);\n        --magenta-1: color(display-p3 0.9996 0.9883 0.9916);\n        --magenta-2: color(display-p3 0.9948 0.9673 0.9755);\n        --magenta-3: color(display-p3 0.9991 0.9113 0.9382);\n        --magenta-4: color(display-p3 0.9933 0.8568 0.8996);\n        --magenta-5: color(display-p3 0.9765 0.8008 0.8571);\n        --magenta-6: color(display-p3 0.9485 0.7399 0.8082);\n        --magenta-7: color(display-p3 0.9129 0.664 0.7479);\n        --magenta-8: color(display-p3 0.8739 0.5664 0.6751);\n        --magenta-9: color(display-p3 0.9175 0.2003 0.5465);\n        --magenta-10: color(display-p3 0.8588 0.1293 0.4997);\n        --magenta-11: color(display-p3 0.7746 0 0.4344);\n        --magenta-12: color(display-p3 0.3807 0.0555 0.2136);\n        --magenta-a1: color(display-p3 0.6745 0.0235 0.349 / 0.012);\n        --magenta-a2: color(display-p3 0.7843 0.0235 0.349 / 0.036);\n        --magenta-a3: color(display-p3 0.8314 0.0118 0.3137 / 0.091);\n        --magenta-a4: color(display-p3 0.8392 0.0078 0.3294 / 0.146);\n        --magenta-a5: color(display-p3 0.8431 0 0.2941 / 0.2);\n        --magenta-a6: color(display-p3 0.8039 0.0039 0.2588 / 0.259);\n        --magenta-a7: color(display-p3 0.7451 0.0039 0.2471 / 0.338);\n        --magenta-a8: color(display-p3 0.7098 0.0039 0.2471 / 0.432);\n        --magenta-a9: color(display-p3 0.898 0 0.4314 / 0.8);\n        --magenta-a10: color(display-p3 0.8275 0 0.3882 / 0.816);\n        --magenta-a11: color(display-p3 0.7137 0 0.3255 / 0.84);\n        --magenta-a12: color(display-p3 0.3451 0 0.1608 / 0.942);\n        --gray-surface: color(display-p3 1 1 1 / 0.8);\n        --mauve-surface: color(display-p3 1 1 1 / 0.8);\n        --slate-surface: color(display-p3 1 1 1 / 0.8);\n        --sage-surface: color(display-p3 1 1 1 / 0.8);\n        --olive-surface: color(display-p3 1 1 1 / 0.8);\n        --sand-surface: color(display-p3 1 1 1 / 0.8);\n        --tomato-surface: color(display-p3 0.9922 0.9647 0.9608 / 0.8);\n        --red-surface: color(display-p3 0.9961 0.9647 0.9647 / 0.8);\n        --ruby-surface: color(display-p3 0.9961 0.9647 0.9647 / 0.8);\n        --crimson-surface: color(display-p3 0.9922 0.9608 0.9725 / 0.8);\n        --pink-surface: color(display-p3 0.9922 0.9608 0.9804 / 0.8);\n        --plum-surface: color(display-p3 0.9843 0.9647 0.9843 / 0.8);\n        --purple-surface: color(display-p3 0.9804 0.9647 0.9922 / 0.8);\n        --violet-surface: color(display-p3 0.9725 0.9647 0.9961 / 0.8);\n        --iris-surface: color(display-p3 0.9647 0.9647 0.9961 / 0.8);\n        --cyan-surface: color(display-p3 0.9412 0.9765 0.9804 / 0.8);\n        --teal-surface: color(display-p3 0.9451 0.9804 0.9725 / 0.8);\n        --jade-surface: color(display-p3 0.9529 0.9804 0.9608 / 0.8);\n        --green-surface: color(display-p3 0.9569 0.9804 0.9608 / 0.8);\n        --grass-surface: color(display-p3 0.9569 0.9804 0.9569 / 0.8);\n        --brown-surface: color(display-p3 0.9843 0.9725 0.9569 / 0.8);\n        --bronze-surface: color(display-p3 0.9843 0.9608 0.9529 / 0.8);\n        --gold-surface: color(display-p3 0.9765 0.9725 0.9412 / 0.8);\n        --sky-surface: color(display-p3 0.9412 0.9765 0.9843 / 0.8);\n        --mint-surface: color(display-p3 0.9451 0.9804 0.9725 / 0.8);\n        --yellow-surface: color(display-p3 0.9961 0.9922 0.902 / 0.8);\n        --amber-surface: color(display-p3 0.9922 0.9843 0.902 / 0.8);\n        --blue-surface: color(display-p3 0.9608 0.9725 1 / 0.8);\n        --orange-surface: color(display-p3 1 0.9608 0.9529 / 0.8);\n        --indigo-surface: color(display-p3 0.9647 0.9647 1 / 0.8);\n        --magenta-surface: color(display-p3 0.9961 0.9608 0.9725 / 0.8);\n        --lemon-surface: color(display-p3 0.9725 0.9843 0.9216 / 0.8);\n        --lime-surface: color(display-p3 0.9529 0.9804 0.9451 / 0.8);\n      }\n    }\n  }\n  .dark, .dark-theme {\n    --tomato-1: #181111;\n    --tomato-2: #1f1513;\n    --tomato-3: #391714;\n    --tomato-4: #4e1511;\n    --tomato-5: #5e1c16;\n    --tomato-6: #6e2920;\n    --tomato-7: #853a2d;\n    --tomato-8: #ac4d39;\n    --tomato-9: #e54d2e;\n    --tomato-10: #ec6142;\n    --tomato-11: #ff977d;\n    --tomato-12: #fbd3cb;\n    --tomato-a1: #f1121208;\n    --tomato-a2: #ff55330f;\n    --tomato-a3: #ff35232b;\n    --tomato-a4: #fd201142;\n    --tomato-a5: #fe332153;\n    --tomato-a6: #ff4f3864;\n    --tomato-a7: #fd644a7d;\n    --tomato-a8: #fe6d4ea7;\n    --tomato-a9: #fe5431e4;\n    --tomato-a10: #ff6847eb;\n    --tomato-a11: #ff977d;\n    --tomato-a12: #ffd6cefb;\n    --red-1: #191111;\n    --red-2: #201314;\n    --red-3: #3b1219;\n    --red-4: #500f1c;\n    --red-5: #611623;\n    --red-6: #72232d;\n    --red-7: #8c333a;\n    --red-8: #b54548;\n    --red-9: #e5484d;\n    --red-10: #ec5d5e;\n    --red-11: #ff9592;\n    --red-12: #ffd1d9;\n    --red-a1: #f4121209;\n    --red-a2: #f22f3e11;\n    --red-a3: #ff173f2d;\n    --red-a4: #fe0a3b44;\n    --red-a5: #ff204756;\n    --red-a6: #ff3e5668;\n    --red-a7: #ff536184;\n    --red-a8: #ff5d61b0;\n    --red-a9: #fe4e54e4;\n    --red-a10: #ff6465eb;\n    --red-a11: #ff9592;\n    --red-a12: #ffd1d9;\n    --ruby-1: #191113;\n    --ruby-2: #1e1517;\n    --ruby-3: #3a141e;\n    --ruby-4: #4e1325;\n    --ruby-5: #5e1a2e;\n    --ruby-6: #6f2539;\n    --ruby-7: #883447;\n    --ruby-8: #b3445a;\n    --ruby-9: #e54666;\n    --ruby-10: #ec5a72;\n    --ruby-11: #ff949d;\n    --ruby-12: #fed2e1;\n    --ruby-a1: #f4124a09;\n    --ruby-a2: #fe5a7f0e;\n    --ruby-a3: #ff235d2c;\n    --ruby-a4: #fd195e42;\n    --ruby-a5: #fe2d6b53;\n    --ruby-a6: #ff447665;\n    --ruby-a7: #ff577d80;\n    --ruby-a8: #ff5c7cae;\n    --ruby-a9: #fe4c70e4;\n    --ruby-a10: #ff617beb;\n    --ruby-a11: #ff949d;\n    --ruby-a12: #ffd3e2fe;\n    --crimson-1: #191114;\n    --crimson-2: #201318;\n    --crimson-3: #381525;\n    --crimson-4: #4d122f;\n    --crimson-5: #5c1839;\n    --crimson-6: #6d2545;\n    --crimson-7: #873356;\n    --crimson-8: #b0436e;\n    --crimson-9: #e93d82;\n    --crimson-10: #ee518a;\n    --crimson-11: #ff92ad;\n    --crimson-12: #fdd3e8;\n    --crimson-a1: #f4126709;\n    --crimson-a2: #f22f7a11;\n    --crimson-a3: #fe2a8b2a;\n    --crimson-a4: #fd158741;\n    --crimson-a5: #fd278f51;\n    --crimson-a6: #fe459763;\n    --crimson-a7: #fd559b7f;\n    --crimson-a8: #fe5b9bab;\n    --crimson-a9: #fe418de8;\n    --crimson-a10: #ff5693ed;\n    --crimson-a11: #ff92ad;\n    --crimson-a12: #ffd5eafd;\n    --pink-1: #191117;\n    --pink-2: #21121d;\n    --pink-3: #37172f;\n    --pink-4: #4b143d;\n    --pink-5: #591c47;\n    --pink-6: #692955;\n    --pink-7: #833869;\n    --pink-8: #a84885;\n    --pink-9: #d6409f;\n    --pink-10: #de51a8;\n    --pink-11: #ff8dcc;\n    --pink-12: #fdd1ea;\n    --pink-a1: #f412bc09;\n    --pink-a2: #f420bb12;\n    --pink-a3: #fe37cc29;\n    --pink-a4: #fc1ec43f;\n    --pink-a5: #fd35c24e;\n    --pink-a6: #fd51c75f;\n    --pink-a7: #fd62c87b;\n    --pink-a8: #ff68c8a2;\n    --pink-a9: #fe49bcd4;\n    --pink-a10: #ff5cc0dc;\n    --pink-a11: #ff8dcc;\n    --pink-a12: #ffd3ecfd;\n    --plum-1: #181118;\n    --plum-2: #201320;\n    --plum-3: #351a35;\n    --plum-4: #451d47;\n    --plum-5: #512454;\n    --plum-6: #5e3061;\n    --plum-7: #734079;\n    --plum-8: #92549c;\n    --plum-9: #ab4aba;\n    --plum-10: #b658c4;\n    --plum-11: #e796f3;\n    --plum-12: #f4d4f4;\n    --plum-a1: #f112f108;\n    --plum-a2: #f22ff211;\n    --plum-a3: #fd4cfd27;\n    --plum-a4: #f646ff3a;\n    --plum-a5: #f455ff48;\n    --plum-a6: #f66dff56;\n    --plum-a7: #f07cfd70;\n    --plum-a8: #ee84ff95;\n    --plum-a9: #e961feb6;\n    --plum-a10: #ed70ffc0;\n    --plum-a11: #f19cfef3;\n    --plum-a12: #feddfef4;\n    --purple-1: #18111b;\n    --purple-2: #1e1523;\n    --purple-3: #301c3b;\n    --purple-4: #3d224e;\n    --purple-5: #48295c;\n    --purple-6: #54346b;\n    --purple-7: #664282;\n    --purple-8: #8457aa;\n    --purple-9: #8e4ec6;\n    --purple-10: #9a5cd0;\n    --purple-11: #d19dff;\n    --purple-12: #ecd9fa;\n    --purple-a1: #b412f90b;\n    --purple-a2: #b744f714;\n    --purple-a3: #c150ff2d;\n    --purple-a4: #bb53fd42;\n    --purple-a5: #be5cfd51;\n    --purple-a6: #c16dfd61;\n    --purple-a7: #c378fd7a;\n    --purple-a8: #c47effa4;\n    --purple-a9: #b661ffc2;\n    --purple-a10: #bc6fffcd;\n    --purple-a11: #d19dff;\n    --purple-a12: #f1ddfffa;\n    --violet-1: #14121f;\n    --violet-2: #1b1525;\n    --violet-3: #291f43;\n    --violet-4: #33255b;\n    --violet-5: #3c2e69;\n    --violet-6: #473876;\n    --violet-7: #56468b;\n    --violet-8: #6958ad;\n    --violet-9: #6e56cf;\n    --violet-10: #7d66d9;\n    --violet-11: #baa7ff;\n    --violet-12: #e2ddfe;\n    --violet-a1: #4422ff0f;\n    --violet-a2: #853ff916;\n    --violet-a3: #8354fe36;\n    --violet-a4: #7d51fd50;\n    --violet-a5: #845ffd5f;\n    --violet-a6: #8f6cfd6d;\n    --violet-a7: #9879ff83;\n    --violet-a8: #977dfea8;\n    --violet-a9: #8668ffcc;\n    --violet-a10: #9176fed7;\n    --violet-a11: #baa7ff;\n    --violet-a12: #e3defffe;\n    --iris-1: #13131e;\n    --iris-2: #171625;\n    --iris-3: #202248;\n    --iris-4: #262a65;\n    --iris-5: #303374;\n    --iris-6: #3d3e82;\n    --iris-7: #4a4a95;\n    --iris-8: #5958b1;\n    --iris-9: #5b5bd6;\n    --iris-10: #6e6ade;\n    --iris-11: #b1a9ff;\n    --iris-12: #e0dffe;\n    --iris-a1: #3636fe0e;\n    --iris-a2: #564bf916;\n    --iris-a3: #525bff3b;\n    --iris-a4: #4d58ff5a;\n    --iris-a5: #5b62fd6b;\n    --iris-a6: #6d6ffd7a;\n    --iris-a7: #7777fe8e;\n    --iris-a8: #7b7afeac;\n    --iris-a9: #6a6afed4;\n    --iris-a10: #7d79ffdc;\n    --iris-a11: #b1a9ff;\n    --iris-a12: #e1e0fffe;\n    --cyan-1: #0b161a;\n    --cyan-2: #101b20;\n    --cyan-3: #082c36;\n    --cyan-4: #003848;\n    --cyan-5: #004558;\n    --cyan-6: #045468;\n    --cyan-7: #12677e;\n    --cyan-8: #11809c;\n    --cyan-9: #00a2c7;\n    --cyan-10: #23afd0;\n    --cyan-11: #4ccce6;\n    --cyan-12: #b6ecf7;\n    --cyan-a1: #0091f70a;\n    --cyan-a2: #02a7f211;\n    --cyan-a3: #00befd28;\n    --cyan-a4: #00baff3b;\n    --cyan-a5: #00befd4d;\n    --cyan-a6: #00c7fd5e;\n    --cyan-a7: #14cdff75;\n    --cyan-a8: #11cfff95;\n    --cyan-a9: #00cfffc3;\n    --cyan-a10: #28d6ffcd;\n    --cyan-a11: #52e1fee5;\n    --cyan-a12: #bbf3fef7;\n    --teal-1: #0d1514;\n    --teal-2: #111c1b;\n    --teal-3: #0d2d2a;\n    --teal-4: #023b37;\n    --teal-5: #084843;\n    --teal-6: #145750;\n    --teal-7: #1c6961;\n    --teal-8: #207e73;\n    --teal-9: #12a594;\n    --teal-10: #0eb39e;\n    --teal-11: #0bd8b6;\n    --teal-12: #adf0dd;\n    --teal-a1: #00deab05;\n    --teal-a2: #12fbe60c;\n    --teal-a3: #00ffe61e;\n    --teal-a4: #00ffe92d;\n    --teal-a5: #00ffea3b;\n    --teal-a6: #1cffe84b;\n    --teal-a7: #2efde85f;\n    --teal-a8: #32ffe775;\n    --teal-a9: #13ffe49f;\n    --teal-a10: #0dffe0ae;\n    --teal-a11: #0afed5d6;\n    --teal-a12: #b8ffebef;\n    --jade-1: #0d1512;\n    --jade-2: #121c18;\n    --jade-3: #0f2e22;\n    --jade-4: #0b3b2c;\n    --jade-5: #114837;\n    --jade-6: #1b5745;\n    --jade-7: #246854;\n    --jade-8: #2a7e68;\n    --jade-9: #29a383;\n    --jade-10: #27b08b;\n    --jade-11: #1fd8a4;\n    --jade-12: #adf0d4;\n    --jade-a1: #00de4505;\n    --jade-a2: #27fba60c;\n    --jade-a3: #02f99920;\n    --jade-a4: #00ffaa2d;\n    --jade-a5: #11ffb63b;\n    --jade-a6: #34ffc24b;\n    --jade-a7: #45fdc75e;\n    --jade-a8: #48ffcf75;\n    --jade-a9: #38feca9d;\n    --jade-a10: #31fec7ab;\n    --jade-a11: #21fec0d6;\n    --jade-a12: #b8ffe1ef;\n    --green-1: #0e1512;\n    --green-2: #121b17;\n    --green-3: #132d21;\n    --green-4: #113b29;\n    --green-5: #174933;\n    --green-6: #20573e;\n    --green-7: #28684a;\n    --green-8: #2f7c57;\n    --green-9: #30a46c;\n    --green-10: #33b074;\n    --green-11: #3dd68c;\n    --green-12: #b1f1cb;\n    --green-a1: #00de4505;\n    --green-a2: #29f99d0b;\n    --green-a3: #22ff991e;\n    --green-a4: #11ff992d;\n    --green-a5: #2bffa23c;\n    --green-a6: #44ffaa4b;\n    --green-a7: #50fdac5e;\n    --green-a8: #54ffad73;\n    --green-a9: #44ffa49e;\n    --green-a10: #43fea4ab;\n    --green-a11: #46fea5d4;\n    --green-a12: #bbffd7f0;\n    --grass-1: #0e1511;\n    --grass-2: #141a15;\n    --grass-3: #1b2a1e;\n    --grass-4: #1d3a24;\n    --grass-5: #25482d;\n    --grass-6: #2d5736;\n    --grass-7: #366740;\n    --grass-8: #3e7949;\n    --grass-9: #46a758;\n    --grass-10: #53b365;\n    --grass-11: #71d083;\n    --grass-12: #c2f0c2;\n    --grass-a1: #00de1205;\n    --grass-a2: #5ef7780a;\n    --grass-a3: #70fe8c1b;\n    --grass-a4: #57ff802c;\n    --grass-a5: #68ff8b3b;\n    --grass-a6: #71ff8f4b;\n    --grass-a7: #77fd925d;\n    --grass-a8: #77fd9070;\n    --grass-a9: #65ff82a1;\n    --grass-a10: #72ff8dae;\n    --grass-a11: #89ff9fcd;\n    --grass-a12: #ceffceef;\n    --brown-1: #12110f;\n    --brown-2: #1c1816;\n    --brown-3: #28211d;\n    --brown-4: #322922;\n    --brown-5: #3e3128;\n    --brown-6: #4d3c2f;\n    --brown-7: #614a39;\n    --brown-8: #7c5f46;\n    --brown-9: #ad7f58;\n    --brown-10: #b88c67;\n    --brown-11: #dbb594;\n    --brown-12: #f2e1ca;\n    --brown-a1: #91110002;\n    --brown-a2: #fba67c0c;\n    --brown-a3: #fcb58c19;\n    --brown-a4: #fbbb8a24;\n    --brown-a5: #fcb88931;\n    --brown-a6: #fdba8741;\n    --brown-a7: #ffbb8856;\n    --brown-a8: #ffbe8773;\n    --brown-a9: #feb87da8;\n    --brown-a10: #ffc18cb3;\n    --brown-a11: #fed1aad9;\n    --brown-a12: #feecd4f2;\n    --sky-1: #0d141f;\n    --sky-2: #111a27;\n    --sky-3: #112840;\n    --sky-4: #113555;\n    --sky-5: #154467;\n    --sky-6: #1b537b;\n    --sky-7: #1f6692;\n    --sky-8: #197cae;\n    --sky-9: #7ce2fe;\n    --sky-10: #a8eeff;\n    --sky-11: #75c7f0;\n    --sky-12: #c2f3ff;\n    --sky-a1: #0044ff0f;\n    --sky-a2: #1171fb18;\n    --sky-a3: #1184fc33;\n    --sky-a4: #128fff49;\n    --sky-a5: #1c9dfd5d;\n    --sky-a6: #28a5ff72;\n    --sky-a7: #2badfe8b;\n    --sky-a8: #1db2fea9;\n    --sky-a9: #7ce3fffe;\n    --sky-a10: #a8eeff;\n    --sky-a11: #7cd3ffef;\n    --sky-a12: #c2f3ff;\n    --mint-1: #0e1515;\n    --mint-2: #0f1b1b;\n    --mint-3: #092c2b;\n    --mint-4: #003a38;\n    --mint-5: #004744;\n    --mint-6: #105650;\n    --mint-7: #1e685f;\n    --mint-8: #277f70;\n    --mint-9: #86ead4;\n    --mint-10: #a8f5e5;\n    --mint-11: #58d5ba;\n    --mint-12: #c4f5e1;\n    --mint-a1: #00dede05;\n    --mint-a2: #00f9f90b;\n    --mint-a3: #00fff61d;\n    --mint-a4: #00fff42c;\n    --mint-a5: #00fff23a;\n    --mint-a6: #0effeb4a;\n    --mint-a7: #34fde55e;\n    --mint-a8: #41ffdf76;\n    --mint-a9: #92ffe7e9;\n    --mint-a10: #aefeedf5;\n    --mint-a11: #67ffded2;\n    --mint-a12: #cbfee9f5;\n    --yellow-1: #14120b;\n    --yellow-2: #1b180f;\n    --yellow-3: #2d2305;\n    --yellow-4: #362b00;\n    --yellow-5: #433500;\n    --yellow-6: #524202;\n    --yellow-7: #665417;\n    --yellow-8: #836a21;\n    --yellow-9: #ffe629;\n    --yellow-10: #ffff57;\n    --yellow-11: #f5e147;\n    --yellow-12: #f6eeb4;\n    --yellow-a1: #d1510004;\n    --yellow-a2: #f9b4000b;\n    --yellow-a3: #ffaa001e;\n    --yellow-a4: #fdb70028;\n    --yellow-a5: #febb0036;\n    --yellow-a6: #fec40046;\n    --yellow-a7: #fdcb225c;\n    --yellow-a8: #fdca327b;\n    --yellow-a9: #ffe629;\n    --yellow-a10: #ffff57;\n    --yellow-a11: #fee949f5;\n    --yellow-a12: #fef6baf6;\n    --amber-1: #16120c;\n    --amber-2: #1d180f;\n    --amber-3: #302008;\n    --amber-4: #3f2700;\n    --amber-5: #4d3000;\n    --amber-6: #5c3d05;\n    --amber-7: #714f19;\n    --amber-8: #8f6424;\n    --amber-9: #ffc53d;\n    --amber-10: #ffd60a;\n    --amber-11: #ffca16;\n    --amber-12: #ffe7b3;\n    --amber-a1: #e63c0006;\n    --amber-a2: #fd9b000d;\n    --amber-a3: #fa820022;\n    --amber-a4: #fc820032;\n    --amber-a5: #fd8b0041;\n    --amber-a6: #fd9b0051;\n    --amber-a7: #ffab2567;\n    --amber-a8: #ffae3587;\n    --amber-a9: #ffc53d;\n    --amber-a10: #ffd60a;\n    --amber-a11: #ffca16;\n    --amber-a12: #ffe7b3;\n    --gold-1: #121211;\n    --gold-2: #1b1a17;\n    --gold-3: #24231f;\n    --gold-4: #2d2b26;\n    --gold-5: #38352e;\n    --gold-6: #444039;\n    --gold-7: #544f46;\n    --gold-8: #696256;\n    --gold-9: #978365;\n    --gold-10: #a39073;\n    --gold-11: #cbb99f;\n    --gold-12: #e8e2d9;\n    --gold-a1: #91911102;\n    --gold-a2: #f9e29d0b;\n    --gold-a3: #f8ecbb15;\n    --gold-a4: #ffeec41e;\n    --gold-a5: #feecc22a;\n    --gold-a6: #feebcb37;\n    --gold-a7: #ffedcd48;\n    --gold-a8: #fdeaca5f;\n    --gold-a9: #ffdba690;\n    --gold-a10: #fedfb09d;\n    --gold-a11: #fee7c6c8;\n    --gold-a12: #fef7ede7;\n    --bronze-1: #141110;\n    --bronze-2: #1c1917;\n    --bronze-3: #262220;\n    --bronze-4: #302a27;\n    --bronze-5: #3b3330;\n    --bronze-6: #493e3a;\n    --bronze-7: #5a4c47;\n    --bronze-8: #6f5f58;\n    --bronze-9: #a18072;\n    --bronze-10: #ae8c7e;\n    --bronze-11: #d4b3a5;\n    --bronze-12: #ede0d9;\n    --bronze-a1: #d1110004;\n    --bronze-a2: #fbbc910c;\n    --bronze-a3: #faceb817;\n    --bronze-a4: #facdb622;\n    --bronze-a5: #ffd2c12d;\n    --bronze-a6: #ffd1c03c;\n    --bronze-a7: #fdd0c04f;\n    --bronze-a8: #ffd6c565;\n    --bronze-a9: #fec7b09b;\n    --bronze-a10: #fecab5a9;\n    --bronze-a11: #ffd7c6d1;\n    --bronze-a12: #fff1e9ec;\n    --gray-1: #111111;\n    --gray-2: #191919;\n    --gray-3: #222222;\n    --gray-4: #2a2a2a;\n    --gray-5: #313131;\n    --gray-6: #3a3a3a;\n    --gray-7: #484848;\n    --gray-8: #606060;\n    --gray-9: #6e6e6e;\n    --gray-10: #7b7b7b;\n    --gray-11: #b4b4b4;\n    --gray-12: #eeeeee;\n    --gray-a1: #00000000;\n    --gray-a2: #ffffff09;\n    --gray-a3: #ffffff12;\n    --gray-a4: #ffffff1b;\n    --gray-a5: #ffffff22;\n    --gray-a6: #ffffff2c;\n    --gray-a7: #ffffff3b;\n    --gray-a8: #ffffff55;\n    --gray-a9: #ffffff64;\n    --gray-a10: #ffffff72;\n    --gray-a11: #ffffffaf;\n    --gray-a12: #ffffffed;\n    --mauve-1: #121113;\n    --mauve-2: #1a191b;\n    --mauve-3: #232225;\n    --mauve-4: #2b292d;\n    --mauve-5: #323035;\n    --mauve-6: #3c393f;\n    --mauve-7: #49474e;\n    --mauve-8: #625f69;\n    --mauve-9: #6f6d78;\n    --mauve-10: #7c7a85;\n    --mauve-11: #b5b2bc;\n    --mauve-12: #eeeef0;\n    --mauve-a1: #00000000;\n    --mauve-a2: #f5f4f609;\n    --mauve-a3: #ebeaf814;\n    --mauve-a4: #eee5f81d;\n    --mauve-a5: #efe6fe25;\n    --mauve-a6: #f1e6fd30;\n    --mauve-a7: #eee9ff40;\n    --mauve-a8: #eee7ff5d;\n    --mauve-a9: #eae6fd6e;\n    --mauve-a10: #ece9fd7c;\n    --mauve-a11: #f5f1ffb7;\n    --mauve-a12: #fdfdffef;\n    --slate-1: #111113;\n    --slate-2: #18191b;\n    --slate-3: #212225;\n    --slate-4: #272a2d;\n    --slate-5: #2e3135;\n    --slate-6: #363a3f;\n    --slate-7: #43484e;\n    --slate-8: #5a6169;\n    --slate-9: #696e77;\n    --slate-10: #777b84;\n    --slate-11: #b0b4ba;\n    --slate-12: #edeef0;\n    --slate-a1: #00000000;\n    --slate-a2: #d8f4f609;\n    --slate-a3: #ddeaf814;\n    --slate-a4: #d3edf81d;\n    --slate-a5: #d9edfe25;\n    --slate-a6: #d6ebfd30;\n    --slate-a7: #d9edff40;\n    --slate-a8: #d9edff5d;\n    --slate-a9: #dfebfd6d;\n    --slate-a10: #e5edfd7b;\n    --slate-a11: #f1f7feb5;\n    --slate-a12: #fcfdffef;\n    --sage-1: #101211;\n    --sage-2: #171918;\n    --sage-3: #202221;\n    --sage-4: #272a29;\n    --sage-5: #2e3130;\n    --sage-6: #373b39;\n    --sage-7: #444947;\n    --sage-8: #5b625f;\n    --sage-9: #63706b;\n    --sage-10: #717d79;\n    --sage-11: #adb5b2;\n    --sage-12: #eceeed;\n    --sage-a1: #00000000;\n    --sage-a2: #f0f2f108;\n    --sage-a3: #f3f5f412;\n    --sage-a4: #f2fefd1a;\n    --sage-a5: #f1fbfa22;\n    --sage-a6: #edfbf42d;\n    --sage-a7: #edfcf73c;\n    --sage-a8: #ebfdf657;\n    --sage-a9: #dffdf266;\n    --sage-a10: #e5fdf674;\n    --sage-a11: #f4fefbb0;\n    --sage-a12: #fdfffeed;\n    --olive-1: #111210;\n    --olive-2: #181917;\n    --olive-3: #212220;\n    --olive-4: #282a27;\n    --olive-5: #2f312e;\n    --olive-6: #383a36;\n    --olive-7: #454843;\n    --olive-8: #5c625b;\n    --olive-9: #687066;\n    --olive-10: #767d74;\n    --olive-11: #afb5ad;\n    --olive-12: #eceeec;\n    --olive-a1: #00000000;\n    --olive-a2: #f1f2f008;\n    --olive-a3: #f4f5f312;\n    --olive-a4: #f3fef21a;\n    --olive-a5: #f2fbf122;\n    --olive-a6: #f4faed2c;\n    --olive-a7: #f2fced3b;\n    --olive-a8: #edfdeb57;\n    --olive-a9: #ebfde766;\n    --olive-a10: #f0fdec74;\n    --olive-a11: #f6fef4b0;\n    --olive-a12: #fdfffded;\n    --sand-1: #111110;\n    --sand-2: #191918;\n    --sand-3: #222221;\n    --sand-4: #2a2a28;\n    --sand-5: #31312e;\n    --sand-6: #3b3a37;\n    --sand-7: #494844;\n    --sand-8: #62605b;\n    --sand-9: #6f6d66;\n    --sand-10: #7c7b74;\n    --sand-11: #b5b3ad;\n    --sand-12: #eeeeec;\n    --sand-a1: #00000000;\n    --sand-a2: #f4f4f309;\n    --sand-a3: #f6f6f513;\n    --sand-a4: #fefef31b;\n    --sand-a5: #fbfbeb23;\n    --sand-a6: #fffaed2d;\n    --sand-a7: #fffbed3c;\n    --sand-a8: #fff9eb57;\n    --sand-a9: #fffae965;\n    --sand-a10: #fffdee73;\n    --sand-a11: #fffcf4b0;\n    --sand-a12: #fffffded;\n    --blue-1: #0b111d;\n    --blue-2: #0f1727;\n    --blue-3: #11254c;\n    --blue-4: #142f66;\n    --blue-5: #1a3979;\n    --blue-6: #23448a;\n    --blue-7: #2b519e;\n    --blue-8: #325eb9;\n    --blue-9: #1754d8;\n    --blue-10: #406cc8;\n    --blue-11: #88b5ff;\n    --blue-12: #d1e2ff;\n    --blue-a1: #0012fd0d;\n    --blue-a2: #0051fb18;\n    --blue-a3: #1161fd40;\n    --blue-a4: #1a65fd5c;\n    --blue-a5: #256cfd70;\n    --blue-a6: #3576ff82;\n    --blue-a7: #3d7cfe98;\n    --blue-a8: #407effb4;\n    --blue-a9: #1861fed6;\n    --blue-a10: #6397ffa8;\n    --blue-a11: #88b5ff;\n    --blue-a12: #d1e2ff;\n    --orange-1: #170f0d;\n    --orange-2: #1f1412;\n    --orange-3: #3b150c;\n    --orange-4: #521002;\n    --orange-5: #631704;\n    --orange-6: #732412;\n    --orange-7: #8b3521;\n    --orange-8: #b4452c;\n    --orange-9: #fa4616;\n    --orange-10: #eb3600;\n    --orange-11: #ff9275;\n    --orange-12: #ffd2c6;\n    --orange-a1: #ec000007;\n    --orange-a2: #ff44220f;\n    --orange-a3: #ff28002d;\n    --orange-a4: #fe0e0046;\n    --orange-a5: #ff230058;\n    --orange-a6: #ff401469;\n    --orange-a7: #ff583183;\n    --orange-a8: #ff5d39af;\n    --orange-a9: #ff4716fa;\n    --orange-a10: #ff3a00ea;\n    --orange-a11: #ff9275;\n    --orange-a12: #ffd2c6;\n    --lemon-1: #10120a;\n    --lemon-2: #17190f;\n    --lemon-3: #232710;\n    --lemon-4: #2d330d;\n    --lemon-5: #383f0d;\n    --lemon-6: #454d15;\n    --lemon-7: #545e1f;\n    --lemon-8: #677326;\n    --lemon-9: #dbf505;\n    --lemon-10: #d2e94c;\n    --lemon-11: #cfe648;\n    --lemon-12: #e8f5b2;\n    --lemon-a1: #00910002;\n    --lemon-a2: #bcf40009;\n    --lemon-a3: #d1fb0718;\n    --lemon-a4: #d2fb0025;\n    --lemon-a5: #d8fc0032;\n    --lemon-a6: #ddfd2141;\n    --lemon-a7: #dffe3d53;\n    --lemon-a8: #e2ff4469;\n    --lemon-a9: #e3fe05f5;\n    --lemon-a10: #e5fe51e8;\n    --lemon-a11: #e4fe4ee5;\n    --lemon-a12: #f0feb8f5;\n    --indigo-1: #100f1f;\n    --indigo-2: #16142b;\n    --indigo-3: #241a57;\n    --indigo-4: #30177b;\n    --indigo-5: #3a218d;\n    --indigo-6: #432d9c;\n    --indigo-7: #5039b3;\n    --indigo-8: #6045d6;\n    --indigo-9: #6318f8;\n    --indigo-10: #7855ff;\n    --indigo-11: #ada8ff;\n    --indigo-12: #ddf;\n    --indigo-a1: #0000ff0f;\n    --indigo-a2: #3f2dfe1c;\n    --indigo-a3: #5230ff4b;\n    --indigo-a4: #571fff72;\n    --indigo-a5: #6030ff85;\n    --indigo-a6: #6741ff95;\n    --indigo-a7: #6e4cffae;\n    --indigo-a8: #704ffed4;\n    --indigo-a9: #6619fff8;\n    --indigo-a10: #8869ffd7;\n    --indigo-a11: #ada8ff;\n    --indigo-a12: #ddddff;\n    --lime-1: #0c130b;\n    --lime-2: #121b11;\n    --lime-3: #132e12;\n    --lime-4: #103d0f;\n    --lime-5: #154b15;\n    --lime-6: #1d5a1c;\n    --lime-7: #246b23;\n    --lime-8: #297f28;\n    --lime-9: #06d718;\n    --lime-10: #00cb00;\n    --lime-11: #1cdd24;\n    --lime-12: #adf6a8;\n    --lime-a1: #00bb0003;\n    --lime-a2: #29f9120b;\n    --lime-a3: #21f91920;\n    --lime-a4: #0cfb0730;\n    --lime-a5: #22fc223f;\n    --lime-a6: #38fd354f;\n    --lime-a7: #43fd4061;\n    --lime-a8: #45ff4376;\n    --lime-a9: #04fe19d5;\n    --lime-a10: #00fe00c8;\n    --lime-a11: #1eff28db;\n    --lime-a12: #b2feadf6;\n    --magenta-1: #180e11;\n    --magenta-2: #211217;\n    --magenta-3: #3d1022;\n    --magenta-4: #55012b;\n    --magenta-5: #650635;\n    --magenta-6: #761642;\n    --magenta-7: #912554;\n    --magenta-8: #bc2f6e;\n    --magenta-9: #ff008d;\n    --magenta-10: #f00081;\n    --magenta-11: #ff89b8;\n    --magenta-12: #ffcfe0;\n    --magenta-a1: #f1001208;\n    --magenta-a2: #f4206612;\n    --magenta-a3: #fb0c6c30;\n    --magenta-a4: #ff006c49;\n    --magenta-a5: #ff00775a;\n    --magenta-a6: #fd1d846d;\n    --magenta-a7: #fe368d8a;\n    --magenta-a8: #fe3a92b8;\n    --magenta-a9: #ff008d;\n    --magenta-a10: #ff0089ef;\n    --magenta-a11: #ff89b8;\n    --magenta-a12: #ffcfe0;\n    --gray-2-translucent: #1d1d1d80;\n    --mauve-2-translucent: #1e1d1e80;\n    --slate-2-translucent: #1b1d1e80;\n    --sage-2-translucent: #1a1c1b80;\n    --olive-2-translucent: #1b1c1a80;\n    --sand-2-translucent: #1d1d1b80;\n    --gray-surface: #21212180;\n    --mauve-surface: #22212380;\n    --slate-surface: #1f212380;\n    --sage-surface: #1e201f80;\n    --olive-surface: #1f201e80;\n    --sand-surface: #21212080;\n    --tomato-surface: #2d191580;\n    --red-surface: #2f151780;\n    --ruby-surface: #2b191d80;\n    --crimson-surface: #2f151f80;\n    --pink-surface: #31132980;\n    --plum-surface: #2f152f80;\n    --purple-surface: #2b173580;\n    --violet-surface: #25193980;\n    --iris-surface: #1d1b3980;\n    --cyan-surface: #11252d80;\n    --teal-surface: #13272580;\n    --jade-surface: #13271f80;\n    --green-surface: #15251d80;\n    --grass-surface: #19231b80;\n    --brown-surface: #271f1b80;\n    --bronze-surface: #27211d80;\n    --gold-surface: #25231d80;\n    --sky-surface: #13233b80;\n    --mint-surface: #15272780;\n    --yellow-surface: #231f1380;\n    --amber-surface: #271f1380;\n    --blue-surface: #0e1d3d80;\n    --orange-surface: #2d171380;\n    --indigo-surface: #1b174580;\n    --magenta-surface: #31131d80;\n    --lemon-surface: #1d210e80;\n    --lime-surface: #13251180;\n  }\n  @supports (color: color(display-p3 1 1 1)) {\n    @media (color-gamut: p3) {\n      .dark, .dark-theme {\n        --tomato-1: color(display-p3 0.09 0.068 0.067);\n        --tomato-2: color(display-p3 0.115 0.084 0.076);\n        --tomato-3: color(display-p3 0.205 0.097 0.083);\n        --tomato-4: color(display-p3 0.282 0.099 0.077);\n        --tomato-5: color(display-p3 0.339 0.129 0.101);\n        --tomato-6: color(display-p3 0.398 0.179 0.141);\n        --tomato-7: color(display-p3 0.487 0.245 0.194);\n        --tomato-8: color(display-p3 0.629 0.322 0.248);\n        --tomato-9: color(display-p3 0.831 0.345 0.231);\n        --tomato-10: color(display-p3 0.862 0.415 0.298);\n        --tomato-11: color(display-p3 1 0.585 0.455);\n        --tomato-12: color(display-p3 0.959 0.833 0.802);\n        --tomato-a1: color(display-p3 0.973 0.071 0.071 / 0.026);\n        --tomato-a2: color(display-p3 0.992 0.376 0.224 / 0.051);\n        --tomato-a3: color(display-p3 0.996 0.282 0.176 / 0.148);\n        --tomato-a4: color(display-p3 1 0.204 0.118 / 0.232);\n        --tomato-a5: color(display-p3 1 0.286 0.192 / 0.29);\n        --tomato-a6: color(display-p3 1 0.392 0.278 / 0.353);\n        --tomato-a7: color(display-p3 1 0.459 0.349 / 0.45);\n        --tomato-a8: color(display-p3 1 0.49 0.369 / 0.601);\n        --tomato-a9: color(display-p3 1 0.408 0.267 / 0.82);\n        --tomato-a10: color(display-p3 1 0.478 0.341 / 0.853);\n        --tomato-a11: color(display-p3 1 0.585 0.455);\n        --tomato-a12: color(display-p3 0.959 0.833 0.802);\n        --red-1: color(display-p3 0.093 0.068 0.067);\n        --red-2: color(display-p3 0.118 0.077 0.079);\n        --red-3: color(display-p3 0.211 0.081 0.099);\n        --red-4: color(display-p3 0.287 0.079 0.113);\n        --red-5: color(display-p3 0.348 0.11 0.142);\n        --red-6: color(display-p3 0.414 0.16 0.183);\n        --red-7: color(display-p3 0.508 0.224 0.236);\n        --red-8: color(display-p3 0.659 0.298 0.297);\n        --red-9: color(display-p3 0.83 0.329 0.324);\n        --red-10: color(display-p3 0.861 0.403 0.387);\n        --red-11: color(display-p3 1 0.57 0.55);\n        --red-12: color(display-p3 0.971 0.826 0.852);\n        --red-a1: color(display-p3 0.984 0.071 0.071 / 0.03);\n        --red-a2: color(display-p3 0.996 0.282 0.282 / 0.055);\n        --red-a3: color(display-p3 1 0.169 0.271 / 0.156);\n        --red-a4: color(display-p3 1 0.118 0.267 / 0.236);\n        --red-a5: color(display-p3 1 0.212 0.314 / 0.303);\n        --red-a6: color(display-p3 1 0.318 0.38 / 0.374);\n        --red-a7: color(display-p3 1 0.4 0.424 / 0.475);\n        --red-a8: color(display-p3 1 0.431 0.431 / 0.635);\n        --red-a9: color(display-p3 1 0.388 0.384 / 0.82);\n        --red-a10: color(display-p3 1 0.463 0.447 / 0.853);\n        --red-a11: color(display-p3 1 0.57 0.55);\n        --red-a12: color(display-p3 0.971 0.826 0.852);\n        --ruby-1: color(display-p3 0.093 0.068 0.074);\n        --ruby-2: color(display-p3 0.113 0.083 0.089);\n        --ruby-3: color(display-p3 0.208 0.088 0.117);\n        --ruby-4: color(display-p3 0.279 0.092 0.147);\n        --ruby-5: color(display-p3 0.337 0.12 0.18);\n        --ruby-6: color(display-p3 0.401 0.166 0.223);\n        --ruby-7: color(display-p3 0.495 0.224 0.281);\n        --ruby-8: color(display-p3 0.652 0.295 0.359);\n        --ruby-9: color(display-p3 0.83 0.323 0.408);\n        --ruby-10: color(display-p3 0.857 0.392 0.455);\n        --ruby-11: color(display-p3 1 0.57 0.59);\n        --ruby-12: color(display-p3 0.968 0.83 0.88);\n        --ruby-a1: color(display-p3 0.984 0.071 0.329 / 0.03);\n        --ruby-a2: color(display-p3 0.992 0.376 0.529 / 0.051);\n        --ruby-a3: color(display-p3 0.996 0.196 0.404 / 0.152);\n        --ruby-a4: color(display-p3 1 0.173 0.416 / 0.227);\n        --ruby-a5: color(display-p3 1 0.259 0.459 / 0.29);\n        --ruby-a6: color(display-p3 1 0.341 0.506 / 0.358);\n        --ruby-a7: color(display-p3 1 0.412 0.541 / 0.458);\n        --ruby-a8: color(display-p3 1 0.431 0.537 / 0.627);\n        --ruby-a9: color(display-p3 1 0.376 0.482 / 0.82);\n        --ruby-a10: color(display-p3 1 0.447 0.522 / 0.849);\n        --ruby-a11: color(display-p3 1 0.57 0.59);\n        --ruby-a12: color(display-p3 0.968 0.83 0.88);\n        --crimson-1: color(display-p3 0.093 0.068 0.078);\n        --crimson-2: color(display-p3 0.117 0.078 0.095);\n        --crimson-3: color(display-p3 0.203 0.091 0.143);\n        --crimson-4: color(display-p3 0.277 0.087 0.182);\n        --crimson-5: color(display-p3 0.332 0.115 0.22);\n        --crimson-6: color(display-p3 0.394 0.162 0.268);\n        --crimson-7: color(display-p3 0.489 0.222 0.336);\n        --crimson-8: color(display-p3 0.638 0.289 0.429);\n        --crimson-9: color(display-p3 0.843 0.298 0.507);\n        --crimson-10: color(display-p3 0.864 0.364 0.539);\n        --crimson-11: color(display-p3 1 0.56 0.66);\n        --crimson-12: color(display-p3 0.966 0.834 0.906);\n        --crimson-a1: color(display-p3 0.984 0.071 0.463 / 0.03);\n        --crimson-a2: color(display-p3 0.996 0.282 0.569 / 0.055);\n        --crimson-a3: color(display-p3 0.996 0.227 0.573 / 0.148);\n        --crimson-a4: color(display-p3 1 0.157 0.569 / 0.227);\n        --crimson-a5: color(display-p3 1 0.231 0.604 / 0.286);\n        --crimson-a6: color(display-p3 1 0.337 0.643 / 0.349);\n        --crimson-a7: color(display-p3 1 0.416 0.663 / 0.454);\n        --crimson-a8: color(display-p3 0.996 0.427 0.651 / 0.614);\n        --crimson-a9: color(display-p3 1 0.345 0.596 / 0.832);\n        --crimson-a10: color(display-p3 1 0.42 0.62 / 0.853);\n        --crimson-a11: color(display-p3 1 0.56 0.66);\n        --crimson-a12: color(display-p3 0.966 0.834 0.906);\n        --pink-1: color(display-p3 0.093 0.068 0.089);\n        --pink-2: color(display-p3 0.121 0.073 0.11);\n        --pink-3: color(display-p3 0.198 0.098 0.179);\n        --pink-4: color(display-p3 0.271 0.095 0.231);\n        --pink-5: color(display-p3 0.32 0.127 0.273);\n        --pink-6: color(display-p3 0.382 0.177 0.326);\n        --pink-7: color(display-p3 0.477 0.238 0.405);\n        --pink-8: color(display-p3 0.612 0.304 0.51);\n        --pink-9: color(display-p3 0.775 0.297 0.61);\n        --pink-10: color(display-p3 0.808 0.356 0.645);\n        --pink-11: color(display-p3 1 0.535 0.78);\n        --pink-12: color(display-p3 0.964 0.826 0.912);\n        --pink-a1: color(display-p3 0.984 0.071 0.855 / 0.03);\n        --pink-a2: color(display-p3 1 0.2 0.8 / 0.059);\n        --pink-a3: color(display-p3 1 0.294 0.886 / 0.139);\n        --pink-a4: color(display-p3 1 0.192 0.82 / 0.219);\n        --pink-a5: color(display-p3 1 0.282 0.827 / 0.274);\n        --pink-a6: color(display-p3 1 0.396 0.835 / 0.337);\n        --pink-a7: color(display-p3 1 0.459 0.831 / 0.442);\n        --pink-a8: color(display-p3 1 0.478 0.827 / 0.585);\n        --pink-a9: color(display-p3 1 0.373 0.784 / 0.761);\n        --pink-a10: color(display-p3 1 0.435 0.792 / 0.795);\n        --pink-a11: color(display-p3 1 0.535 0.78);\n        --pink-a12: color(display-p3 0.964 0.826 0.912);\n        --plum-1: color(display-p3 0.09 0.068 0.092);\n        --plum-2: color(display-p3 0.118 0.077 0.121);\n        --plum-3: color(display-p3 0.192 0.105 0.202);\n        --plum-4: color(display-p3 0.25 0.121 0.271);\n        --plum-5: color(display-p3 0.293 0.152 0.319);\n        --plum-6: color(display-p3 0.343 0.198 0.372);\n        --plum-7: color(display-p3 0.424 0.262 0.461);\n        --plum-8: color(display-p3 0.54 0.341 0.595);\n        --plum-9: color(display-p3 0.624 0.313 0.708);\n        --plum-10: color(display-p3 0.666 0.365 0.748);\n        --plum-11: color(display-p3 0.86 0.602 0.933);\n        --plum-12: color(display-p3 0.936 0.836 0.949);\n        --plum-a1: color(display-p3 0.973 0.071 0.973 / 0.026);\n        --plum-a2: color(display-p3 0.933 0.267 1 / 0.059);\n        --plum-a3: color(display-p3 0.918 0.333 0.996 / 0.148);\n        --plum-a4: color(display-p3 0.91 0.318 1 / 0.219);\n        --plum-a5: color(display-p3 0.914 0.388 1 / 0.269);\n        --plum-a6: color(display-p3 0.906 0.463 1 / 0.328);\n        --plum-a7: color(display-p3 0.906 0.529 1 / 0.425);\n        --plum-a8: color(display-p3 0.906 0.553 1 / 0.568);\n        --plum-a9: color(display-p3 0.875 0.427 1 / 0.69);\n        --plum-a10: color(display-p3 0.886 0.471 0.996 / 0.732);\n        --plum-a11: color(display-p3 0.86 0.602 0.933);\n        --plum-a12: color(display-p3 0.936 0.836 0.949);\n        --purple-1: color(display-p3 0.09 0.068 0.103);\n        --purple-2: color(display-p3 0.113 0.082 0.134);\n        --purple-3: color(display-p3 0.175 0.112 0.224);\n        --purple-4: color(display-p3 0.224 0.137 0.297);\n        --purple-5: color(display-p3 0.264 0.167 0.349);\n        --purple-6: color(display-p3 0.311 0.208 0.406);\n        --purple-7: color(display-p3 0.381 0.266 0.496);\n        --purple-8: color(display-p3 0.49 0.349 0.649);\n        --purple-9: color(display-p3 0.523 0.318 0.751);\n        --purple-10: color(display-p3 0.57 0.373 0.791);\n        --purple-11: color(display-p3 0.8 0.62 1);\n        --purple-12: color(display-p3 0.913 0.854 0.971);\n        --purple-a1: color(display-p3 0.686 0.071 0.996 / 0.038);\n        --purple-a2: color(display-p3 0.722 0.286 0.996 / 0.072);\n        --purple-a3: color(display-p3 0.718 0.349 0.996 / 0.169);\n        --purple-a4: color(display-p3 0.702 0.353 1 / 0.248);\n        --purple-a5: color(display-p3 0.718 0.404 1 / 0.303);\n        --purple-a6: color(display-p3 0.733 0.455 1 / 0.366);\n        --purple-a7: color(display-p3 0.753 0.506 1 / 0.458);\n        --purple-a8: color(display-p3 0.749 0.522 1 / 0.622);\n        --purple-a9: color(display-p3 0.686 0.408 1 / 0.736);\n        --purple-a10: color(display-p3 0.71 0.459 1 / 0.778);\n        --purple-a11: color(display-p3 0.8 0.62 1);\n        --purple-a12: color(display-p3 0.913 0.854 0.971);\n        --violet-1: color(display-p3 0.077 0.071 0.118);\n        --violet-2: color(display-p3 0.101 0.084 0.141);\n        --violet-3: color(display-p3 0.154 0.123 0.256);\n        --violet-4: color(display-p3 0.191 0.148 0.345);\n        --violet-5: color(display-p3 0.226 0.182 0.396);\n        --violet-6: color(display-p3 0.269 0.223 0.449);\n        --violet-7: color(display-p3 0.326 0.277 0.53);\n        --violet-8: color(display-p3 0.399 0.346 0.656);\n        --violet-9: color(display-p3 0.417 0.341 0.784);\n        --violet-10: color(display-p3 0.477 0.402 0.823);\n        --violet-11: color(display-p3 0.72 0.65 1);\n        --violet-12: color(display-p3 0.883 0.867 0.986);\n        --violet-a1: color(display-p3 0.282 0.141 0.996 / 0.055);\n        --violet-a2: color(display-p3 0.51 0.263 1 / 0.08);\n        --violet-a3: color(display-p3 0.494 0.337 0.996 / 0.202);\n        --violet-a4: color(display-p3 0.49 0.345 1 / 0.299);\n        --violet-a5: color(display-p3 0.525 0.392 1 / 0.353);\n        --violet-a6: color(display-p3 0.569 0.455 1 / 0.408);\n        --violet-a7: color(display-p3 0.588 0.494 1 / 0.496);\n        --violet-a8: color(display-p3 0.596 0.51 1 / 0.631);\n        --violet-a9: color(display-p3 0.522 0.424 1 / 0.769);\n        --violet-a10: color(display-p3 0.576 0.482 1 / 0.811);\n        --violet-a11: color(display-p3 0.72 0.65 1);\n        --violet-a12: color(display-p3 0.883 0.867 0.986);\n        --iris-1: color(display-p3 0.075 0.075 0.114);\n        --iris-2: color(display-p3 0.089 0.086 0.14);\n        --iris-3: color(display-p3 0.128 0.134 0.272);\n        --iris-4: color(display-p3 0.153 0.165 0.382);\n        --iris-5: color(display-p3 0.192 0.201 0.44);\n        --iris-6: color(display-p3 0.239 0.241 0.491);\n        --iris-7: color(display-p3 0.291 0.289 0.565);\n        --iris-8: color(display-p3 0.35 0.345 0.673);\n        --iris-9: color(display-p3 0.357 0.357 0.81);\n        --iris-10: color(display-p3 0.428 0.416 0.843);\n        --iris-11: color(display-p3 0.685 0.662 1);\n        --iris-12: color(display-p3 0.878 0.875 0.986);\n        --iris-a1: color(display-p3 0.224 0.224 0.992 / 0.051);\n        --iris-a2: color(display-p3 0.361 0.314 1 / 0.08);\n        --iris-a3: color(display-p3 0.357 0.373 1 / 0.219);\n        --iris-a4: color(display-p3 0.325 0.361 1 / 0.337);\n        --iris-a5: color(display-p3 0.38 0.4 1 / 0.4);\n        --iris-a6: color(display-p3 0.447 0.447 1 / 0.454);\n        --iris-a7: color(display-p3 0.486 0.486 1 / 0.534);\n        --iris-a8: color(display-p3 0.502 0.494 1 / 0.652);\n        --iris-a9: color(display-p3 0.431 0.431 1 / 0.799);\n        --iris-a10: color(display-p3 0.502 0.486 1 / 0.832);\n        --iris-a11: color(display-p3 0.685 0.662 1);\n        --iris-a12: color(display-p3 0.878 0.875 0.986);\n        --cyan-1: color(display-p3 0.053 0.085 0.098);\n        --cyan-2: color(display-p3 0.072 0.105 0.122);\n        --cyan-3: color(display-p3 0.073 0.168 0.209);\n        --cyan-4: color(display-p3 0.063 0.216 0.277);\n        --cyan-5: color(display-p3 0.091 0.267 0.336);\n        --cyan-6: color(display-p3 0.137 0.324 0.4);\n        --cyan-7: color(display-p3 0.186 0.398 0.484);\n        --cyan-8: color(display-p3 0.23 0.496 0.6);\n        --cyan-9: color(display-p3 0.282 0.627 0.765);\n        --cyan-10: color(display-p3 0.331 0.675 0.801);\n        --cyan-11: color(display-p3 0.446 0.79 0.887);\n        --cyan-12: color(display-p3 0.757 0.919 0.962);\n        --cyan-a1: color(display-p3 0 0.647 0.992 / 0.034);\n        --cyan-a2: color(display-p3 0.133 0.733 1 / 0.059);\n        --cyan-a3: color(display-p3 0.122 0.741 0.996 / 0.152);\n        --cyan-a4: color(display-p3 0.051 0.725 1 / 0.227);\n        --cyan-a5: color(display-p3 0.149 0.757 1 / 0.29);\n        --cyan-a6: color(display-p3 0.267 0.792 1 / 0.358);\n        --cyan-a7: color(display-p3 0.333 0.808 1 / 0.446);\n        --cyan-a8: color(display-p3 0.357 0.816 1 / 0.572);\n        --cyan-a9: color(display-p3 0.357 0.82 1 / 0.748);\n        --cyan-a10: color(display-p3 0.4 0.839 1 / 0.786);\n        --cyan-a11: color(display-p3 0.446 0.79 0.887);\n        --cyan-a12: color(display-p3 0.757 0.919 0.962);\n        --teal-1: color(display-p3 0.059 0.083 0.079);\n        --teal-2: color(display-p3 0.075 0.11 0.107);\n        --teal-3: color(display-p3 0.087 0.175 0.165);\n        --teal-4: color(display-p3 0.087 0.227 0.214);\n        --teal-5: color(display-p3 0.12 0.277 0.261);\n        --teal-6: color(display-p3 0.162 0.335 0.314);\n        --teal-7: color(display-p3 0.205 0.406 0.379);\n        --teal-8: color(display-p3 0.245 0.489 0.453);\n        --teal-9: color(display-p3 0.297 0.637 0.581);\n        --teal-10: color(display-p3 0.319 0.69 0.62);\n        --teal-11: color(display-p3 0.388 0.835 0.719);\n        --teal-12: color(display-p3 0.734 0.934 0.87);\n        --teal-a1: color(display-p3 0 0.992 0.761 / 0.017);\n        --teal-a2: color(display-p3 0.235 0.988 0.902 / 0.047);\n        --teal-a3: color(display-p3 0.235 1 0.898 / 0.118);\n        --teal-a4: color(display-p3 0.18 0.996 0.929 / 0.173);\n        --teal-a5: color(display-p3 0.31 1 0.933 / 0.227);\n        --teal-a6: color(display-p3 0.396 1 0.933 / 0.286);\n        --teal-a7: color(display-p3 0.443 1 0.925 / 0.366);\n        --teal-a8: color(display-p3 0.459 1 0.925 / 0.454);\n        --teal-a9: color(display-p3 0.443 0.996 0.906 / 0.61);\n        --teal-a10: color(display-p3 0.439 0.996 0.89 / 0.669);\n        --teal-a11: color(display-p3 0.388 0.835 0.719);\n        --teal-a12: color(display-p3 0.734 0.934 0.87);\n        --jade-1: color(display-p3 0.059 0.083 0.071);\n        --jade-2: color(display-p3 0.078 0.11 0.094);\n        --jade-3: color(display-p3 0.091 0.176 0.138);\n        --jade-4: color(display-p3 0.102 0.228 0.177);\n        --jade-5: color(display-p3 0.133 0.279 0.221);\n        --jade-6: color(display-p3 0.174 0.334 0.273);\n        --jade-7: color(display-p3 0.219 0.402 0.335);\n        --jade-8: color(display-p3 0.263 0.488 0.411);\n        --jade-9: color(display-p3 0.319 0.63 0.521);\n        --jade-10: color(display-p3 0.338 0.68 0.555);\n        --jade-11: color(display-p3 0.4 0.835 0.656);\n        --jade-12: color(display-p3 0.734 0.934 0.838);\n        --jade-a1: color(display-p3 0 0.992 0.298 / 0.017);\n        --jade-a2: color(display-p3 0.318 0.988 0.651 / 0.047);\n        --jade-a3: color(display-p3 0.267 1 0.667 / 0.118);\n        --jade-a4: color(display-p3 0.275 0.996 0.702 / 0.173);\n        --jade-a5: color(display-p3 0.361 1 0.741 / 0.227);\n        --jade-a6: color(display-p3 0.439 1 0.796 / 0.286);\n        --jade-a7: color(display-p3 0.49 1 0.804 / 0.362);\n        --jade-a8: color(display-p3 0.506 1 0.835 / 0.45);\n        --jade-a9: color(display-p3 0.478 0.996 0.816 / 0.606);\n        --jade-a10: color(display-p3 0.478 1 0.816 / 0.656);\n        --jade-a11: color(display-p3 0.4 0.835 0.656);\n        --jade-a12: color(display-p3 0.734 0.934 0.838);\n        --green-1: color(display-p3 0.062 0.083 0.071);\n        --green-2: color(display-p3 0.079 0.106 0.09);\n        --green-3: color(display-p3 0.1 0.173 0.133);\n        --green-4: color(display-p3 0.115 0.229 0.166);\n        --green-5: color(display-p3 0.147 0.282 0.206);\n        --green-6: color(display-p3 0.185 0.338 0.25);\n        --green-7: color(display-p3 0.227 0.403 0.298);\n        --green-8: color(display-p3 0.27 0.479 0.351);\n        --green-9: color(display-p3 0.332 0.634 0.442);\n        --green-10: color(display-p3 0.357 0.682 0.474);\n        --green-11: color(display-p3 0.434 0.828 0.573);\n        --green-12: color(display-p3 0.747 0.938 0.807);\n        --green-a1: color(display-p3 0 0.992 0.298 / 0.017);\n        --green-a2: color(display-p3 0.341 0.98 0.616 / 0.043);\n        --green-a3: color(display-p3 0.376 0.996 0.655 / 0.114);\n        --green-a4: color(display-p3 0.341 0.996 0.635 / 0.173);\n        --green-a5: color(display-p3 0.408 1 0.678 / 0.232);\n        --green-a6: color(display-p3 0.475 1 0.706 / 0.29);\n        --green-a7: color(display-p3 0.514 1 0.706 / 0.362);\n        --green-a8: color(display-p3 0.529 1 0.718 / 0.442);\n        --green-a9: color(display-p3 0.502 0.996 0.682 / 0.61);\n        --green-a10: color(display-p3 0.506 1 0.682 / 0.66);\n        --green-a11: color(display-p3 0.434 0.828 0.573);\n        --green-a12: color(display-p3 0.747 0.938 0.807);\n        --grass-1: color(display-p3 0.062 0.083 0.067);\n        --grass-2: color(display-p3 0.083 0.103 0.085);\n        --grass-3: color(display-p3 0.118 0.163 0.122);\n        --grass-4: color(display-p3 0.142 0.225 0.15);\n        --grass-5: color(display-p3 0.178 0.279 0.186);\n        --grass-6: color(display-p3 0.217 0.337 0.224);\n        --grass-7: color(display-p3 0.258 0.4 0.264);\n        --grass-8: color(display-p3 0.302 0.47 0.305);\n        --grass-9: color(display-p3 0.38 0.647 0.378);\n        --grass-10: color(display-p3 0.426 0.694 0.426);\n        --grass-11: color(display-p3 0.535 0.807 0.542);\n        --grass-12: color(display-p3 0.797 0.936 0.776);\n        --grass-a1: color(display-p3 0 0.992 0.071 / 0.017);\n        --grass-a2: color(display-p3 0.482 0.996 0.584 / 0.038);\n        --grass-a3: color(display-p3 0.549 0.992 0.588 / 0.106);\n        --grass-a4: color(display-p3 0.51 0.996 0.557 / 0.169);\n        --grass-a5: color(display-p3 0.553 1 0.588 / 0.227);\n        --grass-a6: color(display-p3 0.584 1 0.608 / 0.29);\n        --grass-a7: color(display-p3 0.604 1 0.616 / 0.358);\n        --grass-a8: color(display-p3 0.608 1 0.62 / 0.433);\n        --grass-a9: color(display-p3 0.573 1 0.569 / 0.622);\n        --grass-a10: color(display-p3 0.6 0.996 0.6 / 0.673);\n        --grass-a11: color(display-p3 0.535 0.807 0.542);\n        --grass-a12: color(display-p3 0.797 0.936 0.776);\n        --brown-1: color(display-p3 0.071 0.067 0.059);\n        --brown-2: color(display-p3 0.107 0.095 0.087);\n        --brown-3: color(display-p3 0.151 0.13 0.115);\n        --brown-4: color(display-p3 0.191 0.161 0.138);\n        --brown-5: color(display-p3 0.235 0.194 0.162);\n        --brown-6: color(display-p3 0.291 0.237 0.192);\n        --brown-7: color(display-p3 0.365 0.295 0.232);\n        --brown-8: color(display-p3 0.469 0.377 0.287);\n        --brown-9: color(display-p3 0.651 0.505 0.368);\n        --brown-10: color(display-p3 0.697 0.557 0.423);\n        --brown-11: color(display-p3 0.835 0.715 0.597);\n        --brown-12: color(display-p3 0.938 0.885 0.802);\n        --brown-a1: color(display-p3 0.855 0.071 0 / 0.005);\n        --brown-a2: color(display-p3 0.98 0.706 0.525 / 0.043);\n        --brown-a3: color(display-p3 0.996 0.745 0.576 / 0.093);\n        --brown-a4: color(display-p3 1 0.765 0.592 / 0.135);\n        --brown-a5: color(display-p3 1 0.761 0.588 / 0.181);\n        --brown-a6: color(display-p3 1 0.773 0.592 / 0.24);\n        --brown-a7: color(display-p3 0.996 0.776 0.58 / 0.32);\n        --brown-a8: color(display-p3 1 0.78 0.573 / 0.433);\n        --brown-a9: color(display-p3 1 0.769 0.549 / 0.627);\n        --brown-a10: color(display-p3 1 0.792 0.596 / 0.677);\n        --brown-a11: color(display-p3 0.835 0.715 0.597);\n        --brown-a12: color(display-p3 0.938 0.885 0.802);\n        --sky-1: color(display-p3 0.056 0.078 0.116);\n        --sky-2: color(display-p3 0.075 0.101 0.149);\n        --sky-3: color(display-p3 0.089 0.154 0.244);\n        --sky-4: color(display-p3 0.106 0.207 0.323);\n        --sky-5: color(display-p3 0.135 0.261 0.394);\n        --sky-6: color(display-p3 0.17 0.322 0.469);\n        --sky-7: color(display-p3 0.205 0.394 0.557);\n        --sky-8: color(display-p3 0.232 0.48 0.665);\n        --sky-9: color(display-p3 0.585 0.877 0.983);\n        --sky-10: color(display-p3 0.718 0.925 0.991);\n        --sky-11: color(display-p3 0.536 0.772 0.924);\n        --sky-12: color(display-p3 0.799 0.947 0.993);\n        --sky-a1: color(display-p3 0 0.282 0.996 / 0.055);\n        --sky-a2: color(display-p3 0.157 0.467 0.992 / 0.089);\n        --sky-a3: color(display-p3 0.192 0.522 0.996 / 0.19);\n        --sky-a4: color(display-p3 0.212 0.584 1 / 0.274);\n        --sky-a5: color(display-p3 0.259 0.631 1 / 0.349);\n        --sky-a6: color(display-p3 0.302 0.655 1 / 0.433);\n        --sky-a7: color(display-p3 0.329 0.686 1 / 0.526);\n        --sky-a8: color(display-p3 0.325 0.71 1 / 0.643);\n        --sky-a9: color(display-p3 0.592 0.894 1 / 0.984);\n        --sky-a10: color(display-p3 0.722 0.933 1 / 0.992);\n        --sky-a11: color(display-p3 0.536 0.772 0.924);\n        --sky-a12: color(display-p3 0.799 0.947 0.993);\n        --mint-1: color(display-p3 0.059 0.082 0.081);\n        --mint-2: color(display-p3 0.068 0.104 0.105);\n        --mint-3: color(display-p3 0.077 0.17 0.168);\n        --mint-4: color(display-p3 0.068 0.224 0.22);\n        --mint-5: color(display-p3 0.104 0.275 0.264);\n        --mint-6: color(display-p3 0.154 0.332 0.313);\n        --mint-7: color(display-p3 0.207 0.403 0.373);\n        --mint-8: color(display-p3 0.258 0.49 0.441);\n        --mint-9: color(display-p3 0.62 0.908 0.834);\n        --mint-10: color(display-p3 0.725 0.954 0.898);\n        --mint-11: color(display-p3 0.482 0.825 0.733);\n        --mint-12: color(display-p3 0.807 0.955 0.887);\n        --mint-a1: color(display-p3 0 0.992 0.992 / 0.017);\n        --mint-a2: color(display-p3 0.071 0.98 0.98 / 0.043);\n        --mint-a3: color(display-p3 0.176 0.996 0.996 / 0.11);\n        --mint-a4: color(display-p3 0.071 0.996 0.973 / 0.169);\n        --mint-a5: color(display-p3 0.243 1 0.949 / 0.223);\n        --mint-a6: color(display-p3 0.369 1 0.933 / 0.286);\n        --mint-a7: color(display-p3 0.459 1 0.914 / 0.362);\n        --mint-a8: color(display-p3 0.49 1 0.89 / 0.454);\n        --mint-a9: color(display-p3 0.678 0.996 0.914 / 0.904);\n        --mint-a10: color(display-p3 0.761 1 0.941 / 0.95);\n        --mint-a11: color(display-p3 0.482 0.825 0.733);\n        --mint-a12: color(display-p3 0.807 0.955 0.887);\n        --yellow-1: color(display-p3 0.078 0.069 0.047);\n        --yellow-2: color(display-p3 0.103 0.094 0.063);\n        --yellow-3: color(display-p3 0.168 0.137 0.039);\n        --yellow-4: color(display-p3 0.209 0.169 0);\n        --yellow-5: color(display-p3 0.255 0.209 0);\n        --yellow-6: color(display-p3 0.31 0.261 0.07);\n        --yellow-7: color(display-p3 0.389 0.331 0.135);\n        --yellow-8: color(display-p3 0.497 0.42 0.182);\n        --yellow-9: color(display-p3 1 0.92 0.22);\n        --yellow-10: color(display-p3 1 1 0.456);\n        --yellow-11: color(display-p3 0.948 0.885 0.392);\n        --yellow-12: color(display-p3 0.959 0.934 0.731);\n        --yellow-a1: color(display-p3 0.973 0.369 0 / 0.013);\n        --yellow-a2: color(display-p3 0.996 0.792 0 / 0.038);\n        --yellow-a3: color(display-p3 0.996 0.71 0 / 0.11);\n        --yellow-a4: color(display-p3 0.996 0.741 0 / 0.152);\n        --yellow-a5: color(display-p3 0.996 0.765 0 / 0.202);\n        --yellow-a6: color(display-p3 0.996 0.816 0.082 / 0.261);\n        --yellow-a7: color(display-p3 1 0.831 0.263 / 0.345);\n        --yellow-a8: color(display-p3 1 0.831 0.314 / 0.463);\n        --yellow-a9: color(display-p3 1 0.922 0.22);\n        --yellow-a10: color(display-p3 1 1 0.455);\n        --yellow-a11: color(display-p3 0.948 0.885 0.392);\n        --yellow-a12: color(display-p3 0.959 0.934 0.731);\n        --amber-1: color(display-p3 0.082 0.07 0.05);\n        --amber-2: color(display-p3 0.111 0.094 0.064);\n        --amber-3: color(display-p3 0.178 0.128 0.049);\n        --amber-4: color(display-p3 0.239 0.156 0);\n        --amber-5: color(display-p3 0.29 0.193 0);\n        --amber-6: color(display-p3 0.344 0.245 0.076);\n        --amber-7: color(display-p3 0.422 0.314 0.141);\n        --amber-8: color(display-p3 0.535 0.399 0.189);\n        --amber-9: color(display-p3 1 0.77 0.26);\n        --amber-10: color(display-p3 1 0.87 0.15);\n        --amber-11: color(display-p3 1 0.8 0.29);\n        --amber-12: color(display-p3 0.984 0.909 0.726);\n        --amber-a1: color(display-p3 0.992 0.298 0 / 0.017);\n        --amber-a2: color(display-p3 0.988 0.651 0 / 0.047);\n        --amber-a3: color(display-p3 1 0.6 0 / 0.118);\n        --amber-a4: color(display-p3 1 0.557 0 / 0.185);\n        --amber-a5: color(display-p3 1 0.592 0 / 0.24);\n        --amber-a6: color(display-p3 1 0.659 0.094 / 0.299);\n        --amber-a7: color(display-p3 1 0.714 0.263 / 0.383);\n        --amber-a8: color(display-p3 0.996 0.729 0.306 / 0.5);\n        --amber-a9: color(display-p3 1 0.769 0.259);\n        --amber-a10: color(display-p3 1 0.871 0.149);\n        --amber-a11: color(display-p3 1 0.8 0.29);\n        --amber-a12: color(display-p3 0.984 0.909 0.726);\n        --gold-1: color(display-p3 0.071 0.071 0.067);\n        --gold-2: color(display-p3 0.104 0.101 0.09);\n        --gold-3: color(display-p3 0.141 0.136 0.122);\n        --gold-4: color(display-p3 0.177 0.17 0.152);\n        --gold-5: color(display-p3 0.217 0.207 0.185);\n        --gold-6: color(display-p3 0.265 0.252 0.225);\n        --gold-7: color(display-p3 0.327 0.31 0.277);\n        --gold-8: color(display-p3 0.407 0.384 0.342);\n        --gold-9: color(display-p3 0.579 0.517 0.41);\n        --gold-10: color(display-p3 0.628 0.566 0.463);\n        --gold-11: color(display-p3 0.784 0.728 0.635);\n        --gold-12: color(display-p3 0.906 0.887 0.855);\n        --gold-a1: color(display-p3 0.855 0.855 0.071 / 0.005);\n        --gold-a2: color(display-p3 0.98 0.89 0.616 / 0.043);\n        --gold-a3: color(display-p3 1 0.949 0.753 / 0.08);\n        --gold-a4: color(display-p3 1 0.933 0.8 / 0.118);\n        --gold-a5: color(display-p3 1 0.949 0.804 / 0.16);\n        --gold-a6: color(display-p3 1 0.925 0.8 / 0.215);\n        --gold-a7: color(display-p3 1 0.945 0.831 / 0.278);\n        --gold-a8: color(display-p3 1 0.937 0.82 / 0.366);\n        --gold-a9: color(display-p3 0.996 0.882 0.69 / 0.551);\n        --gold-a10: color(display-p3 1 0.894 0.725 / 0.601);\n        --gold-a11: color(display-p3 0.784 0.728 0.635);\n        --gold-a12: color(display-p3 0.906 0.887 0.855);\n        --bronze-1: color(display-p3 0.076 0.067 0.063);\n        --bronze-2: color(display-p3 0.106 0.097 0.093);\n        --bronze-3: color(display-p3 0.147 0.132 0.125);\n        --bronze-4: color(display-p3 0.185 0.166 0.156);\n        --bronze-5: color(display-p3 0.227 0.202 0.19);\n        --bronze-6: color(display-p3 0.278 0.246 0.23);\n        --bronze-7: color(display-p3 0.343 0.302 0.281);\n        --bronze-8: color(display-p3 0.426 0.374 0.347);\n        --bronze-9: color(display-p3 0.611 0.507 0.455);\n        --bronze-10: color(display-p3 0.66 0.556 0.504);\n        --bronze-11: color(display-p3 0.81 0.707 0.655);\n        --bronze-12: color(display-p3 0.921 0.88 0.854);\n        --bronze-a1: color(display-p3 0.941 0.067 0 / 0.009);\n        --bronze-a2: color(display-p3 0.98 0.8 0.706 / 0.043);\n        --bronze-a3: color(display-p3 0.988 0.851 0.761 / 0.085);\n        --bronze-a4: color(display-p3 0.996 0.839 0.78 / 0.127);\n        --bronze-a5: color(display-p3 0.996 0.863 0.773 / 0.173);\n        --bronze-a6: color(display-p3 1 0.863 0.796 / 0.227);\n        --bronze-a7: color(display-p3 1 0.867 0.8 / 0.295);\n        --bronze-a8: color(display-p3 1 0.859 0.788 / 0.387);\n        --bronze-a9: color(display-p3 1 0.82 0.733 / 0.585);\n        --bronze-a10: color(display-p3 1 0.839 0.761 / 0.635);\n        --bronze-a11: color(display-p3 0.81 0.707 0.655);\n        --bronze-a12: color(display-p3 0.921 0.88 0.854);\n        --gray-1: color(display-p3 0.067 0.067 0.067);\n        --gray-2: color(display-p3 0.098 0.098 0.098);\n        --gray-3: color(display-p3 0.135 0.135 0.135);\n        --gray-4: color(display-p3 0.163 0.163 0.163);\n        --gray-5: color(display-p3 0.192 0.192 0.192);\n        --gray-6: color(display-p3 0.228 0.228 0.228);\n        --gray-7: color(display-p3 0.283 0.283 0.283);\n        --gray-8: color(display-p3 0.375 0.375 0.375);\n        --gray-9: color(display-p3 0.431 0.431 0.431);\n        --gray-10: color(display-p3 0.484 0.484 0.484);\n        --gray-11: color(display-p3 0.706 0.706 0.706);\n        --gray-12: color(display-p3 0.933 0.933 0.933);\n        --gray-a1: color(display-p3 0 0 0 / 0);\n        --gray-a2: color(display-p3 1 1 1 / 0.034);\n        --gray-a3: color(display-p3 1 1 1 / 0.071);\n        --gray-a4: color(display-p3 1 1 1 / 0.105);\n        --gray-a5: color(display-p3 1 1 1 / 0.134);\n        --gray-a6: color(display-p3 1 1 1 / 0.172);\n        --gray-a7: color(display-p3 1 1 1 / 0.231);\n        --gray-a8: color(display-p3 1 1 1 / 0.332);\n        --gray-a9: color(display-p3 1 1 1 / 0.391);\n        --gray-a10: color(display-p3 1 1 1 / 0.445);\n        --gray-a11: color(display-p3 1 1 1 / 0.685);\n        --gray-a12: color(display-p3 1 1 1 / 0.929);\n        --mauve-1: color(display-p3 0.07 0.067 0.074);\n        --mauve-2: color(display-p3 0.101 0.098 0.105);\n        --mauve-3: color(display-p3 0.138 0.134 0.144);\n        --mauve-4: color(display-p3 0.167 0.161 0.175);\n        --mauve-5: color(display-p3 0.196 0.189 0.206);\n        --mauve-6: color(display-p3 0.232 0.225 0.245);\n        --mauve-7: color(display-p3 0.286 0.277 0.302);\n        --mauve-8: color(display-p3 0.383 0.373 0.408);\n        --mauve-9: color(display-p3 0.434 0.428 0.467);\n        --mauve-10: color(display-p3 0.487 0.48 0.519);\n        --mauve-11: color(display-p3 0.707 0.7 0.735);\n        --mauve-12: color(display-p3 0.933 0.933 0.94);\n        --mauve-a1: color(display-p3 0 0 0 / 0);\n        --mauve-a2: color(display-p3 0.996 0.992 1 / 0.034);\n        --mauve-a3: color(display-p3 0.937 0.933 0.992 / 0.077);\n        --mauve-a4: color(display-p3 0.957 0.918 0.996 / 0.111);\n        --mauve-a5: color(display-p3 0.937 0.906 0.996 / 0.145);\n        --mauve-a6: color(display-p3 0.953 0.925 0.996 / 0.183);\n        --mauve-a7: color(display-p3 0.945 0.929 1 / 0.246);\n        --mauve-a8: color(display-p3 0.937 0.918 1 / 0.361);\n        --mauve-a9: color(display-p3 0.933 0.918 1 / 0.424);\n        --mauve-a10: color(display-p3 0.941 0.925 1 / 0.479);\n        --mauve-a11: color(display-p3 0.965 0.961 1 / 0.712);\n        --mauve-a12: color(display-p3 0.992 0.992 1 / 0.937);\n        --slate-1: color(display-p3 0.067 0.067 0.074);\n        --slate-2: color(display-p3 0.095 0.098 0.105);\n        --slate-3: color(display-p3 0.13 0.135 0.145);\n        --slate-4: color(display-p3 0.156 0.163 0.176);\n        --slate-5: color(display-p3 0.183 0.191 0.206);\n        --slate-6: color(display-p3 0.215 0.226 0.244);\n        --slate-7: color(display-p3 0.265 0.28 0.302);\n        --slate-8: color(display-p3 0.357 0.381 0.409);\n        --slate-9: color(display-p3 0.415 0.431 0.463);\n        --slate-10: color(display-p3 0.469 0.483 0.514);\n        --slate-11: color(display-p3 0.692 0.704 0.728);\n        --slate-12: color(display-p3 0.93 0.933 0.94);\n        --slate-a1: color(display-p3 0 0 0 / 0);\n        --slate-a2: color(display-p3 0.875 0.992 1 / 0.034);\n        --slate-a3: color(display-p3 0.882 0.933 0.992 / 0.077);\n        --slate-a4: color(display-p3 0.882 0.953 0.996 / 0.111);\n        --slate-a5: color(display-p3 0.878 0.929 0.996 / 0.145);\n        --slate-a6: color(display-p3 0.882 0.949 0.996 / 0.183);\n        --slate-a7: color(display-p3 0.882 0.929 1 / 0.246);\n        --slate-a8: color(display-p3 0.871 0.937 1 / 0.361);\n        --slate-a9: color(display-p3 0.898 0.937 1 / 0.42);\n        --slate-a10: color(display-p3 0.918 0.945 1 / 0.475);\n        --slate-a11: color(display-p3 0.949 0.969 0.996 / 0.708);\n        --slate-a12: color(display-p3 0.988 0.992 1 / 0.937);\n        --sage-1: color(display-p3 0.064 0.07 0.067);\n        --sage-2: color(display-p3 0.092 0.098 0.094);\n        --sage-3: color(display-p3 0.128 0.135 0.131);\n        --sage-4: color(display-p3 0.155 0.164 0.159);\n        --sage-5: color(display-p3 0.183 0.193 0.188);\n        --sage-6: color(display-p3 0.218 0.23 0.224);\n        --sage-7: color(display-p3 0.269 0.285 0.277);\n        --sage-8: color(display-p3 0.362 0.382 0.373);\n        --sage-9: color(display-p3 0.398 0.438 0.421);\n        --sage-10: color(display-p3 0.453 0.49 0.474);\n        --sage-11: color(display-p3 0.685 0.709 0.697);\n        --sage-12: color(display-p3 0.927 0.933 0.93);\n        --sage-a1: color(display-p3 0 0 0 / 0);\n        --sage-a2: color(display-p3 0.976 0.988 0.984 / 0.03);\n        --sage-a3: color(display-p3 0.992 0.945 0.941 / 0.072);\n        --sage-a4: color(display-p3 0.988 0.996 0.992 / 0.102);\n        --sage-a5: color(display-p3 0.992 1 0.996 / 0.131);\n        --sage-a6: color(display-p3 0.973 1 0.976 / 0.173);\n        --sage-a7: color(display-p3 0.957 1 0.976 / 0.233);\n        --sage-a8: color(display-p3 0.957 1 0.984 / 0.334);\n        --sage-a9: color(display-p3 0.902 1 0.957 / 0.397);\n        --sage-a10: color(display-p3 0.929 1 0.973 / 0.452);\n        --sage-a11: color(display-p3 0.969 1 0.988 / 0.688);\n        --sage-a12: color(display-p3 0.992 1 0.996 / 0.929);\n        --olive-1: color(display-p3 0.067 0.07 0.063);\n        --olive-2: color(display-p3 0.095 0.098 0.091);\n        --olive-3: color(display-p3 0.131 0.135 0.126);\n        --olive-4: color(display-p3 0.158 0.163 0.153);\n        --olive-5: color(display-p3 0.186 0.192 0.18);\n        --olive-6: color(display-p3 0.221 0.229 0.215);\n        --olive-7: color(display-p3 0.273 0.284 0.266);\n        --olive-8: color(display-p3 0.365 0.382 0.359);\n        --olive-9: color(display-p3 0.414 0.438 0.404);\n        --olive-10: color(display-p3 0.467 0.49 0.458);\n        --olive-11: color(display-p3 0.69 0.709 0.682);\n        --olive-12: color(display-p3 0.927 0.933 0.926);\n        --olive-a1: color(display-p3 0 0 0 / 0);\n        --olive-a2: color(display-p3 0.984 0.988 0.976 / 0.03);\n        --olive-a3: color(display-p3 0.992 0.996 0.988 / 0.068);\n        --olive-a4: color(display-p3 0.953 0.996 0.949 / 0.102);\n        --olive-a5: color(display-p3 0.969 1 0.965 / 0.131);\n        --olive-a6: color(display-p3 0.973 1 0.969 / 0.169);\n        --olive-a7: color(display-p3 0.98 1 0.961 / 0.228);\n        --olive-a8: color(display-p3 0.961 1 0.957 / 0.334);\n        --olive-a9: color(display-p3 0.949 1 0.922 / 0.397);\n        --olive-a10: color(display-p3 0.953 1 0.941 / 0.452);\n        --olive-a11: color(display-p3 0.976 1 0.965 / 0.688);\n        --olive-a12: color(display-p3 0.992 1 0.992 / 0.929);\n        --sand-1: color(display-p3 0.067 0.067 0.063);\n        --sand-2: color(display-p3 0.098 0.098 0.094);\n        --sand-3: color(display-p3 0.135 0.135 0.129);\n        --sand-4: color(display-p3 0.164 0.163 0.156);\n        --sand-5: color(display-p3 0.193 0.192 0.183);\n        --sand-6: color(display-p3 0.23 0.229 0.217);\n        --sand-7: color(display-p3 0.285 0.282 0.267);\n        --sand-8: color(display-p3 0.384 0.378 0.357);\n        --sand-9: color(display-p3 0.434 0.428 0.403);\n        --sand-10: color(display-p3 0.487 0.481 0.456);\n        --sand-11: color(display-p3 0.707 0.703 0.68);\n        --sand-12: color(display-p3 0.933 0.933 0.926);\n        --sand-a1: color(display-p3 0 0 0 / 0);\n        --sand-a2: color(display-p3 0.992 0.992 0.988 / 0.034);\n        --sand-a3: color(display-p3 0.996 0.996 0.992 / 0.072);\n        --sand-a4: color(display-p3 0.992 0.992 0.953 / 0.106);\n        --sand-a5: color(display-p3 1 1 0.965 / 0.135);\n        --sand-a6: color(display-p3 1 0.976 0.929 / 0.177);\n        --sand-a7: color(display-p3 1 0.984 0.929 / 0.236);\n        --sand-a8: color(display-p3 1 0.976 0.925 / 0.341);\n        --sand-a9: color(display-p3 1 0.98 0.925 / 0.395);\n        --sand-a10: color(display-p3 1 0.992 0.933 / 0.45);\n        --sand-a11: color(display-p3 1 0.996 0.961 / 0.685);\n        --sand-a12: color(display-p3 1 1 0.992 / 0.929);\n        --blue-1: color(display-p3 0.0465 0.0657 0.1105);\n        --blue-2: color(display-p3 0.0663 0.0906 0.1476);\n        --blue-3: color(display-p3 0.0859 0.1415 0.2862);\n        --blue-4: color(display-p3 0.1043 0.1801 0.3856);\n        --blue-5: color(display-p3 0.1332 0.2211 0.4586);\n        --blue-6: color(display-p3 0.1682 0.2649 0.5215);\n        --blue-7: color(display-p3 0.2045 0.3132 0.5992);\n        --blue-8: color(display-p3 0.2392 0.3661 0.7024);\n        --blue-9: color(display-p3 0.1632 0.3246 0.8163);\n        --blue-10: color(display-p3 0.290 0.420 0.760);\n        --blue-11: color(display-p3 0.5682 0.7055 1);\n        --blue-12: color(display-p3 0.8327 0.8855 1);\n        --blue-a1: color(display-p3 0 0.0667 0.9882 / 0.047);\n        --blue-a2: color(display-p3 0.0667 0.3333 0.9922 / 0.089);\n        --blue-a3: color(display-p3 0.1529 0.4 1 / 0.236);\n        --blue-a4: color(display-p3 0.1843 0.4039 1 / 0.341);\n        --blue-a5: color(display-p3 0.2275 0.4314 1 / 0.421);\n        --blue-a6: color(display-p3 0.2745 0.4667 1 / 0.488);\n        --blue-a7: color(display-p3 0.3098 0.502 1 / 0.572);\n        --blue-a8: color(display-p3 0.3216 0.5059 1 / 0.681);\n        --blue-a9: color(display-p3 0.1922 0.3922 1 / 0.803);\n        --blue-a10: color(display-p3 0.430 0.590 0.970 / 0.660);\n        --blue-a11: color(display-p3 0.5843 0.7255 1 / 0.975);\n        --blue-a12: color(display-p3 0.8431 0.8941 1 / 0.988);\n        --orange-1: color(display-p3 0.0838 0.06 0.0528);\n        --orange-2: color(display-p3 0.1165 0.0814 0.0709);\n        --orange-3: color(display-p3 0.2127 0.0912 0.0582);\n        --orange-4: color(display-p3 0.2949 0.0848 0.0301);\n        --orange-5: color(display-p3 0.3542 0.1132 0.0498);\n        --orange-6: color(display-p3 0.4147 0.164 0.098);\n        --orange-7: color(display-p3 0.5064 0.2289 0.1549);\n        --orange-8: color(display-p3 0.6542 0.2998 0.2054);\n        --orange-9: color(display-p3 0.9049 0.3335 0.1831);\n        --orange-10: color(display-p3 0.8499 0.2787 0.124);\n        --orange-11: color(display-p3 1 0.5537 0.4215);\n        --orange-12: color(display-p3 0.9711 0.8289 0.7855);\n        --orange-a1: color(display-p3 0.9608 0 0 / 0.022);\n        --orange-a2: color(display-p3 0.9922 0.298 0.1451 / 0.051);\n        --orange-a3: color(display-p3 1 0.2196 0.0196 / 0.156);\n        --orange-a4: color(display-p3 1 0.149 0 / 0.244);\n        --orange-a5: color(display-p3 1 0.2196 0.0196 / 0.311);\n        --orange-a6: color(display-p3 1 0.3294 0.149 / 0.374);\n        --orange-a7: color(display-p3 1 0.4118 0.2588 / 0.471);\n        --orange-a8: color(display-p3 1 0.4353 0.2941 / 0.631);\n        --orange-a9: color(display-p3 0.9961 0.3608 0.1961 / 0.9);\n        --orange-a10: color(display-p3 1 0.3176 0.149 / 0.837);\n        --orange-a11: color(display-p3 1 0.6275 0.5137 / 0.937);\n        --orange-a12: color(display-p3 1 0.8549 0.8078 / 0.971);\n        --lemon-1: color(display-p3 0.0656 0.0703 0.044);\n        --lemon-2: color(display-p3 0.0916 0.098 0.0617);\n        --lemon-3: color(display-p3 0.1412 0.1537 0.0731);\n        --lemon-4: color(display-p3 0.1825 0.2001 0.0756);\n        --lemon-5: color(display-p3 0.226 0.2481 0.086);\n        --lemon-6: color(display-p3 0.2762 0.3016 0.1182);\n        --lemon-7: color(display-p3 0.3378 0.3671 0.1606);\n        --lemon-8: color(display-p3 0.4128 0.4482 0.1964);\n        --lemon-9: color(display-p3 0.8781 0.9576 0.3125);\n        --lemon-10: color(display-p3 0.842 0.9116 0.4038);\n        --lemon-11: color(display-p3 0.8302 0.8994 0.3913);\n        --lemon-12: color(display-p3 0.9183 0.9596 0.7245);\n        --lemon-a1: color(display-p3 0 0.8549 0 / 0.005);\n        --lemon-a2: color(display-p3 0.7608 0.9922 0 / 0.034);\n        --lemon-a3: color(display-p3 0.8706 0.9961 0.1529 / 0.093);\n        --lemon-a4: color(display-p3 0.8627 1 0.1216 / 0.143);\n        --lemon-a5: color(display-p3 0.8784 1 0.1686 / 0.194);\n        --lemon-a6: color(display-p3 0.8902 0.9961 0.2706 / 0.253);\n        --lemon-a7: color(display-p3 0.902 1 0.3608 / 0.324);\n        --lemon-a8: color(display-p3 0.9059 1 0.3843 / 0.412);\n        --lemon-a9: color(display-p3 0.9176 1 0.3255 / 0.954);\n        --lemon-a10: color(display-p3 0.9216 0.9961 0.4392 / 0.904);\n        --lemon-a11: color(display-p3 0.9216 1 0.4314 / 0.891);\n        --lemon-a12: color(display-p3 0.9529 1 0.7529 / 0.958);\n        --indigo-1: color(display-p3 0.0604 0.0576 0.1185);\n        --indigo-2: color(display-p3 0.0833 0.0784 0.163);\n        --indigo-3: color(display-p3 0.1353 0.1026 0.326);\n        --indigo-4: color(display-p3 0.176 0.0967 0.4632);\n        --indigo-5: color(display-p3 0.2121 0.1331 0.5303);\n        --indigo-6: color(display-p3 0.251 0.1807 0.5884);\n        --indigo-7: color(display-p3 0.3001 0.2284 0.6756);\n        --indigo-8: color(display-p3 0.3613 0.2735 0.8079);\n        --indigo-9: color(display-p3 0.3562 0.1176 0.9345);\n        --indigo-10: color(display-p3 0.450 0.340 0.980);\n        --indigo-11: color(display-p3 0.6727 0.6534 1);\n        --indigo-12: color(display-p3 0.8675 0.8681 1);\n        --indigo-a1: color(display-p3 0 0 0.9961 / 0.055);\n        --indigo-a2: color(display-p3 0.2627 0.1843 1 / 0.101);\n        --indigo-a3: color(display-p3 0.3059 0.1922 1 / 0.282);\n        --indigo-a4: color(display-p3 0.3255 0.1333 1 / 0.425);\n        --indigo-a5: color(display-p3 0.3529 0.1961 0.9961 / 0.5);\n        --indigo-a6: color(display-p3 0.4 0.2706 1 / 0.559);\n        --indigo-a7: color(display-p3 0.4275 0.3137 1 / 0.652);\n        --indigo-a8: color(display-p3 0.4392 0.3294 1 / 0.795);\n        --indigo-a9: color(display-p3 0.3804 0.1216 1 / 0.929);\n        --indigo-a10: color(display-p3 0.510 0.410 1 / 0.84);\n        --indigo-a11: color(display-p3 0.6902 0.6745 1 / 0.975);\n        --indigo-a12: color(display-p3 0.8784 0.8784 1 / 0.988);\n        --lime-1: color(display-p3 0.0529 0.075 0.0475);\n        --lime-2: color(display-p3 0.0775 0.1066 0.0704);\n        --lime-3: color(display-p3 0.1005 0.1769 0.0818);\n        --lime-4: color(display-p3 0.1152 0.2353 0.0855);\n        --lime-5: color(display-p3 0.1467 0.2898 0.1112);\n        --lime-6: color(display-p3 0.1839 0.3473 0.1435);\n        --lime-7: color(display-p3 0.2236 0.4142 0.1764);\n        --lime-8: color(display-p3 0.2636 0.4926 0.2067);\n        --lime-9: color(display-p3 0.3843 0.8306 0.2661);\n        --lime-10: color(display-p3 0.3386 0.7852 0.2155);\n        --lime-11: color(display-p3 0.4067 0.8532 0.29);\n        --lime-12: color(display-p3 0.7388 0.9555 0.686);\n        --lime-a1: color(display-p3 0 0.9412 0 / 0.009);\n        --lime-a2: color(display-p3 0.3412 0.9804 0.1608 / 0.043);\n        --lime-a3: color(display-p3 0.3686 1 0.2 / 0.118);\n        --lime-a4: color(display-p3 0.3294 1 0.1765 / 0.181);\n        --lime-a5: color(display-p3 0.3961 1 0.2627 / 0.24);\n        --lime-a6: color(display-p3 0.4588 1 0.3294 / 0.303);\n        --lime-a7: color(display-p3 0.4863 1 0.3608 / 0.374);\n        --lime-a8: color(display-p3 0.502 1 0.3804 / 0.454);\n        --lime-a9: color(display-p3 0.4549 1 0.3098 / 0.82);\n        --lime-a10: color(display-p3 0.451 1 0.2824 / 0.769);\n        --lime-a11: color(display-p3 0.4706 1 0.3294 / 0.845);\n        --lime-a12: color(display-p3 0.7725 1 0.7137 / 0.954);\n        --magenta-1: color(display-p3 0.0869 0.0558 0.066);\n        --magenta-2: color(display-p3 0.1221 0.0724 0.0894);\n        --magenta-3: color(display-p3 0.2188 0.074 0.133);\n        --magenta-4: color(display-p3 0.3021 0.0442 0.1671);\n        --magenta-5: color(display-p3 0.3605 0.0696 0.2057);\n        --magenta-6: color(display-p3 0.4246 0.1213 0.2555);\n        --magenta-7: color(display-p3 0.5232 0.1797 0.327);\n        --magenta-8: color(display-p3 0.6796 0.233 0.4267);\n        --magenta-9: color(display-p3 0.9175 0.2003 0.5465);\n        --magenta-10: color(display-p3 0.8614 0.1183 0.4996);\n        --magenta-11: color(display-p3 1 0.5058 0.7167);\n        --magenta-12: color(display-p3 0.9986 0.8207 0.8777);\n        --magenta-a1: color(display-p3 0.9608 0 0.0667 / 0.022);\n        --magenta-a2: color(display-p3 1 0.2 0.4667 / 0.059);\n        --magenta-a3: color(display-p3 1 0.1176 0.4745 / 0.164);\n        --magenta-a4: color(display-p3 0.9961 0 0.4549 / 0.253);\n        --magenta-a5: color(display-p3 0.9961 0.0784 0.502 / 0.316);\n        --magenta-a6: color(display-p3 1 0.2118 0.5608 / 0.383);\n        --magenta-a7: color(display-p3 1 0.298 0.5961 / 0.488);\n        --magenta-a8: color(display-p3 1 0.3176 0.6157 / 0.656);\n        --magenta-a9: color(display-p3 1 0.2157 0.5922 / 0.912);\n        --magenta-a10: color(display-p3 1 0.2078 0.5804 / 0.853);\n        --magenta-a11: color(display-p3 1 0.5961 0.7647 / 0.933);\n        --magenta-a12: color(display-p3 0.9961 0.8431 0.902 / 0.967);\n        --gray-2-translucent: color(display-p3 0.1137 0.1137 0.1137 / 0.5);\n        --mauve-2-translucent: color(display-p3 0.1176 0.1137 0.1176 / 0.5);\n        --slate-2-translucent: color(display-p3 0.1059 0.1137 0.1176 / 0.5);\n        --sage-2-translucent: color(display-p3 0.102 0.1137 0.1059 / 0.5);\n        --olive-2-translucent: color(display-p3 0.1059 0.1137 0.102 / 0.5);\n        --sand-2-translucent: color(display-p3 0.1137 0.1137 0.1059 / 0.5);\n        --gray-surface: color(display-p3 0.1255 0.1255 0.1255 / 0.5);\n        --mauve-surface: color(display-p3 0.1333 0.1255 0.1333 / 0.5);\n        --slate-surface: color(display-p3 0.1176 0.1255 0.1333 / 0.5);\n        --sage-surface: color(display-p3 0.1176 0.1255 0.1176 / 0.5);\n        --olive-surface: color(display-p3 0.1176 0.1255 0.1176 / 0.5);\n        --sand-surface: color(display-p3 0.1255 0.1255 0.1255 / 0.5);\n        --tomato-surface: color(display-p3 0.1569 0.0941 0.0784 / 0.5);\n        --red-surface: color(display-p3 0.1647 0.0863 0.0863 / 0.5);\n        --ruby-surface: color(display-p3 0.1569 0.0941 0.1098 / 0.5);\n        --crimson-surface: color(display-p3 0.1647 0.0863 0.1176 / 0.5);\n        --pink-surface: color(display-p3 0.1725 0.0784 0.149 / 0.5);\n        --plum-surface: color(display-p3 0.1647 0.0863 0.1725 / 0.5);\n        --purple-surface: color(display-p3 0.149 0.0941 0.1961 / 0.5);\n        --violet-surface: color(display-p3 0.1333 0.102 0.2118 / 0.5);\n        --iris-surface: color(display-p3 0.1098 0.102 0.2118 / 0.5);\n        --cyan-surface: color(display-p3 0.0784 0.1412 0.1725 / 0.5);\n        --teal-surface: color(display-p3 0.0863 0.149 0.1412 / 0.5);\n        --jade-surface: color(display-p3 0.0863 0.149 0.1176 / 0.5);\n        --green-surface: color(display-p3 0.0941 0.1412 0.1098 / 0.5);\n        --grass-surface: color(display-p3 0.102 0.1333 0.102 / 0.5);\n        --brown-surface: color(display-p3 0.1412 0.1176 0.102 / 0.5);\n        --bronze-surface: color(display-p3 0.1412 0.1255 0.1176 / 0.5);\n        --gold-surface: color(display-p3 0.1412 0.1333 0.1098 / 0.5);\n        --sky-surface: color(display-p3 0.0863 0.1333 0.2196 / 0.5);\n        --mint-surface: color(display-p3 0.0941 0.149 0.1412 / 0.5);\n        --yellow-surface: color(display-p3 0.1333 0.1176 0.0706 / 0.5);\n        --amber-surface: color(display-p3 0.1412 0.1176 0.0784 / 0.5);\n        --blue-surface: color(display-p3 0.0627 0.1098 0.2275 / 0.5);\n        --orange-surface: color(display-p3 0.1647 0.0941 0.0706 / 0.5);\n        --indigo-surface: color(display-p3 0.0941 0.0863 0.2588 / 0.5);\n        --magenta-surface: color(display-p3 0.1725 0.0706 0.1098 / 0.5);\n        --lemon-surface: color(display-p3 0.1098 0.1255 0.0588 / 0.5);\n        --lime-surface: color(display-p3 0.0863 0.1412 0.0706 / 0.5);\n      }\n    }\n  }\n  :root {\n    --black-a1: rgba(0, 0, 0, 0.05);\n    --black-a2: rgba(0, 0, 0, 0.1);\n    --black-a3: rgba(0, 0, 0, 0.15);\n    --black-a4: rgba(0, 0, 0, 0.2);\n    --black-a5: rgba(0, 0, 0, 0.3);\n    --black-a6: rgba(0, 0, 0, 0.4);\n    --black-a7: rgba(0, 0, 0, 0.5);\n    --black-a8: rgba(0, 0, 0, 0.6);\n    --black-a9: rgba(0, 0, 0, 0.7);\n    --black-a10: rgba(0, 0, 0, 0.8);\n    --black-a11: rgba(0, 0, 0, 0.9);\n    --black-a12: rgba(0, 0, 0, 0.95);\n    --white-a1: rgba(255, 255, 255, 0.05);\n    --white-a2: rgba(255, 255, 255, 0.1);\n    --white-a3: rgba(255, 255, 255, 0.15);\n    --white-a4: rgba(255, 255, 255, 0.2);\n    --white-a5: rgba(255, 255, 255, 0.3);\n    --white-a6: rgba(255, 255, 255, 0.4);\n    --white-a7: rgba(255, 255, 255, 0.5);\n    --white-a8: rgba(255, 255, 255, 0.6);\n    --white-a9: rgba(255, 255, 255, 0.7);\n    --white-a10: rgba(255, 255, 255, 0.8);\n    --white-a11: rgba(255, 255, 255, 0.9);\n    --white-a12: rgba(255, 255, 255, 0.95);\n    --tomato-9-contrast: white;\n    --red-9-contrast: white;\n    --ruby-9-contrast: white;\n    --crimson-9-contrast: white;\n    --pink-9-contrast: white;\n    --plum-9-contrast: white;\n    --purple-9-contrast: white;\n    --violet-9-contrast: white;\n    --iris-9-contrast: white;\n    --cyan-9-contrast: white;\n    --teal-9-contrast: white;\n    --jade-9-contrast: white;\n    --green-9-contrast: white;\n    --grass-9-contrast: white;\n    --brown-9-contrast: white;\n    --sky-9-contrast: #1c2024;\n    --mint-9-contrast: #1a211e;\n    --yellow-9-contrast: #21201c;\n    --amber-9-contrast: #21201c;\n    --gold-9-contrast: white;\n    --bronze-9-contrast: white;\n    --gray-9-contrast: white;\n    --blue-9-contrast: white;\n    --orange-9-contrast: white;\n    --indigo-9-contrast: white;\n    --magenta-9-contrast: #141212;\n    --lemon-9-contrast: #20240d;\n    --lime-9-contrast: #162715;\n    --radius-factor: 1;\n    --radius-full: 0px;\n    --radius-thumb: 9999px;\n    --radius-1: 3px;\n    --radius-2: 4px;\n    --radius-3: 6px;\n    --radius-4: 8px;\n    --radius-5: 12px;\n    --radius-6: 16px;\n  }\n  @supports (color: color(display-p3 1 1 1)) {\n    @media (color-gamut: p3) {\n      :root {\n        --black-a1: color(display-p3 0 0 0 / 0.05);\n        --black-a2: color(display-p3 0 0 0 / 0.1);\n        --black-a3: color(display-p3 0 0 0 / 0.15);\n        --black-a4: color(display-p3 0 0 0 / 0.2);\n        --black-a5: color(display-p3 0 0 0 / 0.3);\n        --black-a6: color(display-p3 0 0 0 / 0.4);\n        --black-a7: color(display-p3 0 0 0 / 0.5);\n        --black-a8: color(display-p3 0 0 0 / 0.6);\n        --black-a9: color(display-p3 0 0 0 / 0.7);\n        --black-a10: color(display-p3 0 0 0 / 0.8);\n        --black-a11: color(display-p3 0 0 0 / 0.9);\n        --black-a12: color(display-p3 0 0 0 / 0.95);\n        --white-a1: color(display-p3 1 1 1 / 0.05);\n        --white-a2: color(display-p3 1 1 1 / 0.1);\n        --white-a3: color(display-p3 1 1 1 / 0.15);\n        --white-a4: color(display-p3 1 1 1 / 0.2);\n        --white-a5: color(display-p3 1 1 1 / 0.3);\n        --white-a6: color(display-p3 1 1 1 / 0.4);\n        --white-a7: color(display-p3 1 1 1 / 0.5);\n        --white-a8: color(display-p3 1 1 1 / 0.6);\n        --white-a9: color(display-p3 1 1 1 / 0.7);\n        --white-a10: color(display-p3 1 1 1 / 0.8);\n        --white-a11: color(display-p3 1 1 1 / 0.9);\n        --white-a12: color(display-p3 1 1 1 / 0.95);\n      }\n    }\n  }\n  :where(.frosted-ui) {\n    --color-background: white;\n    --color-overlay: var(--black-a6);\n    --color-panel-solid: white;\n    --color-panel-translucent: rgba(255, 255, 255, 0.7);\n    --color-surface: rgba(255, 255, 255, 0.9);\n    --backdrop-filter-panel: blur(20px) saturate(190%) contrast(50%) brightness(130%);\n    --color-stroke: var(--gray-a5);\n    --color-panel-elevation-a1: rgba(255, 255, 255, 0.01);\n    --color-panel-elevation-a2: rgba(255, 255, 255, 0.014);\n    --color-panel-elevation-a3: rgba(255, 255, 255, 0.027);\n    --color-panel-elevation-a4: rgba(255, 255, 255, 0.045);\n    --color-panel-elevation-a5: rgba(255, 255, 255, 0.067);\n    --color-panel-elevation-a6: rgba(255, 255, 255, 0.092);\n    --color-panel-elevation-a7: rgba(255, 255, 255, 0.118);\n    --color-panel-elevation-a8: rgba(255, 255, 255, 0.143);\n    --color-panel-elevation-a9: rgba(255, 255, 255, 0.165);\n    --color-panel-elevation-a10: rgba(255, 255, 255, 0.183);\n    --color-panel-elevation-a11: rgba(255, 255, 255, 0.196);\n    --color-panel-elevation-a12: rgba(255, 255, 255, 0.2);\n    --color-transparent: rgb(0 0 0 / 0);\n    --shadow-1: inset 0 0 0 1px var(--gray-a5),\n    inset 0 1.5px 2px 0 var(--gray-a2),\n    inset 0 1.5px 2px 0 var(--black-a2);\n    --shadow-2: 0 0 0 1px var(--gray-a3),\n    0 0 0 0.5px var(--black-a1),\n    0 1px 1px 0 var(--gray-a4),\n    0 2px 1px -1px var(--black-a1),\n    0 1px 3px 0 var(--black-a1);\n    --shadow-3: 0 0 0 1px var(--gray-a3),\n    0 2px 3px -2px var(--gray-a3),\n    0 3px 12px -4px var(--black-a2),\n    0 4px 16px -8px var(--black-a2);\n    --shadow-4: 0 0 0 1px var(--gray-a6),\n    0 8px 40px var(--black-a1),\n    0 12px 32px -16px var(--gray-a3);\n    --shadow-5: 0 0 0 1px var(--gray-a5),\n    0 12px 60px var(--black-a3),\n    0 12px 32px -16px var(--gray-a5);\n    --shadow-6: 0 0 0 1px var(--gray-a3),\n    0 12px 60px var(--black-a3),\n    0 16px 64px var(--gray-a2),\n    0 16px 36px -20px var(--gray-a7);\n    --base-button-classic-active-filter: brightness(0.92) saturate(1.1);\n    --base-button-classic-high-contrast-hover-filter: contrast(0.88) saturate(1.1) brightness(1.1);\n    --base-button-classic-high-contrast-active-filter: contrast(0.82) saturate(1.2) brightness(1.16);\n    --base-button-solid-active-filter: brightness(0.92) saturate(1.1);\n    --base-button-solid-high-contrast-hover-filter: contrast(0.88) saturate(1.1) brightness(1.1);\n    --base-button-solid-high-contrast-active-filter: contrast(0.82) saturate(1.2) brightness(1.16);\n    --card-classic-hover-box-shadow: 0 0 0 1px var(--gray-a5),\n    0 1px 1px 1px var(--black-a2),\n    0 2px 1px -1px var(--gray-a3),\n    0 2px 3px -2px var(--black-a1),\n    0 3px 12px -4px var(--gray-a3),\n    0 4px 16px -8px var(--black-a1);\n    --card-background: var(--color-panel-solid);\n    --color-base-menu-outline: transparent;\n    --color-popover-outline: transparent;\n    --kbd-box-shadow: inset 0 -0.05em 0.5em var(--gray-a2),\n    inset 0 0.05em var(--white-a12),\n    inset 0 0.25em 0.5em var(--gray-a2),\n    inset 0 -0.05em var(--gray-a6),\n    0 0 0 0.05em var(--gray-a5),\n    0 0.08em 0.17em var(--gray-a7);\n    --color-popover-outline: transparent;\n    --color-segmented-control-thumb: var(--color-panel-solid);\n    --select-trigger-classic-box-shadow: inset 0 0 0 1px var(--gray-a5),\n\t\tinset 0 2px 1px var(--white-a11),\n\t\tinset 0 -2px 1px var(--gray-a4);\n    --color-select-outline: transparent;\n    --slider-range-high-contrast-background-image: linear-gradient(var(--black-a8), var(--black-a8));\n    --slider-disabled-blend-mode: multiply;\n    --switch-disabled-blend-mode: multiply;\n    --switch-button-high-contrast-checked-color-overlay: var(--black-a8);\n    --switch-button-high-contrast-checked-active-before-filter: contrast(0.82) saturate(1.2) brightness(1.16);\n    --switch-button-surface-checked-active-filter: brightness(0.92) saturate(1.1);\n    --data-table-border-color: var(--gray-a5);\n    @supports (color: color-mix(in lab, red, red)) {\n      --data-table-border-color: color-mix(in oklab, var(--gray-a5), var(--gray-6));\n    }\n    --color-tooltip-outline: transparent;\n  }\n  :is(.dark, .dark-theme), :is(.dark, .dark-theme) :where(.frosted-ui:not(.light, .light-theme)) {\n    --color-background: var(--gray-1);\n    --color-overlay: var(--black-a8);\n    --color-panel-solid: var(--gray-2);\n    --color-panel-translucent: var(--gray-2-translucent);\n    --color-surface: rgba(0, 0, 0, 0.25);\n    --backdrop-filter-panel: blur(20px) saturate(190%) contrast(90%) brightness(80%);\n    --color-stroke: var(--gray-a4);\n    --shadow-1: inset 0 -1px 1px 0 var(--gray-a3),\n    inset 0 0 0 1px var(--gray-a3),\n    inset 0 3px 4px 0 var(--black-a5),\n    inset 0 0 0 1px var(--gray-a4);\n    --shadow-2: 0 0 0 1px var(--gray-a6),\n    0 0 0 0.5px var(--black-a3),\n    0 1px 1px 0 var(--black-a6),\n    0 2px 1px -1px var(--black-a6),\n    0 1px 3px 0 var(--black-a8);\n    --shadow-3: 0 0 0 1px var(--gray-a6),\n    0 2px 3px -2px var(--black-a3),\n    0 3px 8px -2px var(--black-a6),\n    0 4px 12px -4px var(--black-a7);\n    --shadow-4: 0 0 0 1px var(--gray-a6),\n    0 8px 40px var(--black-a3),\n    0 12px 32px -16px var(--black-a5);\n    --shadow-5: 0 0 0 1px var(--gray-a6) inset,\n    0 12px 60px var(--black-a5),\n    0 12px 32px -16px var(--black-a7);\n    --shadow-6: 0 0 0 1px var(--gray-a6),\n    0 12px 60px var(--black-a4),\n    0 16px 64px var(--black-a6),\n    0 16px 36px -20px var(--black-a11);\n    --base-button-classic-active-filter: brightness(1.08);\n    --base-button-classic-high-contrast-hover-filter: contrast(0.88) saturate(1.3) brightness(1.14);\n    --base-button-classic-high-contrast-active-filter: brightness(0.95) saturate(1.2);\n    --base-button-solid-active-filter: brightness(1.08);\n    --base-button-solid-high-contrast-hover-filter: contrast(0.88) saturate(1.3) brightness(1.18);\n    --base-button-solid-high-contrast-active-filter: brightness(0.95) saturate(1.2);\n    --card-classic-hover-box-shadow: 0 0 0 1px var(--gray-a7),\n    0 0 1px 1px var(--gray-a7),\n    0 0 1px -1px var(--gray-a4),\n    0 0 3px -2px var(--gray-a3),\n    0 0 12px -2px var(--gray-a3),\n    0 0 16px -8px var(--gray-a9);\n    --card-background: var(--color-panel-solid);\n    --color-base-menu-outline: black;\n    --color-popover-outline: black;\n    --kbd-box-shadow: inset 0 -0.05em 0.5em var(--gray-a3),\n    inset 0 0.05em var(--gray-a11),\n    inset 0 0.25em 0.5em var(--gray-a2),\n    inset 0 -0.1em var(--black-a11),\n    0 0 0 0.075em var(--gray-a7),\n    0 0.08em 0.17em var(--black-a12);\n    --color-popover-outline: black;\n    --color-segmented-control-thumb: transparent;\n    --select-trigger-classic-box-shadow: inset 0 0 0 1px var(--white-a4),\n    inset 0 1px 1px var(--white-a4),\n    inset 0 -1px 1px var(--black-a9);\n    --color-select-outline: black;\n    --slider-range-high-contrast-background-image: none;\n    --slider-disabled-blend-mode: screen;\n    --switch-disabled-blend-mode: screen;\n    --switch-button-high-contrast-checked-color-overlay: transparent;\n    --switch-button-high-contrast-checked-active-before-filter: brightness(1.08);\n    --switch-button-surface-checked-active-filter: brightness(1.08);\n    --data-table-border-color: var(--gray-a3);\n    @supports (color: color-mix(in lab, red, red)) {\n      --data-table-border-color: color-mix(in oklab, var(--gray-a3), var(--gray-4));\n    }\n    --color-tooltip-outline: black;\n  }\n  @supports (color: color(display-p3 1 1 1)) {\n    @media (color-gamut: p3) {\n      .frosted-ui {\n        --color-transparent: color(display-p3 0 0 0 / 0);\n      }\n    }\n  }\n  .frosted-ui:where(.light, .light-theme) {\n    color-scheme: light;\n  }\n  .frosted-ui:where(.dark, .dark-theme) {\n    color-scheme: dark;\n  }\n  .frosted-ui, [data-accent-color]:where(:not([data-accent-color='gray'])) {\n    --color-selection-root: var(--accent-a5);\n    --color-focus-root: var(--accent-8);\n  }\n  .frosted-ui ::selection {\n    background-color: var(--color-selection-root);\n  }\n  .frosted-ui:where([data-has-background='true']) {\n    background-color: var(--color-background);\n  }\n  .frosted-ui:where([data-panel-background='solid']) {\n    --color-panel: var(--color-panel-solid);\n  }\n  .frosted-ui:where([data-panel-background='translucent']) {\n    --color-panel: var(--color-panel-translucent);\n  }\n  [data-accent-color='tomato'] {\n    --color-surface-accent: var(--tomato-surface);\n    --accent-1: var(--tomato-1);\n    --accent-2: var(--tomato-2);\n    --accent-3: var(--tomato-3);\n    --accent-4: var(--tomato-4);\n    --accent-5: var(--tomato-5);\n    --accent-6: var(--tomato-6);\n    --accent-7: var(--tomato-7);\n    --accent-8: var(--tomato-8);\n    --accent-9: var(--tomato-9);\n    --accent-9-contrast: var(--tomato-9-contrast);\n    --accent-10: var(--tomato-10);\n    --accent-11: var(--tomato-11);\n    --accent-12: var(--tomato-12);\n    --accent-a1: var(--tomato-a1);\n    --accent-a2: var(--tomato-a2);\n    --accent-a3: var(--tomato-a3);\n    --accent-a4: var(--tomato-a4);\n    --accent-a5: var(--tomato-a5);\n    --accent-a6: var(--tomato-a6);\n    --accent-a7: var(--tomato-a7);\n    --accent-a8: var(--tomato-a8);\n    --accent-a9: var(--tomato-a9);\n    --accent-a10: var(--tomato-a10);\n    --accent-a11: var(--tomato-a11);\n    --accent-a12: var(--tomato-a12);\n  }\n  [data-accent-color='red'] {\n    --color-surface-accent: var(--red-surface);\n    --accent-1: var(--red-1);\n    --accent-2: var(--red-2);\n    --accent-3: var(--red-3);\n    --accent-4: var(--red-4);\n    --accent-5: var(--red-5);\n    --accent-6: var(--red-6);\n    --accent-7: var(--red-7);\n    --accent-8: var(--red-8);\n    --accent-9: var(--red-9);\n    --accent-9-contrast: var(--red-9-contrast);\n    --accent-10: var(--red-10);\n    --accent-11: var(--red-11);\n    --accent-12: var(--red-12);\n    --accent-a1: var(--red-a1);\n    --accent-a2: var(--red-a2);\n    --accent-a3: var(--red-a3);\n    --accent-a4: var(--red-a4);\n    --accent-a5: var(--red-a5);\n    --accent-a6: var(--red-a6);\n    --accent-a7: var(--red-a7);\n    --accent-a8: var(--red-a8);\n    --accent-a9: var(--red-a9);\n    --accent-a10: var(--red-a10);\n    --accent-a11: var(--red-a11);\n    --accent-a12: var(--red-a12);\n  }\n  [data-accent-color='ruby'] {\n    --color-surface-accent: var(--ruby-surface);\n    --accent-1: var(--ruby-1);\n    --accent-2: var(--ruby-2);\n    --accent-3: var(--ruby-3);\n    --accent-4: var(--ruby-4);\n    --accent-5: var(--ruby-5);\n    --accent-6: var(--ruby-6);\n    --accent-7: var(--ruby-7);\n    --accent-8: var(--ruby-8);\n    --accent-9: var(--ruby-9);\n    --accent-9-contrast: var(--ruby-9-contrast);\n    --accent-10: var(--ruby-10);\n    --accent-11: var(--ruby-11);\n    --accent-12: var(--ruby-12);\n    --accent-a1: var(--ruby-a1);\n    --accent-a2: var(--ruby-a2);\n    --accent-a3: var(--ruby-a3);\n    --accent-a4: var(--ruby-a4);\n    --accent-a5: var(--ruby-a5);\n    --accent-a6: var(--ruby-a6);\n    --accent-a7: var(--ruby-a7);\n    --accent-a8: var(--ruby-a8);\n    --accent-a9: var(--ruby-a9);\n    --accent-a10: var(--ruby-a10);\n    --accent-a11: var(--ruby-a11);\n    --accent-a12: var(--ruby-a12);\n  }\n  [data-accent-color='crimson'] {\n    --color-surface-accent: var(--crimson-surface);\n    --accent-1: var(--crimson-1);\n    --accent-2: var(--crimson-2);\n    --accent-3: var(--crimson-3);\n    --accent-4: var(--crimson-4);\n    --accent-5: var(--crimson-5);\n    --accent-6: var(--crimson-6);\n    --accent-7: var(--crimson-7);\n    --accent-8: var(--crimson-8);\n    --accent-9: var(--crimson-9);\n    --accent-9-contrast: var(--crimson-9-contrast);\n    --accent-10: var(--crimson-10);\n    --accent-11: var(--crimson-11);\n    --accent-12: var(--crimson-12);\n    --accent-a1: var(--crimson-a1);\n    --accent-a2: var(--crimson-a2);\n    --accent-a3: var(--crimson-a3);\n    --accent-a4: var(--crimson-a4);\n    --accent-a5: var(--crimson-a5);\n    --accent-a6: var(--crimson-a6);\n    --accent-a7: var(--crimson-a7);\n    --accent-a8: var(--crimson-a8);\n    --accent-a9: var(--crimson-a9);\n    --accent-a10: var(--crimson-a10);\n    --accent-a11: var(--crimson-a11);\n    --accent-a12: var(--crimson-a12);\n  }\n  [data-accent-color='pink'] {\n    --color-surface-accent: var(--pink-surface);\n    --accent-1: var(--pink-1);\n    --accent-2: var(--pink-2);\n    --accent-3: var(--pink-3);\n    --accent-4: var(--pink-4);\n    --accent-5: var(--pink-5);\n    --accent-6: var(--pink-6);\n    --accent-7: var(--pink-7);\n    --accent-8: var(--pink-8);\n    --accent-9: var(--pink-9);\n    --accent-9-contrast: var(--pink-9-contrast);\n    --accent-10: var(--pink-10);\n    --accent-11: var(--pink-11);\n    --accent-12: var(--pink-12);\n    --accent-a1: var(--pink-a1);\n    --accent-a2: var(--pink-a2);\n    --accent-a3: var(--pink-a3);\n    --accent-a4: var(--pink-a4);\n    --accent-a5: var(--pink-a5);\n    --accent-a6: var(--pink-a6);\n    --accent-a7: var(--pink-a7);\n    --accent-a8: var(--pink-a8);\n    --accent-a9: var(--pink-a9);\n    --accent-a10: var(--pink-a10);\n    --accent-a11: var(--pink-a11);\n    --accent-a12: var(--pink-a12);\n  }\n  [data-accent-color='plum'] {\n    --color-surface-accent: var(--plum-surface);\n    --accent-1: var(--plum-1);\n    --accent-2: var(--plum-2);\n    --accent-3: var(--plum-3);\n    --accent-4: var(--plum-4);\n    --accent-5: var(--plum-5);\n    --accent-6: var(--plum-6);\n    --accent-7: var(--plum-7);\n    --accent-8: var(--plum-8);\n    --accent-9: var(--plum-9);\n    --accent-9-contrast: var(--plum-9-contrast);\n    --accent-10: var(--plum-10);\n    --accent-11: var(--plum-11);\n    --accent-12: var(--plum-12);\n    --accent-a1: var(--plum-a1);\n    --accent-a2: var(--plum-a2);\n    --accent-a3: var(--plum-a3);\n    --accent-a4: var(--plum-a4);\n    --accent-a5: var(--plum-a5);\n    --accent-a6: var(--plum-a6);\n    --accent-a7: var(--plum-a7);\n    --accent-a8: var(--plum-a8);\n    --accent-a9: var(--plum-a9);\n    --accent-a10: var(--plum-a10);\n    --accent-a11: var(--plum-a11);\n    --accent-a12: var(--plum-a12);\n  }\n  [data-accent-color='purple'] {\n    --color-surface-accent: var(--purple-surface);\n    --accent-1: var(--purple-1);\n    --accent-2: var(--purple-2);\n    --accent-3: var(--purple-3);\n    --accent-4: var(--purple-4);\n    --accent-5: var(--purple-5);\n    --accent-6: var(--purple-6);\n    --accent-7: var(--purple-7);\n    --accent-8: var(--purple-8);\n    --accent-9: var(--purple-9);\n    --accent-9-contrast: var(--purple-9-contrast);\n    --accent-10: var(--purple-10);\n    --accent-11: var(--purple-11);\n    --accent-12: var(--purple-12);\n    --accent-a1: var(--purple-a1);\n    --accent-a2: var(--purple-a2);\n    --accent-a3: var(--purple-a3);\n    --accent-a4: var(--purple-a4);\n    --accent-a5: var(--purple-a5);\n    --accent-a6: var(--purple-a6);\n    --accent-a7: var(--purple-a7);\n    --accent-a8: var(--purple-a8);\n    --accent-a9: var(--purple-a9);\n    --accent-a10: var(--purple-a10);\n    --accent-a11: var(--purple-a11);\n    --accent-a12: var(--purple-a12);\n  }\n  [data-accent-color='violet'] {\n    --color-surface-accent: var(--violet-surface);\n    --accent-1: var(--violet-1);\n    --accent-2: var(--violet-2);\n    --accent-3: var(--violet-3);\n    --accent-4: var(--violet-4);\n    --accent-5: var(--violet-5);\n    --accent-6: var(--violet-6);\n    --accent-7: var(--violet-7);\n    --accent-8: var(--violet-8);\n    --accent-9: var(--violet-9);\n    --accent-9-contrast: var(--violet-9-contrast);\n    --accent-10: var(--violet-10);\n    --accent-11: var(--violet-11);\n    --accent-12: var(--violet-12);\n    --accent-a1: var(--violet-a1);\n    --accent-a2: var(--violet-a2);\n    --accent-a3: var(--violet-a3);\n    --accent-a4: var(--violet-a4);\n    --accent-a5: var(--violet-a5);\n    --accent-a6: var(--violet-a6);\n    --accent-a7: var(--violet-a7);\n    --accent-a8: var(--violet-a8);\n    --accent-a9: var(--violet-a9);\n    --accent-a10: var(--violet-a10);\n    --accent-a11: var(--violet-a11);\n    --accent-a12: var(--violet-a12);\n  }\n  [data-accent-color='iris'] {\n    --color-surface-accent: var(--iris-surface);\n    --accent-1: var(--iris-1);\n    --accent-2: var(--iris-2);\n    --accent-3: var(--iris-3);\n    --accent-4: var(--iris-4);\n    --accent-5: var(--iris-5);\n    --accent-6: var(--iris-6);\n    --accent-7: var(--iris-7);\n    --accent-8: var(--iris-8);\n    --accent-9: var(--iris-9);\n    --accent-9-contrast: var(--iris-9-contrast);\n    --accent-10: var(--iris-10);\n    --accent-11: var(--iris-11);\n    --accent-12: var(--iris-12);\n    --accent-a1: var(--iris-a1);\n    --accent-a2: var(--iris-a2);\n    --accent-a3: var(--iris-a3);\n    --accent-a4: var(--iris-a4);\n    --accent-a5: var(--iris-a5);\n    --accent-a6: var(--iris-a6);\n    --accent-a7: var(--iris-a7);\n    --accent-a8: var(--iris-a8);\n    --accent-a9: var(--iris-a9);\n    --accent-a10: var(--iris-a10);\n    --accent-a11: var(--iris-a11);\n    --accent-a12: var(--iris-a12);\n  }\n  [data-accent-color='cyan'] {\n    --color-surface-accent: var(--cyan-surface);\n    --accent-1: var(--cyan-1);\n    --accent-2: var(--cyan-2);\n    --accent-3: var(--cyan-3);\n    --accent-4: var(--cyan-4);\n    --accent-5: var(--cyan-5);\n    --accent-6: var(--cyan-6);\n    --accent-7: var(--cyan-7);\n    --accent-8: var(--cyan-8);\n    --accent-9: var(--cyan-9);\n    --accent-9-contrast: var(--cyan-9-contrast);\n    --accent-10: var(--cyan-10);\n    --accent-11: var(--cyan-11);\n    --accent-12: var(--cyan-12);\n    --accent-a1: var(--cyan-a1);\n    --accent-a2: var(--cyan-a2);\n    --accent-a3: var(--cyan-a3);\n    --accent-a4: var(--cyan-a4);\n    --accent-a5: var(--cyan-a5);\n    --accent-a6: var(--cyan-a6);\n    --accent-a7: var(--cyan-a7);\n    --accent-a8: var(--cyan-a8);\n    --accent-a9: var(--cyan-a9);\n    --accent-a10: var(--cyan-a10);\n    --accent-a11: var(--cyan-a11);\n    --accent-a12: var(--cyan-a12);\n  }\n  [data-accent-color='teal'] {\n    --color-surface-accent: var(--teal-surface);\n    --accent-1: var(--teal-1);\n    --accent-2: var(--teal-2);\n    --accent-3: var(--teal-3);\n    --accent-4: var(--teal-4);\n    --accent-5: var(--teal-5);\n    --accent-6: var(--teal-6);\n    --accent-7: var(--teal-7);\n    --accent-8: var(--teal-8);\n    --accent-9: var(--teal-9);\n    --accent-9-contrast: var(--teal-9-contrast);\n    --accent-10: var(--teal-10);\n    --accent-11: var(--teal-11);\n    --accent-12: var(--teal-12);\n    --accent-a1: var(--teal-a1);\n    --accent-a2: var(--teal-a2);\n    --accent-a3: var(--teal-a3);\n    --accent-a4: var(--teal-a4);\n    --accent-a5: var(--teal-a5);\n    --accent-a6: var(--teal-a6);\n    --accent-a7: var(--teal-a7);\n    --accent-a8: var(--teal-a8);\n    --accent-a9: var(--teal-a9);\n    --accent-a10: var(--teal-a10);\n    --accent-a11: var(--teal-a11);\n    --accent-a12: var(--teal-a12);\n  }\n  [data-accent-color='jade'] {\n    --color-surface-accent: var(--jade-surface);\n    --accent-1: var(--jade-1);\n    --accent-2: var(--jade-2);\n    --accent-3: var(--jade-3);\n    --accent-4: var(--jade-4);\n    --accent-5: var(--jade-5);\n    --accent-6: var(--jade-6);\n    --accent-7: var(--jade-7);\n    --accent-8: var(--jade-8);\n    --accent-9: var(--jade-9);\n    --accent-9-contrast: var(--jade-9-contrast);\n    --accent-10: var(--jade-10);\n    --accent-11: var(--jade-11);\n    --accent-12: var(--jade-12);\n    --accent-a1: var(--jade-a1);\n    --accent-a2: var(--jade-a2);\n    --accent-a3: var(--jade-a3);\n    --accent-a4: var(--jade-a4);\n    --accent-a5: var(--jade-a5);\n    --accent-a6: var(--jade-a6);\n    --accent-a7: var(--jade-a7);\n    --accent-a8: var(--jade-a8);\n    --accent-a9: var(--jade-a9);\n    --accent-a10: var(--jade-a10);\n    --accent-a11: var(--jade-a11);\n    --accent-a12: var(--jade-a12);\n  }\n  [data-accent-color='green'] {\n    --color-surface-accent: var(--green-surface);\n    --accent-1: var(--green-1);\n    --accent-2: var(--green-2);\n    --accent-3: var(--green-3);\n    --accent-4: var(--green-4);\n    --accent-5: var(--green-5);\n    --accent-6: var(--green-6);\n    --accent-7: var(--green-7);\n    --accent-8: var(--green-8);\n    --accent-9: var(--green-9);\n    --accent-9-contrast: var(--green-9-contrast);\n    --accent-10: var(--green-10);\n    --accent-11: var(--green-11);\n    --accent-12: var(--green-12);\n    --accent-a1: var(--green-a1);\n    --accent-a2: var(--green-a2);\n    --accent-a3: var(--green-a3);\n    --accent-a4: var(--green-a4);\n    --accent-a5: var(--green-a5);\n    --accent-a6: var(--green-a6);\n    --accent-a7: var(--green-a7);\n    --accent-a8: var(--green-a8);\n    --accent-a9: var(--green-a9);\n    --accent-a10: var(--green-a10);\n    --accent-a11: var(--green-a11);\n    --accent-a12: var(--green-a12);\n  }\n  [data-accent-color='grass'] {\n    --color-surface-accent: var(--grass-surface);\n    --accent-1: var(--grass-1);\n    --accent-2: var(--grass-2);\n    --accent-3: var(--grass-3);\n    --accent-4: var(--grass-4);\n    --accent-5: var(--grass-5);\n    --accent-6: var(--grass-6);\n    --accent-7: var(--grass-7);\n    --accent-8: var(--grass-8);\n    --accent-9: var(--grass-9);\n    --accent-9-contrast: var(--grass-9-contrast);\n    --accent-10: var(--grass-10);\n    --accent-11: var(--grass-11);\n    --accent-12: var(--grass-12);\n    --accent-a1: var(--grass-a1);\n    --accent-a2: var(--grass-a2);\n    --accent-a3: var(--grass-a3);\n    --accent-a4: var(--grass-a4);\n    --accent-a5: var(--grass-a5);\n    --accent-a6: var(--grass-a6);\n    --accent-a7: var(--grass-a7);\n    --accent-a8: var(--grass-a8);\n    --accent-a9: var(--grass-a9);\n    --accent-a10: var(--grass-a10);\n    --accent-a11: var(--grass-a11);\n    --accent-a12: var(--grass-a12);\n  }\n  [data-accent-color='brown'] {\n    --color-surface-accent: var(--brown-surface);\n    --accent-1: var(--brown-1);\n    --accent-2: var(--brown-2);\n    --accent-3: var(--brown-3);\n    --accent-4: var(--brown-4);\n    --accent-5: var(--brown-5);\n    --accent-6: var(--brown-6);\n    --accent-7: var(--brown-7);\n    --accent-8: var(--brown-8);\n    --accent-9: var(--brown-9);\n    --accent-9-contrast: var(--brown-9-contrast);\n    --accent-10: var(--brown-10);\n    --accent-11: var(--brown-11);\n    --accent-12: var(--brown-12);\n    --accent-a1: var(--brown-a1);\n    --accent-a2: var(--brown-a2);\n    --accent-a3: var(--brown-a3);\n    --accent-a4: var(--brown-a4);\n    --accent-a5: var(--brown-a5);\n    --accent-a6: var(--brown-a6);\n    --accent-a7: var(--brown-a7);\n    --accent-a8: var(--brown-a8);\n    --accent-a9: var(--brown-a9);\n    --accent-a10: var(--brown-a10);\n    --accent-a11: var(--brown-a11);\n    --accent-a12: var(--brown-a12);\n  }\n  [data-accent-color='sky'] {\n    --color-surface-accent: var(--sky-surface);\n    --accent-1: var(--sky-1);\n    --accent-2: var(--sky-2);\n    --accent-3: var(--sky-3);\n    --accent-4: var(--sky-4);\n    --accent-5: var(--sky-5);\n    --accent-6: var(--sky-6);\n    --accent-7: var(--sky-7);\n    --accent-8: var(--sky-8);\n    --accent-9: var(--sky-9);\n    --accent-9-contrast: var(--sky-9-contrast);\n    --accent-10: var(--sky-10);\n    --accent-11: var(--sky-11);\n    --accent-12: var(--sky-12);\n    --accent-a1: var(--sky-a1);\n    --accent-a2: var(--sky-a2);\n    --accent-a3: var(--sky-a3);\n    --accent-a4: var(--sky-a4);\n    --accent-a5: var(--sky-a5);\n    --accent-a6: var(--sky-a6);\n    --accent-a7: var(--sky-a7);\n    --accent-a8: var(--sky-a8);\n    --accent-a9: var(--sky-a9);\n    --accent-a10: var(--sky-a10);\n    --accent-a11: var(--sky-a11);\n    --accent-a12: var(--sky-a12);\n  }\n  [data-accent-color='mint'] {\n    --color-surface-accent: var(--mint-surface);\n    --accent-1: var(--mint-1);\n    --accent-2: var(--mint-2);\n    --accent-3: var(--mint-3);\n    --accent-4: var(--mint-4);\n    --accent-5: var(--mint-5);\n    --accent-6: var(--mint-6);\n    --accent-7: var(--mint-7);\n    --accent-8: var(--mint-8);\n    --accent-9: var(--mint-9);\n    --accent-9-contrast: var(--mint-9-contrast);\n    --accent-10: var(--mint-10);\n    --accent-11: var(--mint-11);\n    --accent-12: var(--mint-12);\n    --accent-a1: var(--mint-a1);\n    --accent-a2: var(--mint-a2);\n    --accent-a3: var(--mint-a3);\n    --accent-a4: var(--mint-a4);\n    --accent-a5: var(--mint-a5);\n    --accent-a6: var(--mint-a6);\n    --accent-a7: var(--mint-a7);\n    --accent-a8: var(--mint-a8);\n    --accent-a9: var(--mint-a9);\n    --accent-a10: var(--mint-a10);\n    --accent-a11: var(--mint-a11);\n    --accent-a12: var(--mint-a12);\n  }\n  [data-accent-color='yellow'] {\n    --color-surface-accent: var(--yellow-surface);\n    --accent-1: var(--yellow-1);\n    --accent-2: var(--yellow-2);\n    --accent-3: var(--yellow-3);\n    --accent-4: var(--yellow-4);\n    --accent-5: var(--yellow-5);\n    --accent-6: var(--yellow-6);\n    --accent-7: var(--yellow-7);\n    --accent-8: var(--yellow-8);\n    --accent-9: var(--yellow-9);\n    --accent-9-contrast: var(--yellow-9-contrast);\n    --accent-10: var(--yellow-10);\n    --accent-11: var(--yellow-11);\n    --accent-12: var(--yellow-12);\n    --accent-a1: var(--yellow-a1);\n    --accent-a2: var(--yellow-a2);\n    --accent-a3: var(--yellow-a3);\n    --accent-a4: var(--yellow-a4);\n    --accent-a5: var(--yellow-a5);\n    --accent-a6: var(--yellow-a6);\n    --accent-a7: var(--yellow-a7);\n    --accent-a8: var(--yellow-a8);\n    --accent-a9: var(--yellow-a9);\n    --accent-a10: var(--yellow-a10);\n    --accent-a11: var(--yellow-a11);\n    --accent-a12: var(--yellow-a12);\n  }\n  [data-accent-color='amber'] {\n    --color-surface-accent: var(--amber-surface);\n    --accent-1: var(--amber-1);\n    --accent-2: var(--amber-2);\n    --accent-3: var(--amber-3);\n    --accent-4: var(--amber-4);\n    --accent-5: var(--amber-5);\n    --accent-6: var(--amber-6);\n    --accent-7: var(--amber-7);\n    --accent-8: var(--amber-8);\n    --accent-9: var(--amber-9);\n    --accent-9-contrast: var(--amber-9-contrast);\n    --accent-10: var(--amber-10);\n    --accent-11: var(--amber-11);\n    --accent-12: var(--amber-12);\n    --accent-a1: var(--amber-a1);\n    --accent-a2: var(--amber-a2);\n    --accent-a3: var(--amber-a3);\n    --accent-a4: var(--amber-a4);\n    --accent-a5: var(--amber-a5);\n    --accent-a6: var(--amber-a6);\n    --accent-a7: var(--amber-a7);\n    --accent-a8: var(--amber-a8);\n    --accent-a9: var(--amber-a9);\n    --accent-a10: var(--amber-a10);\n    --accent-a11: var(--amber-a11);\n    --accent-a12: var(--amber-a12);\n  }\n  [data-accent-color='gold'] {\n    --color-surface-accent: var(--gold-surface);\n    --accent-1: var(--gold-1);\n    --accent-2: var(--gold-2);\n    --accent-3: var(--gold-3);\n    --accent-4: var(--gold-4);\n    --accent-5: var(--gold-5);\n    --accent-6: var(--gold-6);\n    --accent-7: var(--gold-7);\n    --accent-8: var(--gold-8);\n    --accent-9: var(--gold-9);\n    --accent-9-contrast: var(--gold-9-contrast);\n    --accent-10: var(--gold-10);\n    --accent-11: var(--gold-11);\n    --accent-12: var(--gold-12);\n    --accent-a1: var(--gold-a1);\n    --accent-a2: var(--gold-a2);\n    --accent-a3: var(--gold-a3);\n    --accent-a4: var(--gold-a4);\n    --accent-a5: var(--gold-a5);\n    --accent-a6: var(--gold-a6);\n    --accent-a7: var(--gold-a7);\n    --accent-a8: var(--gold-a8);\n    --accent-a9: var(--gold-a9);\n    --accent-a10: var(--gold-a10);\n    --accent-a11: var(--gold-a11);\n    --accent-a12: var(--gold-a12);\n  }\n  [data-accent-color='bronze'] {\n    --color-surface-accent: var(--bronze-surface);\n    --accent-1: var(--bronze-1);\n    --accent-2: var(--bronze-2);\n    --accent-3: var(--bronze-3);\n    --accent-4: var(--bronze-4);\n    --accent-5: var(--bronze-5);\n    --accent-6: var(--bronze-6);\n    --accent-7: var(--bronze-7);\n    --accent-8: var(--bronze-8);\n    --accent-9: var(--bronze-9);\n    --accent-9-contrast: var(--bronze-9-contrast);\n    --accent-10: var(--bronze-10);\n    --accent-11: var(--bronze-11);\n    --accent-12: var(--bronze-12);\n    --accent-a1: var(--bronze-a1);\n    --accent-a2: var(--bronze-a2);\n    --accent-a3: var(--bronze-a3);\n    --accent-a4: var(--bronze-a4);\n    --accent-a5: var(--bronze-a5);\n    --accent-a6: var(--bronze-a6);\n    --accent-a7: var(--bronze-a7);\n    --accent-a8: var(--bronze-a8);\n    --accent-a9: var(--bronze-a9);\n    --accent-a10: var(--bronze-a10);\n    --accent-a11: var(--bronze-a11);\n    --accent-a12: var(--bronze-a12);\n  }\n  [data-accent-color='gray'] {\n    --color-surface-accent: var(--gray-surface);\n    --accent-1: var(--gray-1);\n    --accent-2: var(--gray-2);\n    --accent-3: var(--gray-3);\n    --accent-4: var(--gray-4);\n    --accent-5: var(--gray-5);\n    --accent-6: var(--gray-6);\n    --accent-7: var(--gray-7);\n    --accent-8: var(--gray-8);\n    --accent-9: var(--gray-9);\n    --accent-9-contrast: var(--gray-9-contrast);\n    --accent-10: var(--gray-10);\n    --accent-11: var(--gray-11);\n    --accent-12: var(--gray-12);\n    --accent-a1: var(--gray-a1);\n    --accent-a2: var(--gray-a2);\n    --accent-a3: var(--gray-a3);\n    --accent-a4: var(--gray-a4);\n    --accent-a5: var(--gray-a5);\n    --accent-a6: var(--gray-a6);\n    --accent-a7: var(--gray-a7);\n    --accent-a8: var(--gray-a8);\n    --accent-a9: var(--gray-a9);\n    --accent-a10: var(--gray-a10);\n    --accent-a11: var(--gray-a11);\n    --accent-a12: var(--gray-a12);\n  }\n  [data-accent-color='blue'] {\n    --color-surface-accent: var(--blue-surface);\n    --accent-1: var(--blue-1);\n    --accent-2: var(--blue-2);\n    --accent-3: var(--blue-3);\n    --accent-4: var(--blue-4);\n    --accent-5: var(--blue-5);\n    --accent-6: var(--blue-6);\n    --accent-7: var(--blue-7);\n    --accent-8: var(--blue-8);\n    --accent-9: var(--blue-9);\n    --accent-9-contrast: var(--blue-9-contrast);\n    --accent-10: var(--blue-10);\n    --accent-11: var(--blue-11);\n    --accent-12: var(--blue-12);\n    --accent-a1: var(--blue-a1);\n    --accent-a2: var(--blue-a2);\n    --accent-a3: var(--blue-a3);\n    --accent-a4: var(--blue-a4);\n    --accent-a5: var(--blue-a5);\n    --accent-a6: var(--blue-a6);\n    --accent-a7: var(--blue-a7);\n    --accent-a8: var(--blue-a8);\n    --accent-a9: var(--blue-a9);\n    --accent-a10: var(--blue-a10);\n    --accent-a11: var(--blue-a11);\n    --accent-a12: var(--blue-a12);\n  }\n  [data-accent-color='orange'] {\n    --color-surface-accent: var(--orange-surface);\n    --accent-1: var(--orange-1);\n    --accent-2: var(--orange-2);\n    --accent-3: var(--orange-3);\n    --accent-4: var(--orange-4);\n    --accent-5: var(--orange-5);\n    --accent-6: var(--orange-6);\n    --accent-7: var(--orange-7);\n    --accent-8: var(--orange-8);\n    --accent-9: var(--orange-9);\n    --accent-9-contrast: var(--orange-9-contrast);\n    --accent-10: var(--orange-10);\n    --accent-11: var(--orange-11);\n    --accent-12: var(--orange-12);\n    --accent-a1: var(--orange-a1);\n    --accent-a2: var(--orange-a2);\n    --accent-a3: var(--orange-a3);\n    --accent-a4: var(--orange-a4);\n    --accent-a5: var(--orange-a5);\n    --accent-a6: var(--orange-a6);\n    --accent-a7: var(--orange-a7);\n    --accent-a8: var(--orange-a8);\n    --accent-a9: var(--orange-a9);\n    --accent-a10: var(--orange-a10);\n    --accent-a11: var(--orange-a11);\n    --accent-a12: var(--orange-a12);\n  }\n  [data-accent-color='indigo'] {\n    --color-surface-accent: var(--indigo-surface);\n    --accent-1: var(--indigo-1);\n    --accent-2: var(--indigo-2);\n    --accent-3: var(--indigo-3);\n    --accent-4: var(--indigo-4);\n    --accent-5: var(--indigo-5);\n    --accent-6: var(--indigo-6);\n    --accent-7: var(--indigo-7);\n    --accent-8: var(--indigo-8);\n    --accent-9: var(--indigo-9);\n    --accent-9-contrast: var(--indigo-9-contrast);\n    --accent-10: var(--indigo-10);\n    --accent-11: var(--indigo-11);\n    --accent-12: var(--indigo-12);\n    --accent-a1: var(--indigo-a1);\n    --accent-a2: var(--indigo-a2);\n    --accent-a3: var(--indigo-a3);\n    --accent-a4: var(--indigo-a4);\n    --accent-a5: var(--indigo-a5);\n    --accent-a6: var(--indigo-a6);\n    --accent-a7: var(--indigo-a7);\n    --accent-a8: var(--indigo-a8);\n    --accent-a9: var(--indigo-a9);\n    --accent-a10: var(--indigo-a10);\n    --accent-a11: var(--indigo-a11);\n    --accent-a12: var(--indigo-a12);\n  }\n  [data-accent-color='magenta'] {\n    --color-surface-accent: var(--magenta-surface);\n    --accent-1: var(--magenta-1);\n    --accent-2: var(--magenta-2);\n    --accent-3: var(--magenta-3);\n    --accent-4: var(--magenta-4);\n    --accent-5: var(--magenta-5);\n    --accent-6: var(--magenta-6);\n    --accent-7: var(--magenta-7);\n    --accent-8: var(--magenta-8);\n    --accent-9: var(--magenta-9);\n    --accent-9-contrast: var(--magenta-9-contrast);\n    --accent-10: var(--magenta-10);\n    --accent-11: var(--magenta-11);\n    --accent-12: var(--magenta-12);\n    --accent-a1: var(--magenta-a1);\n    --accent-a2: var(--magenta-a2);\n    --accent-a3: var(--magenta-a3);\n    --accent-a4: var(--magenta-a4);\n    --accent-a5: var(--magenta-a5);\n    --accent-a6: var(--magenta-a6);\n    --accent-a7: var(--magenta-a7);\n    --accent-a8: var(--magenta-a8);\n    --accent-a9: var(--magenta-a9);\n    --accent-a10: var(--magenta-a10);\n    --accent-a11: var(--magenta-a11);\n    --accent-a12: var(--magenta-a12);\n  }\n  [data-accent-color='lemon'] {\n    --color-surface-accent: var(--lemon-surface);\n    --accent-1: var(--lemon-1);\n    --accent-2: var(--lemon-2);\n    --accent-3: var(--lemon-3);\n    --accent-4: var(--lemon-4);\n    --accent-5: var(--lemon-5);\n    --accent-6: var(--lemon-6);\n    --accent-7: var(--lemon-7);\n    --accent-8: var(--lemon-8);\n    --accent-9: var(--lemon-9);\n    --accent-9-contrast: var(--lemon-9-contrast);\n    --accent-10: var(--lemon-10);\n    --accent-11: var(--lemon-11);\n    --accent-12: var(--lemon-12);\n    --accent-a1: var(--lemon-a1);\n    --accent-a2: var(--lemon-a2);\n    --accent-a3: var(--lemon-a3);\n    --accent-a4: var(--lemon-a4);\n    --accent-a5: var(--lemon-a5);\n    --accent-a6: var(--lemon-a6);\n    --accent-a7: var(--lemon-a7);\n    --accent-a8: var(--lemon-a8);\n    --accent-a9: var(--lemon-a9);\n    --accent-a10: var(--lemon-a10);\n    --accent-a11: var(--lemon-a11);\n    --accent-a12: var(--lemon-a12);\n  }\n  [data-accent-color='lime'] {\n    --color-surface-accent: var(--lime-surface);\n    --accent-1: var(--lime-1);\n    --accent-2: var(--lime-2);\n    --accent-3: var(--lime-3);\n    --accent-4: var(--lime-4);\n    --accent-5: var(--lime-5);\n    --accent-6: var(--lime-6);\n    --accent-7: var(--lime-7);\n    --accent-8: var(--lime-8);\n    --accent-9: var(--lime-9);\n    --accent-9-contrast: var(--lime-9-contrast);\n    --accent-10: var(--lime-10);\n    --accent-11: var(--lime-11);\n    --accent-12: var(--lime-12);\n    --accent-a1: var(--lime-a1);\n    --accent-a2: var(--lime-a2);\n    --accent-a3: var(--lime-a3);\n    --accent-a4: var(--lime-a4);\n    --accent-a5: var(--lime-a5);\n    --accent-a6: var(--lime-a6);\n    --accent-a7: var(--lime-a7);\n    --accent-a8: var(--lime-a8);\n    --accent-a9: var(--lime-a9);\n    --accent-a10: var(--lime-a10);\n    --accent-a11: var(--lime-a11);\n    --accent-a12: var(--lime-a12);\n  }\n  .frosted-ui:where([data-gray-color='mauve']) {\n    --gray-surface: var(--mauve-surface);\n    --gray-1: var(--mauve-1);\n    --gray-2: var(--mauve-2);\n    --gray-2-translucent: var(--mauve-2-translucent);\n    --gray-3: var(--mauve-3);\n    --gray-4: var(--mauve-4);\n    --gray-5: var(--mauve-5);\n    --gray-6: var(--mauve-6);\n    --gray-7: var(--mauve-7);\n    --gray-8: var(--mauve-8);\n    --gray-9: var(--mauve-9);\n    --gray-10: var(--mauve-10);\n    --gray-11: var(--mauve-11);\n    --gray-12: var(--mauve-12);\n    --gray-a1: var(--mauve-a1);\n    --gray-a2: var(--mauve-a2);\n    --gray-a3: var(--mauve-a3);\n    --gray-a4: var(--mauve-a4);\n    --gray-a5: var(--mauve-a5);\n    --gray-a6: var(--mauve-a6);\n    --gray-a7: var(--mauve-a7);\n    --gray-a8: var(--mauve-a8);\n    --gray-a9: var(--mauve-a9);\n    --gray-a10: var(--mauve-a10);\n    --gray-a11: var(--mauve-a11);\n    --gray-a12: var(--mauve-a12);\n  }\n  .frosted-ui:where([data-gray-color='slate']) {\n    --gray-surface: var(--slate-surface);\n    --gray-1: var(--slate-1);\n    --gray-2: var(--slate-2);\n    --gray-2-translucent: var(--slate-2-translucent);\n    --gray-3: var(--slate-3);\n    --gray-4: var(--slate-4);\n    --gray-5: var(--slate-5);\n    --gray-6: var(--slate-6);\n    --gray-7: var(--slate-7);\n    --gray-8: var(--slate-8);\n    --gray-9: var(--slate-9);\n    --gray-10: var(--slate-10);\n    --gray-11: var(--slate-11);\n    --gray-12: var(--slate-12);\n    --gray-a1: var(--slate-a1);\n    --gray-a2: var(--slate-a2);\n    --gray-a3: var(--slate-a3);\n    --gray-a4: var(--slate-a4);\n    --gray-a5: var(--slate-a5);\n    --gray-a6: var(--slate-a6);\n    --gray-a7: var(--slate-a7);\n    --gray-a8: var(--slate-a8);\n    --gray-a9: var(--slate-a9);\n    --gray-a10: var(--slate-a10);\n    --gray-a11: var(--slate-a11);\n    --gray-a12: var(--slate-a12);\n  }\n  .frosted-ui:where([data-gray-color='sage']) {\n    --gray-surface: var(--sage-surface);\n    --gray-1: var(--sage-1);\n    --gray-2: var(--sage-2);\n    --gray-2-translucent: var(--sage-2-translucent);\n    --gray-3: var(--sage-3);\n    --gray-4: var(--sage-4);\n    --gray-5: var(--sage-5);\n    --gray-6: var(--sage-6);\n    --gray-7: var(--sage-7);\n    --gray-8: var(--sage-8);\n    --gray-9: var(--sage-9);\n    --gray-10: var(--sage-10);\n    --gray-11: var(--sage-11);\n    --gray-12: var(--sage-12);\n    --gray-a1: var(--sage-a1);\n    --gray-a2: var(--sage-a2);\n    --gray-a3: var(--sage-a3);\n    --gray-a4: var(--sage-a4);\n    --gray-a5: var(--sage-a5);\n    --gray-a6: var(--sage-a6);\n    --gray-a7: var(--sage-a7);\n    --gray-a8: var(--sage-a8);\n    --gray-a9: var(--sage-a9);\n    --gray-a10: var(--sage-a10);\n    --gray-a11: var(--sage-a11);\n    --gray-a12: var(--sage-a12);\n  }\n  .frosted-ui:where([data-gray-color='olive']) {\n    --gray-surface: var(--olive-surface);\n    --gray-1: var(--olive-1);\n    --gray-2: var(--olive-2);\n    --gray-2-translucent: var(--olive-2-translucent);\n    --gray-3: var(--olive-3);\n    --gray-4: var(--olive-4);\n    --gray-5: var(--olive-5);\n    --gray-6: var(--olive-6);\n    --gray-7: var(--olive-7);\n    --gray-8: var(--olive-8);\n    --gray-9: var(--olive-9);\n    --gray-10: var(--olive-10);\n    --gray-11: var(--olive-11);\n    --gray-12: var(--olive-12);\n    --gray-a1: var(--olive-a1);\n    --gray-a2: var(--olive-a2);\n    --gray-a3: var(--olive-a3);\n    --gray-a4: var(--olive-a4);\n    --gray-a5: var(--olive-a5);\n    --gray-a6: var(--olive-a6);\n    --gray-a7: var(--olive-a7);\n    --gray-a8: var(--olive-a8);\n    --gray-a9: var(--olive-a9);\n    --gray-a10: var(--olive-a10);\n    --gray-a11: var(--olive-a11);\n    --gray-a12: var(--olive-a12);\n  }\n  .frosted-ui:where([data-gray-color='sand']) {\n    --gray-surface: var(--sand-surface);\n    --gray-1: var(--sand-1);\n    --gray-2: var(--sand-2);\n    --gray-2-translucent: var(--sand-2-translucent);\n    --gray-3: var(--sand-3);\n    --gray-4: var(--sand-4);\n    --gray-5: var(--sand-5);\n    --gray-6: var(--sand-6);\n    --gray-7: var(--sand-7);\n    --gray-8: var(--sand-8);\n    --gray-9: var(--sand-9);\n    --gray-10: var(--sand-10);\n    --gray-11: var(--sand-11);\n    --gray-12: var(--sand-12);\n    --gray-a1: var(--sand-a1);\n    --gray-a2: var(--sand-a2);\n    --gray-a3: var(--sand-a3);\n    --gray-a4: var(--sand-a4);\n    --gray-a5: var(--sand-a5);\n    --gray-a6: var(--sand-a6);\n    --gray-a7: var(--sand-a7);\n    --gray-a8: var(--sand-a8);\n    --gray-a9: var(--sand-a9);\n    --gray-a10: var(--sand-a10);\n    --gray-a11: var(--sand-a11);\n    --gray-a12: var(--sand-a12);\n  }\n  :root, [data-danger-color='red'] {\n    --color-surface-danger: var(--red-surface);\n    --danger-1: var(--red-1);\n    --danger-2: var(--red-2);\n    --danger-3: var(--red-3);\n    --danger-4: var(--red-4);\n    --danger-5: var(--red-5);\n    --danger-6: var(--red-6);\n    --danger-7: var(--red-7);\n    --danger-8: var(--red-8);\n    --danger-9: var(--red-9);\n    --danger-9-contrast: var(--red-9-contrast);\n    --danger-10: var(--red-10);\n    --danger-11: var(--red-11);\n    --danger-12: var(--red-12);\n    --danger-a1: var(--red-a1);\n    --danger-a2: var(--red-a2);\n    --danger-a3: var(--red-a3);\n    --danger-a4: var(--red-a4);\n    --danger-a5: var(--red-a5);\n    --danger-a6: var(--red-a6);\n    --danger-a7: var(--red-a7);\n    --danger-a8: var(--red-a8);\n    --danger-a9: var(--red-a9);\n    --danger-a10: var(--red-a10);\n    --danger-a11: var(--red-a11);\n    --danger-a12: var(--red-a12);\n  }\n  [data-danger-color='tomato'] {\n    --color-surface-danger: var(--tomato-surface);\n    --danger-1: var(--tomato-1);\n    --danger-2: var(--tomato-2);\n    --danger-3: var(--tomato-3);\n    --danger-4: var(--tomato-4);\n    --danger-5: var(--tomato-5);\n    --danger-6: var(--tomato-6);\n    --danger-7: var(--tomato-7);\n    --danger-8: var(--tomato-8);\n    --danger-9: var(--tomato-9);\n    --danger-9-contrast: var(--tomato-9-contrast);\n    --danger-10: var(--tomato-10);\n    --danger-11: var(--tomato-11);\n    --danger-12: var(--tomato-12);\n    --danger-a1: var(--tomato-a1);\n    --danger-a2: var(--tomato-a2);\n    --danger-a3: var(--tomato-a3);\n    --danger-a4: var(--tomato-a4);\n    --danger-a5: var(--tomato-a5);\n    --danger-a6: var(--tomato-a6);\n    --danger-a7: var(--tomato-a7);\n    --danger-a8: var(--tomato-a8);\n    --danger-a9: var(--tomato-a9);\n    --danger-a10: var(--tomato-a10);\n    --danger-a11: var(--tomato-a11);\n    --danger-a12: var(--tomato-a12);\n  }\n  [data-danger-color='ruby'] {\n    --color-surface-danger: var(--ruby-surface);\n    --danger-1: var(--ruby-1);\n    --danger-2: var(--ruby-2);\n    --danger-3: var(--ruby-3);\n    --danger-4: var(--ruby-4);\n    --danger-5: var(--ruby-5);\n    --danger-6: var(--ruby-6);\n    --danger-7: var(--ruby-7);\n    --danger-8: var(--ruby-8);\n    --danger-9: var(--ruby-9);\n    --danger-9-contrast: var(--ruby-9-contrast);\n    --danger-10: var(--ruby-10);\n    --danger-11: var(--ruby-11);\n    --danger-12: var(--ruby-12);\n    --danger-a1: var(--ruby-a1);\n    --danger-a2: var(--ruby-a2);\n    --danger-a3: var(--ruby-a3);\n    --danger-a4: var(--ruby-a4);\n    --danger-a5: var(--ruby-a5);\n    --danger-a6: var(--ruby-a6);\n    --danger-a7: var(--ruby-a7);\n    --danger-a8: var(--ruby-a8);\n    --danger-a9: var(--ruby-a9);\n    --danger-a10: var(--ruby-a10);\n    --danger-a11: var(--ruby-a11);\n    --danger-a12: var(--ruby-a12);\n  }\n  :root, [data-warning-color='amber'] {\n    --color-surface-warning: var(--amber-surface);\n    --warning-1: var(--amber-1);\n    --warning-2: var(--amber-2);\n    --warning-3: var(--amber-3);\n    --warning-4: var(--amber-4);\n    --warning-5: var(--amber-5);\n    --warning-6: var(--amber-6);\n    --warning-7: var(--amber-7);\n    --warning-8: var(--amber-8);\n    --warning-9: var(--amber-9);\n    --warning-9-contrast: var(--amber-9-contrast);\n    --warning-10: var(--amber-10);\n    --warning-11: var(--amber-11);\n    --warning-12: var(--amber-12);\n    --warning-a1: var(--amber-a1);\n    --warning-a2: var(--amber-a2);\n    --warning-a3: var(--amber-a3);\n    --warning-a4: var(--amber-a4);\n    --warning-a5: var(--amber-a5);\n    --warning-a6: var(--amber-a6);\n    --warning-a7: var(--amber-a7);\n    --warning-a8: var(--amber-a8);\n    --warning-a9: var(--amber-a9);\n    --warning-a10: var(--amber-a10);\n    --warning-a11: var(--amber-a11);\n    --warning-a12: var(--amber-a12);\n  }\n  [data-warning-color='yellow'] {\n    --color-surface-warning: var(--yellow-surface);\n    --warning-1: var(--yellow-1);\n    --warning-2: var(--yellow-2);\n    --warning-3: var(--yellow-3);\n    --warning-4: var(--yellow-4);\n    --warning-5: var(--yellow-5);\n    --warning-6: var(--yellow-6);\n    --warning-7: var(--yellow-7);\n    --warning-8: var(--yellow-8);\n    --warning-9: var(--yellow-9);\n    --warning-9-contrast: var(--yellow-9-contrast);\n    --warning-10: var(--yellow-10);\n    --warning-11: var(--yellow-11);\n    --warning-12: var(--yellow-12);\n    --warning-a1: var(--yellow-a1);\n    --warning-a2: var(--yellow-a2);\n    --warning-a3: var(--yellow-a3);\n    --warning-a4: var(--yellow-a4);\n    --warning-a5: var(--yellow-a5);\n    --warning-a6: var(--yellow-a6);\n    --warning-a7: var(--yellow-a7);\n    --warning-a8: var(--yellow-a8);\n    --warning-a9: var(--yellow-a9);\n    --warning-a10: var(--yellow-a10);\n    --warning-a11: var(--yellow-a11);\n    --warning-a12: var(--yellow-a12);\n  }\n  :root, [data-success-color='green'] {\n    --color-surface-success: var(--green-surface);\n    --success-1: var(--green-1);\n    --success-2: var(--green-2);\n    --success-3: var(--green-3);\n    --success-4: var(--green-4);\n    --success-5: var(--green-5);\n    --success-6: var(--green-6);\n    --success-7: var(--green-7);\n    --success-8: var(--green-8);\n    --success-9: var(--green-9);\n    --success-9-contrast: var(--green-9-contrast);\n    --success-10: var(--green-10);\n    --success-11: var(--green-11);\n    --success-12: var(--green-12);\n    --success-a1: var(--green-a1);\n    --success-a2: var(--green-a2);\n    --success-a3: var(--green-a3);\n    --success-a4: var(--green-a4);\n    --success-a5: var(--green-a5);\n    --success-a6: var(--green-a6);\n    --success-a7: var(--green-a7);\n    --success-a8: var(--green-a8);\n    --success-a9: var(--green-a9);\n    --success-a10: var(--green-a10);\n    --success-a11: var(--green-a11);\n    --success-a12: var(--green-a12);\n  }\n  [data-success-color='teal'] {\n    --color-surface-success: var(--teal-surface);\n    --success-1: var(--teal-1);\n    --success-2: var(--teal-2);\n    --success-3: var(--teal-3);\n    --success-4: var(--teal-4);\n    --success-5: var(--teal-5);\n    --success-6: var(--teal-6);\n    --success-7: var(--teal-7);\n    --success-8: var(--teal-8);\n    --success-9: var(--teal-9);\n    --success-9-contrast: var(--teal-9-contrast);\n    --success-10: var(--teal-10);\n    --success-11: var(--teal-11);\n    --success-12: var(--teal-12);\n    --success-a1: var(--teal-a1);\n    --success-a2: var(--teal-a2);\n    --success-a3: var(--teal-a3);\n    --success-a4: var(--teal-a4);\n    --success-a5: var(--teal-a5);\n    --success-a6: var(--teal-a6);\n    --success-a7: var(--teal-a7);\n    --success-a8: var(--teal-a8);\n    --success-a9: var(--teal-a9);\n    --success-a10: var(--teal-a10);\n    --success-a11: var(--teal-a11);\n    --success-a12: var(--teal-a12);\n  }\n  [data-success-color='jade'] {\n    --color-surface-success: var(--jade-surface);\n    --success-1: var(--jade-1);\n    --success-2: var(--jade-2);\n    --success-3: var(--jade-3);\n    --success-4: var(--jade-4);\n    --success-5: var(--jade-5);\n    --success-6: var(--jade-6);\n    --success-7: var(--jade-7);\n    --success-8: var(--jade-8);\n    --success-9: var(--jade-9);\n    --success-9-contrast: var(--jade-9-contrast);\n    --success-10: var(--jade-10);\n    --success-11: var(--jade-11);\n    --success-12: var(--jade-12);\n    --success-a1: var(--jade-a1);\n    --success-a2: var(--jade-a2);\n    --success-a3: var(--jade-a3);\n    --success-a4: var(--jade-a4);\n    --success-a5: var(--jade-a5);\n    --success-a6: var(--jade-a6);\n    --success-a7: var(--jade-a7);\n    --success-a8: var(--jade-a8);\n    --success-a9: var(--jade-a9);\n    --success-a10: var(--jade-a10);\n    --success-a11: var(--jade-a11);\n    --success-a12: var(--jade-a12);\n  }\n  [data-success-color='grass'] {\n    --color-surface-success: var(--grass-surface);\n    --success-1: var(--grass-1);\n    --success-2: var(--grass-2);\n    --success-3: var(--grass-3);\n    --success-4: var(--grass-4);\n    --success-5: var(--grass-5);\n    --success-6: var(--grass-6);\n    --success-7: var(--grass-7);\n    --success-8: var(--grass-8);\n    --success-9: var(--grass-9);\n    --success-9-contrast: var(--grass-9-contrast);\n    --success-10: var(--grass-10);\n    --success-11: var(--grass-11);\n    --success-12: var(--grass-12);\n    --success-a1: var(--grass-a1);\n    --success-a2: var(--grass-a2);\n    --success-a3: var(--grass-a3);\n    --success-a4: var(--grass-a4);\n    --success-a5: var(--grass-a5);\n    --success-a6: var(--grass-a6);\n    --success-a7: var(--grass-a7);\n    --success-a8: var(--grass-a8);\n    --success-a9: var(--grass-a9);\n    --success-a10: var(--grass-a10);\n    --success-a11: var(--grass-a11);\n    --success-a12: var(--grass-a12);\n  }\n  :root, [data-info-color='sky'] {\n    --color-surface-info: var(--sky-surface);\n    --info-1: var(--sky-1);\n    --info-2: var(--sky-2);\n    --info-3: var(--sky-3);\n    --info-4: var(--sky-4);\n    --info-5: var(--sky-5);\n    --info-6: var(--sky-6);\n    --info-7: var(--sky-7);\n    --info-8: var(--sky-8);\n    --info-9: var(--sky-9);\n    --info-9-contrast: var(--sky-9-contrast);\n    --info-10: var(--sky-10);\n    --info-11: var(--sky-11);\n    --info-12: var(--sky-12);\n    --info-a1: var(--sky-a1);\n    --info-a2: var(--sky-a2);\n    --info-a3: var(--sky-a3);\n    --info-a4: var(--sky-a4);\n    --info-a5: var(--sky-a5);\n    --info-a6: var(--sky-a6);\n    --info-a7: var(--sky-a7);\n    --info-a8: var(--sky-a8);\n    --info-a9: var(--sky-a9);\n    --info-a10: var(--sky-a10);\n    --info-a11: var(--sky-a11);\n    --info-a12: var(--sky-a12);\n  }\n  [data-info-color='blue'] {\n    --color-surface-info: var(--blue-surface);\n    --info-1: var(--blue-1);\n    --info-2: var(--blue-2);\n    --info-3: var(--blue-3);\n    --info-4: var(--blue-4);\n    --info-5: var(--blue-5);\n    --info-6: var(--blue-6);\n    --info-7: var(--blue-7);\n    --info-8: var(--blue-8);\n    --info-9: var(--blue-9);\n    --info-9-contrast: var(--blue-9-contrast);\n    --info-10: var(--blue-10);\n    --info-11: var(--blue-11);\n    --info-12: var(--blue-12);\n    --info-a1: var(--blue-a1);\n    --info-a2: var(--blue-a2);\n    --info-a3: var(--blue-a3);\n    --info-a4: var(--blue-a4);\n    --info-a5: var(--blue-a5);\n    --info-a6: var(--blue-a6);\n    --info-a7: var(--blue-a7);\n    --info-a8: var(--blue-a8);\n    --info-a9: var(--blue-a9);\n    --info-a10: var(--blue-a10);\n    --info-a11: var(--blue-a11);\n    --info-a12: var(--blue-a12);\n  }\n  [data-accent-color='danger'] {\n    --color-surface-accent: var(--color-surface-danger);\n    --accent-1: var(--danger-1);\n    --accent-2: var(--danger-2);\n    --accent-3: var(--danger-3);\n    --accent-4: var(--danger-4);\n    --accent-5: var(--danger-5);\n    --accent-6: var(--danger-6);\n    --accent-7: var(--danger-7);\n    --accent-8: var(--danger-8);\n    --accent-9: var(--danger-9);\n    --accent-9-contrast: var(--danger-9-contrast);\n    --accent-10: var(--danger-10);\n    --accent-11: var(--danger-11);\n    --accent-12: var(--danger-12);\n    --accent-a1: var(--danger-a1);\n    --accent-a2: var(--danger-a2);\n    --accent-a3: var(--danger-a3);\n    --accent-a4: var(--danger-a4);\n    --accent-a5: var(--danger-a5);\n    --accent-a6: var(--danger-a6);\n    --accent-a7: var(--danger-a7);\n    --accent-a8: var(--danger-a8);\n    --accent-a9: var(--danger-a9);\n    --accent-a10: var(--danger-a10);\n    --accent-a11: var(--danger-a11);\n    --accent-a12: var(--danger-a12);\n  }\n  [data-accent-color='warning'] {\n    --color-surface-accent: var(--color-surface-warning);\n    --accent-1: var(--warning-1);\n    --accent-2: var(--warning-2);\n    --accent-3: var(--warning-3);\n    --accent-4: var(--warning-4);\n    --accent-5: var(--warning-5);\n    --accent-6: var(--warning-6);\n    --accent-7: var(--warning-7);\n    --accent-8: var(--warning-8);\n    --accent-9: var(--warning-9);\n    --accent-9-contrast: var(--warning-9-contrast);\n    --accent-10: var(--warning-10);\n    --accent-11: var(--warning-11);\n    --accent-12: var(--warning-12);\n    --accent-a1: var(--warning-a1);\n    --accent-a2: var(--warning-a2);\n    --accent-a3: var(--warning-a3);\n    --accent-a4: var(--warning-a4);\n    --accent-a5: var(--warning-a5);\n    --accent-a6: var(--warning-a6);\n    --accent-a7: var(--warning-a7);\n    --accent-a8: var(--warning-a8);\n    --accent-a9: var(--warning-a9);\n    --accent-a10: var(--warning-a10);\n    --accent-a11: var(--warning-a11);\n    --accent-a12: var(--warning-a12);\n  }\n  [data-accent-color='success'] {\n    --color-surface-accent: var(--color-surface-success);\n    --accent-1: var(--success-1);\n    --accent-2: var(--success-2);\n    --accent-3: var(--success-3);\n    --accent-4: var(--success-4);\n    --accent-5: var(--success-5);\n    --accent-6: var(--success-6);\n    --accent-7: var(--success-7);\n    --accent-8: var(--success-8);\n    --accent-9: var(--success-9);\n    --accent-9-contrast: var(--success-9-contrast);\n    --accent-10: var(--success-10);\n    --accent-11: var(--success-11);\n    --accent-12: var(--success-12);\n    --accent-a1: var(--success-a1);\n    --accent-a2: var(--success-a2);\n    --accent-a3: var(--success-a3);\n    --accent-a4: var(--success-a4);\n    --accent-a5: var(--success-a5);\n    --accent-a6: var(--success-a6);\n    --accent-a7: var(--success-a7);\n    --accent-a8: var(--success-a8);\n    --accent-a9: var(--success-a9);\n    --accent-a10: var(--success-a10);\n    --accent-a11: var(--success-a11);\n    --accent-a12: var(--success-a12);\n  }\n  [data-accent-color='info'] {\n    --color-surface-accent: var(--color-surface-info);\n    --accent-1: var(--info-1);\n    --accent-2: var(--info-2);\n    --accent-3: var(--info-3);\n    --accent-4: var(--info-4);\n    --accent-5: var(--info-5);\n    --accent-6: var(--info-6);\n    --accent-7: var(--info-7);\n    --accent-8: var(--info-8);\n    --accent-9: var(--info-9);\n    --accent-9-contrast: var(--info-9-contrast);\n    --accent-10: var(--info-10);\n    --accent-11: var(--info-11);\n    --accent-12: var(--info-12);\n    --accent-a1: var(--info-a1);\n    --accent-a2: var(--info-a2);\n    --accent-a3: var(--info-a3);\n    --accent-a4: var(--info-a4);\n    --accent-a5: var(--info-a5);\n    --accent-a6: var(--info-a6);\n    --accent-a7: var(--info-a7);\n    --accent-a8: var(--info-a8);\n    --accent-a9: var(--info-a9);\n    --accent-a10: var(--info-a10);\n    --accent-a11: var(--info-a11);\n    --accent-a12: var(--info-a12);\n  }\n  .frosted-ui {\n    --cursor-button: default;\n    --cursor-checkbox: default;\n    --cursor-disabled: not-allowed;\n    --cursor-link: pointer;\n    --cursor-menu-item: default;\n    --cursor-radio: default;\n    --cursor-slider-thumb: default;\n    --cursor-slider-thumb-active: default;\n    --cursor-switch: default;\n    --space-1: 4px;\n    --space-2: 8px;\n    --space-3: 12px;\n    --space-4: 16px;\n    --space-5: 24px;\n    --space-6: 32px;\n    --space-7: 40px;\n    --space-8: 48px;\n    --space-9: 64px;\n    --font-size-0: 10px;\n    --font-size-1: 12px;\n    --font-size-2: 14px;\n    --font-size-3: 16px;\n    --font-size-4: 18px;\n    --font-size-5: 20px;\n    --font-size-6: 24px;\n    --font-size-7: 28px;\n    --font-size-8: 32px;\n    --font-size-9: 40px;\n    --font-weight-light: 300;\n    --font-weight-regular: 400;\n    --font-weight-medium: 500;\n    --font-weight-semi-bold: 600;\n    --font-weight-bold: 700;\n    --line-height-0: 12px;\n    --line-height-1: 16px;\n    --line-height-2: 20px;\n    --line-height-3: 24px;\n    --line-height-4: 26px;\n    --line-height-5: 28px;\n    --line-height-6: 30px;\n    --line-height-7: 34px;\n    --line-height-8: 38px;\n    --line-height-9: 48px;\n    --letter-spacing-0: 0.01em;\n    --letter-spacing-1: 0.01em;\n    --letter-spacing-2: 0.01em;\n    --letter-spacing-3: 0.01em;\n    --letter-spacing-4: 0.01em;\n    --letter-spacing-5: 0.01em;\n    --letter-spacing-6: 0.01em;\n    --letter-spacing-7: 0.005em;\n    --letter-spacing-8: 0em;\n    --letter-spacing-9: 0em;\n    --default-font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI (Custom)', Roboto, 'Helvetica Neue',\n    'Open Sans (Custom)', system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji';\n    --default-font-size: var(--font-size-3);\n    --default-font-style: normal;\n    --default-font-weight: var(--font-weight-regular);\n    --default-line-height: 1.5;\n    --default-letter-spacing: 0em;\n    --default-leading-trim-start: 0.42em;\n    --default-leading-trim-end: 0.36em;\n    --heading-font-family: var(--default-font-family);\n    --heading-font-size-adjust: 1;\n    --heading-font-style: normal;\n    --heading-leading-trim-start: var(--default-leading-trim-start);\n    --heading-leading-trim-end: var(--default-leading-trim-end);\n    --heading-letter-spacing: 0em;\n    --heading-line-height-1: 16px;\n    --heading-line-height-2: 18px;\n    --heading-line-height-3: 22px;\n    --heading-line-height-4: 24px;\n    --heading-line-height-5: 26px;\n    --heading-line-height-6: 30px;\n    --heading-line-height-7: 36px;\n    --heading-line-height-8: 40px;\n    --heading-line-height-9: 60px;\n    --code-font-family: 'Menlo', 'Consolas (Custom)', 'Bitstream Vera Sans Mono', monospace, 'Apple Color Emoji',\n    'Segoe UI Emoji';\n    --code-font-size-adjust: 0.95;\n    --code-font-style: normal;\n    --code-font-weight: inherit;\n    --code-letter-spacing: -0.007em;\n    --code-padding-top: 0.1em;\n    --code-padding-bottom: 0.1em;\n    --strong-font-family: var(--default-font-family);\n    --strong-font-size-adjust: 1;\n    --strong-font-style: inherit;\n    --strong-font-weight: var(--font-weight-bold);\n    --strong-letter-spacing: 0em;\n    --em-font-family: 'Times New Roman', 'Times', serif;\n    --em-font-size-adjust: 1.18;\n    --em-font-style: italic;\n    --em-font-weight: inherit;\n    --em-letter-spacing: -0.025em;\n    --quote-font-family: 'Times New Roman', 'Times', serif;\n    --quote-font-size-adjust: 1.18;\n    --quote-font-style: italic;\n    --quote-font-weight: inherit;\n    --quote-letter-spacing: -0.025em;\n    --tabs-trigger-active-letter-spacing: -0.01em;\n    --tabs-trigger-active-word-spacing: 0em;\n    --tabs-trigger-inactive-letter-spacing: 0em;\n    --tabs-trigger-inactive-word-spacing: 0em;\n    overflow-wrap: break-word;\n    font-family: var(--default-font-family);\n    font-size: var(--default-font-size);\n    font-weight: var(--default-font-weight);\n    font-style: var(--default-font-style);\n    line-height: var(--default-line-height);\n    letter-spacing: var(--default-letter-spacing);\n    -webkit-text-size-adjust: none;\n    -moz-text-size-adjust: none;\n    text-size-adjust: none;\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n    --scrollarea-scrollbar-horizontal-margin-top: var(--space-1);\n    --scrollarea-scrollbar-horizontal-margin-bottom: var(--space-1);\n    --scrollarea-scrollbar-horizontal-margin-left: var(--space-1);\n    --scrollarea-scrollbar-horizontal-margin-right: var(--space-1);\n    --scrollarea-scrollbar-vertical-margin-top: var(--space-1);\n    --scrollarea-scrollbar-vertical-margin-bottom: var(--space-1);\n    --scrollarea-scrollbar-vertical-margin-left: var(--space-1);\n    --scrollarea-scrollbar-vertical-margin-right: var(--space-1);\n    --spinner-opacity: 0.65;\n    color: var(--gray-12);\n  }\n  @supports (color: color-mix(in oklab, white, black)) {\n    :is(.dark, .dark-theme), :is(.dark, .dark-theme) :where(.frosted-ui:not(.light, .light-theme)) {\n      --shadow-1: inset 0 -1px 1px 0 var(--gray-a3),\n      inset 0 0 0 1px var(--gray-a3),\n      inset 0 3px 4px 0 var(--black-a5),\n      inset 0 0 0 1px var(--gray-a4);\n      --shadow-2: 0 0 0 1px var(--gray-a6),\n      0 0 1px 1px var(--gray-a5),\n      0 1px 1px 0 var(--black-a6),\n      0 2px 1px -1px var(--black-a6),\n      0 1px 3px 0 var(--black-a8);\n      @supports (color: color-mix(in lab, red, red)) {\n        --shadow-2: 0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),\n      0 0 1px 1px var(--gray-a5),\n      0 1px 1px 0 var(--black-a6),\n      0 2px 1px -1px var(--black-a6),\n      0 1px 3px 0 var(--black-a8);\n      }\n      --shadow-3: 0 0 0 1px var(--gray-a6),\n      0 2px 3px -2px var(--black-a3),\n      0 3px 8px -2px var(--black-a6),\n      0 4px 12px -4px var(--black-a7);\n      @supports (color: color-mix(in lab, red, red)) {\n        --shadow-3: 0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),\n      0 2px 3px -2px var(--black-a3),\n      0 3px 8px -2px var(--black-a6),\n      0 4px 12px -4px var(--black-a7);\n      }\n      --shadow-4: 0 0 0 1px var(--gray-a6),\n      0 8px 40px var(--black-a3),\n      0 12px 32px -16px var(--black-a5);\n      @supports (color: color-mix(in lab, red, red)) {\n        --shadow-4: 0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),\n      0 8px 40px var(--black-a3),\n      0 12px 32px -16px var(--black-a5);\n      }\n      --shadow-5: 0 0 0 1px var(--gray-a6) inset,\n      0 12px 60px var(--black-a5),\n      0 12px 32px -16px var(--black-a7);\n      @supports (color: color-mix(in lab, red, red)) {\n        --shadow-5: 0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)) inset,\n      0 12px 60px var(--black-a5),\n      0 12px 32px -16px var(--black-a7);\n      }\n      --shadow-6: 0 0 0 1px var(--gray-a6),\n      0 12px 60px var(--black-a4),\n      0 16px 64px var(--black-a6),\n      0 16px 36px -20px var(--black-a11);\n      @supports (color: color-mix(in lab, red, red)) {\n        --shadow-6: 0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6)),\n      0 12px 60px var(--black-a4),\n      0 16px 64px var(--black-a6),\n      0 16px 36px -20px var(--black-a11);\n      }\n      --card-classic-hover-box-shadow: 0 0 0 1px var(--gray-a7),\n      0 0 1px 1px var(--gray-a7),\n      0 0 1px -1px var(--gray-a4),\n      0 0 3px -2px var(--gray-a3),\n      0 0 12px -2px var(--gray-a3),\n      0 0 16px -8px var(--gray-a9);\n      @supports (color: color-mix(in lab, red, red)) {\n        --card-classic-hover-box-shadow: 0 0 0 1px color-mix(in oklab, var(--gray-a7), var(--gray-8)),\n      0 0 1px 1px var(--gray-a7),\n      0 0 1px -1px var(--gray-a4),\n      0 0 3px -2px var(--gray-a3),\n      0 0 12px -2px var(--gray-a3),\n      0 0 16px -8px var(--gray-a9);\n      }\n    }\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 300;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI Semilight'), local('Segoe UI');\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 300;\n    font-style: italic;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI Semilight Italic'), local('Segoe UI Italic');\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 400;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI');\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 400;\n    font-style: italic;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI Italic');\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 500;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI Semibold'), local('Segoe UI');\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 500;\n    font-style: italic;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI Semibold Italic'), local('Segoe UI Italic');\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 700;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI Bold');\n  }\n  @font-face {\n    font-family: 'Segoe UI (Custom)';\n    font-weight: 700;\n    font-style: italic;\n    size-adjust: 103%;\n    descent-override: 35%;\n    ascent-override: 105%;\n    src: local('Segoe UI Bold Italic');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 300;\n    descent-override: 35%;\n    src: local('Open Sans Light'), local('Open Sans Regular');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 300;\n    font-style: italic;\n    descent-override: 35%;\n    src: local('Open Sans Light Italic'), local('Open Sans Italic');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 400;\n    descent-override: 35%;\n    src: local('Open Sans Regular');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 400;\n    font-style: italic;\n    descent-override: 35%;\n    src: local('Open Sans Italic');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 500;\n    descent-override: 35%;\n    src: local('Open Sans Medium'), local('Open Sans Regular');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 500;\n    font-style: italic;\n    descent-override: 35%;\n    src: local('Open Sans Medium Italic'), local('Open Sans Italic');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 700;\n    descent-override: 35%;\n    src: local('Open Sans Bold');\n  }\n  @font-face {\n    font-family: 'Open Sans (Custom)';\n    font-weight: 700;\n    font-style: italic;\n    descent-override: 35%;\n    src: local('Open Sans Bold Italic');\n  }\n  @font-face {\n    font-family: 'Consolas (Custom)';\n    font-weight: 400;\n    size-adjust: 110%;\n    ascent-override: 85%;\n    descent-override: 22%;\n    src: local('Consolas');\n  }\n  @font-face {\n    font-family: 'Consolas (Custom)';\n    font-weight: 400;\n    font-style: italic;\n    size-adjust: 110%;\n    ascent-override: 85%;\n    descent-override: 22%;\n    src: local('Consolas Italic');\n  }\n  @font-face {\n    font-family: 'Consolas (Custom)';\n    font-weight: 700;\n    size-adjust: 110%;\n    ascent-override: 85%;\n    descent-override: 22%;\n    src: local('Consolas Bold');\n  }\n  @font-face {\n    font-family: 'Consolas (Custom)';\n    font-weight: 700;\n    font-style: italic;\n    size-adjust: 110%;\n    ascent-override: 85%;\n    descent-override: 22%;\n    src: local('Consolas Bold Italic');\n  }\n  .fui-Heading {\n    margin: 0;\n    font-family: var(--heading-font-family);\n    font-style: var(--heading-font-style);\n    --leading-trim-start: var(--heading-leading-trim-start);\n    --leading-trim-end: var(--heading-leading-trim-end);\n    line-height: var(--line-height);\n  }\n  .fui-Heading:where([data-accent-color]) {\n    color: var(--accent-a11);\n  }\n  .fui-Heading:where([data-accent-color]):where(.fui-high-contrast) {\n    color: var(--accent-12);\n  }\n  .fui-Heading:where(.fui-r-size-0) {\n    font-size: calc(var(--font-size-0) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-0);\n    letter-spacing: calc(var(--letter-spacing-0) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-1) {\n    font-size: calc(var(--font-size-1) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-1);\n    letter-spacing: calc(var(--letter-spacing-1) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-2) {\n    font-size: calc(var(--font-size-2) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-2);\n    letter-spacing: calc(var(--letter-spacing-2) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-3) {\n    font-size: calc(var(--font-size-3) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-3);\n    letter-spacing: calc(var(--letter-spacing-3) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-4) {\n    font-size: calc(var(--font-size-4) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-4);\n    letter-spacing: calc(var(--letter-spacing-4) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-5) {\n    font-size: calc(var(--font-size-5) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-5);\n    letter-spacing: calc(var(--letter-spacing-5) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-6) {\n    font-size: calc(var(--font-size-6) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-6);\n    letter-spacing: calc(var(--letter-spacing-6) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-7) {\n    font-size: calc(var(--font-size-7) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-7);\n    letter-spacing: calc(var(--letter-spacing-7) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-8) {\n    font-size: calc(var(--font-size-8) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-8);\n    letter-spacing: calc(var(--letter-spacing-8) + var(--heading-letter-spacing));\n  }\n  .fui-Heading:where(.fui-r-size-9) {\n    font-size: calc(var(--font-size-9) * var(--heading-font-size-adjust));\n    --line-height: var(--heading-line-height-9);\n    letter-spacing: calc(var(--letter-spacing-9) + var(--heading-letter-spacing));\n  }\n  .fui-Text {\n    margin: 0;\n    line-height: var(--line-height, var(--default-line-height));\n    letter-spacing: var(--letter-spacing, inherit);\n  }\n  .fui-Text:where([data-accent-color]) {\n    color: var(--accent-a11);\n  }\n  .fui-Text:where([data-accent-color]):where(.fui-high-contrast), :where([data-accent-color]):where(.fui-Text, .fui-Heading) .fui-Text:where(.fui-high-contrast) {\n    color: var(--accent-12);\n  }\n  .fui-Text:where(.fui-r-size-0) {\n    font-size: var(--font-size-0);\n    --line-height: var(--line-height-0);\n    --letter-spacing: var(--letter-spacing-0);\n  }\n  .fui-Text:where(.fui-r-size-1) {\n    font-size: var(--font-size-1);\n    --line-height: var(--line-height-1);\n    --letter-spacing: var(--letter-spacing-1);\n  }\n  .fui-Text:where(.fui-r-size-2) {\n    font-size: var(--font-size-2);\n    --line-height: var(--line-height-2);\n    --letter-spacing: var(--letter-spacing-2);\n  }\n  .fui-Text:where(.fui-r-size-3) {\n    font-size: var(--font-size-3);\n    --line-height: var(--line-height-3);\n    --letter-spacing: var(--letter-spacing-3);\n  }\n  .fui-Text:where(.fui-r-size-4) {\n    font-size: var(--font-size-4);\n    --line-height: var(--line-height-4);\n    --letter-spacing: var(--letter-spacing-4);\n  }\n  .fui-Text:where(.fui-r-size-5) {\n    font-size: var(--font-size-5);\n    --line-height: var(--line-height-5);\n    --letter-spacing: var(--letter-spacing-5);\n  }\n  .fui-Text:where(.fui-r-size-6) {\n    font-size: var(--font-size-6);\n    --line-height: var(--line-height-6);\n    --letter-spacing: var(--letter-spacing-6);\n  }\n  .fui-Text:where(.fui-r-size-7) {\n    font-size: var(--font-size-7);\n    --line-height: var(--line-height-7);\n    --letter-spacing: var(--letter-spacing-7);\n  }\n  .fui-Text:where(.fui-r-size-8) {\n    font-size: var(--font-size-8);\n    --line-height: var(--line-height-8);\n    --letter-spacing: var(--letter-spacing-8);\n  }\n  .fui-Text:where(.fui-r-size-9) {\n    font-size: var(--font-size-9);\n    --line-height: var(--line-height-9);\n    --letter-spacing: var(--letter-spacing-9);\n  }\n  .fui-DialogOverlay {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    position: fixed;\n    inset: 0;\n    --dialog-overlay-padding-top: var(--space-4);\n    --dialog-overlay-padding-bottom: max(var(--space-4), 4vh);\n    padding-top: var(--dialog-overlay-padding-top);\n    padding-bottom: var(--dialog-overlay-padding-bottom);\n    padding-left: var(--space-4);\n    padding-right: var(--space-4);\n  }\n  .fui-DialogOverlay::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n  }\n  .fui-DialogContent {\n    width: 100%;\n    max-width: 580px;\n    outline: none;\n    overflow: auto;\n    background-color: var(--color-panel-solid);\n    box-shadow: var(--shadow-6);\n    box-sizing: border-box;\n    z-index: 1;\n    --inset-padding: var(--dialog-content-padding);\n    padding: var(--inset-padding);\n    max-height: calc(100vh - var(--dialog-overlay-padding-top) - var(--dialog-overlay-padding-bottom));\n  }\n  @supports (max-height: 100dvh) {\n    .fui-DialogContent {\n      max-height: calc(100dvh - var(--dialog-overlay-padding-top) - var(--dialog-overlay-padding-bottom));\n    }\n  }\n  .fui-DialogContent:where(.fui-r-size-1) {\n    --dialog-title-mb: var(--space-1);\n    --dialog-description-mb: var(--space-3);\n    --dialog-content-padding: var(--space-3);\n    border-radius: 8px;\n  }\n  .fui-DialogContent:where(.fui-r-size-2) {\n    --dialog-title-mb: var(--space-2);\n    --dialog-description-mb: var(--space-4);\n    --dialog-content-padding: var(--space-4);\n    border-radius: 12px;\n  }\n  .fui-DialogContent:where(.fui-r-size-3) {\n    --dialog-title-mb: var(--space-3);\n    --dialog-description-mb: 20px;\n    --dialog-content-padding: 20px;\n    border-radius: 16px;\n  }\n  .fui-DialogContent:where(.fui-r-size-4) {\n    --dialog-title-mb: var(--space-3);\n    --dialog-description-mb: var(--space-5);\n    --dialog-content-padding: var(--space-5);\n    border-radius: 20px;\n  }\n  .fui-DialogTitle:where(.fui-Heading) {\n    margin-bottom: var(--dialog-title-mb);\n  }\n  .fui-DialogDescription {\n    margin-bottom: var(--dialog-description-mb);\n  }\n  @property --overlay-blur {\n    syntax: '<length>';\n    inherits: false;\n    initial-value: 0px;\n  }\n  @property --overlay-brightness {\n    syntax: '<number>';\n    inherits: false;\n    initial-value: 0;\n  }\n  @keyframes fui-blur-in {\n    from {\n      --overlay-blur: 0px;\n      --overlay-brightness: 1;\n    }\n    to {\n      --overlay-blur: 3px;\n      --overlay-brightness: 0.7;\n    }\n  }\n  @keyframes fui-blur-out {\n    from {\n      --overlay-blur: 3px;\n      --overlay-brightness: 0.7;\n    }\n    to {\n      --overlay-blur: 0px;\n      --overlay-brightness: 1;\n    }\n  }\n  @media (prefers-reduced-motion: no-preference) {\n    @keyframes fui-dialog-overlay-no-op {\n      from {\n        opacity: 1;\n      }\n      to {\n        opacity: 1;\n      }\n    }\n    @keyframes fui-dialog-content-show {\n      from {\n        opacity: 0;\n        transform: translateY(5px) scale(0.97);\n      }\n      to {\n        opacity: 1;\n        transform: translateY(0px) scale(1);\n      }\n    }\n    @keyframes fui-dialog-content-hide {\n      from {\n        opacity: 1;\n        transform: translateY(0px) scale(1);\n      }\n      to {\n        opacity: 0;\n        transform: translateY(5px) scale(0.99);\n      }\n    }\n    .fui-DialogOverlay::before {\n      -webkit-backdrop-filter: blur(var(--overlay-blur)) brightness(var(--overlay-brightness));\n      backdrop-filter: blur(var(--overlay-blur)) brightness(var(--overlay-brightness));\n    }\n    .fui-DialogOverlay:where([data-state='closed']) {\n      animation: fui-blur-out 400ms cubic-bezier(0.16, 1, 0.3, 1) forwards;\n    }\n    .fui-DialogOverlay:where([data-state='open'])::before {\n      animation: fui-blur-in 400ms cubic-bezier(0.16, 1, 0.3, 1) forwards;\n    }\n    .fui-DialogOverlay:where([data-state='closed'])::before {\n      animation: fui-blur-out 400ms cubic-bezier(0.16, 1, 0.3, 1) forwards;\n    }\n    .fui-DialogContent:where([data-state='open']) {\n      animation: fui-dialog-content-show 400ms cubic-bezier(0.16, 1, 0.3, 1);\n    }\n    .fui-DialogContent:where([data-state='closed']) {\n      animation: fui-dialog-content-hide 150ms cubic-bezier(0.16, 1, 0.3, 1);\n    }\n  }\n  .fui-DrawerContent {\n    z-index: 1;\n    position: fixed;\n    top: 8px;\n    right: 8px;\n    bottom: 8px;\n    display: flex;\n    flex-direction: column;\n    width: 100%;\n    max-width: 380px;\n    box-sizing: border-box;\n    background-color: var(--color-panel-solid);\n    border-radius: 12px;\n    box-shadow: 0px 0px 0px 1px var(--gray-a5);\n    --drawer-content-padding: 16px;\n  }\n  .fui-DrawerHeader {\n    flex-shrink: 0;\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    gap: var(--space-3);\n    --inset-padding: var(--drawer-content-padding);\n    padding: var(--inset-padding);\n    border-bottom: 1px solid var(--color-stroke);\n  }\n  .fui-BodyScrollArea {\n    flex-shrink: 0;\n    flex: 1;\n    overflow-y: auto;\n    flex-grow: 1;\n  }\n  .fui-DrawerBody {\n    --inset-padding: var(--drawer-content-padding);\n    padding: var(--inset-padding);\n  }\n  .fui-DrawerStickyFooter {\n    flex-shrink: 0;\n    --inset-padding: var(--drawer-content-padding);\n    padding: var(--inset-padding);\n    border-top: 1px solid var(--color-stroke);\n  }\n  @media (prefers-reduced-motion: no-preference) {\n    @keyframes fui-drawer-content-show {\n      from {\n        transform: translateX(100%);\n      }\n      to {\n        transform: translateX(0%);\n      }\n    }\n    @keyframes fui-drawer-content-hide {\n      from {\n        transform: translateX(0%);\n      }\n      to {\n        transform: translateX(100%);\n      }\n    }\n    .fui-DrawerContent:where([data-state='open']) {\n      animation: fui-drawer-content-show 400ms cubic-bezier(0.32, 0.72, 0, 1);\n      animation: fui-drawer-content-show 400ms cubic-bezier(0.16, 1, 0.3, 1);\n    }\n    .fui-DrawerContent:where([data-state='closed']) {\n      animation: fui-drawer-content-hide 400ms cubic-bezier(0.32, 0.72, 0, 1);\n      animation: fui-drawer-content-hide 300ms cubic-bezier(0.16, 1, 0.3, 1);\n    }\n  }\n  .fui-SheetOverlay {\n    position: fixed;\n    inset: 0;\n    background: rgba(0, 0, 0, 0.4);\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-SheetContent {\n    --sheet-content-padding: 20px;\n    --inset-padding: var(--sheet-content-padding);\n    --sheet-border-color: var(--gray-a3);\n    position: fixed;\n    left: 0;\n    right: 0;\n    bottom: 0;\n    max-height: calc(100% - 48px);\n    display: flex;\n    flex-direction: column;\n    height: auto;\n    border-top-left-radius: 24px;\n    border-top-right-radius: 24px;\n    border: 1px solid var(--sheet-border-color);\n    background-color: var(--color-panel-solid);\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-SheetContent, .fui-SheetContent:focus, .fui-SheetContent:focus-visible {\n    outline: none;\n  }\n  .fui-SheetContent[vaul-drawer][vaul-drawer-direction='bottom']::after {\n    border-left: 1px solid var(--sheet-border-color);\n    border-right: 1px solid var(--sheet-border-color);\n    left: -1px;\n    right: -1px;\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-SheetBody {\n    padding: var(--inset-padding);\n    flex: 1;\n    height: 100%;\n    display: flex;\n    flex-direction: column;\n  }\n  .fui-SheetContentHandle {\n    margin-left: auto;\n    margin-right: auto;\n    margin-top: 16px;\n    margin-bottom: 12px;\n    height: 5px;\n    width: 40px;\n    border-radius: 999px;\n    background-color: var(--gray-a4);\n    -webkit-user-select: none;\n    user-select: none;\n    pointer-events: none;\n  }\n  .fui-SheetHeader {\n    display: grid;\n    padding: 0 var(--inset-padding) var(--inset-padding) var(--inset-padding);\n    gap: 12px;\n  }\n  .fui-SheetContentHandle, .fui-SheetHeader, .fui-SheetBody {\n    min-height: 0;\n  }\n  .fui-SheetContentHandle, .fui-SheetHeader {\n    flex-shrink: 0;\n  }\n  .fui-DatePickerRoot {\n    display: flex;\n    align-items: center;\n    gap: var(--space-1);\n  }\n  .fui-DateRangePickerRoot {\n    display: flex;\n    align-items: center;\n    gap: var(--space-1);\n  }\n  .fui-AccordionItem:focus-within {\n    position: relative;\n    z-index: 1;\n    box-shadow: 0 0 0 2px var(--color-focus-root) inset;\n    border-radius: var(--radius-4);\n  }\n  .fui-AccordionTrigger {\n    display: flex;\n    align-items: center;\n    width: 100%;\n    gap: var(--space-2);\n    background: var(--gray-a3);\n    border-radius: var(--radius-4);\n    padding: var(--space-2) var(--space-4);\n    box-shadow: 0px 0px 0px 1px var(--gray-a5) inset;\n    font-size: var(--font-size-1);\n    color: var(--gray-a11);\n    font-weight: var(--font-weight-bold);\n    text-transform: uppercase;\n    letter-spacing: 0.06em;\n  }\n  .fui-AccordionTriggerIcon {\n    transition: 300ms transform ease-out;\n    transform-origin: center;\n    transform: rotate(180deg);\n  }\n  .fui-AccordionTrigger[data-state='open'] .fui-AccordionTriggerIcon {\n    transform: rotate(0deg);\n  }\n  .fui-AccordionContent {\n    overflow: hidden;\n  }\n  .fui-AccordionContent[data-state='open'] {\n    animation: fui-accordion-slide-down 300ms ease-out;\n  }\n  .fui-AccordionContent[data-state='closed'] {\n    animation: fui-accordion-slide-up 300ms ease-out;\n  }\n  @keyframes fui-accordion-slide-down {\n    from {\n      height: 0;\n    }\n    to {\n      height: var(--radix-accordion-content-height);\n    }\n  }\n  @keyframes fui-accordion-slide-up {\n    from {\n      height: var(--radix-accordion-content-height);\n    }\n    to {\n      height: 0;\n    }\n  }\n  .fui-AccordionContentInner {\n    padding: var(--space-4) var(--space-5);\n  }\n  .fui-AvatarRoot {\n    container-type: inline-size;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    vertical-align: middle;\n    -webkit-user-select: none;\n    user-select: none;\n    width: var(--avatar-size);\n    height: var(--avatar-size);\n    flex-shrink: 0;\n    outline: 1px solid var(--accent-a5);\n    outline-offset: -1px;\n    background-color: var(--accent-a3);\n    border-radius: max(25%, var(--radius-full));\n  }\n  .fui-AvatarRoot :where(.fui-AvatarFallback) {\n    color: var(--accent-a11);\n  }\n  .fui-AvatarRoot:where(.fui-high-contrast) :where(.fui-AvatarFallback) {\n    color: var(--accent-12);\n  }\n  .fui-AvatarRoot:where(.fui-variant-round) {\n    --radius-full: var(--radius-thumb);\n  }\n  .fui-AvatarRoot:where([data-status='loaded']) {\n    outline: none;\n    background: none;\n  }\n  .fui-AvatarImage {\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n    border-radius: inherit;\n    transform: translateZ(0px);\n  }\n  .fui-AvatarImage:where(.fui-AvatarRoot[data-status='loaded'] .fui-AvatarImage) {\n    outline: 1px solid var(--gray-a5);\n    outline-offset: -1px;\n  }\n  .fui-AvatarFallback {\n    z-index: 0;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    line-height: 1;\n    border-radius: inherit;\n    font-weight: var(--font-weight-medium);\n    text-transform: uppercase;\n    line-height: 1.4;\n    letter-spacing: 0.05em;\n  }\n  .fui-AvatarFallback:where(.fui-one-letter) {\n    font-size: 45cqw;\n  }\n  .fui-AvatarFallback:where(.fui-two-letters) {\n    font-size: 40cqw;\n  }\n  .fui-AvatarRoot:where(.fui-r-size-1) {\n    --avatar-size: var(--space-5);\n  }\n  .fui-AvatarRoot:where(.fui-r-size-2) {\n    --avatar-size: var(--space-6);\n  }\n  .fui-AvatarRoot:where(.fui-r-size-3) {\n    --avatar-size: var(--space-7);\n  }\n  .fui-AvatarRoot:where(.fui-r-size-4) {\n    --avatar-size: var(--space-8);\n  }\n  .fui-AvatarRoot:where(.fui-r-size-5) {\n    --avatar-size: var(--space-9);\n  }\n  .fui-AvatarRoot:where(.fui-r-size-6) {\n    --avatar-size: 80px;\n  }\n  .fui-AvatarRoot:where(.fui-r-size-7) {\n    --avatar-size: 96px;\n  }\n  .fui-AvatarRoot:where(.fui-r-size-8) {\n    --avatar-size: 128px;\n  }\n  .fui-AvatarRoot:where(.fui-r-size-9) {\n    --avatar-size: 160px;\n  }\n  .fui-AvatarGroupRoot {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    vertical-align: middle;\n    -webkit-user-select: none;\n    user-select: none;\n    width: var(--avatar-group-size);\n    height: var(--avatar-group-size);\n    flex-shrink: 0;\n    contain: size layout;\n    overflow: hidden;\n    background-color: var(--accent-a3);\n    --avatar-group-inner-inset: 0.5;\n    --size-int: tan(atan2(var(--avatar-group-size), 1px));\n    --base-size-int: tan(atan2(var(--space-7), 1px));\n    --avatar-group-inner-scale: calc((var(--size-int) - var(--avatar-group-inner-inset) * 2) / var(--base-size-int));\n    border-radius: max(25%, var(--radius-full));\n  }\n  .fui-AvatarGroupRoot:where(.fui-high-contrast) :where(.fui-AvatarFallback) {\n    color: var(--accent-12);\n  }\n  .fui-AvatarGroupRoot:where(.fui-variant-round) {\n    --radius-full: var(--radius-thumb);\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-1) {\n    --avatar-group-size: var(--space-5);\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-2) {\n    --avatar-group-size: var(--space-6);\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-3) {\n    --avatar-group-size: var(--space-7);\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-4) {\n    --avatar-group-size: var(--space-8);\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-5) {\n    --avatar-group-size: var(--space-9);\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-6) {\n    --avatar-group-size: 80px;\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-7) {\n    --avatar-group-size: 96px;\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-8) {\n    --avatar-group-size: 128px;\n  }\n  .fui-AvatarGroupRoot:where(.fui-r-size-9) {\n    --avatar-group-size: 160px;\n  }\n  .fui-AvatarGroupRootInner {\n    position: relative;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    width: 100%;\n    height: 100%;\n    transform-origin: 0 0;\n    transform: scale(var(--avatar-group-inner-scale));\n    pointer-events: none;\n    left: calc(var(--avatar-group-inner-inset) * 1px);\n    top: calc(var(--avatar-group-inner-inset) * 1px);\n  }\n  .fui-AvatarGroupRoot[data-status='loaded'] {\n    outline: 1px solid var(--gray-a5);\n    background: none;\n  }\n  .fui-AvatarGroupAvatar {\n    position: absolute;\n    left: 0;\n    top: 0;\n    transition: transform 0.5s cubic-bezier(0.32, 0.72, 0, 1);\n    transform-origin: center;\n    visibility: hidden;\n    background-color: var(--accent-4);\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(1)) .fui-AvatarGroupAvatar:nth-child(1) {\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(2)) .fui-AvatarGroupAvatar:nth-child(1) {\n    transform: scale(0.5) translate(-12px, -12px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(2)) .fui-AvatarGroupAvatar:nth-child(2) {\n    transform: scale(0.4) translate(18px, 18px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(3)) .fui-AvatarGroupAvatar:nth-child(1) {\n    transform: scale(0.5) translate(-12px, -12px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(3)) .fui-AvatarGroupAvatar:nth-child(2) {\n    transform: scale(0.4) translate(26px, 10px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(3)) .fui-AvatarGroupAvatar:nth-child(3) {\n    transform: scale(0.35) translate(-10px, 33px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(1) {\n    transform: scale(0.5) translate(-10px, -14px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(2) {\n    transform: scale(0.4) translate(23px, 12px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(3) {\n    transform: scale(0.35) translate(-16px, 30px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(4)) .fui-AvatarGroupAvatar:nth-child(4) {\n    transform: scale(0.25) translate(42px, -36px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(1) {\n    transform: scale(0.46) translate(-16px, -12px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(2) {\n    transform: scale(0.4) translate(18px, 18px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(3) {\n    transform: scale(0.32) translate(-23px, 32px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(4) {\n    transform: scale(0.28) translate(40px, -22px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(5)) .fui-AvatarGroupAvatar:nth-child(5) {\n    transform: scale(0.22) translate(18px, -64px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(1) {\n    transform: scale(0.46) translate(-16px, -12px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(2) {\n    transform: scale(0.4) translate(22px, 12px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(3) {\n    transform: scale(0.32) translate(-14px, 36px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(4) {\n    transform: scale(0.24) translate(44px, -34px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(5) {\n    transform: scale(0.2) translate(16px, -72px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(6)) .fui-AvatarGroupAvatar:nth-child(6) {\n    transform: scale(0.18) translate(-76px, 36px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(1) {\n    transform: scale(0.46) translate(-16px, -12px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(2) {\n    transform: scale(0.4) translate(24px, 16px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(3) {\n    transform: scale(0.32) translate(-14px, 36px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(4) {\n    transform: scale(0.22) translate(50px, -44px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(5) {\n    transform: scale(0.2) translate(16px, -72px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(6) {\n    transform: scale(0.18) translate(-76px, 36px);\n    visibility: visible;\n  }\n  .fui-AvatarGroupRoot:has(.fui-AvatarGroupAvatar:nth-child(7)) .fui-AvatarGroupAvatar:nth-child(7) {\n    transform: scale(0.16) translate(34px, -26px);\n    visibility: visible;\n  }\n  .fui-Badge {\n    display: inline-flex;\n    align-items: center;\n    box-sizing: border-box;\n    white-space: nowrap;\n    font-weight: var(--font-weight-medium);\n    flex-shrink: 0;\n    line-height: 1;\n    -webkit-user-select: none;\n    user-select: none;\n    cursor: default;\n  }\n  .fui-Badge:where(.fui-r-size-1) {\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n    padding: 2px 8px;\n    gap: 4px;\n    border-radius: max(var(--radius-3), var(--radius-full));\n  }\n  .fui-Badge:where(.fui-r-size-2) {\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    padding: 4px 12px;\n    gap: 6px;\n    border-radius: max(var(--radius-4), var(--radius-full));\n  }\n  .fui-Badge:where(.fui-variant-solid) {\n    background-color: var(--accent-9);\n    color: var(--accent-9-contrast);\n  }\n  .fui-Badge:where(.fui-variant-solid):where(.fui-high-contrast) {\n    background-color: var(--accent-12);\n    color: var(--accent-1);\n  }\n  .fui-Badge:where(.fui-variant-surface) {\n    background-color: var(--color-surface-accent);\n    box-shadow: inset 0 0 0 1px var(--accent-a7);\n    color: var(--accent-a11);\n  }\n  .fui-Badge:where(.fui-variant-surface):where(.fui-high-contrast) {\n    color: var(--accent-12);\n  }\n  .fui-Badge:where(.fui-variant-soft) {\n    background-color: var(--accent-a3);\n    color: var(--accent-a11);\n  }\n  .fui-Badge:where(.fui-variant-soft):where(.fui-high-contrast) {\n    color: var(--accent-12);\n  }\n  .fui-Badge:where(.fui-variant-outline) {\n    box-shadow: inset 0 0 0 1px var(--accent-a8);\n    color: var(--accent-a11);\n  }\n  .fui-Badge:where(.fui-variant-outline):where(.fui-high-contrast) {\n    box-shadow: inset 0 0 0 1px var(--accent-a11);\n    color: var(--accent-12);\n  }\n  .fui-Blockquote {\n    border-left: clamp(2px, 0.125em, 6px) solid var(--accent-a6);\n    padding-left: min(var(--space-5), max(var(--space-3), 0.5em));\n  }\n  .fui-BreadcrumbsRoot {\n    display: inline-flex;\n    align-items: center;\n    gap: 10px;\n    -webkit-user-select: none;\n    user-select: none;\n    user-select: none;\n  }\n  .fui-BreadcrumbsItem {\n    margin-left: -8px;\n    margin-right: -8px;\n    font-weight: var(--font-weight-medium);\n  }\n  .fui-BreadcrumbsItem:not(:hover) {\n    color: var(--accent-a10);\n  }\n  .fui-BreadcrumbsItem:last-child, .fui-BreadcrumbsRoot .fui-BreadcrumbsLastItem {\n    color: var(--accent-a12);\n    font-weight: var(--font-weight-medium);\n  }\n  .fui-BreadcrumbsSeparator {\n    color: var(--accent-a8);\n  }\n  .fui-BaseButton:where([aria-busy]) {\n    position: relative;\n  }\n  .fui-BaseButton {\n    height: var(--base-button-height);\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n    flex-shrink: 0;\n    -webkit-user-select: none;\n    user-select: none;\n    vertical-align: top;\n    color: var(--base-button-color);\n  }\n  .fui-BaseButton:where(.fui-r-size-1) {\n    --base-button-classic-active-padding-top: 1px;\n    --base-button-height: var(--space-5);\n    border-radius: 6px;\n    --base-button-spinner-size: 12px;\n  }\n  .fui-BaseButton:where(.fui-r-size-2) {\n    --base-button-classic-active-padding-top: 2px;\n    --base-button-height: var(--space-6);\n    border-radius: 8px;\n    --base-button-spinner-size: 16px;\n  }\n  .fui-BaseButton:where(.fui-r-size-3) {\n    --base-button-classic-active-padding-top: 2px;\n    --base-button-height: var(--space-7);\n    border-radius: 10px;\n    --base-button-spinner-size: 18px;\n  }\n  .fui-BaseButton:where(.fui-r-size-4) {\n    --base-button-classic-active-padding-top: 2px;\n    --base-button-height: var(--space-8);\n    border-radius: 14px;\n    --base-button-spinner-size: 22px;\n  }\n  .fui-BaseButton:where(.fui-variant-classic) {\n    background: var(--accent-9);\n    --base-button-color: var(--accent-9-contrast);\n    position: relative;\n    z-index: 0;\n    text-shadow: 0px 1px 2px rgba(0, 0, 0, 0.32);\n    box-shadow: 0 0 0 1px var(--accent-9) inset, 0px 1px 2px 0px rgba(0, 0, 0, 0.15), 0px -1px 1px 1px rgba(0, 0, 0, 0.08) inset, 0px 1px 1px 1px rgba(255, 255, 255, 0.25) inset;\n  }\n  .fui-BaseButton:where(.fui-variant-classic):where(.fui-high-contrast) {\n    background-color: var(--accent-12);\n    --base-button-color: var(--gray-1);\n  }\n  .fui-BaseButton:where(.fui-variant-classic):where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  @media (hover: hover) {\n    .fui-BaseButton:where(.fui-variant-classic):where(:hover) {\n      background-color: var(--accent-10);\n    }\n    .fui-BaseButton:where(.fui-variant-classic):where(:hover):where(.fui-high-contrast) {\n      background-color: var(--accent-12);\n      filter: var(--base-button-classic-high-contrast-hover-filter);\n    }\n  }\n  .fui-BaseButton:where(.fui-variant-classic):where([data-state='open'])::after {\n    background-color: var(--accent-10);\n  }\n  .fui-BaseButton:where(.fui-variant-classic):where([data-state='open']):where(.fui-high-contrast) {\n    filter: var(--base-button-classic-high-contrast-hover-filter);\n  }\n  .fui-BaseButton:where(.fui-variant-classic):where(:active:not([data-state='open'], [data-disabled])) {\n    background: var(--accent-9) linear-gradient(to bottom, transparent, var(--white-a4));\n    padding-top: var(--base-button-classic-active-padding-top);\n    box-shadow: 0px 3px 4px 0px rgba(0, 0, 0, 0.2) inset;\n  }\n  .fui-BaseButton:where(.fui-variant-classic):where(:active:not([data-state='open'], [data-disabled])):where(.fui-high-contrast) {\n    background-color: var(--accent-12);\n    filter: var(--base-button-classic-high-contrast-active-filter);\n  }\n  .fui-BaseButton:where(.fui-variant-classic):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    --base-button-color: var(--gray-a8);\n    background-color: var(--gray-3);\n    box-shadow: 0 0 0 1px var(--gray-a4) inset, 0px 1px 2px 0px rgba(0, 0, 0, 0.15), 0px -1px 1px 1px rgba(0, 0, 0, 0.08) inset, 0px 1px 1px 1px rgba(255, 255, 255, 0.25) inset;\n    background-image: none;\n    filter: none;\n    text-shadow: none;\n  }\n  .fui-BaseButton:where(.fui-variant-solid) {\n    position: relative;\n    background-color: var(--accent-9);\n    color: var(--accent-9-contrast);\n  }\n  @media (hover: hover) {\n    .fui-BaseButton:where(.fui-variant-solid):where(:hover) {\n      background-color: var(--accent-10);\n    }\n  }\n  .fui-BaseButton:where(.fui-variant-solid):where([data-state='open']) {\n    background-color: var(--accent-10);\n  }\n  .fui-BaseButton:where(.fui-variant-solid):where(:active:not([data-state='open'])) {\n    background-color: var(--accent-10);\n    filter: var(--base-button-solid-active-filter);\n  }\n  @media (pointer: coarse) {\n    .fui-BaseButton:where(.fui-variant-solid):where(:active:not([data-state='open'])) {\n      outline: 0.5em solid var(--accent-a4);\n      outline-offset: 0;\n    }\n  }\n  .fui-BaseButton:where(.fui-variant-solid):where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  .fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast) {\n    background-color: var(--accent-12);\n    color: var(--gray-1);\n  }\n  @media (hover: hover) {\n    .fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast):where(:hover) {\n      background-color: var(--accent-12);\n      filter: var(--base-button-solid-high-contrast-hover-filter);\n    }\n  }\n  .fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast):where([data-state='open']) {\n    background-color: var(--accent-12);\n    filter: var(--base-button-solid-high-contrast-hover-filter);\n  }\n  .fui-BaseButton:where(.fui-variant-solid):where(.fui-high-contrast):where(:active:not([data-state='open'])) {\n    background-color: var(--accent-12);\n    filter: var(--base-button-solid-high-contrast-active-filter);\n  }\n  .fui-BaseButton:where(.fui-variant-solid):where([data-disabled]) {\n    color: var(--gray-a8);\n    background-color: var(--gray-a3);\n    outline: none;\n    filter: none;\n  }\n  .fui-BaseButton:where(.fui-variant-soft, .fui-variant-ghost) {\n    --base-button-color: var(--accent-a11);\n  }\n  .fui-BaseButton:where(.fui-variant-soft, .fui-variant-ghost):where(.fui-high-contrast) {\n    --base-button-color: var(--accent-12);\n  }\n  .fui-BaseButton:where(.fui-variant-soft, .fui-variant-ghost):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    --base-button-color: var(--gray-a8);\n    background-color: var(--gray-a3);\n  }\n  .fui-BaseButton:where(.fui-variant-soft) {\n    background-color: var(--accent-a3);\n  }\n  .fui-BaseButton:where(.fui-variant-soft):where(:focus-visible) {\n    outline: 2px solid var(--accent-8);\n    outline-offset: -1px;\n  }\n  @media (hover: hover) {\n    .fui-BaseButton:where(.fui-variant-soft):where(:hover) {\n      background-color: var(--accent-a4);\n    }\n  }\n  .fui-BaseButton:where(.fui-variant-soft):where([data-state='open']) {\n    background-color: var(--accent-a4);\n  }\n  .fui-BaseButton:where(.fui-variant-soft):where(:active:not([data-state='open'])) {\n    background-color: var(--accent-a5);\n  }\n  .fui-BaseButton:where(.fui-variant-soft):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    --base-button-color: var(--gray-a8);\n    background-color: var(--gray-a3);\n  }\n  @media (hover: hover) {\n    .fui-BaseButton:where(.fui-variant-ghost):where(:hover) {\n      background-color: var(--accent-a3);\n    }\n  }\n  .fui-BaseButton:where(.fui-variant-ghost):where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-BaseButton:where(.fui-variant-ghost):where([data-state='open']) {\n    background-color: var(--accent-a3);\n  }\n  .fui-BaseButton:where(.fui-variant-ghost):where(:active:not([data-state='open'])) {\n    background-color: var(--accent-a4);\n  }\n  .fui-BaseButton:where(.fui-variant-ghost):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    --base-button-color: var(--gray-a8);\n    background-color: transparent;\n  }\n  .fui-BaseButton:where(.fui-variant-surface) {\n    background-color: var(--color-panel-solid);\n    box-shadow: inset 0 0 0 1px var(--gray-a5), 0px 1px 2px 0px rgba(0, 0, 0, 0.05);\n    --base-button-color: var(--accent-11);\n  }\n  .fui-BaseButton:where(.fui-variant-surface):where([data-accent-color='gray']) {\n    --base-button-color: var(--accent-12);\n  }\n  @media (hover: hover) {\n    .fui-BaseButton:where(.fui-variant-surface):where(:hover) {\n      box-shadow: inset 0 0 0 1px var(--gray-a7), 0px 1px 2px 0px rgba(0, 0, 0, 0.05);\n    }\n  }\n  .fui-BaseButton:where(.fui-variant-surface):where([data-state='open']), .fui-BaseButton:where(.fui-variant-surface):where(:active) {\n    background-color: var(--gray-a3);\n    box-shadow: inset 0 0 0 1px var(--gray-a6);\n  }\n  .fui-BaseButton:where(.fui-variant-surface):where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-BaseButton:where(.fui-variant-surface):where(.fui-high-contrast) {\n    --base-button-color: var(--accent-12);\n  }\n  .fui-BaseButton:where(.fui-variant-surface):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    --base-button-color: var(--gray-a8);\n    box-shadow: inset 0 0 0 1px var(--gray-a6);\n    background-color: var(--gray-a2);\n  }\n  .fui-Button:where(:not(.fui-variant-ghost)) :where(svg) {\n    opacity: 0.9;\n  }\n  .fui-Button:where(.fui-r-size-1) {\n    gap: var(--space-1);\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n    padding-left: var(--space-2);\n    padding-right: var(--space-2);\n  }\n  .fui-Button:where(.fui-r-size-2) {\n    gap: var(--space-2);\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    padding-left: var(--space-3);\n    padding-right: var(--space-3);\n  }\n  .fui-Button:where(.fui-r-size-3) {\n    gap: var(--space-3);\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n    letter-spacing: var(--letter-spacing-3);\n    padding-left: var(--space-4);\n    padding-right: var(--space-4);\n  }\n  .fui-Button:where(.fui-r-size-4) {\n    gap: var(--space-3);\n    font-size: var(--font-size-4);\n    line-height: var(--line-height-4);\n    letter-spacing: var(--letter-spacing-4);\n    padding-left: var(--space-5);\n    padding-right: var(--space-5);\n  }\n  .fui-Button:where(:not(.fui-variant-ghost)) {\n    font-weight: var(--font-weight-medium);\n  }\n  .fui-CalendarRoot {\n    display: inline-block;\n  }\n  .fui-CalendarHeader {\n    display: flex;\n    align-items: center;\n    gap: 6px;\n    margin: 0 2px 12px 2px;\n  }\n  .fui-CalendarDropdowns {\n    flex: 1;\n    display: flex;\n    gap: 6px;\n    justify-content: center;\n  }\n  .fui-CalendarDropdowns > * {\n    flex: 1;\n  }\n  .fui-CalendarGrid {\n    width: 100%;\n    -webkit-user-select: none;\n    user-select: none;\n    border-spacing: 0;\n    border-collapse: collapse;\n    isolation: isolate;\n    font-size: var(--font-size-2);\n    font-weight: var(--font-weight-regular);\n    color: var(--gray-a10);\n  }\n  .fui-CalendarGridCellInner {\n    position: relative;\n    cursor: default;\n    text-align: center;\n    padding: 6px 8px;\n    margin: 2px 0;\n    position: relative;\n    outline: none;\n    font-size: var(--font-size-2);\n    font-weight: var(--font-weight-regular);\n    line-height: var(--line-height-2);\n    color: var(--gray-a12);\n  }\n  .fui-CalendarGridCellInner::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    z-index: -1;\n  }\n  .fui-CalendarGridCellInner[data-selection-state]::before {\n    background: var(--accent-a4);\n    border-top-right-radius: inherit;\n    border-bottom-right-radius: inherit;\n    border-top-left-radius: inherit;\n    border-bottom-left-radius: inherit;\n  }\n  .fui-CalendarGridCellInner[data-selection-state='selected'] {\n    border-radius: 6px;\n  }\n  .fui-CalendarGridCellInner:where(:not([aria-disabled]):hover) {\n    background: var(--gray-a3);\n    border-radius: 6px;\n  }\n  .fui-CalendarGridCellInner[data-selection-state='range']::before {\n    border-radius: 0;\n  }\n  .fui-CalendarGridCellInner.fui-CalendarGridCell-selected + .fui-CalendarGridCellInner.fui-CalendarGridCell-selected {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .fui-CalendarGridCellInner:where([aria-disabled]:hover) {\n    cursor: var(--cursor-disabled);\n  }\n  .fui-CalendarGridCellInner[data-rounded='left']::before {\n    border-top-left-radius: 6px;\n    border-bottom-left-radius: 6px;\n  }\n  .fui-CalendarGridCellInner[data-rounded='right']::before {\n    border-top-right-radius: 6px;\n    border-bottom-right-radius: 6px;\n  }\n  .fui-CalendarGridCellInner.fui-CalendarGridCell-selected {\n    background: var(--accent-9);\n    color: white;\n  }\n  .fui-CalendarGridCellInner[data-selection-state='start'], .fui-CalendarGridCellInner[data-selection-state='end'] {\n    border-radius: 6px;\n  }\n  .fui-CalendarGridCellInner[data-selection-state='end']::before {\n    border-top-left-radius: unset;\n    border-bottom-left-radius: unset;\n  }\n  .fui-CalendarGridCellInner[data-selection-state='start']::before {\n    border-top-right-radius: unset;\n    border-bottom-right-radius: unset;\n  }\n  .fui-CalendarGridCell:has([data-selection-state='start']) + .fui-CalendarGridCell:has([data-selection-state='end']) .fui-CalendarGridCellInner {\n    border-top-left-radius: 0;\n    border-bottom-left-radius: 0;\n  }\n  .fui-CalendarGridCell:has([data-selection-state='start']):has(+ .fui-CalendarGridCell [data-selection-state='end']) .fui-CalendarGridCellInner {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .fui-CalendarGridCellInner.fui-CalendarGridCell-focusRing:focus-visible:after {\n    content: '';\n    position: absolute;\n    inset: 0px;\n    border-radius: 6px;\n    z-index: -1;\n    outline: 2px solid var(--accent-9);\n    outline-offset: 2px;\n    z-index: 1;\n    mix-blend-mode: screen;\n  }\n  .fui-CalendarGridCellInner[aria-disabled] {\n    opacity: 0.6;\n  }\n  .fui-CalendarGridCellInner.fui-CalendarGridCell-unavailable {\n    color: var(--danger-9);\n    text-decoration: line-through;\n  }\n  .fui-CalendarGridCellInnerPlaceholder {\n    opacity: 0;\n    pointer-events: none;\n  }\n  .fui-CalloutRoot {\n    display: grid;\n    align-items: flex-start;\n    justify-content: flex-start;\n    text-align: left;\n    color: var(--accent-a11);\n  }\n  .fui-CalloutRoot:where(.fui-high-contrast) {\n    color: var(--accent-12);\n  }\n  .fui-CalloutIcon {\n    display: flex;\n    align-items: center;\n    grid-column-start: -2;\n    height: var(--line-height);\n  }\n  .fui-CalloutRoot > :where(:not(.fui-CalloutIcon)) {\n    grid-column-start: -1;\n  }\n  .fui-CalloutRoot:where(.fui-r-size-1) {\n    row-gap: var(--space-2);\n    column-gap: var(--space-2);\n    padding: var(--space-3) var(--space-4);\n    border-radius: var(--radius-5);\n  }\n  .fui-CalloutRoot:where(.fui-r-size-2) {\n    row-gap: var(--space-2);\n    column-gap: var(--space-3);\n    padding: var(--space-4);\n    border-radius: var(--radius-5);\n  }\n  .fui-CalloutRoot:where(.fui-r-size-3) {\n    row-gap: var(--space-3);\n    column-gap: var(--space-3);\n    padding: var(--space-5);\n    border-radius: var(--radius-6);\n  }\n  .fui-CalloutRoot:where(.fui-variant-soft) {\n    background-color: var(--accent-a3);\n  }\n  .fui-CalloutRoot:where(.fui-variant-surface) {\n    box-shadow: inset 0 0 0 1px var(--accent-a6);\n    background-color: var(--accent-a2);\n  }\n  .fui-CalloutRoot:where(.fui-variant-outline) {\n    box-shadow: inset 0 0 0 1px var(--accent-a7);\n  }\n  .fui-Card {\n    border-radius: var(--card-border-radius);\n    position: relative;\n    text-align: initial;\n    --card-after-border-radius: calc(var(--card-border-radius) - var(--card-border-width));\n    border: var(--card-border-width) solid transparent;\n    background-clip: padding-box;\n  }\n  .fui-Card:where(button, a) {\n    display: block;\n  }\n  .fui-Card::after {\n    inset: 0;\n    position: absolute;\n    pointer-events: none;\n    border-radius: inherit;\n    content: '';\n  }\n  .fui-Card:where(:focus-visible)::after {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-CardInner {\n    --inset-border-width: var(--card-border-width);\n    --inset-border-radius: var(--card-border-radius);\n    position: relative;\n    box-sizing: border-box;\n    border-radius: inherit;\n    overflow: hidden;\n    padding: var(--card-padding);\n    --inset-padding: calc(var(--card-padding) - var(--card-border-width));\n    width: calc(100% + var(--card-border-width) * 2);\n    height: calc(100% + var(--card-border-width) * 2);\n    margin: calc(-1 * var(--card-border-width));\n  }\n  .fui-Card:where(.fui-variant-ghost) {\n    --margin-top: 0px;\n    --margin-right: 0px;\n    --margin-bottom: 0px;\n    --margin-left: 0px;\n    --margin-top-override: calc(var(--margin-top) - var(--card-padding));\n    --margin-right-override: calc(var(--margin-right) - var(--card-padding));\n    --margin-bottom-override: calc(var(--margin-bottom) - var(--card-padding));\n    --margin-left-override: calc(var(--margin-left) - var(--card-padding));\n    margin: var(--margin-top-override) var(--margin-right-override) var(--margin-bottom-override) var(--margin-left-override);\n    --card-border-width: 0px;\n  }\n  :where(.fui-Card:where(.fui-variant-ghost)) > * {\n    --margin-top-override: initial;\n    --margin-right-override: initial;\n    --margin-bottom-override: initial;\n    --margin-left-override: initial;\n  }\n  .fui-Card:where(.fui-r-size-1) {\n    --card-padding: var(--space-3);\n    --card-border-radius: 12px;\n  }\n  .fui-Card:where(.fui-r-size-2) {\n    --card-padding: var(--space-4);\n    --card-border-radius: 16px;\n  }\n  .fui-Card:where(.fui-r-size-3) {\n    --card-padding: var(--space-5);\n    --card-border-radius: 16px;\n  }\n  .fui-Card:where(.fui-r-size-4) {\n    --card-padding: var(--space-6);\n    --card-border-radius: 20px;\n  }\n  .fui-Card:where(.fui-r-size-5) {\n    --card-padding: var(--space-7);\n    --card-border-radius: 24px;\n  }\n  .fui-Card:where(.fui-variant-surface) {\n    --card-border-width: 1px;\n    background: var(--card-background);\n  }\n  .fui-Card:where(.fui-variant-surface)::after {\n    border-radius: var(--card-after-border-radius);\n    box-shadow: 0 0 0 1px var(--gray-a6);\n    box-shadow: 0 0 0 1px var(--gray-a6);\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 0 0 1px color-mix(in oklab, var(--gray-a6), var(--gray-6) 25%);\n    }\n  }\n  @media (hover: hover) {\n    .fui-Card:where(.fui-variant-surface):where(button, [href], [type='button']):where(:hover)::after {\n      box-shadow: 0 0 0 1px var(--gray-a8);\n      box-shadow: 0 0 0 1px var(--gray-a8);\n      @supports (color: color-mix(in lab, red, red)) {\n        box-shadow: 0 0 0 1px color-mix(in oklab, var(--gray-a8), var(--gray-8) 25%);\n      }\n    }\n  }\n  .fui-Card:where(.fui-variant-surface):where(button, [href], [type='button']):where(:active:not([data-state='open']))::after {\n    box-shadow: 0 0 0 1px var(--gray-a8), 0 0 0 1px var(--gray-a6);\n    box-shadow: 0 0 0 1px var(--gray-a8), 0 0 0 1px var(--gray-a6);\n    @supports (color: color-mix(in lab, red, red)) {\n      box-shadow: 0 0 0 1px color-mix(in oklab, var(--gray-a8), var(--gray-8) 25%), 0 0 0 1px var(--gray-a6);\n    }\n  }\n  .fui-Card:where(.fui-variant-surface):where(button, [href], [type='button']):where(:active:not([data-state='open'])):where(:focus-visible) {\n    background-color: var(--gray-a3);\n  }\n  .fui-Card:where(.fui-variant-classic) {\n    --card-border-width: 1px;\n    background: var(--card-background);\n  }\n  .fui-Card:where(.fui-variant-classic)::after {\n    border-radius: var(--card-after-border-radius);\n    box-shadow: 0 0 0 1px var(--color-transparent), var(--shadow-2);\n    transition: box-shadow 120ms;\n  }\n  @media (hover: hover) {\n    .fui-Card:where(.fui-variant-classic):where(button, [href], [type='button']):where(:hover)::after {\n      transition-duration: 40ms;\n      box-shadow: var(--card-classic-hover-box-shadow);\n    }\n  }\n  .fui-Card:where(.fui-variant-classic):where(button, [href], [type='button']):where(:active:not([data-state='open']))::after {\n    transition-duration: 40ms;\n    box-shadow: 0 0 0 1px var(--gray-a5), var(--shadow-2);\n  }\n  .fui-Card:where(.fui-variant-classic):where(button, [href], [type='button']):where(:active:not([data-state='open'])):where(:focus-visible) {\n    background-color: var(--gray-a3);\n  }\n  @media (hover: hover) {\n    .fui-Card:where(.fui-variant-ghost):where(button, [href], [type='button']):where(:hover) {\n      background-color: var(--gray-a3);\n    }\n  }\n  .fui-Card:where(.fui-variant-ghost):where(button, [href], [type='button']):where(:active:not([data-state='open'])) {\n    background-color: var(--gray-a4);\n  }\n  .fui-CheckboxRoot {\n    display: inline-flex;\n    align-items: center;\n    vertical-align: top;\n    flex-shrink: 0;\n    height: var(--line-height, var(--checkbox-size));\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-CheckboxButton {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n    height: var(--checkbox-size);\n    width: var(--checkbox-size);\n    cursor: var(--cursor-checkbox);\n    position: relative;\n  }\n  .fui-CheckboxButton:where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  .fui-CheckboxButton:where([data-state='unchecked']) {\n    background-color: var(--color-surface);\n    box-shadow: inset 0 0 0 1px var(--gray-a7);\n  }\n  .fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])) {\n    background-color: var(--accent-9);\n    color: var(--accent-9-contrast);\n  }\n  .fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])):where(.fui-high-contrast) {\n    background-color: var(--accent-12);\n    color: var(--accent-1);\n  }\n  .fui-CheckboxButton:where(:disabled) {\n    box-shadow: inset 0 0 0 1px var(--gray-a5);\n    background-color: var(--gray-a2);\n    background-image: none;\n    cursor: var(--cursor-disabled);\n    color: var(--gray-a8);\n  }\n  @media (hover: hover) {\n    .fui-CheckboxButton:where([data-state='unchecked']):not(:disabled):hover {\n      box-shadow: inset 0 0 0 1px var(--gray-a8);\n    }\n    .fui-CheckboxButton:where([data-state='unchecked']):not(:disabled):active {\n      box-shadow: inset 0 0 0 1px var(--gray-a9);\n    }\n    .fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])):not(:disabled):not(.fui-high-contrast):hover {\n      background-color: var(--accent-10);\n    }\n    .fui-CheckboxButton:where(:is([data-state='checked'], [data-state='indeterminate'])):not(:disabled):not(.fui-high-contrast):active {\n      background-color: var(--accent-11);\n    }\n  }\n  .fui-CheckboxIndicator {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 100%;\n    width: 100%;\n    position: absolute;\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-1) {\n    --checkbox-size: var(--space-4);\n    gap: var(--space-2);\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-1) :where(.fui-CheckboxButton) {\n    border-radius: var(--radius-2);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-1) :where(.fui-CheckboxIndicatorIcon) {\n    width: var(--checkbox-size);\n    height: var(--checkbox-size);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-2) {\n    --checkbox-size: calc(var(--space-4) * 1.25);\n    gap: var(--space-2);\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n    letter-spacing: var(--letter-spacing-3);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-2) :where(.fui-CheckboxButton) {\n    border-radius: var(--radius-3);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-2) :where(.fui-CheckboxIndicatorIcon) {\n    width: var(--checkbox-size);\n    height: var(--checkbox-size);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-3) {\n    --checkbox-size: var(--space-5);\n    gap: var(--space-3);\n    font-size: var(--font-size-4);\n    line-height: var(--line-height-4);\n    letter-spacing: var(--letter-spacing-4);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-3) :where(.fui-CheckboxButton) {\n    border-radius: var(--radius-3);\n  }\n  .fui-CheckboxRoot:where(.fui-r-size-3) :where(.fui-CheckboxIndicatorIcon) {\n    width: var(--checkbox-size);\n    height: var(--checkbox-size);\n  }\n  .fui-Code {\n    --code-variant-font-size-adjust: calc(var(--code-font-size-adjust) * 0.95);\n    box-sizing: border-box;\n    font-family: var(--code-font-family);\n    font-size: calc(var(--code-variant-font-size-adjust) * 1em);\n    font-style: var(--code-font-style);\n    font-weight: var(--code-font-weight);\n    line-height: 1.25;\n    letter-spacing: calc(var(--code-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));\n    border-radius: calc((0.5px + 0.2em) * var(--radius-factor));\n  }\n  .fui-Code:where(.fui-r-size-1) {\n    font-size: calc(var(--font-size-1) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-1);\n    --letter-spacing: var(--letter-spacing-1);\n  }\n  .fui-Code:where(.fui-r-size-2) {\n    font-size: calc(var(--font-size-2) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-2);\n    --letter-spacing: var(--letter-spacing-2);\n  }\n  .fui-Code:where(.fui-r-size-3) {\n    font-size: calc(var(--font-size-3) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-3);\n    --letter-spacing: var(--letter-spacing-3);\n  }\n  .fui-Code:where(.fui-r-size-4) {\n    font-size: calc(var(--font-size-4) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-4);\n    --letter-spacing: var(--letter-spacing-4);\n  }\n  .fui-Code:where(.fui-r-size-5) {\n    font-size: calc(var(--font-size-5) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-5);\n    --letter-spacing: var(--letter-spacing-5);\n  }\n  .fui-Code:where(.fui-r-size-6) {\n    font-size: calc(var(--font-size-6) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-6);\n    --letter-spacing: var(--letter-spacing-6);\n  }\n  .fui-Code:where(.fui-r-size-7) {\n    font-size: calc(var(--font-size-7) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-7);\n    --letter-spacing: var(--letter-spacing-7);\n  }\n  .fui-Code:where(.fui-r-size-8) {\n    font-size: calc(var(--font-size-8) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-8);\n    --letter-spacing: var(--letter-spacing-8);\n  }\n  .fui-Code:where(.fui-r-size-9) {\n    font-size: calc(var(--font-size-9) * var(--code-variant-font-size-adjust));\n    line-height: var(--line-height-9);\n    --letter-spacing: var(--letter-spacing-9);\n  }\n  .fui-Code:where(.fui-variant-ghost) {\n    --code-variant-font-size-adjust: var(--code-font-size-adjust);\n    color: var(--accent-a11);\n  }\n  .fui-Code:where(.fui-variant-ghost):where(.fui-high-contrast) {\n    color: var(--accent-12);\n  }\n  :where(.fui-Link) .fui-Code:where(.fui-variant-ghost) {\n    text-decoration-line: underline;\n    text-decoration-color: inherit;\n    text-decoration-thickness: inherit;\n  }\n  :where(.fui-Link:has(.fui-Code:where(.fui-variant-ghost):only-child)):where(:focus-visible) .fui-Code {\n    text-decoration: none;\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  .fui-Code:where(.fui-variant-solid) {\n    padding: var(--code-padding-top) 0.25em var(--code-padding-bottom);\n    background-color: var(--accent-a9);\n    color: var(--accent-9-contrast);\n  }\n  .fui-Code:where(.fui-variant-solid):where(.fui-high-contrast) {\n    background-color: var(--accent-12);\n    color: var(--accent-1);\n  }\n  .fui-Code:where(.fui-variant-solid)::selection {\n    background-color: var(--accent-7);\n    color: var(--accent-12);\n  }\n  @media (hover: hover) {\n    :where(.fui-Link:has(.fui-Code:where(.fui-variant-solid):only-child)) .fui-Code:where(:hover) {\n      background-color: var(--accent-10);\n    }\n    :where(.fui-Link:has(.fui-Code:where(.fui-variant-solid):only-child)) .fui-Code:where(.fui-high-contrast:hover) {\n      background-color: var(--accent-12);\n      filter: var(--base-button-solid-high-contrast-hover-filter);\n    }\n  }\n  :where(.fui-Link:has(.fui-Code:where(.fui-variant-solid):only-child)):where(:focus-visible) .fui-Code {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  .fui-Code:where(.fui-variant-soft) {\n    padding: var(--code-padding-top) 0.25em var(--code-padding-bottom);\n    background-color: var(--accent-a3);\n    color: var(--accent-a11);\n  }\n  .fui-Code:where(.fui-variant-soft):where(.fui-high-contrast) {\n    background-color: var(--accent-a4);\n    color: var(--accent-12);\n  }\n  @media (hover: hover) {\n    :where(.fui-Link:has(.fui-Code:where(.fui-variant-soft):only-child)) .fui-Code:where(:hover) {\n      background-color: var(--accent-a4);\n    }\n  }\n  :where(.fui-Link:has(.fui-Code:where(.fui-variant-soft):only-child)):where(:focus-visible) .fui-Code {\n    outline: 2px solid var(--accent-8);\n    outline-offset: -1px;\n  }\n  .fui-Code:where(.fui-variant-outline) {\n    padding: var(--code-padding-top) 0.25em var(--code-padding-bottom);\n    box-shadow: inset 0 0 0 max(1px, 0.033em) var(--accent-a8);\n    color: var(--accent-a11);\n  }\n  .fui-Code:where(.fui-variant-outline):where(.fui-high-contrast) {\n    box-shadow: inset 0 0 0 max(1px, 0.033em) var(--accent-a7), inset 0 0 0 max(1px, 0.033em) var(--gray-a11);\n    color: var(--accent-12);\n  }\n  @media (hover: hover) {\n    :where(.fui-Link:has(.fui-Code:where(.fui-variant-outline):only-child)) .fui-Code:where(:hover) {\n      background-color: var(--accent-a2);\n    }\n  }\n  :where(.fui-Link:has(.fui-Code:where(.fui-variant-outline):only-child)):where(:focus-visible) .fui-Code {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-BaseMenuContent {\n    --scrollarea-scrollbar-vertical-margin-top: var(--base-menu-content-padding);\n    --scrollarea-scrollbar-vertical-margin-bottom: var(--base-menu-content-padding);\n    --scrollarea-scrollbar-horizontal-margin-left: var(--base-menu-content-padding);\n    --scrollarea-scrollbar-horizontal-margin-right: var(--base-menu-content-padding);\n    display: flex;\n    flex-direction: column;\n    box-sizing: border-box;\n    overflow: hidden;\n    background-color: var(--base-menu-bg);\n    --base-menu-content-padding: var(--space-1);\n    --base-menu-bg: var(--color-panel-translucent);\n    -webkit-backdrop-filter: var(--backdrop-filter-panel);\n    backdrop-filter: var(--backdrop-filter-panel);\n    box-shadow: var(--shadow-5);\n    outline: 0.5px solid var(--color-base-menu-outline) !important;\n  }\n  .fui-BaseMenuViewport {\n    flex: 1 1 0%;\n    display: flex;\n    flex-direction: column;\n    overflow-y: auto;\n    overflow-x: hidden;\n    padding: var(--base-menu-content-padding);\n  }\n  :where(.fui-BaseMenuContent:has(.fui-ScrollAreaScrollbar[data-orientation='vertical'])) .fui-BaseMenuViewport {\n    padding-right: var(--space-3);\n  }\n  .fui-BaseMenuItem {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    height: var(--base-menu-item-height);\n    padding-left: var(--base-menu-item-padding-left);\n    padding-right: var(--base-menu-item-padding-right);\n    position: relative;\n    box-sizing: border-box;\n    outline: none;\n    cursor: var(--cursor-menu-item);\n    scroll-margin: var(--base-menu-content-padding) 0;\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-BaseMenuShortcut {\n    display: flex;\n    align-items: center;\n    margin-left: var(--space-5);\n    color: var(--gray-a10);\n  }\n  .fui-BaseMenuSubTriggerIcon {\n    color: inherit;\n    margin-right: -2px;\n  }\n  .fui-BaseMenuItemIndicator {\n    position: absolute;\n    left: 0;\n    width: var(--base-menu-item-padding-left);\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n  }\n  .fui-BaseMenuSeparator {\n    height: 1px;\n    margin-top: 4px;\n    margin-bottom: 4px;\n    margin-left: calc(-1 * var(--base-menu-content-padding) + 1px);\n    margin-right: calc(-1 * var(--base-menu-content-padding) + 1px);\n    background-color: var(--gray-a6);\n  }\n  .fui-BaseMenuLabel {\n    display: flex;\n    align-items: center;\n    height: var(--base-menu-item-height);\n    padding-left: var(--base-menu-item-padding-left);\n    padding-right: var(--base-menu-item-padding-right);\n    color: var(--gray-a10);\n    -webkit-user-select: none;\n    user-select: none;\n    cursor: default;\n    font-weight: var(--font-weight-semi-bold);\n  }\n  :where(.fui-BaseMenuItem) + .fui-BaseMenuLabel {\n    margin-top: var(--space-2);\n  }\n  .fui-BaseMenuArrow {\n    fill: var(--base-menu-bg);\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-1) {\n    --base-menu-item-padding-left: 8px;\n    --base-menu-item-padding-right: 8px;\n    --base-menu-item-height: var(--space-5);\n    border-radius: 8px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-1) :where(.fui-BaseMenuItem) {\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n    border-radius: 4px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-1) :where(.fui-BaseMenuLabel) {\n    font-size: var(--font-size-0);\n    line-height: var(--line-height-0);\n    letter-spacing: var(--letter-spacing-0);\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-1) :where(.fui-BaseMenuItemIndicatorIcon, .fui-BaseMenuSubTriggerIcon) {\n    width: 8px;\n    height: 8px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-1):where(:not(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem))) {\n    --base-menu-item-padding-left: 8px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-1):where(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem)) {\n    --base-menu-item-padding-left: 24px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-2) {\n    --base-menu-item-padding-left: 10px;\n    --base-menu-item-padding-right: 10px;\n    --base-menu-item-height: var(--space-6);\n    border-radius: 10px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-2) :where(.fui-BaseMenuItem) {\n    border-radius: var(--radius-3);\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    border-radius: 6px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-2) :where(.fui-BaseMenuLabel) {\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-2) :where(.fui-BaseMenuItemIndicatorIcon, .fui-BaseMenuSubTriggerIcon) {\n    width: 10px;\n    height: 10px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-2):where(:not(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem))) {\n    --base-menu-item-padding-left: 10px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-2):where(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem)) {\n    --base-menu-item-padding-left: 26px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-3) {\n    --base-menu-item-padding-left: 12px;\n    --base-menu-item-padding-right: 12px;\n    --base-menu-item-height: var(--space-7);\n    border-radius: 12px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-3) :where(.fui-BaseMenuItem) {\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n    letter-spacing: var(--letter-spacing-3);\n    border-radius: 8px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-3) :where(.fui-BaseMenuLabel) {\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-3) :where(.fui-BaseMenuItemIndicatorIcon, .fui-BaseMenuSubTriggerIcon) {\n    width: 12px;\n    height: 12px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-3):where(:not(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem))) {\n    --base-menu-item-padding-left: 12px;\n  }\n  .fui-BaseMenuContent:where(.fui-r-size-3):where(:has(.fui-BaseMenuCheckboxItem, .fui-BaseMenuRadioItem)) {\n    --base-menu-item-padding-left: 28px;\n  }\n  .fui-BaseMenuContent:where(.fui-variant-translucent) {\n    background-color: var(--color-panel-translucent);\n    -webkit-backdrop-filter: var(--backdrop-filter-panel);\n    backdrop-filter: var(--backdrop-filter-panel);\n    outline: 0.5px solid var(--color-popover-outline);\n  }\n  .fui-BaseMenuContent:where(.fui-variant-solid) {\n    background-color: var(--color-panel-solid);\n  }\n  .fui-BaseMenuContent :where(.fui-BaseMenuSubTrigger[data-state='open']) {\n    background-color: var(--gray-a3);\n  }\n  .fui-BaseMenuContent :where(.fui-BaseMenuItem[data-highlighted]) {\n    background-color: var(--gray-a4);\n  }\n  .fui-BaseMenuContent :where(.fui-BaseMenuItem[data-highlighted]):where([data-accent-color]) {\n    background-color: var(--accent-a5);\n    color: var(--accent-12);\n  }\n  .fui-BaseMenuItem:where([data-accent-color]) {\n    color: var(--accent-a11);\n  }\n  .fui-BaseMenuItem:where([data-disabled]) {\n    color: var(--gray-a8);\n    pointer-events: none;\n  }\n  .fui-BaseMenuItem:where([data-disabled], [data-highlighted]) :where(.fui-BaseMenuShortcut), .fui-BaseMenuSubTrigger:where([data-state='open']) :where(.fui-BaseMenuShortcut) {\n    color: inherit;\n  }\n  .fui-ContextMenuContent {\n    max-height: var(--radix-context-menu-content-available-height);\n    transform-origin: var(--radix-context-menu-content-transform-origin);\n  }\n  .fui-r-ai-start {\n    align-items: flex-start;\n  }\n  .fui-r-ai-center {\n    align-items: center;\n  }\n  .fui-r-ai-end {\n    align-items: flex-end;\n  }\n  .fui-r-ai-baseline {\n    align-items: baseline;\n  }\n  .fui-r-ai-stretch {\n    align-items: stretch;\n  }\n  .fui-DataListRoot {\n    font-family: var(--default-font-family);\n    font-weight: var(--font-weight-normal);\n    font-style: normal;\n    text-align: start;\n    --data-list-leading-trim-start: calc(var(--default-leading-trim-start) - var(--line-height) / 2);\n    --data-list-leading-trim-end: calc(var(--default-leading-trim-end) - var(--line-height) / 2);\n  }\n  .fui-DataListLabel {\n    display: flex;\n    color: var(--gray-a11);\n  }\n  .fui-DataListLabel:where(.fui-high-contrast) {\n    color: var(--gray-12);\n  }\n  .fui-DataListLabel:where([data-accent-color]) {\n    color: var(--accent-a11);\n  }\n  .fui-DataListLabel:where([data-accent-color]):where(.fui-high-contrast) {\n    color: var(--accent-12);\n  }\n  .fui-DataListValue {\n    display: flex;\n    margin: 0;\n    min-width: 0px;\n    margin-top: var(--data-list-value-margin-top);\n    margin-bottom: var(--data-list-value-margin-bottom);\n  }\n  .fui-DataListItem {\n    --data-list-value-margin-top: 0px;\n    --data-list-value-margin-bottom: 0px;\n    --data-list-first-item-value-margin-top: 0px;\n    --data-list-last-item-value-margin-bottom: 0px;\n    --data-list-value-trim-start: -0.25em;\n    --data-list-value-trim-end: -0.25em;\n    --data-list-first-item-value-trim-start: 0px;\n    --data-list-last-item-value-trim-end: 0px;\n  }\n  :where(.fui-DataListItem:first-child) .fui-DataListValue {\n    margin-top: var(--data-list-first-item-value-margin-top);\n  }\n  :where(.fui-DataListItem:last-child) .fui-DataListValue {\n    margin-bottom: var(--data-list-last-item-value-margin-bottom);\n  }\n  .fui-DataListRoot:where(.fui-r-size-1) {\n    gap: var(--space-3);\n  }\n  .fui-DataListRoot:where(.fui-r-size-2) {\n    gap: var(--space-4);\n  }\n  .fui-DataListRoot:where(.fui-r-size-3) {\n    gap: calc(var(--space-4) * 1.25);\n  }\n  .fui-DataListRoot:where(.fui-r-orientation-vertical) {\n    display: flex;\n    flex-direction: column;\n  }\n  .fui-DataListRoot:where(.fui-r-orientation-vertical) :where(.fui-DataListItem) {\n    --data-list-value-margin-top: 0px;\n    --data-list-value-margin-bottom: 0px;\n    --data-list-first-item-value-margin-top: 0px;\n    --data-list-last-item-value-margin-bottom: 0px;\n    display: flex;\n    flex-direction: column;\n    gap: var(--space-1);\n  }\n  .fui-DataListRoot:where(.fui-r-orientation-vertical) :where(.fui-DataListLabel) {\n    min-width: 0px;\n  }\n  .fui-DataListRoot:where(.fui-r-orientation-horizontal) {\n    display: grid;\n    grid-template-columns: auto 1fr;\n  }\n  .fui-DataListRoot:where(.fui-r-orientation-horizontal) :where(.fui-DataListItem) {\n    --data-list-value-margin-top: var(--data-list-value-trim-start);\n    --data-list-value-margin-bottom: var(--data-list-value-trim-end);\n    --data-list-first-item-value-margin-top: var(--data-list-first-item-value-trim-start);\n    --data-list-last-item-value-margin-bottom: var(--data-list-last-item-value-trim-end);\n    display: grid;\n    grid-template-columns: inherit;\n    grid-template-columns: subgrid;\n    gap: inherit;\n    grid-column: span 2;\n    align-items: baseline;\n  }\n  .fui-DataListRoot:where(.fui-r-orientation-horizontal) :where(.fui-DataListLabel) {\n    min-width: 120px;\n  }\n  .fui-DataListLabel::before, .fui-DataListValue::before {\n    content: '‍';\n  }\n  .fui-DataListItem:where(.fui-r-ai-baseline) {\n    --data-list-value-trim-start: -0.25em;\n    --data-list-value-trim-end: -0.25em;\n    --data-list-first-item-value-trim-start: 0px;\n    --data-list-last-item-value-trim-end: 0px;\n  }\n  .fui-DataListItem:where(.fui-r-ai-start) {\n    --data-list-value-trim-start: 0px;\n    --data-list-value-trim-end: -0.25em;\n    --data-list-first-item-value-trim-start: 0px;\n    --data-list-last-item-value-trim-end: 0px;\n  }\n  .fui-DataListItem:where(.fui-r-ai-center) {\n    --data-list-value-trim-start: -0.25em;\n    --data-list-value-trim-end: -0.25em;\n    --data-list-first-item-value-trim-start: -0.25em;\n    --data-list-last-item-value-trim-end: -0.25em;\n  }\n  .fui-DataListItem:where(.fui-r-ai-end) {\n    --data-list-value-trim-start: -0.25em;\n    --data-list-value-trim-end: 0px;\n    --data-list-first-item-value-trim-start: 0px;\n    --data-list-last-item-value-trim-end: 0px;\n  }\n  .fui-DataListItem:where(.fui-r-ai-stretch) {\n    --data-list-value-trim-start: 0px;\n    --data-list-value-trim-end: 0px;\n    --data-list-first-item-value-trim-start: 0px;\n    --data-list-last-item-value-trim-end: 0px;\n  }\n  .fui-DataListItem:where(:first-child) {\n    margin-top: var(--leading-trim-start);\n  }\n  .fui-DataListItem:where(:last-child) {\n    margin-bottom: var(--leading-trim-end);\n  }\n  .fui-DataListRoot:where(.fui-r-trim-normal) {\n    --leading-trim-start: initial;\n    --leading-trim-end: initial;\n  }\n  .fui-DataListRoot:where(.fui-r-trim-start) {\n    --leading-trim-start: var(--data-list-leading-trim-start);\n    --leading-trim-end: initial;\n  }\n  .fui-DataListRoot:where(.fui-r-trim-end) {\n    --leading-trim-start: initial;\n    --leading-trim-end: var(--data-list-leading-trim-end);\n  }\n  .fui-DataListRoot:where(.fui-r-trim-both) {\n    --leading-trim-start: var(--data-list-leading-trim-start);\n    --leading-trim-end: var(--data-list-leading-trim-end);\n  }\n  .fui-DropdownMenuContent {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .fui-Em {\n    box-sizing: border-box;\n    font-family: var(--em-font-family);\n    font-size: calc(var(--em-font-size-adjust) * 1em);\n    font-style: var(--em-font-style);\n    font-weight: var(--em-font-weight);\n    line-height: 1.25;\n    letter-spacing: calc(var(--em-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));\n    color: inherit;\n  }\n  .fui-BaseChip {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n    flex-shrink: 0;\n    -webkit-user-select: none;\n    user-select: none;\n    vertical-align: top;\n    color: var(--base-chip-color);\n    height: var(--base-chip-height);\n    border-radius: var(--radius-thumb);\n  }\n  .fui-BaseChip:where(.fui-r-size-1) {\n    --base-chip-height: var(--space-5);\n    padding-left: var(--space-2);\n    padding-right: var(--space-2);\n    gap: var(--space-1);\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n    font-weight: var(--font-weight-medium);\n  }\n  .fui-BaseChip:where(.fui-r-size-2) {\n    --base-chip-height: var(--space-6);\n    padding-left: var(--space-3);\n    padding-right: var(--space-3);\n    gap: calc(1.5 * var(--space-1));\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    font-weight: var(--font-weight-medium);\n  }\n  .fui-BaseChip:where(.fui-r-size-3) {\n    --base-chip-height: var(--space-7);\n    padding-left: var(--space-4);\n    padding-right: var(--space-4);\n    gap: var(--space-2);\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n    letter-spacing: var(--letter-spacing-3);\n    font-weight: var(--font-weight-medium);\n  }\n  .fui-BaseChip:where([data-state='unchecked']) {\n    box-shadow: inset 0 0 0 1px var(--gray-a5);\n    --base-chip-color: var(--gray-a12);\n  }\n  .fui-BaseChip:where([data-state='unchecked']):where(:hover) {\n    background-color: var(--gray-a2);\n  }\n  .fui-BaseChip:where([data-state='unchecked']):where(:active) {\n    background-color: var(--gray-a3);\n  }\n  .fui-BaseChip:where([data-state='unchecked']):where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-BaseChip:where([data-state='unchecked']):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    --base-chip-color: var(--gray-a8);\n    box-shadow: inset 0 0 0 1px var(--gray-a4);\n    background-color: transparent;\n  }\n  .fui-BaseChip:where([data-state='unchecked']):where(:not([data-disabled])) > svg {\n    color: var(--gray-a11);\n  }\n  .fui-BaseChip:where([data-state='checked']) {\n    --base-chip-color: var(--accent-11);\n    background-color: var(--accent-a3);\n    box-shadow: inset 0 0 0 1px var(--accent-a6);\n  }\n  .fui-BaseChip:where([data-state='checked']):where(:focus-visible) {\n    outline: 2px solid var(--accent-8);\n    outline-offset: -1px;\n  }\n  @media (hover: hover) {\n    .fui-BaseChip:where([data-state='checked']):where(:hover) {\n      background-color: var(--accent-a4);\n    }\n  }\n  .fui-BaseChip:where([data-state='checked']):where(:active) {\n    background-color: var(--accent-a5);\n  }\n  .fui-BaseChip:where([data-state='checked']):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    --base-chip-color: var(--gray-8);\n    background-color: var(--gray-a3);\n    box-shadow: inset 0 0 0 1px var(--gray-a5);\n  }\n  .fui-BaseChip:where([data-state='checked']):where(:not([data-disabled])) > svg {\n    color: var(--accent-a11);\n  }\n  .fui-HoverCardContent {\n    background-color: var(--color-panel-solid);\n    box-shadow: var(--shadow-4);\n    overflow: auto;\n    --inset-padding: var(--hover-card-content-padding);\n    padding: var(--hover-card-content-padding);\n    transform-origin: var(--radix-hover-card-content-transform-origin);\n  }\n  .fui-HoverCardContent:where(.fui-variant-translucent) {\n    background-color: var(--color-panel-translucent);\n    -webkit-backdrop-filter: var(--backdrop-filter-panel);\n    backdrop-filter: var(--backdrop-filter-panel);\n    outline: 0.5px solid var(--color-popover-outline);\n  }\n  .fui-HoverCardContent:where(.fui-variant-solid) {\n    background-color: var(--color-panel-solid);\n  }\n  .fui-HoverCardContent:where(.fui-r-size-1) {\n    --hover-card-content-padding: var(--space-3);\n    border-radius: 8px;\n  }\n  .fui-HoverCardContent:where(.fui-r-size-2) {\n    --hover-card-content-padding: var(--space-4);\n    border-radius: 12px;\n  }\n  .fui-HoverCardContent:where(.fui-r-size-3) {\n    --hover-card-content-padding: var(--space-5);\n    border-radius: 16px;\n  }\n  .fui-IconButton {\n    height: var(--base-button-height);\n    width: var(--base-button-height);\n  }\n  .fui-r-p-0 {\n    padding: 0;\n  }\n  .fui-r-p-current {\n    padding: var(--inset-padding);\n  }\n  .fui-r-px-0 {\n    padding-left: 0;\n    padding-right: 0;\n  }\n  .fui-r-px-current {\n    padding-left: var(--inset-padding);\n    padding-right: var(--inset-padding);\n  }\n  .fui-r-py-0 {\n    padding-top: 0;\n    padding-bottom: 0;\n  }\n  .fui-r-py-current {\n    padding-top: var(--inset-padding);\n    padding-bottom: var(--inset-padding);\n  }\n  .fui-r-pt-0 {\n    padding-top: 0;\n  }\n  .fui-r-pt-current {\n    padding-top: var(--inset-padding);\n  }\n  .fui-r-pr-0 {\n    padding-right: 0;\n  }\n  .fui-r-pr-current {\n    padding-right: var(--inset-padding);\n  }\n  .fui-r-pb-0 {\n    padding-bottom: 0;\n  }\n  .fui-r-pb-current {\n    padding-bottom: var(--inset-padding);\n  }\n  .fui-r-pl-0 {\n    padding-left: 0;\n  }\n  .fui-r-pl-current {\n    padding-left: var(--inset-padding);\n  }\n  .fui-Inset {\n    --margin-top: 0px;\n    --margin-right: 0px;\n    --margin-bottom: 0px;\n    --margin-left: 0px;\n    overflow: hidden;\n  }\n  :where(.fui-Inset) > * {\n    --margin-top-override: initial;\n    --margin-right-override: initial;\n    --margin-bottom-override: initial;\n    --margin-left-override: initial;\n  }\n  .fui-Inset:where(.fui-r-clip-border-box) {\n    --inset-border-radius-calc: calc(var(--inset-border-radius, 0px) - var(--inset-border-width, 0px));\n    --inset-padding-calc: var(--inset-padding, 0px);\n  }\n  .fui-Inset:where(.fui-r-clip-padding-box) {\n    --inset-border-radius-calc: var(--inset-border-radius, 0px);\n    --inset-padding-calc: calc(var(--inset-padding, 0px) + var(--inset-border-width, 0px));\n  }\n  .fui-Inset:where(.fui-r-side-top) {\n    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));\n    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));\n    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));\n    margin-top: var(--margin-top-override);\n    margin-left: var(--margin-left-override);\n    margin-right: var(--margin-right-override);\n    border-top-left-radius: var(--inset-border-radius-calc);\n    border-top-right-radius: var(--inset-border-radius-calc);\n  }\n  .fui-Inset:where(.fui-r-side-bottom) {\n    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));\n    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));\n    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));\n    margin-left: var(--margin-left-override);\n    margin-right: var(--margin-right-override);\n    margin-bottom: var(--margin-bottom-override);\n    border-bottom-left-radius: var(--inset-border-radius-calc);\n    border-bottom-right-radius: var(--inset-border-radius-calc);\n  }\n  .fui-Inset:where(.fui-r-side-left) {\n    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));\n    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));\n    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));\n    margin-top: var(--margin-top-override);\n    margin-bottom: var(--margin-bottom-override);\n    margin-left: var(--margin-left-override);\n    border-top-left-radius: var(--inset-border-radius-calc);\n    border-bottom-left-radius: var(--inset-border-radius-calc);\n  }\n  .fui-Inset:where(.fui-r-side-right) {\n    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));\n    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));\n    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));\n    margin-top: var(--margin-top-override);\n    margin-bottom: var(--margin-bottom-override);\n    margin-right: var(--margin-right-override);\n    border-top-right-radius: var(--inset-border-radius-calc);\n    border-bottom-right-radius: var(--inset-border-radius-calc);\n  }\n  .fui-Inset:where(.fui-r-side-x) {\n    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));\n    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));\n    margin-left: var(--margin-left-override);\n    margin-right: var(--margin-right-override);\n  }\n  .fui-Inset:where(.fui-r-side-y) {\n    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));\n    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));\n    margin-top: var(--margin-top-override);\n    margin-bottom: var(--margin-bottom-override);\n  }\n  .fui-Inset:where(.fui-r-side-all) {\n    --margin-top-override: calc(var(--margin-top) - var(--inset-padding-calc));\n    --margin-right-override: calc(var(--margin-right) - var(--inset-padding-calc));\n    --margin-bottom-override: calc(var(--margin-bottom) - var(--inset-padding-calc));\n    --margin-left-override: calc(var(--margin-left) - var(--inset-padding-calc));\n    margin: var(--margin-top-override) var(--margin-right-override) var(--margin-bottom-override) var(--margin-left-override);\n    border-radius: var(--inset-border-radius-calc);\n  }\n  .fui-Kbd {\n    box-sizing: border-box;\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    flex-shrink: 0;\n    font-family: var(--default-font-family);\n    font-weight: var(--font-weight-medium);\n    vertical-align: text-top;\n    white-space: nowrap;\n    -webkit-user-select: none;\n    user-select: none;\n    cursor: default;\n    position: relative;\n    top: -0.03em;\n    font-size: 0.75em;\n    min-width: 1.75em;\n    line-height: 1.7em;\n    padding-left: 0.5em;\n    padding-right: 0.5em;\n    padding-bottom: 0.05em;\n    word-spacing: -0.1em;\n    border-radius: calc(var(--radius-factor) * 0.35em);\n    letter-spacing: var(--letter-spacing, var(--default-letter-spacing));\n    color: var(--gray-12);\n    background-color: var(--gray-1);\n    box-shadow: var(--kbd-box-shadow);\n  }\n  .fui-Kbd:where(.fui-r-size-1) {\n    font-size: calc(var(--font-size-1) * 0.8);\n    --letter-spacing: var(--letter-spacing-1);\n  }\n  .fui-Kbd:where(.fui-r-size-2) {\n    font-size: calc(var(--font-size-2) * 0.8);\n    --letter-spacing: var(--letter-spacing-2);\n  }\n  .fui-Kbd:where(.fui-r-size-3) {\n    font-size: calc(var(--font-size-3) * 0.8);\n    --letter-spacing: var(--letter-spacing-3);\n  }\n  .fui-Kbd:where(.fui-r-size-4) {\n    font-size: calc(var(--font-size-4) * 0.8);\n    --letter-spacing: var(--letter-spacing-4);\n  }\n  .fui-Kbd:where(.fui-r-size-5) {\n    font-size: calc(var(--font-size-5) * 0.8);\n    --letter-spacing: var(--letter-spacing-5);\n  }\n  .fui-Kbd:where(.fui-r-size-6) {\n    font-size: calc(var(--font-size-6) * 0.8);\n    --letter-spacing: var(--letter-spacing-6);\n  }\n  .fui-Kbd:where(.fui-r-size-7) {\n    font-size: calc(var(--font-size-7) * 0.8);\n    --letter-spacing: var(--letter-spacing-7);\n  }\n  .fui-Kbd:where(.fui-r-size-8) {\n    font-size: calc(var(--font-size-8) * 0.8);\n    --letter-spacing: var(--letter-spacing-8);\n  }\n  .fui-Kbd:where(.fui-r-size-9) {\n    font-size: calc(var(--font-size-9) * 0.8);\n    --letter-spacing: var(--letter-spacing-9);\n  }\n  .fui-Link {\n    cursor: var(--cursor-link);\n    color: var(--accent-a11);\n    border-radius: calc(0.07em * var(--radius-factor));\n    text-decoration-line: none;\n    text-decoration-style: solid;\n    text-decoration-thickness: min(2px, max(1px, 0.05em));\n    text-underline-offset: calc(0.025em + 2px);\n    text-decoration-color: var(--accent-a7);\n  }\n  .fui-Link:where(.fui-high-contrast), :where(.fui-CalloutRoot:not(.fui-high-contrast)) .fui-Link, :where(.fui-Text, .fui-Heading):where([data-accent-color]:not(.fui-high-contrast)) .fui-Link {\n    color: var(--accent-12);\n  }\n  @supports (color: color-mix(in oklab, white, black)) {\n    .fui-Link {\n      text-decoration-color: var(--accent-a7);\n      @supports (color: color-mix(in lab, red, red)) {\n        text-decoration-color: color-mix(in oklab, var(--accent-a7), var(--gray-a7));\n      }\n    }\n  }\n  @media (hover: hover) {\n    .fui-Link:where(.fui-underline-auto):where(:hover) {\n      text-decoration-line: underline;\n    }\n  }\n  .fui-Link:where(.fui-underline-auto):where(.fui-high-contrast), :where(.fui-CalloutRoot:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto), :where(.fui-Text, .fui-Heading):where([data-accent-color]:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto) {\n    text-decoration-line: underline;\n    text-decoration-color: var(--accent-a8);\n  }\n  @supports (color: color-mix(in oklab, white, black)) {\n    .fui-Link:where(.fui-underline-auto):where(.fui-high-contrast), :where(.fui-CalloutRoot:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto), :where(.fui-Text, .fui-Heading):where([data-accent-color]:not(.fui-high-contrast)) .fui-Link:where(.fui-underline-auto) {\n      text-decoration-color: var(--accent-a8);\n      @supports (color: color-mix(in lab, red, red)) {\n        text-decoration-color: color-mix(in oklab, var(--accent-a8), var(--gray-a6));\n      }\n    }\n  }\n  @media (hover: hover) {\n    .fui-Link:where(.fui-underline-hover):where(:hover) {\n      text-decoration-line: underline;\n    }\n  }\n  .fui-Link:where(.fui-underline-always) {\n    text-decoration-line: underline;\n  }\n  .fui-Link:where(:focus-visible) {\n    text-decoration-line: none;\n    outline-color: var(--color-focus-root);\n    outline-width: 2px;\n    outline-style: solid;\n    outline-offset: 2px;\n  }\n  .fui-Link:where(:has(.fui-Code:not(.fui-variant-ghost):only-child)) {\n    text-decoration-line: none;\n  }\n  .fui-Link:where(:has(.fui-Code:only-child)) {\n    outline: none;\n  }\n  .fui-PopoverContent {\n    box-shadow: var(--shadow-5);\n    min-width: var(--radix-popover-trigger-width);\n    outline: 0;\n    overflow: auto;\n    --inset-padding: var(--popover-content-padding);\n    padding: var(--popover-content-padding);\n    transform-origin: var(--radix-popover-content-transform-origin);\n  }\n  .fui-PopoverContent:where(.fui-variant-translucent) {\n    background-color: var(--color-panel-translucent);\n    -webkit-backdrop-filter: var(--backdrop-filter-panel);\n    backdrop-filter: var(--backdrop-filter-panel);\n    outline: 0.5px solid var(--color-popover-outline);\n  }\n  .fui-PopoverContent:where(.fui-variant-solid) {\n    background-color: var(--color-panel-solid);\n  }\n  .fui-PopoverContent:where(.fui-r-size-1) {\n    --popover-content-padding: var(--space-3);\n    border-radius: 8px;\n  }\n  .fui-PopoverContent:where(.fui-r-size-2) {\n    --popover-content-padding: var(--space-4);\n    border-radius: 12px;\n  }\n  .fui-PopoverContent:where(.fui-r-size-3) {\n    --popover-content-padding: var(--space-5);\n    border-radius: 16px;\n  }\n  .fui-PopoverContent:where(.fui-r-size-4) {\n    --popover-content-padding: var(--space-6);\n    border-radius: 20px;\n  }\n  .fui-ProgressRoot {\n    position: relative;\n    display: block;\n    width: 100%;\n    height: var(--progress-height);\n    border-radius: var(--radius-thumb);\n    overflow: hidden;\n    background-color: var(--gray-a4);\n  }\n  .fui-ProgressIndicator {\n    height: 100%;\n    width: 0%;\n    background-color: var(--accent-9);\n    border-radius: inherit;\n  }\n  .fui-ProgressRoot:where(.fui-r-size-1) {\n    --progress-height: 2px;\n  }\n  .fui-ProgressRoot:where(.fui-r-size-2) {\n    --progress-height: 4px;\n  }\n  .fui-ProgressRoot:where(.fui-r-size-3) {\n    --progress-height: 6px;\n  }\n  .fui-ProgressRoot:where(.fui-r-size-4) {\n    --progress-height: 8px;\n  }\n  .fui-ProgressRoot:where(.fui-r-size-5) {\n    --progress-height: 12px;\n  }\n  .fui-ProgressRoot:where(.fui-r-size-6) {\n    --progress-height: 16px;\n  }\n  .fui-ProgressRoot:where(.fui-high-contrast) :where(.fui-ProgressIndicator) {\n    background-color: var(--accent-12);\n  }\n  .fui-CircularProgressRoot {\n    position: relative;\n    display: block;\n    width: var(--circular-progress-size);\n    height: var(--circular-progress-size);\n    border-radius: 50%;\n    overflow: hidden;\n    --circular-progress-color: var(--accent-9);\n  }\n  .fui-CircularProgressRoot::before {\n    content: '';\n    inset: 0;\n    z-index: 1;\n    position: absolute;\n    border-radius: inherit;\n    border: var(--circular-progress-border-thickness) solid var(--accent-a3);\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-1) {\n    --circular-progress-size: 16px;\n    --circular-progress-border-thickness: 3px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-2) {\n    --circular-progress-size: 20px;\n    --circular-progress-border-thickness: 4px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-3) {\n    --circular-progress-size: 24px;\n    --circular-progress-border-thickness: 5px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-4) {\n    --circular-progress-size: 32px;\n    --circular-progress-border-thickness: 5px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-5) {\n    --circular-progress-size: 40px;\n    --circular-progress-border-thickness: 6px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-6) {\n    --circular-progress-size: 48px;\n    --circular-progress-border-thickness: 7px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-7) {\n    --circular-progress-size: 56px;\n    --circular-progress-border-thickness: 8px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-8) {\n    --circular-progress-size: 64px;\n    --circular-progress-border-thickness: 9px;\n  }\n  .fui-CircularProgressRoot:where(.fui-r-size-9) {\n    --circular-progress-size: 72px;\n    --circular-progress-border-thickness: 10px;\n  }\n  .fui-CircularProgressIndicator {\n    position: absolute;\n    inset: 0;\n    border: var(--circular-progress-border-thickness) solid var(--circular-progress-color);\n    border-radius: inherit;\n    --mask: conic-gradient(\n    black calc(1turn * var(--circular-progress-progress)),\n    /* 0.001turn to smoothen out the cut out part */ transparent\n      calc(1turn * var(--circular-progress-progress) + 0.001turn)\n  );\n    -webkit-mask: var(--mask);\n    mask: var(--mask);\n    -webkit-mask-image: var(--mask);\n    mask-image: var(--mask);\n  }\n  .fui-CircularProgressRoot:where(.fui-high-contrast) {\n    --circular-progress-color: var(--accent-12);\n  }\n  .fui-Quote {\n    box-sizing: border-box;\n    font-family: var(--quote-font-family);\n    font-size: calc(var(--quote-font-size-adjust) * 1em);\n    font-style: var(--quote-font-style);\n    font-weight: var(--quote-font-weight);\n    line-height: 1.25;\n    letter-spacing: calc(var(--quote-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));\n    color: inherit;\n  }\n  .fui-RadioGroupItem {\n    display: inline-flex;\n    align-items: center;\n    vertical-align: top;\n    flex-shrink: 0;\n    height: var(--line-height, var(--radio-group-item-size));\n    gap: var(--gap);\n    font-size: var(--font-size);\n    line-height: var(--line-height);\n    letter-spacing: var(--letter-spacing);\n  }\n  .fui-RadioGroupButton {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n    height: var(--radio-group-item-size);\n    width: var(--radio-group-item-size);\n    cursor: var(--cursor-radio);\n    border-radius: 100%;\n  }\n  .fui-RadioGroupButton:where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  .fui-RadioGroupIndicator {\n    background-color: currentColor;\n    height: 100%;\n    width: 100%;\n    border-radius: 100%;\n    transform: scale(0.4);\n  }\n  .fui-RadioGroupRoot:where(.fui-r-size-1) {\n    --gap: var(--space-2);\n    --font-size: var(--font-size-2);\n    --line-height: var(--line-height-2);\n    --letter-spacing: var(--letter-spacing-2);\n    --radio-group-item-size: var(--space-4);\n  }\n  .fui-RadioGroupRoot:where(.fui-r-size-2) {\n    --gap: var(--space-2);\n    --font-size: var(--font-size-3);\n    --line-height: var(--line-height-3);\n    --letter-spacing: var(--letter-spacing-3);\n    --radio-group-item-size: calc(var(--space-4) * 1.25);\n  }\n  .fui-RadioGroupRoot:where(.fui-r-size-3) {\n    --gap: var(--space-3);\n    --font-size: var(--font-size-4);\n    --line-height: var(--line-height-4);\n    --letter-spacing: var(--letter-spacing-4);\n    --radio-group-item-size: var(--space-5);\n  }\n  .fui-RadioGroupRoot :where(.fui-RadioGroupButton[data-state='unchecked']) {\n    background-color: var(--color-surface);\n    box-shadow: inset 0 0 0 1px var(--gray-a7);\n  }\n  .fui-RadioGroupRoot :where(.fui-RadioGroupButton[data-state='checked']) {\n    background-color: var(--accent-9);\n    color: var(--accent-9-contrast);\n  }\n  .fui-RadioGroupRoot:where(.fui-high-contrast) :where(.fui-RadioGroupButton[data-state='checked']) {\n    background-color: var(--accent-12);\n    color: var(--accent-1);\n  }\n  .fui-RadioGroupRoot :where(.fui-RadioGroupButton[data-disabled]) {\n    box-shadow: inset 0 0 0 1px var(--gray-a6);\n    background-color: var(--gray-a3);\n    cursor: var(--cursor-disabled);\n    color: var(--gray-a8);\n  }\n  .fui-RadioButtonGroupRoot {\n    --radio-items-base-color: var(--accent-9);\n  }\n  .fui-RadioButtonGroupRoot:where(.fui-high-contrast) {\n    --radio-items-base-color: var(--accent-12);\n  }\n  .fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton) {\n    position: relative;\n    cursor: default;\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton:focus-visible) {\n    outline: 2px solid var(--accent-a6);\n    outline-offset: 2px;\n  }\n  .fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupOverlay) {\n    position: absolute;\n    pointer-events: none;\n  }\n  .fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton[data-state='checked'] .fui-RadioButtonGroupOverlay) {\n    inset: calc(-1 * var(--parent-border-width));\n    border-radius: var(--parent-border-radius);\n    box-shadow: inset 0 0 0 2px var(--radio-items-base-color), inset 0 0 0 4px var(--color-background);\n  }\n  .fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupIcon) {\n    visibility: hidden;\n    width: 20px;\n    pointer-events: none;\n    height: 20px;\n    border-radius: 999px;\n    color: var(--accent-9-contrast);\n  }\n  .fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton[data-state='checked'] .fui-RadioButtonGroupIcon) {\n    visibility: visible;\n    background: var(--accent-9);\n  }\n  .fui-RadioButtonGroupRoot :where(.fui-RadioButtonGroupButton.fui-Card)::after {\n    outline: none;\n  }\n  .fui-ScrollAreaRoot {\n    display: flex;\n    flex-direction: column;\n    overflow: hidden;\n    width: 100%;\n    height: 100%;\n  }\n  .fui-ScrollAreaViewport {\n    width: 100%;\n    height: 100%;\n    overscroll-behavior-x: contain;\n  }\n  .fui-ScrollAreaViewport:where(:focus-visible) + :where(.fui-ScrollAreaViewportFocusRing) {\n    position: absolute;\n    inset: 0;\n    pointer-events: none;\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -2px;\n  }\n  .fui-ScrollAreaScrollbar {\n    display: flex;\n    -webkit-user-select: none;\n    user-select: none;\n    touch-action: none;\n    background-color: var(--gray-a3);\n    border-radius: var(--scrollarea-scrollbar-border-radius);\n    animation-duration: 150ms;\n    animation-timing-function: ease-out;\n  }\n  .fui-ScrollAreaScrollbar:where([data-orientation='vertical']) {\n    flex-direction: column;\n    width: var(--scrollarea-scrollbar-size);\n    margin-top: var(--scrollarea-scrollbar-vertical-margin-top);\n    margin-bottom: var(--scrollarea-scrollbar-vertical-margin-bottom);\n    margin-left: var(--scrollarea-scrollbar-vertical-margin-left);\n    margin-right: var(--scrollarea-scrollbar-vertical-margin-right);\n  }\n  .fui-ScrollAreaScrollbar:where([data-orientation='horizontal']) {\n    flex-direction: row;\n    height: var(--scrollarea-scrollbar-size);\n    margin-top: var(--scrollarea-scrollbar-horizontal-margin-top);\n    margin-bottom: var(--scrollarea-scrollbar-horizontal-margin-bottom);\n    margin-left: var(--scrollarea-scrollbar-horizontal-margin-left);\n    margin-right: var(--scrollarea-scrollbar-horizontal-margin-right);\n  }\n  .fui-ScrollAreaThumb {\n    position: relative;\n    background-color: var(--gray-a8);\n    border-radius: inherit;\n    transition: background-color 100ms;\n  }\n  .fui-ScrollAreaThumb::before {\n    content: '';\n    position: absolute;\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n    width: 100%;\n    height: 100%;\n    min-width: var(--space-4);\n    min-height: var(--space-4);\n  }\n  .fui-ScrollAreaScrollbar:where(.fui-r-size-1) {\n    --scrollarea-scrollbar-size: var(--space-1);\n    --scrollarea-scrollbar-border-radius: max(var(--radius-1), var(--radius-full));\n  }\n  .fui-ScrollAreaScrollbar:where(.fui-r-size-2) {\n    --scrollarea-scrollbar-size: var(--space-2);\n    --scrollarea-scrollbar-border-radius: max(var(--radius-1), var(--radius-full));\n  }\n  .fui-ScrollAreaScrollbar:where(.fui-r-size-3) {\n    --scrollarea-scrollbar-size: var(--space-3);\n    --scrollarea-scrollbar-border-radius: max(var(--radius-1), var(--radius-full));\n  }\n  .fui-ScrollAreaScrollbar:where([data-state='visible']) {\n    animation-name: fui-fade-in;\n  }\n  .fui-ScrollAreaScrollbar:where([data-state='hidden']) {\n    animation-name: fui-fade-out;\n  }\n  @media (hover: hover) {\n    .fui-ScrollAreaThumb:where(:hover) {\n      background-color: var(--gray-a9);\n    }\n  }\n  .fui-BaseSegmentedControlList::-webkit-scrollbar {\n    display: none;\n  }\n  .fui-BaseSegmentedControlTrigger {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n    flex-shrink: 0;\n    position: relative;\n    -webkit-user-select: none;\n    user-select: none;\n    border-radius: var(--radius-3);\n    padding: 0 var(--space-2);\n    font-weight: var(--font-weight-medium);\n    flex: 1;\n    color: var(--gray-a9);\n  }\n  @media (hover: hover) {\n    .fui-BaseSegmentedControlTrigger:where(:hover) {\n      color: var(--gray-a11);\n    }\n  }\n  .fui-BaseSegmentedControlTrigger:where([data-state='active'], [data-state='checked'], [data-active]) {\n    color: var(--gray-a12);\n  }\n  .fui-BaseSegmentedControlTrigger:before {\n    box-sizing: border-box;\n    content: '';\n    position: absolute;\n    inset: 0;\n    border-radius: inherit;\n  }\n  .fui-BaseSegmentedControlTrigger:where(:focus-visible)::before {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  .fui-BaseSegmentedControlTrigger:where([data-state='active'], [data-state='checked'], [data-active])::before {\n    background: var(--color-segmented-control-thumb);\n    background-image: linear-gradient(var(--white-a3), var(--white-a3));\n    box-shadow: 0px 1px 1px 0px rgba(0, 0, 0, 0.05), 0px 2px 4px 0px rgba(0, 0, 0, 0.05);\n  }\n  .fui-BaseSegmentedControlTriggerInner {\n    display: flex;\n    gap: var(--space-2);\n    align-items: center;\n    justify-content: center;\n    position: relative;\n  }\n  .fui-BaseSegmentedControlList {\n    height: var(--space-7);\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    font-weight: var(--font-weight-medium);\n    box-sizing: border-box;\n    display: flex;\n    overflow-x: auto;\n    white-space: nowrap;\n    background: var(--gray-a3);\n    border-radius: var(--radius-4);\n    scrollbar-width: none;\n    padding: var(--space-1);\n  }\n  .fui-SegmentedControlContent {\n    position: relative;\n    outline: 0;\n  }\n  .fui-SegmentedControlNavItem {\n    display: flex;\n    flex: 1;\n  }\n  .fui-SelectTrigger:where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-SelectTriggerInner {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .fui-SelectIcon {\n    flex-shrink: 0;\n  }\n  :where(.fui-SelectTrigger:not(.fui-variant-ghost)) .fui-SelectIcon {\n    opacity: 0.9;\n  }\n  .fui-SelectContent:where([data-side]) {\n    min-width: var(--radix-select-trigger-width);\n    max-height: var(--radix-select-content-available-height);\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .fui-SelectContent :where(.fui-SelectItem[data-highlighted]) {\n    background-color: var(--gray-a4);\n  }\n  .fui-SelectViewport {\n    box-sizing: border-box;\n    padding: var(--select-content-padding);\n  }\n  :where(.fui-SelectContent:has(.fui-ScrollAreaScrollbar[data-orientation='vertical'])) .fui-SelectViewport {\n    padding-right: var(--space-3);\n  }\n  .fui-SelectItem {\n    display: flex;\n    align-items: center;\n    height: var(--select-item-height);\n    padding-left: var(--select-item-indicator-width);\n    padding-right: var(--select-item-indicator-width);\n    position: relative;\n    box-sizing: border-box;\n    outline: none;\n    cursor: var(--cursor-menu-item);\n    scroll-margin: var(--select-content-padding) 0;\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-SelectItemIndicator {\n    position: absolute;\n    left: 0;\n    width: var(--select-item-indicator-width);\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n  }\n  .fui-SelectSeparator {\n    height: 1px;\n    margin-top: 4px;\n    margin-bottom: 4px;\n    margin-left: calc(-1 * var(--select-content-padding) + 1px);\n    margin-right: calc(-1 * var(--select-content-padding) + 1px);\n    background-color: var(--gray-a6);\n  }\n  .fui-SelectLabel {\n    display: flex;\n    align-items: center;\n    height: var(--select-item-height);\n    padding-left: var(--select-item-indicator-width);\n    padding-right: var(--select-item-indicator-width);\n    color: var(--gray-a10);\n    -webkit-user-select: none;\n    user-select: none;\n    cursor: default;\n    font-weight: var(--font-weight-semi-bold);\n  }\n  :where(.fui-SelectItem) + .fui-SelectLabel {\n    margin-top: var(--space-2);\n  }\n  .fui-SelectTrigger {\n    color: var(--gray-12);\n    height: var(--select-trigger-height);\n    display: inline-flex;\n    align-items: center;\n    justify-content: space-between;\n    box-sizing: border-box;\n    flex-shrink: 0;\n    -webkit-user-select: none;\n    user-select: none;\n    vertical-align: top;\n    line-height: var(--height);\n  }\n  .fui-SelectTrigger:where(.fui-r-size-1) {\n    --select-trigger-height: var(--space-5);\n    gap: var(--space-1);\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n    border-radius: 6px;\n    padding-left: var(--space-2);\n    padding-right: var(--space-2);\n  }\n  .fui-SelectTrigger:where(.fui-r-size-1) :where(.fui-SelectIcon) {\n    width: 8px;\n    height: 8px;\n  }\n  .fui-SelectTrigger:where(.fui-r-size-2) {\n    --select-trigger-height: var(--space-6);\n    gap: calc(var(--space-1) * 1.5);\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    border-radius: 8px;\n    padding-left: var(--space-3);\n    padding-right: var(--space-3);\n  }\n  .fui-SelectTrigger:where(.fui-r-size-2) :where(.fui-SelectIcon) {\n    width: 10px;\n    height: 10px;\n    margin-bottom: -1px;\n  }\n  .fui-SelectTrigger:where(.fui-r-size-3) {\n    --select-trigger-height: var(--space-7);\n    gap: var(--space-2);\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n    letter-spacing: var(--letter-spacing-3);\n    border-radius: 10px;\n    padding-left: var(--space-4);\n    padding-right: var(--space-4);\n  }\n  .fui-SelectTrigger:where(.fui-r-size-3) :where(.fui-SelectIcon) {\n    width: 12px;\n    height: 12px;\n    margin-bottom: -2px;\n  }\n  .fui-SelectContent:where(.fui-r-size-1) {\n    --select-item-height: var(--space-5);\n    --select-item-indicator-width: calc(var(--space-5) / 1.2);\n    --select-separator-margin-right: var(--space-2);\n    border-radius: 8px;\n  }\n  .fui-SelectContent:where(.fui-r-size-1) :where(.fui-SelectLabel) {\n    font-size: var(--font-size-0);\n    letter-spacing: var(--letter-spacing-0);\n    line-height: var(--line-height-0);\n  }\n  .fui-SelectContent:where(.fui-r-size-1) :where(.fui-SelectItem) {\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n    border-radius: 4px;\n  }\n  .fui-SelectContent:where(.fui-r-size-1) :where(.fui-SelectItemIndicatorIcon) {\n    width: 8px;\n    height: 8px;\n  }\n  .fui-SelectContent:where(.fui-r-size-2) {\n    --select-item-height: var(--space-6);\n    --select-item-indicator-width: calc(var(--space-6) / 1.2);\n    --select-separator-margin-right: var(--space-2);\n    border-radius: 10px;\n  }\n  .fui-SelectContent:where(.fui-r-size-2) :where(.fui-SelectLabel) {\n    font-size: var(--font-size-1);\n    letter-spacing: var(--letter-spacing-1);\n    line-height: var(--line-height-1);\n  }\n  .fui-SelectContent:where(.fui-r-size-2) :where(.fui-SelectItem) {\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    border-radius: 6px;\n  }\n  .fui-SelectContent:where(.fui-r-size-2) :where(.fui-SelectItemIndicatorIcon) {\n    width: 10px;\n    height: 10px;\n  }\n  .fui-SelectContent:where(.fui-r-size-3) {\n    --select-item-height: var(--space-7);\n    --select-item-indicator-width: calc(var(--space-7) / 1.2);\n    --select-separator-margin-right: var(--space-2);\n    border-radius: 12px;\n  }\n  .fui-SelectContent:where(.fui-r-size-3) :where(.fui-SelectLabel) {\n    font-size: var(--font-size-2);\n    letter-spacing: var(--letter-spacing-2);\n    line-height: var(--line-height-2);\n  }\n  .fui-SelectContent:where(.fui-r-size-3) :where(.fui-SelectItem) {\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n    letter-spacing: var(--letter-spacing-3);\n    border-radius: 8px;\n  }\n  .fui-SelectContent:where(.fui-r-size-3) :where(.fui-SelectItemIndicatorIcon) {\n    width: 12px;\n    height: 12px;\n  }\n  .fui-SelectTrigger:where(.fui-variant-surface) {\n    color: var(--gray-12);\n    background-color: var(--color-surface);\n    box-shadow: inset 0 0 0 1px var(--gray-a5), 0px 1px 2px 0px rgba(0, 0, 0, 0.05);\n  }\n  @media (hover: hover) {\n    .fui-SelectTrigger:where(.fui-variant-surface):where(:hover) {\n      box-shadow: inset 0 0 0 1px var(--gray-a7), 0px 1px 2px 0px rgba(0, 0, 0, 0.05);\n    }\n  }\n  .fui-SelectTrigger:where(.fui-variant-surface):where([data-state='open']) {\n    box-shadow: inset 0 0 0 1px var(--gray-a7);\n  }\n  .fui-SelectTrigger:where(.fui-variant-surface):where(:disabled) {\n    cursor: var(--cursor-disabled);\n    color: var(--gray-a8);\n    background-color: var(--gray-a2);\n    box-shadow: inset 0 0 0 1px var(--gray-a6);\n  }\n  .fui-SelectTrigger:where(.fui-variant-surface):where([data-placeholder]) :where(.fui-SelectTriggerInner) {\n    color: var(--gray-a10);\n  }\n  .fui-SelectTrigger:where(.fui-variant-classic) {\n    color: var(--gray-12);\n    background-image: linear-gradient(var(--gray-2), var(--gray-1));\n    box-shadow: var(--select-trigger-classic-box-shadow);\n    position: relative;\n    z-index: 0;\n  }\n  .fui-SelectTrigger:where(.fui-variant-classic)::before {\n    content: '';\n    position: absolute;\n    z-index: -1;\n    inset: 0;\n    border: 2px solid transparent;\n    background-clip: content-box;\n    border-radius: inherit;\n    pointer-events: none;\n    background-image: linear-gradient(var(--black-a1) -20%, transparent, var(--white-a1) 130%), linear-gradient(var(--color-surface), transparent);\n  }\n  @media (hover: hover) {\n    .fui-SelectTrigger:where(.fui-variant-classic):where(:hover) {\n      box-shadow: inset 0 0 0 1px var(--gray-a3), var(--select-trigger-classic-box-shadow);\n    }\n    .fui-SelectTrigger:where(.fui-variant-classic):where(:hover)::before {\n      background-image: linear-gradient(var(--black-a1) -15%, transparent, var(--white-a1) 120%), linear-gradient(var(--gray-2), var(--gray-1));\n    }\n  }\n  .fui-SelectTrigger:where(.fui-variant-classic):where([data-state='open']) {\n    box-shadow: inset 0 0 0 1px var(--gray-a3), var(--select-trigger-classic-box-shadow);\n  }\n  .fui-SelectTrigger:where(.fui-variant-classic):where([data-state='open'])::before {\n    background-image: linear-gradient(var(--black-a1) -15%, transparent, var(--white-a1) 120%), linear-gradient(var(--gray-2), var(--gray-1));\n  }\n  .fui-SelectTrigger:where(.fui-variant-classic):where(:disabled) {\n    cursor: var(--cursor-disabled);\n    color: var(--gray-a8);\n    box-shadow: var(--base-button-classic-disabled-box-shadow);\n  }\n  .fui-SelectTrigger:where(.fui-variant-classic):where(:disabled)::before {\n    background-image: linear-gradient(var(--black-a1) -20%, transparent, var(--white-a1) 130%), linear-gradient(var(--color-surface), transparent);\n  }\n  .fui-SelectTrigger:where(.fui-variant-classic):where([data-placeholder]) :where(.fui-SelectTriggerInner) {\n    color: var(--gray-a10);\n  }\n  .fui-SelectTrigger:where(.fui-variant-soft), .fui-SelectTrigger:where(.fui-variant-ghost) {\n    color: var(--accent-12);\n  }\n  .fui-SelectTrigger:where(.fui-variant-soft):where([data-placeholder]) :where(.fui-SelectTriggerInner), .fui-SelectTrigger:where(.fui-variant-ghost):where([data-placeholder]) :where(.fui-SelectTriggerInner) {\n    color: var(--accent-12);\n    opacity: 0.6;\n  }\n  .fui-SelectTrigger:where(.fui-variant-soft) {\n    background-color: var(--accent-a3);\n  }\n  @media (hover: hover) {\n    .fui-SelectTrigger:where(.fui-variant-soft):where(:hover) {\n      background-color: var(--accent-a4);\n    }\n  }\n  .fui-SelectTrigger:where(.fui-variant-soft):where([data-state='open']) {\n    background-color: var(--accent-a4);\n  }\n  .fui-SelectTrigger:where(.fui-variant-soft):where(:focus-visible) {\n    outline-color: var(--accent-8);\n  }\n  .fui-SelectTrigger:where(.fui-variant-soft):where(:disabled) {\n    cursor: var(--cursor-disabled);\n    color: var(--gray-a8);\n    background-color: var(--gray-a3);\n  }\n  @media (hover: hover) {\n    .fui-SelectTrigger:where(.fui-variant-ghost):where(:hover) {\n      background-color: var(--accent-a3);\n    }\n  }\n  .fui-SelectTrigger:where(.fui-variant-ghost):where([data-state='open']) {\n    background-color: var(--accent-a3);\n  }\n  .fui-SelectTrigger:where(.fui-variant-ghost):where(:disabled) {\n    cursor: var(--cursor-disabled);\n    color: var(--gray-a8);\n    background-color: transparent;\n  }\n  .fui-SelectContent {\n    box-shadow: var(--shadow-5);\n    outline: 0.5px solid var(--color-base-menu-outline) !important;\n    --scrollarea-scrollbar-vertical-margin-top: var(--select-content-padding);\n    --scrollarea-scrollbar-vertical-margin-bottom: var(--select-content-padding);\n    --scrollarea-scrollbar-horizontal-margin-left: var(--select-content-padding);\n    --scrollarea-scrollbar-horizontal-margin-right: var(--select-content-padding);\n    overflow: hidden;\n    background-color: var(--color-panel-solid);\n    --select-content-padding: var(--space-1);\n  }\n  .fui-SelectItem:where([data-disabled]) {\n    color: var(--gray-a8);\n  }\n  .fui-Separator {\n    background-color: var(--accent-a6);\n  }\n  .fui-Separator:where([data-orientation='vertical']) {\n    width: 1px;\n    height: var(--separator-size);\n  }\n  .fui-Separator:where([data-orientation='horizontal']) {\n    width: var(--separator-size);\n    height: 1px;\n  }\n  .fui-Separator:where(.fui-r-size-1) {\n    --separator-size: var(--space-4);\n  }\n  .fui-Separator:where(.fui-r-size-2) {\n    --separator-size: var(--space-6);\n  }\n  .fui-Separator:where(.fui-r-size-3) {\n    --separator-size: var(--space-9);\n  }\n  .fui-Separator:where(.fui-r-size-4) {\n    --separator-size: 100%;\n  }\n  .fui-SkeletonAvatar {\n    display: inline-block;\n    vertical-align: middle;\n    -webkit-user-select: none;\n    user-select: none;\n    width: var(--skeleton-avatar-size);\n    height: var(--skeleton-avatar-size);\n    flex-shrink: 0;\n    background-size: 400% 100%;\n    background-image: linear-gradient( 270deg, var(--skeleton-background-color), var(--skeleton-background-shimmer-color), var(--skeleton-background-shimmer-color), var(--skeleton-background-color) );\n    --skeleton-background-color: var(--accent-a2);\n    --skeleton-background-shimmer-color: var(--accent-a3);\n  }\n  .fui-SkeletonAvatar:where(.fui-high-contrast) {\n    --skeleton-background-color: var(--accent-a4);\n    --skeleton-background-shimmer-color: var(--accent-a6);\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-1) {\n    --skeleton-avatar-size: var(--space-5);\n    border-radius: max(var(--radius-2), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-2) {\n    --skeleton-avatar-size: var(--space-6);\n    border-radius: max(var(--radius-2), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-3) {\n    --skeleton-avatar-size: var(--space-7);\n    border-radius: max(var(--radius-3), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-4) {\n    --skeleton-avatar-size: var(--space-8);\n    border-radius: max(var(--radius-3), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-5) {\n    --skeleton-avatar-size: var(--space-9);\n    border-radius: max(var(--radius-4), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-6) {\n    --skeleton-avatar-size: 80px;\n    border-radius: max(var(--radius-5), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-7) {\n    --skeleton-avatar-size: 96px;\n    border-radius: max(var(--radius-5), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-8) {\n    --skeleton-avatar-size: 128px;\n    border-radius: max(var(--radius-6), var(--radius-full));\n  }\n  .fui-SkeletonAvatar:where(.fui-r-size-9) {\n    --skeleton-avatar-size: 160px;\n    border-radius: max(var(--radius-6), var(--radius-full));\n  }\n  .fui-SkeletonText {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    margin: 0;\n    height: var(--skeleton-text-line-height);\n    --skeleton-background-color: var(--accent-a2);\n    --skeleton-background-shimmer-color: var(--accent-a3);\n  }\n  .fui-SkeletonText:where(.fui-high-contrast) {\n    --skeleton-background-color: var(--accent-a4);\n    --skeleton-background-shimmer-color: var(--accent-a6);\n  }\n  .fui-SkeletonText::after {\n    content: '';\n    width: 100%;\n    height: var(--skeleton-text-size);\n    background-size: 400% 100%;\n    background-image: linear-gradient( 270deg, var(--skeleton-background-color), var(--skeleton-background-shimmer-color), var(--skeleton-background-shimmer-color), var(--skeleton-background-color) );\n    border-radius: var(--skeleton-text-border-radius);\n  }\n  .fui-SkeletonText:where(.fui-r-size-0) {\n    --skeleton-text-size: var(--font-size-0);\n    --skeleton-text-line-height: var(--line-height-0);\n    --skeleton-text-border-radius: var(--radius-1);\n  }\n  .fui-SkeletonText:where(.fui-r-size-1) {\n    --skeleton-text-size: var(--font-size-1);\n    --skeleton-text-line-height: var(--line-height-1);\n    --skeleton-text-border-radius: var(--radius-1);\n  }\n  .fui-SkeletonText:where(.fui-r-size-2) {\n    --skeleton-text-size: var(--font-size-2);\n    --skeleton-text-line-height: var(--line-height-2);\n    --skeleton-text-border-radius: var(--radius-1);\n  }\n  .fui-SkeletonText:where(.fui-r-size-3) {\n    --skeleton-text-size: var(--font-size-3);\n    --skeleton-text-line-height: var(--line-height-3);\n    --skeleton-text-border-radius: var(--radius-2);\n  }\n  .fui-SkeletonText:where(.fui-r-size-4) {\n    --skeleton-text-size: var(--font-size-4);\n    --skeleton-text-line-height: var(--line-height-4);\n    --skeleton-text-border-radius: var(--radius-2);\n  }\n  .fui-SkeletonText:where(.fui-r-size-5) {\n    --skeleton-text-size: var(--font-size-5);\n    --skeleton-text-line-height: var(--line-height-5);\n    --skeleton-text-border-radius: var(--radius-3);\n  }\n  .fui-SkeletonText:where(.fui-r-size-6) {\n    --skeleton-text-size: var(--font-size-6);\n    --skeleton-text-line-height: var(--line-height-6);\n    --skeleton-text-border-radius: var(--radius-3);\n  }\n  .fui-SkeletonText:where(.fui-r-size-7) {\n    --skeleton-text-size: var(--font-size-7);\n    --skeleton-text-line-height: var(--line-height-7);\n    --skeleton-text-border-radius: var(--radius-4);\n  }\n  .fui-SkeletonText:where(.fui-r-size-8) {\n    --skeleton-text-size: var(--font-size-8);\n    --skeleton-text-line-height: var(--line-height-8);\n    --skeleton-text-border-radius: var(--radius-4);\n  }\n  .fui-SkeletonText:where(.fui-r-size-9) {\n    --skeleton-text-size: var(--font-size-9);\n    --skeleton-text-line-height: var(--line-height-9);\n    --skeleton-text-border-radius: var(--radius-5);\n  }\n  .fui-SkeletonRect {\n    display: inline-block;\n    vertical-align: middle;\n    -webkit-user-select: none;\n    user-select: none;\n    flex-shrink: 0;\n    background-size: 400% 100%;\n    background-image: linear-gradient( 270deg, var(--skeleton-background-color), var(--skeleton-background-shimmer-color), var(--skeleton-background-shimmer-color), var(--skeleton-background-color) );\n    --skeleton-background-color: var(--accent-a2);\n    --skeleton-background-shimmer-color: var(--accent-a3);\n  }\n  .fui-SkeletonRect:where(.fui-high-contrast) {\n    --skeleton-background-color: var(--accent-a4);\n    --skeleton-background-shimmer-color: var(--accent-a6);\n  }\n  @media (prefers-reduced-motion: no-preference) {\n    @keyframes fui-skeleton-shimmer {\n      from {\n        background-position: 200% 0;\n      }\n      to {\n        background-position: -200% 0;\n      }\n    }\n    .fui-SkeletonAvatar, .fui-SkeletonText::after, .fui-SkeletonRect {\n      animation: fui-skeleton-shimmer 8s ease-in-out infinite;\n    }\n    .fui-DrawerContent:where([data-state='open'])::before {\n      animation: fui-drawer-backdrop-show 400ms cubic-bezier(0.16, 1, 0.3, 1);\n    }\n    .fui-DrawerContent:where([data-state='closed'])::before {\n      animation: fui-drawer-backdrop-hide 300ms cubic-bezier(0.16, 1, 0.3, 1);\n    }\n  }\n  .fui-SliderRoot {\n    --slider-thumb-size: calc(var(--slider-track-size) + var(--space-1));\n    position: relative;\n    display: flex;\n    align-items: center;\n    flex-shrink: 0;\n    -webkit-user-select: none;\n    user-select: none;\n    touch-action: none;\n  }\n  .fui-SliderRoot:where([data-orientation='horizontal']) {\n    height: var(--slider-thumb-size);\n  }\n  .fui-SliderRoot:where([data-orientation='vertical']) {\n    height: 100%;\n    flex-direction: column;\n    width: var(--slider-thumb-size);\n  }\n  .fui-SliderRoot :where(.fui-SliderTrack) {\n    background-color: var(--gray-a4);\n    background-image: linear-gradient(var(--white-a1), var(--white-a1));\n  }\n  .fui-SliderRoot :where(.fui-SliderTrack):where([data-disabled]) {\n    background-color: var(--gray-a4);\n    background-image: none;\n  }\n  .fui-SliderRoot :where(.fui-SliderRange) {\n    background-color: var(--accent-9);\n  }\n  .fui-SliderRoot :where(.fui-SliderThumb) {\n    --slider-thumb-box-shadow: 0 0 0 1px var(--black-a3), 0 0 0 1px var(--gray-a2), 0 0 0 1px var(--accent-a2), 0 1px 2px var(--gray-a4),\n      0 1px 3px -0.5px var(--gray-a3);\n  }\n  .fui-SliderRoot :where(.fui-SliderThumb):where([data-disabled])::after {\n    background-color: var(--gray-1);\n    box-shadow: 0 0 0 1px var(--gray-5);\n  }\n  .fui-SliderTrack {\n    overflow: hidden;\n    position: relative;\n    flex-grow: 1;\n    border-radius: max( calc(var(--radius-factor) * var(--slider-track-size) / 3), calc(var(--radius-factor) * var(--radius-thumb)) );\n  }\n  .fui-SliderTrack:where([data-orientation='horizontal']) {\n    height: var(--slider-track-size);\n  }\n  .fui-SliderTrack:where([data-orientation='vertical']) {\n    width: var(--slider-track-size);\n  }\n  .fui-SliderRange {\n    position: absolute;\n    border-radius: inherit;\n  }\n  .fui-SliderRange:where([data-orientation='horizontal']) {\n    height: 100%;\n  }\n  .fui-SliderRange:where([data-orientation='vertical']) {\n    width: 100%;\n  }\n  .fui-SliderThumb {\n    display: block;\n    width: var(--slider-thumb-size);\n    height: var(--slider-thumb-size);\n  }\n  .fui-SliderThumb::before {\n    content: '';\n    position: absolute;\n    z-index: -1;\n    width: calc(var(--slider-thumb-size) * 3);\n    height: calc(var(--slider-thumb-size) * 3);\n    top: 50%;\n    left: 50%;\n    transform: translate(-50%, -50%);\n  }\n  .fui-SliderThumb::after {\n    content: '';\n    position: absolute;\n    inset: calc(-0.25 * var(--slider-track-size));\n    background-color: white;\n    border-radius: max(var(--radius-1), var(--radius-thumb));\n    box-shadow: var(--slider-thumb-box-shadow);\n    cursor: var(--cursor-slider-thumb);\n  }\n  .fui-SliderThumb:where(:focus-visible)::after {\n    box-shadow: var(--slider-thumb-box-shadow), 0 0 0 3px var(--accent-3), 0 0 0 5px var(--color-focus-root);\n  }\n  .fui-SliderThumb:where(:active) {\n    cursor: var(--cursor-slider-thumb-active);\n  }\n  .fui-SliderRoot:where(.fui-r-size-1) {\n    --slider-track-size: calc(var(--space-2) * 0.75);\n  }\n  .fui-SliderRoot:where(.fui-r-size-2) {\n    --slider-track-size: var(--space-2);\n  }\n  .fui-SliderRoot:where(.fui-r-size-3) {\n    --slider-track-size: calc(var(--space-2) * 1.25);\n  }\n  .fui-SliderRoot:where(.fui-high-contrast) :where(.fui-SliderRange) {\n    background: var(--accent-12);\n  }\n  .fui-SliderRoot:where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    mix-blend-mode: var(--slider-disabled-blend-mode);\n  }\n  .fui-SliderRange:where([data-disabled]) {\n    background-color: var(--gray-a5);\n  }\n  .fui-Spinner {\n    display: block;\n    position: relative;\n    opacity: var(--spinner-opacity);\n  }\n  .fui-SpinnerLeaf {\n    position: absolute;\n    top: 0;\n    left: calc(50% - 12.5% / 2);\n    width: 12.5%;\n    height: 100%;\n    animation: fui-spinner-leaf-fade 800ms linear infinite;\n  }\n  .fui-SpinnerLeaf::before {\n    content: '';\n    display: block;\n    width: 100%;\n    height: 30%;\n    border-radius: var(--radius-1);\n    background-color: currentColor;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(1)) {\n    transform: rotate(0deg);\n    animation-delay: -800ms;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(2)) {\n    transform: rotate(45deg);\n    animation-delay: -700ms;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(3)) {\n    transform: rotate(90deg);\n    animation-delay: -600ms;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(4)) {\n    transform: rotate(135deg);\n    animation-delay: -500ms;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(5)) {\n    transform: rotate(180deg);\n    animation-delay: -400ms;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(6)) {\n    transform: rotate(225deg);\n    animation-delay: -300ms;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(7)) {\n    transform: rotate(270deg);\n    animation-delay: -200ms;\n  }\n  .fui-SpinnerLeaf:where(:nth-child(8)) {\n    transform: rotate(315deg);\n    animation-delay: -100ms;\n  }\n  @keyframes fui-spinner-leaf-fade {\n    from {\n      opacity: 1;\n    }\n    to {\n      opacity: 0.25;\n    }\n  }\n  .fui-Spinner:where(.fui-r-size-1) {\n    width: var(--space-3);\n    height: var(--space-3);\n  }\n  .fui-Spinner:where(.fui-r-size-2) {\n    width: var(--space-4);\n    height: var(--space-4);\n  }\n  .fui-Spinner:where(.fui-r-size-3) {\n    width: calc(1.25 * var(--space-4));\n    height: calc(1.25 * var(--space-4));\n  }\n  .fui-Spinner:where(.fui-r-size-4) {\n    width: var(--space-5);\n    height: var(--space-5);\n  }\n  .fui-Spinner:where(.fui-r-size-5) {\n    width: var(--space-6);\n    height: var(--space-6);\n  }\n  .fui-Spinner:where(.fui-r-size-6) {\n    width: var(--space-7);\n    height: var(--space-7);\n  }\n  .fui-StackedHorizontalBarChart {\n    display: flex;\n    gap: var(--space-1);\n    height: 12px;\n    width: 100%;\n    overflow: hidden;\n    position: relative;\n    border-radius: var(--radius-2);\n  }\n  .fui-StackedHorizontalBarChartBar {\n    height: 100%;\n    background: linear-gradient(180deg, rgba(255, 255, 255, 0.15) 0%, rgba(0, 0, 0, 0.15) 100%), var(--accent-9);\n    transition: 0.2s width cubic-bezier(0.33, 1, 0.68, 1);\n    min-width: 2px;\n    border-radius: 1px;\n  }\n  .fui-StackedHorizontalBarChartTooltip {\n    position: relative;\n  }\n  .fui-StackedHorizontalBarChartTooltip::after {\n    content: '';\n    position: absolute;\n    inset: -1px;\n    border-radius: inherit;\n    background: linear-gradient(to bottom, transparent, transparent, var(--accent-a4));\n    mix-blend-mode: overlay;\n    pointer-events: none;\n  }\n  .fui-Strong {\n    font-family: var(--strong-font-family);\n    font-size: calc(var(--strong-font-size-adjust) * 1em);\n    font-style: var(--strong-font-style);\n    font-weight: var(--strong-font-weight);\n    letter-spacing: calc(var(--strong-letter-spacing) + var(--letter-spacing, var(--default-letter-spacing)));\n  }\n  .fui-SwitchRoot {\n    display: inline-flex;\n    align-items: center;\n    vertical-align: top;\n    flex-shrink: 0;\n    height: var(--line-height, var(--switch-height));\n    --switch-padding: 1px;\n    --switch-width: calc(var(--switch-height) * 1.75);\n    --switch-thumb-size: calc(var(--switch-height) - var(--switch-padding) * 2);\n    --switch-thumb-translate-x: calc(var(--switch-width) - var(--switch-height));\n  }\n  .fui-SwitchButton {\n    position: relative;\n    display: inline-flex;\n    align-items: center;\n    width: var(--switch-width);\n    height: var(--switch-height);\n    padding: var(--switch-padding);\n    border-radius: var(--radius-thumb);\n    cursor: var(--cursor-switch);\n  }\n  .fui-SwitchButton::before {\n    content: '';\n    position: absolute;\n    inset: 0;\n    pointer-events: none;\n    border-radius: inherit;\n    transition: background-position, background-color, box-shadow, filter;\n    transition-timing-function: linear, ease-in-out, ease-in-out, ease-in-out;\n    background-repeat: no-repeat;\n    background-size: calc(var(--switch-width) * 2 + var(--switch-height)) 100%;\n  }\n  .fui-SwitchButton:where([data-state='unchecked'])::before {\n    transition-duration: 120ms, 140ms, 140ms, 140ms;\n    background-position-x: 100%;\n  }\n  .fui-SwitchButton:where([data-state='checked'])::before {\n    transition-duration: 160ms, 140ms, 140ms, 140ms;\n    background-position: 0%;\n  }\n  .fui-SwitchButton:where(:active)::before {\n    transition-duration: 30ms;\n  }\n  .fui-SwitchButton:where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 2px;\n  }\n  .fui-SwitchThumb {\n    background-color: white;\n    position: relative;\n    width: var(--switch-thumb-size);\n    height: var(--switch-thumb-size);\n    border-radius: var(--radius-thumb);\n    transition: transform 140ms cubic-bezier(0.45, 0.05, 0.55, 0.95), box-shadow 140ms ease-in-out;\n  }\n  .fui-SwitchThumb:where([data-state='checked']) {\n    transform: translateX(var(--switch-thumb-translate-x));\n  }\n  .fui-SwitchRoot:where(.fui-r-size-1) {\n    --switch-height: var(--space-4);\n  }\n  .fui-SwitchRoot:where(.fui-r-size-2) {\n    --switch-height: var(--space-5);\n  }\n  .fui-SwitchRoot:where(.fui-r-size-3) {\n    --switch-height: calc(var(--space-6));\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton)::before {\n    background-image: linear-gradient(to right, var(--accent-9) 40%, transparent 60%);\n    background-color: var(--gray-a4);\n    box-shadow: var(--shadow-1);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton):where([data-state='unchecked']:active)::before {\n    background-color: var(--gray-a5);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton):where([data-state='checked'])::before {\n    box-shadow: inset 0 0 0 1px var(--gray-a3), inset 0 0 0 1px var(--accent-a4), inset 0 0 0 1px var(--black-a1), inset 0 1.5px 2px 0 var(--black-a2);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton):where([data-state='checked']:active)::before {\n    filter: var(--switch-button-surface-checked-active-filter);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton):where(.fui-high-contrast)::before {\n    box-shadow: inset 0 0 0 1px var(--gray-a3), inset 0 0 0 1px var(--black-a2), inset 0 1.5px 2px 0 var(--black-a2);\n    background-image: linear-gradient(to right, var(--switch-button-high-contrast-checked-color-overlay) 40%, transparent 60%), linear-gradient(to right, var(--accent-9) 40%, transparent 60%);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton):where(.fui-high-contrast):where([data-state='checked']:active)::before {\n    filter: var(--switch-button-high-contrast-checked-active-before-filter);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton):where([data-disabled]) {\n    cursor: var(--cursor-disabled);\n    background-color: var(--gray-a3);\n    mix-blend-mode: var(--switch-disabled-blend-mode);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchButton):where([data-disabled])::before {\n    filter: none;\n    background-image: none;\n    background-color: transparent;\n    box-shadow: var(--shadow-1);\n    opacity: 0.5;\n  }\n  .fui-SwitchRoot :where(.fui-SwitchThumb):where([data-state='unchecked']) {\n    box-shadow: 0 1px 3px var(--black-a3), 0 2px 4px -1px var(--black-a1), 0 0 0 1px var(--black-a2);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchThumb):where([data-state='checked']) {\n    box-shadow: 0 1px 3px var(--black-a2), 0 2px 4px -1px var(--black-a1), 0 0 0 1px var(--black-a1), 0 0 0 1px var(--accent-a4), -1px 0 1px var(--black-a2);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchThumb):where([data-state='checked']):where(.fui-high-contrast) {\n    box-shadow: 0 1px 3px var(--black-a2), 0 2px 4px -1px var(--black-a1), 0 0 0 1px var(--black-a2), -1px 0 1px var(--black-a2);\n  }\n  .fui-SwitchRoot :where(.fui-SwitchThumb):where([data-disabled]) {\n    background-color: var(--gray-2);\n    box-shadow: 0 0 0 1px var(--gray-a2), 0 1px 3px var(--black-a1);\n    transition: none;\n  }\n  .fui-BaseTabsList::-webkit-scrollbar {\n    display: none;\n  }\n  .fui-BaseTabsTrigger {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    box-sizing: border-box;\n    flex-shrink: 0;\n    position: relative;\n    -webkit-user-select: none;\n    user-select: none;\n    padding-left: var(--tabs-trigger-padding-x);\n    padding-right: var(--tabs-trigger-padding-x);\n    color: var(--gray-a11);\n  }\n  .fui-BaseTabsTriggerInner, .fui-BaseTabsTriggerInnerHidden {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    padding: var(--tabs-trigger-inner-padding-y) var(--tabs-trigger-inner-padding-x);\n    border-radius: var(--tabs-trigger-inner-border-radius);\n  }\n  .fui-BaseTabsTriggerInner {\n    position: absolute;\n  }\n  :where(.fui-BaseTabsTrigger[data-state='inactive'], .fui-TabsNavLink:not([data-active])) .fui-BaseTabsTriggerInner {\n    letter-spacing: var(--tabs-trigger-inactive-letter-spacing);\n    word-spacing: var(--tabs-trigger-inactive-word-spacing);\n  }\n  :where(.fui-BaseTabsTrigger[data-state='active'], .fui-TabsNavLink[data-active]) .fui-BaseTabsTriggerInner {\n    font-weight: var(--font-weight-medium);\n    letter-spacing: var(--tabs-trigger-active-letter-spacing);\n    word-spacing: var(--tabs-trigger-active-word-spacing);\n  }\n  .fui-BaseTabsTriggerInnerHidden {\n    visibility: hidden;\n    font-weight: var(--font-weight-medium);\n    letter-spacing: var(--tabs-trigger-active-letter-spacing);\n    word-spacing: var(--tabs-trigger-active-word-spacing);\n  }\n  .fui-BaseTabsContent {\n    position: relative;\n    outline: 0;\n  }\n  .fui-BaseTabsList:where(.fui-r-size-1) {\n    height: 36px;\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n    --tabs-trigger-padding-x: var(--space-1);\n    --tabs-trigger-inner-padding-x: calc(1.5 * var(--space-1));\n    --tabs-trigger-inner-padding-y: var(--space-1);\n    --tabs-trigger-inner-border-radius: var(--radius-2);\n  }\n  .fui-BaseTabsList:where(.fui-r-size-2) {\n    height: var(--space-7);\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n    --tabs-trigger-padding-x: var(--space-1);\n    --tabs-trigger-inner-padding-x: calc(1.25 * var(--space-2));\n    --tabs-trigger-inner-padding-y: var(--space-1);\n    --tabs-trigger-inner-border-radius: var(--radius-3);\n  }\n  .fui-BaseTabsList {\n    box-shadow: inset 0 -1px 0 0 var(--gray-a5);\n    display: flex;\n    overflow-x: auto;\n    white-space: nowrap;\n    scrollbar-width: none;\n  }\n  @media (hover: hover) {\n    .fui-BaseTabsTrigger:where(:hover) {\n      color: var(--gray-12);\n    }\n    .fui-BaseTabsTrigger:where(:hover) :where(.fui-BaseTabsTriggerInner) {\n      background-color: var(--gray-a3);\n    }\n    .fui-BaseTabsTrigger:where(:focus-visible:hover) :where(.fui-BaseTabsTriggerInner) {\n      background-color: var(--accent-a3);\n    }\n  }\n  .fui-BaseTabsTrigger:where([data-state='active'], [data-active]) {\n    color: var(--gray-12);\n  }\n  .fui-BaseTabsTrigger:where(:focus-visible) :where(.fui-BaseTabsTriggerInner) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -2px;\n  }\n  .fui-BaseTabsTrigger:where([data-state='active'], [data-active])::before {\n    box-sizing: border-box;\n    content: '';\n    height: 2px;\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    right: 0;\n    background-color: var(--accent-10);\n  }\n  .fui-BaseTabsContent:where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n  }\n  .fui-TabsContent {\n    position: relative;\n    outline: 0;\n  }\n  .fui-TabsContent:where(:focus-visible) {\n    outline: 2px solid var(--color-focus-root);\n  }\n  .fui-TabsNavItem {\n    display: flex;\n  }\n  .fui-TableTable {\n    --table-row-background-color: transparent;\n    width: 100%;\n    text-align: left;\n    vertical-align: middle;\n    border-collapse: collapse;\n    border-spacing: 0;\n    box-sizing: border-box;\n    height: 0;\n  }\n  .fui-TableHeader {\n    vertical-align: inherit;\n  }\n  .fui-TableBody {\n    vertical-align: inherit;\n  }\n  .fui-TableRow {\n    vertical-align: inherit;\n    color: var(--gray-12);\n  }\n  .fui-TableCell {\n    background-color: var(--table-row-background-color);\n    box-sizing: border-box;\n    vertical-align: inherit;\n    padding: var(--table-cell-padding);\n    height: var(--table-cell-min-height);\n    color: var(--gray-11);\n    box-shadow: inset 0 -1px var(--data-table-border-color);\n  }\n  .fui-TableCell:where(.fui-TableHeader .fui-TableCell), .fui-TableCell:where(.fui-TableFooter .fui-TableCell) {\n    padding: var(--table-column-header-cell-padding);\n  }\n  .fui-TableCell:where(.fui-TableRoot:not(:has(.fui-TableBottomBar)) .fui-TableTable > :is(:last-child) .fui-TableRow:last-child .fui-TableCell) {\n    box-shadow: none;\n  }\n  .fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)) {\n    padding-right: 0;\n  }\n  .fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)):has(.fui-CheckboxRoot.fui-r-size-1) {\n    width: var(--space-4);\n    min-width: 30px;\n  }\n  .fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)):has(.fui-CheckboxRoot.fui-r-size-2) {\n    width: calc(var(--space-4) * 1.25);\n    min-width: 30px;\n  }\n  .fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot:only-child)):has(.fui-CheckboxRoot.fui-r-size-3) {\n    width: var(--space-5);\n    min-width: 30px;\n  }\n  .fui-TableCell:where(.fui-TableCell:first-child:has(> .fui-CheckboxRoot)) .fui-CheckboxRoot {\n    display: flex;\n  }\n  .fui-Inset :where(.fui-TableCell:first-child) {\n    padding-left: var(--inset-padding, var(--table-cell-padding));\n  }\n  .fui-Inset :where(.fui-TableCell:last-child) {\n    padding-right: var(--inset-padding, var(--table-cell-padding));\n  }\n  .fui-TableColumnHeaderCell {\n    font-weight: bold;\n    color: var(--gray-12);\n  }\n  .fui-TableRowHeaderCell {\n    font-weight: normal;\n    color: var(--gray-12);\n  }\n  .fui-TableRowHeaderCell:where(.fui-TableFooter .fui-TableRowHeaderCell) {\n    font-weight: bold;\n  }\n  .fui-TableBottomBar {\n    padding: var(--table-bottom-padding);\n  }\n  .fui-TableColumnHeaderCellButton:is(.fui-TableColumnHeaderCell .fui-TableColumnHeaderCellButton) {\n    font-size: inherit;\n    line-height: inherit;\n    font-weight: inherit;\n    color: inherit;\n    letter-spacing: inherit;\n    margin-left: calc(-1 * var(--space-2));\n    margin-right: calc(-1 * var(--space-2));\n    padding-left: var(--space-2);\n    padding-right: var(--space-2);\n  }\n  .fui-TableRoot-vars:where(.fui-r-size-1) {\n    --table-border-radius: var(--radius-3);\n    --table-column-header-cell-padding: var(--space-2) var(--space-3);\n    --table-cell-padding: var(--space-2) var(--space-3);\n    --table-cell-min-height: 36px;\n    --table-bottom-padding: var(--space-3) var(--space-3);\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n  }\n  .fui-TableRoot-vars:where(.fui-r-size-2) {\n    --table-border-radius: var(--radius-4);\n    --table-column-header-cell-padding: var(--space-2) var(--space-4);\n    --table-cell-padding: var(--space-3) var(--space-4);\n    --table-cell-min-height: 44px;\n    --table-bottom-padding: var(--space-3) var(--space-4);\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n  }\n  .fui-TableRoot-vars:where(.fui-Inset > .fui-TableRoot-vars) {\n    --table-border-radius: 0px;\n  }\n  .fui-TableRoot:where(.fui-variant-surface) {\n    border: 1px solid var(--gray-a5);\n    border-radius: var(--table-border-radius);\n    overflow: hidden;\n    position: relative;\n    border-color: var(--data-table-border-color);\n  }\n  .fui-TableRoot:where(.fui-variant-surface) :where(.fui-TableTable) {\n    overflow: hidden;\n  }\n  .fui-TableRoot:where(.fui-variant-surface) :where(.fui-TableTable) :where(.fui-TableHeader), .fui-TableRoot:where(.fui-variant-surface) :where(.fui-TableTable) :where(.fui-TableFooter) {\n    --table-row-background-color: var(--gray-a2);\n  }\n  .fui-TableRoot:where(.fui-variant-ghost) {\n    --scrollarea-scrollbar-horizontal-margin-left: 0;\n    --scrollarea-scrollbar-horizontal-margin-right: 0;\n  }\n  .fui-TableRoot:where(.fui-variant-ghost) :where(.fui-TableTable) {\n    overflow: hidden;\n  }\n  .fui-TextAreaRoot {\n    display: flex;\n    flex-direction: column;\n    box-sizing: border-box;\n    position: relative;\n    z-index: 0;\n  }\n  .fui-TextAreaInput {\n    -webkit-appearance: none;\n    appearance: none;\n    padding: 0;\n    border-radius: inherit;\n    background-color: transparent;\n    font-family: inherit;\n    -webkit-tap-highlight-color: transparent;\n    resize: none;\n    box-sizing: border-box;\n    position: relative;\n    display: block;\n    width: 100%;\n    flex-grow: 1;\n    z-index: 1;\n    border: var(--text-area-border-width) solid transparent;\n    padding: var(--text-area-padding-y) var(--text-area-padding-x);\n    cursor: auto;\n    scrollbar-width: thin;\n  }\n  .fui-TextAreaInput:where(:focus) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-TextAreaInput::-webkit-scrollbar {\n    width: var(--space-3);\n    height: var(--space-3);\n  }\n  .fui-TextAreaInput::-webkit-scrollbar-track, .fui-TextAreaInput::-webkit-scrollbar-thumb {\n    background-clip: content-box;\n    border: var(--space-1) solid transparent;\n    border-radius: var(--space-3);\n  }\n  .fui-TextAreaInput::-webkit-scrollbar-track {\n    background-color: var(--gray-a3);\n  }\n  .fui-TextAreaInput::-webkit-scrollbar-thumb {\n    background-color: var(--gray-a8);\n  }\n  @media (hover: hover) {\n    :where(.fui-TextAreaInput:not(:disabled))::-webkit-scrollbar-thumb:hover {\n      background-color: var(--gray-a9);\n    }\n  }\n  .fui-TextAreaChrome {\n    position: absolute;\n    border-radius: inherit;\n    z-index: 0;\n    inset: 0;\n  }\n  .fui-TextAreaRoot:where(.fui-r-size-1) {\n    min-height: var(--space-8);\n    border-radius: 6px;\n  }\n  .fui-TextAreaRoot:where(.fui-r-size-1) :where(.fui-TextAreaInput) {\n    --text-area-padding-y: calc(var(--space-1) - var(--text-area-border-width));\n    --text-area-padding-x: calc(var(--space-1) * 1.5 - var(--text-area-border-width));\n    font-size: var(--font-size-1);\n    line-height: var(--line-height-1);\n    letter-spacing: var(--letter-spacing-1);\n  }\n  .fui-TextAreaRoot:where(.fui-r-size-2) {\n    min-height: var(--space-9);\n    border-radius: 8px;\n  }\n  .fui-TextAreaRoot:where(.fui-r-size-2) :where(.fui-TextAreaInput) {\n    --text-area-padding-y: calc(var(--space-1) * 1.5 - var(--text-area-border-width));\n    --text-area-padding-x: calc(var(--space-2) - var(--text-area-border-width));\n    font-size: var(--font-size-2);\n    line-height: var(--line-height-2);\n    letter-spacing: var(--letter-spacing-2);\n  }\n  .fui-TextAreaRoot:where(.fui-r-size-3) {\n    min-height: 80px;\n    border-radius: 10px;\n  }\n  .fui-TextAreaRoot:where(.fui-r-size-3) :where(.fui-TextAreaInput) {\n    --text-area-padding-y: calc(var(--space-2) - var(--text-area-border-width));\n    --text-area-padding-x: calc(var(--space-3) - var(--text-area-border-width));\n    font-size: var(--font-size-3);\n    line-height: var(--line-height-3);\n    letter-spacing: var(--letter-spacing-3);\n  }\n  .fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput) {\n    --text-area-border-width: 1px;\n    color: var(--gray-12);\n  }\n  :is(.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput)) + :where(.fui-TextAreaChrome) {\n    box-shadow: inset 0 0 0 1px var(--gray-a5), 0px 1px 2px 0px rgba(0, 0, 0, 0.05);\n    background-color: var(--color-surface);\n    padding: 1px;\n    background-clip: content-box;\n  }\n  .fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput)::placeholder {\n    color: var(--gray-a10);\n    opacity: 1;\n  }\n  .fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled]) {\n    -webkit-background-clip: text;\n    background-clip: text;\n    -webkit-text-fill-color: var(--gray-12);\n  }\n  :is(.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled])) + :where(.fui-TextAreaChrome) {\n    background-color: var(--accent-a4);\n  }\n  :is(.fui-TextAreaRoot:where(.fui-variant-surface) :where(.fui-TextAreaInput):where(:disabled, :read-only)) + :where(.fui-TextAreaChrome) {\n    background-image: linear-gradient(var(--gray-a3), var(--gray-a3));\n  }\n  .fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput) {\n    --text-area-border-width: 0px;\n    color: var(--accent-12);\n  }\n  :is(.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput)) + :where(.fui-TextAreaChrome) {\n    background-color: var(--accent-a3);\n  }\n  .fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput)::selection {\n    background-color: var(--accent-a5);\n  }\n  .fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput)::placeholder {\n    color: var(--accent-12);\n    opacity: 0.65;\n  }\n  .fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled]) {\n    -webkit-background-clip: text;\n    background-clip: text;\n    -webkit-text-fill-color: var(--accent-12);\n  }\n  :is(.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:autofill, [data-com-onepassword-filled])) + :where(.fui-TextAreaChrome) {\n    background-color: var(--accent-a5);\n  }\n  .fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:focus) {\n    outline-color: var(--accent-8);\n  }\n  :is(.fui-TextAreaRoot:where(.fui-variant-soft) :where(.fui-TextAreaInput):where(:disabled, :read-only)) + :where(.fui-TextAreaChrome) {\n    background-color: var(--gray-a4);\n  }\n  .fui-TextAreaInput:where(:disabled, :read-only) {\n    cursor: text;\n    color: var(--gray-a11);\n    -webkit-text-fill-color: var(--gray-a11);\n  }\n  .fui-TextAreaInput:where(:disabled, :read-only):where(:focus) {\n    outline: 2px solid var(--gray-8);\n  }\n  .fui-TextAreaInput:where(:disabled, :read-only)::placeholder {\n    opacity: 0.5;\n  }\n  .fui-TextAreaInput:where(:disabled, :read-only):where(:placeholder-shown) {\n    cursor: default;\n  }\n  .fui-TextAreaInput:where(:disabled, :read-only)::selection {\n    background-color: var(--gray-a5);\n  }\n  .fui-TextFieldRoot {\n    display: flex;\n    box-sizing: border-box;\n    position: relative;\n    z-index: 0;\n    cursor: text;\n  }\n  .fui-TextFieldInput {\n    display: block;\n    box-sizing: border-box;\n    padding: 0;\n    width: 100%;\n    -webkit-appearance: none;\n    appearance: none;\n    -webkit-tap-highlight-color: transparent;\n    outline: none;\n    font-family: inherit;\n    background-color: transparent;\n    position: relative;\n    z-index: 1;\n    border: var(--text-field-border-width) solid transparent;\n  }\n  .fui-TextFieldChrome {\n    position: absolute;\n    inset: 0;\n    z-index: 0;\n    pointer-events: none;\n  }\n  :where(.fui-TextFieldInput:focus) + .fui-TextFieldChrome {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: -1px;\n  }\n  .fui-TextFieldSlot {\n    flex-shrink: 0;\n    display: flex;\n    align-items: center;\n    position: relative;\n    z-index: 1;\n    color: var(--gray-a11);\n  }\n  .fui-TextFieldSlot:where([data-accent-color]) {\n    color: var(--accent-a11);\n  }\n  .fui-TextFieldSlot:where(:empty) {\n    display: none;\n  }\n  .fui-TextFieldSlot:where(.fui-r-size-1) {\n    gap: var(--space-2);\n    padding-left: var(--space-1);\n    padding-right: var(--space-1);\n  }\n  .fui-TextFieldSlot:where(.fui-r-size-2) {\n    gap: var(--space-2);\n    padding-left: var(--space-2);\n    padding-right: var(--space-2);\n  }\n  .fui-TextFieldSlot:where(.fui-r-size-3) {\n    gap: var(--space-3);\n    padding-left: var(--space-3);\n    padding-right: var(--space-3);\n  }\n  .fui-TextFieldInput:where(.fui-r-size-1) {\n    height: var(--space-5);\n    font-size: var(--font-size-1);\n    letter-spacing: var(--letter-spacing-1);\n    padding-top: 0.5px;\n    padding-bottom: 1px;\n  }\n  .fui-TextFieldInput:where(.fui-r-size-1):where(:first-child) {\n    text-indent: calc(var(--space-1) * 1.5 - var(--text-field-border-width));\n    border-radius: max(6px, var(--radius-full));\n  }\n  .fui-TextFieldInput:where(.fui-r-size-1) + :where(.fui-TextFieldChrome) {\n    border-radius: max(6px, var(--radius-full));\n  }\n  .fui-TextFieldInput:where(.fui-r-size-2) {\n    height: var(--space-6);\n    font-size: var(--font-size-2);\n    letter-spacing: var(--letter-spacing-2);\n    padding-top: 0px;\n    padding-bottom: 1px;\n  }\n  .fui-TextFieldInput:where(.fui-r-size-2):where(:first-child) {\n    text-indent: calc(var(--space-2) - var(--text-field-border-width));\n    border-radius: max(8px, var(--radius-full));\n  }\n  .fui-TextFieldInput:where(.fui-r-size-2) + :where(.fui-TextFieldChrome) {\n    border-radius: max(8px, var(--radius-full));\n  }\n  .fui-TextFieldInput:where(.fui-r-size-3) {\n    height: var(--space-7);\n    font-size: var(--font-size-3);\n    letter-spacing: var(--letter-spacing-3);\n    padding-top: 0.5px;\n    padding-bottom: 1px;\n  }\n  .fui-TextFieldInput:where(.fui-r-size-3):where(:first-child) {\n    text-indent: calc(var(--space-3) - var(--text-field-border-width));\n    border-radius: max(10px, var(--radius-full));\n  }\n  .fui-TextFieldInput:where(.fui-r-size-3) + :where(.fui-TextFieldChrome) {\n    border-radius: max(10px, var(--radius-full));\n  }\n  .fui-TextFieldInput:where(:has(~ .fui-TextFieldSlot)) {\n    border-top-right-radius: 0;\n    border-bottom-right-radius: 0;\n  }\n  .fui-TextFieldInput:where(.fui-variant-surface) {\n    --text-field-border-width: 1px;\n    color: var(--gray-12);\n  }\n  .fui-TextFieldInput:where(.fui-variant-surface) + :where(.fui-TextFieldChrome) {\n    background-color: var(--color-surface);\n    box-shadow: inset 0 0 0 1px var(--gray-a5), 0px 1px 2px 0px rgba(0, 0, 0, 0.05);\n    padding: 1px;\n    background-clip: content-box;\n  }\n  .fui-TextFieldInput:where(.fui-variant-surface)::placeholder {\n    color: var(--gray-a10);\n    opacity: 1;\n  }\n  .fui-TextFieldInput:where(.fui-variant-surface):where(:autofill, [data-com-onepassword-filled]) {\n    -webkit-background-clip: text;\n    background-clip: text;\n    -webkit-text-fill-color: var(--gray-12);\n  }\n  .fui-TextFieldInput:where(.fui-variant-surface):where(:autofill, [data-com-onepassword-filled]) + :where(.fui-TextFieldChrome) {\n    background-color: var(--accent-a3);\n    box-shadow: inset 0 0 0 1px var(--gray-a7), inset 0 0 0 1px var(--accent-a4);\n  }\n  .fui-TextFieldInput:where(.fui-variant-surface):where(:disabled, :read-only) + :where(.fui-TextFieldChrome) {\n    background-image: linear-gradient(var(--gray-a3), var(--gray-a3));\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft) {\n    --text-field-border-width: 0px;\n    color: var(--accent-12);\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft) + :where(.fui-TextFieldChrome) {\n    background-color: var(--accent-a3);\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft)::placeholder {\n    color: var(--accent-12);\n    opacity: 0.6;\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft):where(:autofill, [data-com-onepassword-filled]) {\n    -webkit-background-clip: text;\n    background-clip: text;\n    -webkit-text-fill-color: var(--accent-12);\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft):where(:autofill, [data-com-onepassword-filled]) + :where(.fui-TextFieldChrome) {\n    background-color: var(--accent-a4);\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft):where(:focus) + :where(.fui-TextFieldChrome) {\n    outline-color: var(--accent-8);\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft):where(:disabled, :read-only) + :where(.fui-TextFieldChrome) {\n    background-color: var(--gray-a4);\n  }\n  .fui-TextFieldInput:where(.fui-variant-soft)::selection {\n    background-color: var(--accent-a5);\n  }\n  .fui-TextFieldInput:where(:disabled, :read-only) {\n    cursor: text;\n    color: var(--gray-a11);\n    -webkit-text-fill-color: var(--gray-a11);\n  }\n  .fui-TextFieldInput:where(:disabled, :read-only):where(:focus) + :where(.fui-TextFieldChrome) {\n    outline: 2px solid var(--gray-8);\n  }\n  .fui-TextFieldInput:where(:disabled, :read-only)::placeholder {\n    opacity: 0.5;\n  }\n  .fui-TextFieldInput:where(:disabled, :read-only):where(:placeholder-shown) {\n    cursor: default;\n  }\n  .fui-TextFieldInput:where(:disabled, :read-only)::selection {\n    background-color: var(--gray-a5);\n  }\n  .fui-TextFieldRoot:where(:has(.fui-TextFieldInput:where(:disabled, :read-only))) {\n    cursor: text;\n  }\n  .fui-TextFieldRoot:where(:has(.fui-TextFieldInput:where(:disabled, :read-only):placeholder-shown)) {\n    cursor: default;\n  }\n  .fui-OTPFieldRoot {\n    display: flex;\n    align-items: center;\n    gap: 8px;\n  }\n  .fui-OTPFieldGroup {\n    display: flex;\n    align-items: center;\n    --otp-focus-color: var(--color-focus-root);\n  }\n  .fui-OTPFieldSlot {\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    height: 40px;\n    width: 40px;\n    border-top-width: 1px;\n    border-bottom-width: 1px;\n    border-right-width: 1px;\n    font-size: 0.875rem;\n    transition-property: outline;\n    transition-duration: 0.2s;\n    transition-timing-function: ease;\n    box-shadow: var(--shadow-1);\n    background-color: var(--color-surface);\n    border-color: transparent;\n    font-variant-numeric: tabular-nums slashed-zero;\n  }\n  .fui-OTPFieldSlot:where(:first-child) {\n    border-left-width: 1px;\n    border-top-left-radius: 10px;\n    border-bottom-left-radius: 10px;\n  }\n  .fui-OTPFieldSlot:where(:last-child) {\n    border-top-right-radius: 10px;\n    border-bottom-right-radius: 10px;\n  }\n  .fui-OTPFieldSlot:where([data-otp-active='true']) {\n    outline: 2px solid var(--otp-focus-color);\n    z-index: 1;\n  }\n  .fui-OTPFieldCaret {\n    position: absolute;\n    inset: 0;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n  }\n  .fui-OTPFieldCaret::before {\n    content: '';\n    height: 16px;\n    width: 1px;\n    border-radius: 1px;\n    background-color: var(--gray-a10);\n    animation: fui-otp-caret-blink 1.25s ease-out infinite;\n  }\n  @keyframes fui-otp-caret-blink {\n    0%, 70%, 100% {\n      opacity: 1;\n    }\n    20%, 50% {\n      opacity: 0;\n    }\n  }\n  .fui-OTPFieldSeparator {\n    position: relative;\n    height: 100%;\n    display: flex;\n    align-items: center;\n  }\n  .fui-OTPFieldSeparator::before {\n    content: '';\n    width: 10px;\n    height: 4px;\n    border-radius: 2px;\n    box-shadow: var(--shadow-1);\n  }\n  .fui-DateFieldRoot {\n    position: relative;\n    display: inline-block;\n  }\n  .fui-DateFieldInput {\n    height: var(--height);\n    font-size: var(--font-size);\n    line-height: var(--font-size);\n    padding: var(--padding);\n    display: inline-flex;\n    flex-wrap: nowrap;\n    gap: 2px;\n    border-radius: var(--radius);\n    background-color: var(--gray-a3);\n    color: var(--gray-12);\n    caret-color: transparent;\n    overflow: hidden;\n  }\n  .fui-DateFieldInput:where([data-invalid]) {\n    background-color: var(--danger-a3);\n    color: var(--danger-12);\n  }\n  .fui-DateFieldInput:where([data-focus-visible]) {\n    outline: 2px solid var(--color-focus-root);\n    outline-offset: 1px;\n  }\n  .fui-DateFieldInput:where([data-invalid][data-focus-visible]) {\n    outline: 2px solid var(--danger-8);\n    outline-offset: 1px;\n  }\n  .fui-DateFieldSegment:where(:not([data-type='literal'])) {\n    box-sizing: content-box;\n    border-radius: max(calc(var(--radius) / 2), 4px);\n    padding: 1px 2px 0px;\n    color: var(--gray-12);\n    display: flex;\n    align-items: center;\n    justify-content: flex-end;\n    text-align: right;\n    font-variant-numeric: tabular-nums;\n    font-family: var(--code-font-family);\n    outline: none;\n  }\n  .fui-DateFieldSegment:where([data-hovered]) {\n    background: var(--gray-a4);\n  }\n  .fui-DateFieldSegment:where([data-invalid][data-hovered]) {\n    background: var(--danger-a4);\n  }\n  .fui-DateFieldSegment:where([data-focused]) {\n    background: var(--accent-a9);\n    color: var(--accent-9-contrast);\n  }\n  .fui-DateFieldSegment:where([data-invalid][data-focused]) {\n    background: var(--danger-a9);\n    color: var(--danger-9-contrast);\n  }\n  .fui-DateFieldSegment[data-type='literal'] {\n    color: var(--gray-a10);\n    display: flex;\n    align-items: center;\n  }\n  .fui-DateFieldRoot:where(.fui-r-size-1) {\n    --height: var(--space-5);\n    --font-size: var(--font-size-1);\n    --letter-spacing: var(--letter-spacing-1);\n    --padding: 4px;\n    --radius: 6px;\n  }\n  .fui-DateFieldRoot:where(.fui-r-size-2) {\n    --height: var(--space-6);\n    --font-size: var(--font-size-2);\n    --letter-spacing: var(--letter-spacing-2);\n    --padding: 6px;\n    --radius: 8px;\n  }\n  .fui-DateFieldRoot:where(.fui-r-size-3) {\n    --height: var(--space-7);\n    --font-size: var(--font-size-3);\n    --letter-spacing: var(--letter-spacing-3);\n    --padding: 8px;\n    --radius: 10px;\n  }\n  .fui-TooltipContent {\n    padding: var(--space-1) var(--space-2);\n    background-color: var(--color-panel-translucent);\n    -webkit-backdrop-filter: var(--backdrop-filter-panel);\n    backdrop-filter: var(--backdrop-filter-panel);\n    border-radius: var(--radius-4);\n    border: 1px solid var(--gray-a6);\n    outline: 0.5px solid var(--color-tooltip-outline);\n    box-shadow: 0px 6px 10px 0px rgba(0, 0, 0, 0.05);\n    transform-origin: var(--radix-tooltip-content-transform-origin);\n    animation-duration: 200ms;\n    animation-timing-function: cubic-bezier(0.16, 1, 0.3, 1);\n  }\n  @media (prefers-reduced-motion: no-preference) {\n    .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='top']) {\n      animation-name: fui-slide-up, fui-fade-in;\n    }\n    .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='bottom']) {\n      animation-name: fui-slide-down, fui-fade-in;\n    }\n    .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='left']) {\n      animation-name: fui-slide-left, fui-fade-in;\n    }\n    .fui-TooltipContent:where([data-state='delayed-open']):where([data-side='right']) {\n      animation-name: fui-slide-right, fui-fade-in;\n    }\n  }\n  .fui-TooltipText {\n    color: var(--gray-12);\n    -webkit-user-select: none;\n    user-select: none;\n    cursor: default;\n  }\n  .fui-TooltipArrow {\n    fill: transparent;\n  }\n  .fui-WidgetStackStack {\n    --widget-stack-radius: 24px;\n    --widget-stack-gutter: 8px;\n    --widget-stack-background: var(--gray-a3);\n    position: relative;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    isolation: isolate;\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-WidgetStackScrollArea {\n    position: absolute;\n    inset: calc(-1 * var(--widget-stack-gutter));\n    contain: strict;\n    will-change: scroll-position;\n    scrollbar-width: none;\n    background: var(--widget-stack-background);\n    -webkit-backdrop-filter: blur(16px);\n    backdrop-filter: blur(16px);\n    transition: 0.2s clip-path ease-out;\n    clip-path: inset( var(--widget-stack-gutter) var(--widget-stack-gutter) var(--widget-stack-gutter) var(--widget-stack-gutter) round var(--widget-stack-radius) );\n    display: flex;\n  }\n  .fui-WidgetStackScrollArea:where(:not(:has(.fui-WidgetStackItem:first-child:last-child)):hover) {\n    clip-path: inset(0 0 0 0 round calc(var(--widget-stack-radius) + var(--widget-stack-gutter)));\n  }\n  .fui-WidgetStackScrollArea:where([data-orientation='vertical']) {\n    flex-direction: column;\n    scroll-snap-type: y mandatory;\n    overflow-y: auto;\n  }\n  .fui-WidgetStackScrollArea:where([data-orientation='horizontal']) {\n    flex-direction: row;\n    scroll-snap-type: x mandatory;\n    overflow-x: auto;\n  }\n  .fui-WidgetStackScrollArea::-webkit-scrollbar {\n    -ms-overflow-style: none;\n    scrollbar-width: none;\n    display: none;\n  }\n  .fui-WidgetStackItem {\n    width: 100%;\n    height: 100%;\n    flex-shrink: 0;\n    scroll-snap-align: center;\n    border: var(--widget-stack-gutter) solid transparent;\n    contain: strict;\n  }\n  .fui-WidgetStackItemContent {\n    width: 100%;\n    height: 100%;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    border-radius: var(--widget-stack-radius);\n    overflow: hidden;\n    transform: translateZ(0) scale(calc(1 + (1 - var(--intersection-ratio)) * -0.3));\n  }\n  .fui-r-weight-light {\n    font-weight: var(--font-weight-light);\n  }\n  .fui-r-weight-regular {\n    font-weight: var(--font-weight-regular);\n  }\n  .fui-r-weight-medium {\n    font-weight: var(--font-weight-medium);\n  }\n  .fui-r-weight-semi-bold {\n    font-weight: var(--font-weight-semi-bold);\n  }\n  .fui-r-weight-bold {\n    font-weight: var(--font-weight-bold);\n  }\n  .fui-r-lt-normal::before, .fui-r-lt-end::before, .fui-r-lt-normal::after, .fui-r-lt-start::after {\n    content: none;\n  }\n  .fui-r-lt-start::before, .fui-r-lt-both::before, .fui-r-lt-end::after, .fui-r-lt-both::after {\n    content: '';\n    display: table;\n  }\n  .fui-r-lt-start::before, .fui-r-lt-both::before {\n    margin-bottom: calc( var(--leading-trim-start, var(--default-leading-trim-start)) - var(--line-height, calc(1em * var(--default-line-height))) / 2 );\n  }\n  .fui-r-lt-end::after, .fui-r-lt-both::after {\n    margin-top: calc( var(--leading-trim-end, var(--default-leading-trim-end)) - var(--line-height, calc(1em * var(--default-line-height))) / 2 );\n  }\n  .fui-r-ta-left {\n    text-align: left;\n  }\n  .fui-r-ta-center {\n    text-align: center;\n  }\n  .fui-r-ta-right {\n    text-align: right;\n  }\n  .fui-r-va-baseline {\n    vertical-align: baseline;\n  }\n  .fui-r-va-top {\n    vertical-align: top;\n  }\n  .fui-r-va-middle {\n    vertical-align: middle;\n  }\n  .fui-r-va-bottom {\n    vertical-align: bottom;\n  }\n  .fui-ThemePanelShortcut:where(:focus-visible) {\n    outline-style: solid;\n    outline-width: 2px;\n    outline-offset: 2px;\n    outline-color: var(--accent-9);\n  }\n  .fui-ThemePanelSwatch, .fui-ThemePanelRadioCard {\n    position: relative;\n  }\n  .fui-ThemePanelSwatchInput, .fui-ThemePanelRadioCardInput {\n    -webkit-appearance: none;\n    appearance: none;\n    margin: 0;\n    outline: none;\n    outline-width: 2px;\n    position: absolute;\n    inset: 0;\n    border-radius: inherit;\n  }\n  .fui-ThemePanelSwatch {\n    width: var(--space-5);\n    height: var(--space-5);\n    border-radius: 100%;\n  }\n  .fui-ThemePanelSwatchInput {\n    outline-offset: 2px;\n  }\n  .fui-ThemePanelSwatchInput:where(:checked) {\n    outline-style: solid;\n    outline-color: var(--gray-12);\n  }\n  .fui-ThemePanelSwatchInput:where(:focus-visible) {\n    outline-style: solid;\n    outline-color: var(--accent-9);\n  }\n  .fui-ThemePanelRadioCard {\n    border-radius: var(--radius-1);\n    box-shadow: 0 0 0 1px var(--gray-7);\n  }\n  .fui-ThemePanelRadioCardInput {\n    outline-offset: -1px;\n  }\n  .fui-ThemePanelRadioCardInput:where(:checked) {\n    outline-style: solid;\n    outline-color: var(--gray-12);\n  }\n  .fui-ThemePanelRadioCardInput:where(:focus-visible) {\n    background-color: var(--accent-a3);\n    outline-style: solid;\n    outline-color: var(--accent-9);\n  }\n  .fui-Shine {\n    position: relative;\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .fui-ShineSvgFilter {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    pointer-events: none;\n  }\n}\nbody {\n  background: var(--background);\n  color: var(--foreground);\n  font-family: Arial, Helvetica, sans-serif;\n}\n@layer base {\n  * {\n    outline-color: currentColor;\n  }\n  html, body {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-divide-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-space-y-reverse: 0;\n      --tw-divide-y-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EA+sWE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA/sWJ;EAEE;;;;;;;;;;;;;;;;;AAFF;EAmBE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EA+2VA;;;;EAGA;;;;;;AAphWF;EAopCE;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;IACE;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;;EAIF;;;;;EAIA;;;;;;;EAMA;;;;;;;;;;;;;;;;;;EAiBA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;IACE;;;;;EAIF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA2zBA;IACE;MACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6zBJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAi0BA;IACE;MACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAm0BJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA+DA;IACE;MACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6EE;IAAgD;;;;;EAKlD;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAiEE;IAAgD;;;;;EAKlD;IACE;MACE;;;;;;EAKJ;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAgIA;IACE;;;;;;;;;;IAUE;MAAgD;;;;;;;;;;;EA2DpD;;;;;;;;;EAQA;;;;;;;;;;EASA;;;;;;;;;EAQA;;;;;;;;;;EASA;;;;;;;;;EAQA;;;;;;;;;;EASA;;;;;;;;;EAQA;;;;;;;;;;EASA;;;;;;;EAMA;;;;;;;;EAOA;;;;;;;EAMA;;;;;;;;EAOA;;;;;;;EAMA;;;;;;;;EAOA;;;;;;;EAMA;;;;;;;;EAOA;;;;;;;;;EAQA;;;;;;;;;;EASA;;;;;;;;;EAQA;;;;;;;;;;EASA;;;;;;;;;EAQA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;;;;;;;;;EAaA;;;;;;EAKA;;;;;;;;;;;;;;EAaA;IACE;;;;;EAIF;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAKA;;;;EAKA;;;;;;;;;;;;EAUA;;;;;;;;;;;;EAUA;IACE;;;;;;;;;;IAQA;;;;;;;;;;;;IAUA;;;;;;;;;;;;IAUA;;;;IAIA;;;;IAGA;;;;IAGA;;;;IAGA;;;;IAGA;;;;;EAIF;;;;;;;;;;;;;;;;;EAgBA;;;;;;;;;;;EAUA;;;;;EAMA;;;;;EAIA;;;;;;;EAMA;IACE;;;;;;;;;;IAQA;;;;;;;;;;IAQA;;;;IAIA;;;;;EAKF;;;;;;;;EAOA;;;;;;;;;;;;;;;;;;;;EAmBA;;;;EAGA;;;;;;;;;EAQA;;;;;;;;EAOA;;;;;;;;;;;EAaA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;;EAUA;;;;;;;EAMA;;;;;;;;;;;;;;;;EAeA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;EAGA;;;;;;;;;;;;;;;;;EAgBA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;;;;;;;;;;;;;EAeA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;;;;;;;EAmBA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;EAaA;;;;;EAIA;;;;;;;;;;EASA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;;;EAYA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;EAQA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;EAYA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;;;EAQA;;;;;EAIA;;;;;EAIA;IACE;;;;IAGA;;;;;;EAKF;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;EAIA;;;;;;;;;;EASA;;;;;;EAKA;IACE;;;;;EAIF;;;;EAGA;;;;;EAIA;IACE;;;;;;EAKF;;;;;EAIA;;;;;EAIA;IACE;;;;;;EAKF;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;;EAIA;IACE;;;;;EAIF;;;;EAGA;;;;EAGA;;;;;;EAKA;IACE;;;;;EAIF;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;IACE;;;;;EAIF;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;;;;;;;;;EAWA;;;;;;;;;;;;;EAaA;;;;;;;EAMA;;;;;;;;EAOA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;;EAWA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;EAOA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;;;EAQA;;;;EAGA;;;;;;;;EAOA;;;;;EAIA;;;;;;;;;;;;;;EAaA;;;;;;;;;;;;;EAYA;;;;;;;EAMA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAIE;IAAgD;;;;;EAIlD;IACE;;;;;IAGE;MAAgD;;;;;;EAKpD;;;;;EAGE;IAAgD;;;;;EAIlD;;;;EAGA;;;;;EAIA;;;;;;EAKA;IACE;;;;;;EAKF;;;;;EAIA;;;;EAGA;IACE;;;;;EAIF;;;;EAGA;;;;;;;;;;EASA;;;;;;;;;;;EAUA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;EAOA;IACE;;;;IAGA;;;;IAGA;;;;IAGA;;;;;EAIF;;;;;;;;;EAQA;;;;;;;;EAOA;;;;EAGA;;;;;EAIA;;;;;;;;EAOA;;;;EAGA;;;;;EAIA;;;;;;;;EAOA;;;;EAGA;;;;;EAIA;;;;;;;;;;;;EAWA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;IACE;;;;IAGA;;;;;;EAKF;;;;;EAIA;;;;;;EAKA;;;;;EAIA;IACE;;;;;EAIF;;;;;EAIA;;;;;;EAKA;;;;;EAIA;IACE;;;;;EAIF;;;;;EAIA;;;;;;;;;;;;;;;;;EAiBA;;;;;;;;EAQA;;;;EAGA;;;;;;;;;;;;;;;;EAeA;;;;;;;EAMA;;;;;EAIA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;;;;;;;;;;EAYA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;;EAOA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;EAOA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;;;;;EASA;;;;EAGA;;;;;EAIA;;;;;;;;;;;;;EAYA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;;;;;;;;;;;EAaA;;;;;;;;;;;EAUA;;;;;;;;;;;EAUA;;;;;;;;;;;EAUA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;;EAMA;;;;EAGA;;;;;;EAKA;;;;;EAIA;IACE;;;;;EAIF;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;;;EAQA;;;;;;EAMA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;EAOA;;;;;;;EAMA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;;;;;;;;EAUA;;;;;;;;;;;EAUA;;;;;;;;;;;EAUA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;;;EAQA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA4BA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;IACE;;;;;EAIF;;;;;EAIA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;IACE;;;;;EAIF;;;;EAGA;;;;;;;;EAOA;;;;EAGA;;;;EAGA;;;;;;;;;;EASA;;;;;;EAMA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;EASA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;EASA;;;;;;;;;EAQA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;;EAeA;;;;EAGA;;;;;;;;;;;EAUA;;;;;;;;;;;;EAWA;;;;;;;;;;;EAUA;;;;;EAIA;;;;;;;;EAOA;;;;;;;;EAOA;;;;;;;;EAOA;;;;;;;;EAOA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;;;;EAQA;;;;;EAIA;;;;EAGA;;;;;;;;EAOA;;;;;;EAKA;;;;;;;;EAOA;;;;;;;;;;;EAUA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;;;;EAMA;;;;;;;;;;;;EAWA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;IACE;;;;;EAIF;;;;EAGA;;;;;;;;;;;;;;;EAeA;IACE;;;;;EAIF;;;;EAGA;;;;;;;;EAOA;;;;;EAIA;;;;;;EAKA;;;;;;;;EAOA;;;;;;;;;;;;;;;;EAeA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;;;;;;;;;EAcA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;;;;;;;;;;EAYA;;;;EAGA;;;;;;;;;;;;;;EAaA;;;;;;;;;;;EAUA;;;;;EAIA;;;;;;;;;;;EAUA;;;;;;EAKA;;;;;;;;;;;EAUA;;;;;;EAKA;;;;;;;EAMA;;;;;;EAKA;;;;;;;EAMA;;;;;EAIA;;;;;;;EAMA;;;;;;EAKA;;;;;;;EAMA;;;;;EAIA;;;;;;;EAMA;;;;;;EAKA;;;;;;;EAMA;;;;;EAIA;;;;;;EAKA;IACE;;;;;EAIF;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;;EAOA;;;;;;;;;;;;EAWA;IACE;;;;IAGA;;;;;EAIF;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;IACE;;;;;EAIF;;;;EAGA;;;;EAGA;;;;;;EAKA;IACE;;;;;EAIF;;;;EAGA;;;;;;EAKA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;EAaA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;EASA;;;;;EAIA;;;;;;;;;EAQA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;;;;;;;EAWA;;;;;EAIA;IACE;;;;;;;;;;IAQA;;;;IAGA;;;;IAGA;;;;;EAIF;;;;;;;;;;;EAUA;;;;EAGA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;;;;;;EAUA;;;;;;;;;;EASA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;;;;EAQA;;;;;;;;;EAQA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;EAQA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;EASA;;;;;;;;EAOA;;;;EAGA;;;;;;;;;;EASA;;;;;;;;EAOA;;;;;;;;;;;;EAWA;;;;;;;;;;;EAUA;;;;;;;;;;;EAWA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;;;;;EAQA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;;;EAOA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;;;;;;;;;;;EAaA;;;;;;;;EAOA;;;;EAGA;;;;;EAIA;;;;;;EAKA;;;;;;;EAMA;;;;;EAIA;;;;;;;;;;;EAUA;;;;;;;;;;;EAUA;;;;;;;;EAOA;IACE;;;;IAGA;;;;IAGA;;;;;EAIF;;;;EAGA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;;;;;EAUA;;;;EAMA;;;;;EAIA;;;;;;;;;;EASA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;;;;;;;EASA;;;;;;;;;;EASA;;;;EAGA;;;;;;;;EAOA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;;;;EAOA;;;;;;;;;;;;;;;;;;;;;EAoBA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;IACE;;;;;EAIF;;;;;;;EAMA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;;;;EAMA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;EAOA;;;;;;;;;;;;;;;;EAeA;;;;;;;EAMA;;;;;EAIA;;;;;;;;;EAQA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;;;EAOA;;;;;EAIA;;;;EAGA;;;;;;;;EAOA;;;;;EAIA;;;;EAGA;;;;;;;;EAOA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;;;;;;;;;;;;;;;EAmBA;;;;;;EAKA;;;;;EAIA;;;;;EAIA;;;;;;;;EAOA;;;;;;;;;EAQA;;;;;;;;;;EAQA;;;;;;;EAMA;;;;;;;;EAOA;;;;;EAIA;;;;;;;;;;;;;;;EAcA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;;;;EAaA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;;;;EAOA;;;;;;;;EAOA;;;;;;;;EAOA;;;;;;;;;;;;;EAaA;IACE;;;;IAGA;;;;IAGA;;;;IAGA;;;;;EAIF;;;;;;;EAMA;;;;EAGA;;;;;;;;;;;;;EAYA;;;;;;;;;;;;;EAaA;;;;EAGA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;;;;EAQA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;;;;EAUA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;;EAKA;;;;;;EAKA;;;;;;;;;;AAlgWF;;AAAA;EAuKE;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;;;EASA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;EAMI;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAOzB;IAA0B;;;;;EAK1B;IAA0B;;;;;EAK1B;IAA0B;;;;;EAK1B;IAA0B;;;;;EAK1B;IAA0B;;;;;;AA63T9B;;;;;;AAcA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA"}}]}