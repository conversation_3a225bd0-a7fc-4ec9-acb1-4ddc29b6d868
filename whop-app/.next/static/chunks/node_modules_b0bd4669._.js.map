{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/node_modules/%40swc/helpers/esm/_check_private_redeclaration.js"], "sourcesContent": ["function _check_private_redeclaration(obj, privateCollection) {\n    if (privateCollection.has(obj)) {\n        throw new TypeError(\"Cannot initialize the same private elements twice on an object\");\n    }\n}\nexport { _check_private_redeclaration as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,6BAA6B,GAAG,EAAE,iBAAiB;IACxD,IAAI,kBAAkB,GAAG,CAAC,MAAM;QAC5B,MAAM,IAAI,UAAU;IACxB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 242, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/node_modules/%40swc/helpers/esm/_class_private_field_init.js"], "sourcesContent": ["import { _ as _check_private_redeclaration } from \"./_check_private_redeclaration.js\";\n\nfunction _class_private_field_init(obj, privateMap, value) {\n    _check_private_redeclaration(obj, privateMap);\n    privateMap.set(obj, value);\n}\nexport { _class_private_field_init as _ };\n"], "names": [], "mappings": ";;;AAAA;;AAEA,SAAS,0BAA0B,GAAG,EAAE,UAAU,EAAE,KAAK;IACrD,CAAA,GAAA,kMAAA,CAAA,IAA4B,AAAD,EAAE,KAAK;IAClC,WAAW,GAAG,CAAC,KAAK;AACxB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 258, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/node_modules/%40swc/helpers/esm/_class_apply_descriptor_get.js"], "sourcesContent": ["function _class_apply_descriptor_get(receiver, descriptor) {\n    if (descriptor.get) return descriptor.get.call(receiver);\n\n    return descriptor.value;\n}\nexport { _class_apply_descriptor_get as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU;IACrD,IAAI,WAAW,GAAG,EAAE,OAAO,WAAW,GAAG,CAAC,IAAI,CAAC;IAE/C,OAAO,WAAW,KAAK;AAC3B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 272, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/node_modules/%40swc/helpers/esm/_class_extract_field_descriptor.js"], "sourcesContent": ["function _class_extract_field_descriptor(receiver, privateMap, action) {\n    if (!privateMap.has(receiver)) throw new TypeError(\"attempted to \" + action + \" private field on non-instance\");\n\n    return privateMap.get(receiver);\n}\nexport { _class_extract_field_descriptor as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,gCAAgC,QAAQ,EAAE,UAAU,EAAE,MAAM;IACjE,IAAI,CAAC,WAAW,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU,kBAAkB,SAAS;IAE9E,OAAO,WAAW,GAAG,CAAC;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 286, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/node_modules/%40swc/helpers/esm/_class_private_field_get.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_get } from \"./_class_apply_descriptor_get.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_get(receiver, privateMap) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"get\");\n    return _class_apply_descriptor_get(receiver, descriptor);\n}\nexport { _class_private_field_get as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU;IAClD,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,OAAO,CAAA,GAAA,iMAAA,CAAA,IAA2B,AAAD,EAAE,UAAU;AACjD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 304, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/node_modules/%40swc/helpers/esm/_class_apply_descriptor_set.js"], "sourcesContent": ["function _class_apply_descriptor_set(receiver, descriptor, value) {\n    if (descriptor.set) descriptor.set.call(receiver, value);\n    else {\n        if (!descriptor.writable) {\n            // This should only throw in strict mode, but class bodies are\n            // always strict and private fields can only be used inside\n            // class bodies.\n            throw new TypeError(\"attempted to set read only private field\");\n        }\n        descriptor.value = value;\n    }\n}\nexport { _class_apply_descriptor_set as _ };\n"], "names": [], "mappings": ";;;AAAA,SAAS,4BAA4B,QAAQ,EAAE,UAAU,EAAE,KAAK;IAC5D,IAAI,WAAW,GAAG,EAAE,WAAW,GAAG,CAAC,IAAI,CAAC,UAAU;SAC7C;QACD,IAAI,CAAC,WAAW,QAAQ,EAAE;YACtB,8DAA8D;YAC9D,2DAA2D;YAC3D,gBAAgB;YAChB,MAAM,IAAI,UAAU;QACxB;QACA,WAAW,KAAK,GAAG;IACvB;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 326, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/node_modules/%40swc/helpers/esm/_class_private_field_set.js"], "sourcesContent": ["import { _ as _class_apply_descriptor_set } from \"./_class_apply_descriptor_set.js\";\nimport { _ as _class_extract_field_descriptor } from \"./_class_extract_field_descriptor.js\";\n\nfunction _class_private_field_set(receiver, privateMap, value) {\n    var descriptor = _class_extract_field_descriptor(receiver, privateMap, \"set\");\n    _class_apply_descriptor_set(receiver, descriptor, value);\n    return value;\n}\nexport { _class_private_field_set as _ };\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,SAAS,yBAAyB,QAAQ,EAAE,UAAU,EAAE,KAAK;IACzD,IAAI,aAAa,CAAA,GAAA,qMAAA,CAAA,IAA+B,AAAD,EAAE,UAAU,YAAY;IACvE,CAAA,GAAA,iMAAA,CAAA,IAA2B,AAAD,EAAE,UAAU,YAAY;IAClD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 345, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/iframe/use-iframe-sdk.mjs"], "sourcesContent": ["import { use } from \"react\";\nimport { WhopIframeSdkContext } from \"./context.mjs\";\nexport function useIframeSdk() {\n    const sdk = use(WhopIframeSdkContext);\n    if (!sdk) {\n        throw new Error(\"useIframeSdk must be used within a WhopIframeSdkProvider\");\n    }\n    return sdk;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS;IACZ,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,MAAG,AAAD,EAAE,gKAAA,CAAA,uBAAoB;IACpC,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/iframe/index.mjs"], "sourcesContent": ["export * from \"./context.mjs\";\nexport * from \"./provider.mjs\";\nexport * from \"./use-iframe-sdk.mjs\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 389, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/theme/script.mjs"], "sourcesContent": ["export function script() {\n    const cookie = document.cookie.match(/whop-frosted-theme=appearance:(?<appearance>light|dark)/)?.groups;\n    const el = document.documentElement;\n    const classes = [\n        \"light\",\n        \"dark\"\n    ];\n    const theme = cookie ? cookie.appearance : getSystemTheme();\n    function updateDOM(theme) {\n        el.classList.remove(...classes);\n        el.classList.add(theme);\n        el.style.colorScheme = theme;\n    }\n    function getSystemTheme() {\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    }\n    updateDOM(theme);\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS;IACZ,MAAM,SAAS,SAAS,MAAM,CAAC,KAAK,CAAC,4DAA4D;IACjG,MAAM,KAAK,SAAS,eAAe;IACnC,MAAM,UAAU;QACZ;QACA;KACH;IACD,MAAM,QAAQ,SAAS,OAAO,UAAU,GAAG;IAC3C,SAAS,UAAU,KAAK;QACpB,GAAG,SAAS,CAAC,MAAM,IAAI;QACvB,GAAG,SAAS,CAAC,GAAG,CAAC;QACjB,GAAG,KAAK,CAAC,WAAW,GAAG;IAC3B;IACA,SAAS;QACL,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;IAChF;IACA,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 416, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/theme/index.mjs"], "sourcesContent": ["import React from \"react\";\nimport { script } from \"./script.mjs\";\nexport function WhopThemeScript() {\n    const scriptString = `(${script.toString()})()`;\n    return React.createElement(React.Fragment, null, React.createElement(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: scriptString\n        }\n    }));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS;IACZ,MAAM,eAAe,CAAC,CAAC,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;IAC/C,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAC3E,yBAAyB;YACrB,QAAQ;QACZ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 437, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/components/whop-app.mjs"], "sourcesContent": ["import React from \"react\";\nimport { Theme } from \"./index.mjs\";\nimport { WhopIframeSdkProvider } from \"../iframe/index.mjs\";\nimport { WhopThemeScript } from \"../theme/index.mjs\";\nexport function WhopApp({ children, sdkOptions, ...themeProps }) {\n    return React.createElement(React.Fragment, null, React.createElement(WhopThemeScript, null), React.createElement(WhopIframeSdkProvider, {\n        options: sdkOptions\n    }, React.createElement(Theme, themeProps, children)));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAAA;AACA;;;;;AACO,SAAS,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,YAAY;IAC3D,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,kBAAe,EAAE,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iKAAA,CAAA,wBAAqB,EAAE;QACpI,SAAS;IACb,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,wJAAA,CAAA,QAAK,EAAE,YAAY;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 461, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/components/index.mjs"], "sourcesContent": ["export * from \"frosted-ui\";\nexport { WhopApp } from \"./whop-app.mjs\";\n"], "names": [], "mappings": ";AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 655, "column": 0}, "map": {"version": 3, "file": "LocalizedStringDictionary.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/string/dist/packages/%40internationalized/string/src/LocalizedStringDictionary.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {LocalizedString} from './LocalizedStringFormatter';\n\nexport type LocalizedStrings<K extends string, T extends LocalizedString> = {\n  [lang: string]: Record<K, T>\n};\n\nconst localeSymbol = Symbol.for('react-aria.i18n.locale');\nconst stringsSymbol = Symbol.for('react-aria.i18n.strings');\nlet cachedGlobalStrings: {[packageName: string]: LocalizedStringDictionary<any, any>} | null | undefined = undefined;\n\n/**\n * Stores a mapping of localized strings. Can be used to find the\n * closest available string for a given locale.\n */\nexport class LocalizedStringDictionary<K extends string = string, T extends LocalizedString = string> {\n  private strings: LocalizedStrings<K, T>;\n  private defaultLocale: string;\n\n  constructor(messages: LocalizedStrings<K, T>, defaultLocale: string = 'en-US') {\n    // Clone messages so we don't modify the original object.\n    // Filter out entries with falsy values which may have been caused by applying optimize-locales-plugin.\n    this.strings = Object.fromEntries(\n      Object.entries(messages).filter(([, v]) => v)\n    );\n    this.defaultLocale = defaultLocale;\n  }\n\n  /** Returns a localized string for the given key and locale. */\n  getStringForLocale(key: K, locale: string): T {\n    let strings = this.getStringsForLocale(locale);\n    let string = strings[key];\n    if (!string) {\n      throw new Error(`Could not find intl message ${key} in ${locale} locale`);\n    }\n\n    return string;\n  }\n\n  /** Returns all localized strings for the given locale. */\n  getStringsForLocale(locale: string): Record<K, T> {\n    let strings = this.strings[locale];\n    if (!strings) {\n      strings = getStringsForLocale(locale, this.strings, this.defaultLocale);\n      this.strings[locale] = strings;\n    }\n\n    return strings;\n  }\n\n  static getGlobalDictionaryForPackage<K extends string = string, T extends LocalizedString = string>(packageName: string): LocalizedStringDictionary<K, T> | null {\n    if (typeof window === 'undefined') {\n      return null;\n    }\n\n    let locale = window[localeSymbol];\n    if (cachedGlobalStrings === undefined) {\n      let globalStrings = window[stringsSymbol];\n      if (!globalStrings) {\n        return null;\n      }\n\n      cachedGlobalStrings = {};\n      for (let pkg in globalStrings) {\n        cachedGlobalStrings[pkg] = new LocalizedStringDictionary({[locale]: globalStrings[pkg]}, locale);\n      }\n    }\n\n    let dictionary = cachedGlobalStrings?.[packageName];\n    if (!dictionary) {\n      throw new Error(`Strings for package \"${packageName}\" were not included by LocalizedStringProvider. Please add it to the list passed to createLocalizedStringDictionary.`);\n    }\n\n    return dictionary;\n  }\n}\n\nfunction getStringsForLocale<K extends string, T extends LocalizedString>(locale: string, strings: LocalizedStrings<K, T>, defaultLocale = 'en-US') {\n  // If there is an exact match, use it.\n  if (strings[locale]) {\n    return strings[locale];\n  }\n\n  // Attempt to find the closest match by language.\n  // For example, if the locale is fr-CA (French Canadian), but there is only\n  // an fr-FR (France) set of strings, use that.\n  // This could be replaced with Intl.LocaleMatcher once it is supported.\n  // https://github.com/tc39/proposal-intl-localematcher\n  let language = getLanguage(locale);\n  if (strings[language]) {\n    return strings[language];\n  }\n\n  for (let key in strings) {\n    if (key.startsWith(language + '-')) {\n      return strings[key];\n    }\n  }\n\n  // Nothing close, use english.\n  return strings[defaultLocale];\n}\n\nfunction getLanguage(locale: string) {\n  // @ts-ignore\n  if (Intl.Locale) {\n    // @ts-ignore\n    return new Intl.Locale(locale).language;\n  }\n\n  return locale.split('-')[0];\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAQD,MAAM,qCAAe,OAAO,GAAG,CAAC;AAChC,MAAM,sCAAgB,OAAO,GAAG,CAAC;AACjC,IAAI,4CAAuG;AAMpG,MAAM;IAaX,6DAA6D,GAC7D,mBAAmB,GAAM,EAAE,MAAc,EAAK;QAC5C,IAAI,UAAU,IAAI,CAAC,mBAAmB,CAAC;QACvC,IAAI,SAAS,OAAO,CAAC,IAAI;QACzB,IAAI,CAAC,QACH,MAAM,IAAI,MAAM,CAAC,4BAA4B,EAAE,IAAI,IAAI,EAAE,OAAO,OAAO,CAAC;QAG1E,OAAO;IACT;IAEA,wDAAwD,GACxD,oBAAoB,MAAc,EAAgB;QAChD,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,OAAO;QAClC,IAAI,CAAC,SAAS;YACZ,UAAU,0CAAoB,QAAQ,IAAI,CAAC,OAAO,EAAE,IAAI,CAAC,aAAa;YACtE,IAAI,CAAC,OAAO,CAAC,OAAO,GAAG;QACzB;QAEA,OAAO;IACT;IAEA,OAAO,8BAA6F,WAAmB,EAA0C;QAC/J,IAAI,OAAO,WAAW,aACpB,OAAO;QAGT,IAAI,SAAS,MAAM,CAAC,mCAAa;QACjC,IAAI,8CAAwB,WAAW;YACrC,IAAI,gBAAgB,MAAM,CAAC,oCAAc;YACzC,IAAI,CAAC,eACH,OAAO;YAGT,4CAAsB,CAAC;YACvB,IAAK,IAAI,OAAO,cACd,yCAAmB,CAAC,IAAI,GAAG,IAAI,0CAA0B;gBAAC,CAAC,OAAO,EAAE,aAAa,CAAC,IAAI;YAAA,GAAG;QAE7F;QAEA,IAAI,aAAa,8CAAA,QAAA,8CAAA,KAAA,IAAA,KAAA,IAAA,yCAAqB,CAAC,YAAY;QACnD,IAAI,CAAC,YACH,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,YAAY,oHAAoH,CAAC;QAG3K,OAAO;IACT;IAvDA,YAAY,QAAgC,EAAE,gBAAwB,OAAO,CAAE;QAC7E,yDAAyD;QACzD,uGAAuG;QACvG,IAAI,CAAC,OAAO,GAAG,OAAO,WAAW,CAC/B,OAAO,OAAO,CAAC,UAAU,MAAM,CAAC,CAAC,GAAG,EAAE,GAAK;QAE7C,IAAI,CAAC,aAAa,GAAG;IACvB;AAiDF;AAEA,SAAS,0CAAiE,MAAc,EAAE,OAA+B,EAAE,gBAAgB,OAAO;IAChJ,sCAAsC;IACtC,IAAI,OAAO,CAAC,OAAO,EACjB,OAAO,OAAO,CAAC,OAAO;IAGxB,iDAAiD;IACjD,2EAA2E;IAC3E,8CAA8C;IAC9C,uEAAuE;IACvE,sDAAsD;IACtD,IAAI,WAAW,kCAAY;IAC3B,IAAI,OAAO,CAAC,SAAS,EACnB,OAAO,OAAO,CAAC,SAAS;IAG1B,IAAK,IAAI,OAAO,QAAS;QACvB,IAAI,IAAI,UAAU,CAAC,WAAW,MAC5B,OAAO,OAAO,CAAC,IAAI;IAEvB;IAEA,8BAA8B;IAC9B,OAAO,OAAO,CAAC,cAAc;AAC/B;AAEA,SAAS,kCAAY,MAAc;IACjC,aAAa;IACb,IAAI,KAAK,MAAM,EACb,AACA,OAAO,IAAI,EADE,GACG,MAAM,CAAC,QAAQ,QAAQ;IAGzC,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;AAC7B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 737, "column": 0}, "map": {"version": 3, "file": "LocalizedStringFormatter.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/string/dist/packages/%40internationalized/string/src/LocalizedStringFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport type {LocalizedStringDictionary} from './LocalizedStringDictionary';\n\nexport type Variables = Record<string, string | number | boolean> | undefined;\nexport type LocalizedString = string | ((args: Variables, formatter?: LocalizedStringFormatter<any, any>) => string);\ntype InternalString = string | (() => string);\n\nconst pluralRulesCache = new Map<string, Intl.PluralRules>();\nconst numberFormatCache = new Map<string, Intl.NumberFormat>();\n\n/**\n * Formats localized strings from a LocalizedStringDictionary. Supports interpolating variables,\n * selecting the correct pluralization, and formatting numbers for the locale.\n */\nexport class LocalizedStringFormatter<K extends string = string, T extends LocalizedString = string> {\n  private locale: string;\n  private strings: LocalizedStringDictionary<K, T>;\n\n  constructor(locale: string, strings: LocalizedStringDictionary<K, T>) {\n    this.locale = locale;\n    this.strings = strings;\n  }\n\n  /** Formats a localized string for the given key with the provided variables. */\n  format(key: K, variables?: Variables): string {\n    let message = this.strings.getStringForLocale(key, this.locale);\n    return typeof message === 'function' ? message(variables, this) : message;\n  }\n\n  protected plural(count: number, options: Record<string, InternalString>, type: Intl.PluralRuleType = 'cardinal'): string {\n    let opt = options['=' + count];\n    if (opt) {\n      return typeof opt === 'function' ? opt() : opt;\n    }\n\n    let key = this.locale + ':' + type;\n    let pluralRules = pluralRulesCache.get(key);\n    if (!pluralRules) {\n      pluralRules = new Intl.PluralRules(this.locale, {type});\n      pluralRulesCache.set(key, pluralRules);\n    }\n\n    let selected = pluralRules.select(count);\n    opt = options[selected] || options.other;\n    return typeof opt === 'function' ? opt() : opt;\n  }\n\n  protected number(value: number): string {\n    let numberFormat = numberFormatCache.get(this.locale);\n    if (!numberFormat) {\n      numberFormat = new Intl.NumberFormat(this.locale);\n      numberFormatCache.set(this.locale, numberFormat);\n    }\n    return numberFormat.format(value);\n  }\n\n  protected select(options: Record<string, InternalString>, value: string): string {\n    let opt = options[value] || options.other;\n    return typeof opt === 'function' ? opt() : opt;\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAQD,MAAM,yCAAmB,IAAI;AAC7B,MAAM,0CAAoB,IAAI;AAMvB,MAAM;IASX,8EAA8E,GAC9E,OAAO,GAAM,EAAE,SAAqB,EAAU;QAC5C,IAAI,UAAU,IAAI,CAAC,OAAO,CAAC,kBAAkB,CAAC,KAAK,IAAI,CAAC,MAAM;QAC9D,OAAO,OAAO,YAAY,aAAa,QAAQ,WAAW,IAAI,IAAI;IACpE;IAEU,OAAO,KAAa,EAAE,OAAuC,EAAE,OAA4B,UAAU,EAAU;QACvH,IAAI,MAAM,OAAO,CAAC,MAAM,MAAM;QAC9B,IAAI,KACF,OAAO,OAAO,QAAQ,aAAa,QAAQ;QAG7C,IAAI,MAAM,IAAI,CAAC,MAAM,GAAG,MAAM;QAC9B,IAAI,cAAc,uCAAiB,GAAG,CAAC;QACvC,IAAI,CAAC,aAAa;YAChB,cAAc,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,MAAM,EAAE;sBAAC;YAAI;YACrD,uCAAiB,GAAG,CAAC,KAAK;QAC5B;QAEA,IAAI,WAAW,YAAY,MAAM,CAAC;QAClC,MAAM,OAAO,CAAC,SAAS,IAAI,QAAQ,KAAK;QACxC,OAAO,OAAO,QAAQ,aAAa,QAAQ;IAC7C;IAEU,OAAO,KAAa,EAAU;QACtC,IAAI,eAAe,wCAAkB,GAAG,CAAC,IAAI,CAAC,MAAM;QACpD,IAAI,CAAC,cAAc;YACjB,eAAe,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,MAAM;YAChD,wCAAkB,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE;QACrC;QACA,OAAO,aAAa,MAAM,CAAC;IAC7B;IAEU,OAAO,OAAuC,EAAE,KAAa,EAAU;QAC/E,IAAI,MAAM,OAAO,CAAC,MAAM,IAAI,QAAQ,KAAK;QACzC,OAAO,OAAO,QAAQ,aAAa,QAAQ;IAC7C;IAzCA,YAAY,MAAc,EAAE,OAAwC,CAAE;QACpE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACjB;AAuCF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 797, "column": 0}, "map": {"version": 3, "file": "NumberFormatter.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/number/dist/packages/%40internationalized/number/src/NumberFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet formatterCache = new Map<string, Intl.NumberFormat>();\n\nlet supportsSignDisplay = false;\ntry {\n  supportsSignDisplay = (new Intl.NumberFormat('de-DE', {signDisplay: 'exceptZero'})).resolvedOptions().signDisplay === 'exceptZero';\n  // eslint-disable-next-line no-empty\n} catch {}\n\nlet supportsUnit = false;\ntry {\n  supportsUnit = (new Intl.NumberFormat('de-DE', {style: 'unit', unit: 'degree'})).resolvedOptions().style === 'unit';\n  // eslint-disable-next-line no-empty\n} catch {}\n\n// Polyfill for units since Safari doesn't support them yet. See https://bugs.webkit.org/show_bug.cgi?id=215438.\n// Currently only polyfilling the unit degree in narrow format for ColorSlider in our supported locales.\n// Values were determined by switching to each locale manually in Chrome.\nconst UNITS = {\n  degree: {\n    narrow: {\n      default: '°',\n      'ja-JP': ' 度',\n      'zh-TW': '度',\n      'sl-SI': ' °'\n      // Arabic?? But Safari already doesn't use Arabic digits so might be ok...\n      // https://bugs.webkit.org/show_bug.cgi?id=218139\n    }\n  }\n};\n\nexport interface NumberFormatOptions extends Intl.NumberFormatOptions {\n  /** Overrides default numbering system for the current locale. */\n  numberingSystem?: string\n}\n\ninterface NumberRangeFormatPart extends Intl.NumberFormatPart {\n  source: 'startRange' | 'endRange' | 'shared'\n}\n\n/**\n * A wrapper around Intl.NumberFormat providing additional options, polyfills, and caching for performance.\n */\nexport class NumberFormatter implements Intl.NumberFormat {\n  private numberFormatter: Intl.NumberFormat;\n  private options: NumberFormatOptions;\n\n  constructor(locale: string, options: NumberFormatOptions = {}) {\n    this.numberFormatter = getCachedNumberFormatter(locale, options);\n    this.options = options;\n  }\n\n  /** Formats a number value as a string, according to the locale and options provided to the constructor. */\n  format(value: number): string {\n    let res = '';\n    if (!supportsSignDisplay && this.options.signDisplay != null) {\n      res = numberFormatSignDisplayPolyfill(this.numberFormatter, this.options.signDisplay, value);\n    } else {\n      res = this.numberFormatter.format(value);\n    }\n\n    if (this.options.style === 'unit' && !supportsUnit) {\n      let {unit, unitDisplay = 'short', locale} = this.resolvedOptions();\n      if (!unit) {\n        return res;\n      }\n      let values = UNITS[unit]?.[unitDisplay];\n      res += values[locale] || values.default;\n    }\n\n    return res;\n  }\n\n  /** Formats a number to an array of parts such as separators, digits, punctuation, and more. */\n  formatToParts(value: number): Intl.NumberFormatPart[] {\n    // TODO: implement signDisplay for formatToParts\n    return this.numberFormatter.formatToParts(value);\n  }\n\n  /** Formats a number range as a string. */\n  formatRange(start: number, end: number): string {\n    if (typeof this.numberFormatter.formatRange === 'function') {\n      return this.numberFormatter.formatRange(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    // Very basic fallback for old browsers.\n    return `${this.format(start)} – ${this.format(end)}`;\n  }\n\n  /** Formats a number range as an array of parts. */\n  formatRangeToParts(start: number, end: number): NumberRangeFormatPart[] {\n    if (typeof this.numberFormatter.formatRangeToParts === 'function') {\n      return this.numberFormatter.formatRangeToParts(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    let startParts = this.numberFormatter.formatToParts(start);\n    let endParts = this.numberFormatter.formatToParts(end);\n    return [\n      ...startParts.map(p => ({...p, source: 'startRange'} as NumberRangeFormatPart)),\n      {type: 'literal', value: ' – ', source: 'shared'},\n      ...endParts.map(p => ({...p, source: 'endRange'} as NumberRangeFormatPart))\n    ];\n  }\n\n  /** Returns the resolved formatting options based on the values passed to the constructor. */\n  resolvedOptions(): Intl.ResolvedNumberFormatOptions {\n    let options = this.numberFormatter.resolvedOptions();\n    if (!supportsSignDisplay && this.options.signDisplay != null) {\n      options = {...options, signDisplay: this.options.signDisplay};\n    }\n\n    if (!supportsUnit && this.options.style === 'unit') {\n      options = {...options, style: 'unit', unit: this.options.unit, unitDisplay: this.options.unitDisplay};\n    }\n\n    return options;\n  }\n}\n\nfunction getCachedNumberFormatter(locale: string, options: NumberFormatOptions = {}): Intl.NumberFormat {\n  let {numberingSystem} = options;\n  if (numberingSystem && locale.includes('-nu-')) {\n    if (!locale.includes('-u-')) {\n      locale += '-u-';\n    }\n    locale += `-nu-${numberingSystem}`;\n  }\n\n  if (options.style === 'unit' && !supportsUnit) {\n    let {unit, unitDisplay = 'short'} = options;\n    if (!unit) {\n      throw new Error('unit option must be provided with style: \"unit\"');\n    }\n    if (!UNITS[unit]?.[unitDisplay]) {\n      throw new Error(`Unsupported unit ${unit} with unitDisplay = ${unitDisplay}`);\n    }\n    options = {...options, style: 'decimal'};\n  }\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (formatterCache.has(cacheKey)) {\n    return formatterCache.get(cacheKey)!;\n  }\n\n  let numberFormatter = new Intl.NumberFormat(locale, options);\n  formatterCache.set(cacheKey, numberFormatter);\n  return numberFormatter;\n}\n\n/** @private - exported for tests */\nexport function numberFormatSignDisplayPolyfill(numberFormat: Intl.NumberFormat, signDisplay: string, num: number): string {\n  if (signDisplay === 'auto') {\n    return numberFormat.format(num);\n  } else if (signDisplay === 'never') {\n    return numberFormat.format(Math.abs(num));\n  } else {\n    let needsPositiveSign = false;\n    if (signDisplay === 'always') {\n      needsPositiveSign = num > 0 || Object.is(num, 0);\n    } else if (signDisplay === 'exceptZero') {\n      if (Object.is(num, -0) || Object.is(num, 0)) {\n        num = Math.abs(num);\n      } else {\n        needsPositiveSign = num > 0;\n      }\n    }\n\n    if (needsPositiveSign) {\n      let negative = numberFormat.format(-num);\n      let noSign = numberFormat.format(num);\n      // ignore RTL/LTR marker character\n      let minus = negative.replace(noSign, '').replace(/\\u200e|\\u061C/, '');\n      if ([...minus].length !== 1) {\n        console.warn('@react-aria/i18n polyfill for NumberFormat signDisplay: Unsupported case');\n      }\n      let positive = negative.replace(noSign, '!!!').replace(minus, '+').replace('!!!', noSign);\n      return positive;\n    } else {\n      return numberFormat.format(num);\n    }\n  }\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;AAED,IAAI,uCAAiB,IAAI;AAEzB,IAAI,4CAAsB;AAC1B,IAAI;IACF,4CAAuB,IAAI,KAAK,YAAY,CAAC,SAAS;QAAC,aAAa;IAAY,GAAI,eAAe,GAAG,WAAW,KAAK;AACtH,oCAAoC;AACtC,EAAE,OAAM,CAAC;AAET,IAAI,qCAAe;AACnB,IAAI;IACF,qCAAgB,IAAI,KAAK,YAAY,CAAC,SAAS;QAAC,OAAO;QAAQ,MAAM;IAAQ,GAAI,eAAe,GAAG,KAAK,KAAK;AAC7G,oCAAoC;AACtC,EAAE,OAAM,CAAC;AAET,gHAAgH;AAChH,wGAAwG;AACxG,yEAAyE;AACzE,MAAM,8BAAQ;IACZ,QAAQ;QACN,QAAQ;YACN,SAAS;YACT,SAAS;YACT,SAAS;YACT,SAAS;QAGX;IACF;AACF;AAcO,MAAM;IASX,yGAAyG,GACzG,OAAO,KAAa,EAAU;QAC5B,IAAI,MAAM;QACV,IAAI,CAAC,6CAAuB,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,MACtD,MAAM,0CAAgC,IAAI,CAAC,eAAe,EAAE,IAAI,CAAC,OAAO,CAAC,WAAW,EAAE;aAEtF,MAAM,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC;QAGpC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,UAAU,CAAC,oCAAc;gBAKrC;YAJb,IAAI,EAAA,MAAC,IAAI,EAAA,aAAE,cAAc,OAAA,EAAA,QAAS,MAAM,EAAC,GAAG,IAAI,CAAC,eAAe;YAChE,IAAI,CAAC,MACH,OAAO;YAET,IAAI,SAAA,CAAS,cAAA,2BAAK,CAAC,KAAK,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,WAAa,CAAC,YAAY;YACvC,OAAO,MAAM,CAAC,OAAO,IAAI,OAAO,OAAO;QACzC;QAEA,OAAO;IACT;IAEA,6FAA6F,GAC7F,cAAc,KAAa,EAA2B;QACpD,gDAAgD;QAChD,OAAO,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;IAC5C;IAEA,wCAAwC,GACxC,YAAY,KAAa,EAAE,GAAW,EAAU;QAC9C,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,KAAK,YAC9C,OAAO,IAAI,CAAC,eAAe,CAAC,WAAW,CAAC,OAAO;QAGjD,IAAI,MAAM,OACR,MAAM,IAAI,WAAW;QAGvB,wCAAwC;QACxC,OAAO,GAAG,IAAI,CAAC,MAAM,CAAC,OAAO,UAAG,EAAE,IAAI,CAAC,MAAM,CAAC,MAAM;IACtD;IAEA,iDAAiD,GACjD,mBAAmB,KAAa,EAAE,GAAW,EAA2B;QACtE,IAAI,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,KAAK,YACrD,OAAO,IAAI,CAAC,eAAe,CAAC,kBAAkB,CAAC,OAAO;QAGxD,IAAI,MAAM,OACR,MAAM,IAAI,WAAW;QAGvB,IAAI,aAAa,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;QACpD,IAAI,WAAW,IAAI,CAAC,eAAe,CAAC,aAAa,CAAC;QAClD,OAAO;eACF,WAAW,GAAG,CAAC,CAAA,IAAM,CAAA;oBAAC,GAAG,CAAC;oBAAE,QAAQ;gBAAY,CAAA;YACnD;gBAAC,MAAM;gBAAW,OAAO;gBAAO,QAAQ;YAAQ;eAC7C,SAAS,GAAG,CAAC,CAAA,IAAM,CAAA;oBAAC,GAAG,CAAC;oBAAE,QAAQ;gBAAU,CAAA;SAChD;IACH;IAEA,2FAA2F,GAC3F,kBAAoD;QAClD,IAAI,UAAU,IAAI,CAAC,eAAe,CAAC,eAAe;QAClD,IAAI,CAAC,6CAAuB,IAAI,CAAC,OAAO,CAAC,WAAW,IAAI,MACtD,UAAU;YAAC,GAAG,OAAO;YAAE,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW;QAAA;QAG9D,IAAI,CAAC,sCAAgB,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,QAC1C,UAAU;YAAC,GAAG,OAAO;YAAE,OAAO;YAAQ,MAAM,IAAI,CAAC,OAAO,CAAC,IAAI;YAAE,aAAa,IAAI,CAAC,OAAO,CAAC,WAAW;QAAA;QAGtG,OAAO;IACT;IA7EA,YAAY,MAAc,EAAE,UAA+B,CAAC,CAAC,CAAE;QAC7D,IAAI,CAAC,eAAe,GAAG,+CAAyB,QAAQ;QACxD,IAAI,CAAC,OAAO,GAAG;IACjB;AA2EF;AAEA,SAAS,+CAAyB,MAAc,EAAE,UAA+B,CAAC,CAAC;IACjF,IAAI,EAAA,iBAAC,eAAe,EAAC,GAAG;IACxB,IAAI,mBAAmB,OAAO,QAAQ,CAAC,SAAS;QAC9C,IAAI,CAAC,OAAO,QAAQ,CAAC,QACnB,UAAU;QAEZ,UAAU,CAAC,IAAI,EAAE,iBAAiB;IACpC;IAEA,IAAI,QAAQ,KAAK,KAAK,UAAU,CAAC,oCAAc;YAKxC;QAJL,IAAI,EAAA,MAAC,IAAI,EAAA,aAAE,cAAc,OAAA,EAAQ,GAAG;QACpC,IAAI,CAAC,MACH,MAAM,IAAI,MAAM;QAElB,IAAI,CAAA,CAAA,CAAC,cAAA,2BAAK,CAAC,KAAK,MAAA,QAAX,gBAAA,KAAA,IAAA,KAAA,IAAA,WAAa,CAAC,YAAY,GAC7B,MAAM,IAAI,MAAM,CAAC,iBAAiB,EAAE,KAAK,oBAAoB,EAAE,aAAa;QAE9E,UAAU;YAAC,GAAG,OAAO;YAAE,OAAO;QAAS;IACzC;IAEA,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAA,IAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,qCAAe,GAAG,CAAC,WACrB,OAAO,qCAAe,GAAG,CAAC;IAG5B,IAAI,kBAAkB,IAAI,KAAK,YAAY,CAAC,QAAQ;IACpD,qCAAe,GAAG,CAAC,UAAU;IAC7B,OAAO;AACT;AAGO,SAAS,0CAAgC,YAA+B,EAAE,WAAmB,EAAE,GAAW;IAC/G,IAAI,gBAAgB,QAClB,OAAO,aAAa,MAAM,CAAC;SACtB,IAAI,gBAAgB,SACzB,OAAO,aAAa,MAAM,CAAC,KAAK,GAAG,CAAC;SAC/B;QACL,IAAI,oBAAoB;QACxB,IAAI,gBAAgB,UAClB,oBAAoB,MAAM,KAAK,OAAO,EAAE,CAAC,KAAK;aACzC,IAAI,gBAAgB,cAAA;YACzB,IAAI,OAAO,EAAE,CAAC,KAAK,CAAA,MAAO,OAAO,EAAE,CAAC,KAAK,IACvC,MAAM,KAAK,GAAG,CAAC;iBAEf,oBAAoB,MAAM;;QAI9B,IAAI,mBAAmB;YACrB,IAAI,WAAW,aAAa,MAAM,CAAC,CAAC;YACpC,IAAI,SAAS,aAAa,MAAM,CAAC;YACjC,kCAAkC;YAClC,IAAI,QAAQ,SAAS,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,iBAAiB;YAClE,IAAI;mBAAI;aAAM,CAAC,MAAM,KAAK,GACxB,QAAQ,IAAI,CAAC;YAEf,IAAI,WAAW,SAAS,OAAO,CAAC,QAAQ,OAAO,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,OAAO;YAClF,OAAO;QACT,OACE,OAAO,aAAa,MAAM,CAAC;IAE/B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "file": "NumberParser.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/number/dist/packages/%40internationalized/number/src/NumberParser.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {NumberFormatter} from './NumberFormatter';\n\ninterface Symbols {\n  minusSign?: string,\n  plusSign?: string,\n  decimal?: string,\n  group?: string,\n  literals: RegExp,\n  numeral: RegExp,\n  index: (v: string) => string\n}\n\nconst CURRENCY_SIGN_REGEX = new RegExp('^.*\\\\(.*\\\\).*$');\nconst NUMBERING_SYSTEMS = ['latn', 'arab', 'hanidec', 'deva', 'beng', 'fullwide'];\n\n/**\n * A NumberParser can be used to perform locale-aware parsing of numbers from Unicode strings,\n * as well as validation of partial user input. It automatically detects the numbering system\n * used in the input, and supports parsing decimals, percentages, currency values, and units\n * according to the locale.\n */\nexport class NumberParser {\n  private locale: string;\n  private options: Intl.NumberFormatOptions;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    this.options = options;\n  }\n\n  /**\n   * Parses the given string to a number. Returns NaN if a valid number could not be parsed.\n   */\n  parse(value: string): number {\n    return getNumberParserImpl(this.locale, this.options, value).parse(value);\n  }\n\n  /**\n   * Returns whether the given string could potentially be a valid number. This should be used to\n   * validate user input as the user types. If a `minValue` or `maxValue` is provided, the validity\n   * of the minus/plus sign characters can be checked.\n   */\n  isValidPartialNumber(value: string, minValue?: number, maxValue?: number): boolean {\n    return getNumberParserImpl(this.locale, this.options, value).isValidPartialNumber(value, minValue, maxValue);\n  }\n\n  /**\n   * Returns a numbering system for which the given string is valid in the current locale.\n   * If no numbering system could be detected, the default numbering system for the current\n   * locale is returned.\n   */\n  getNumberingSystem(value: string): string {\n    return getNumberParserImpl(this.locale, this.options, value).options.numberingSystem;\n  }\n}\n\nconst numberParserCache = new Map<string, NumberParserImpl>();\nfunction getNumberParserImpl(locale: string, options: Intl.NumberFormatOptions, value: string) {\n  // First try the default numbering system for the provided locale\n  let defaultParser = getCachedNumberParser(locale, options);\n\n  // If that doesn't match, and the locale doesn't include a hard coded numbering system,\n  // try each of the other supported numbering systems until we find one that matches.\n  if (!locale.includes('-nu-') && !defaultParser.isValidPartialNumber(value)) {\n    for (let numberingSystem of NUMBERING_SYSTEMS) {\n      if (numberingSystem !== defaultParser.options.numberingSystem) {\n        let parser = getCachedNumberParser(locale + (locale.includes('-u-') ? '-nu-' : '-u-nu-') + numberingSystem, options);\n        if (parser.isValidPartialNumber(value)) {\n          return parser;\n        }\n      }\n    }\n  }\n\n  return defaultParser;\n}\n\nfunction getCachedNumberParser(locale: string, options: Intl.NumberFormatOptions) {\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  let parser = numberParserCache.get(cacheKey);\n  if (!parser) {\n    parser = new NumberParserImpl(locale, options);\n    numberParserCache.set(cacheKey, parser);\n  }\n\n  return parser;\n}\n\n// The actual number parser implementation. Instances of this class are cached\n// based on the locale, options, and detected numbering system.\nclass NumberParserImpl {\n  formatter: Intl.NumberFormat;\n  options: Intl.ResolvedNumberFormatOptions;\n  symbols: Symbols;\n  locale: string;\n\n  constructor(locale: string, options: Intl.NumberFormatOptions = {}) {\n    this.locale = locale;\n    // see https://tc39.es/ecma402/#sec-setnfdigitoptions, when using roundingIncrement, the maximumFractionDigits and minimumFractionDigits must be equal\n    // by default, they are 0 and 3 respectively, so we set them to 0 if neither are set\n    if (options.roundingIncrement !== 1 && options.roundingIncrement != null) {\n      if (options.maximumFractionDigits == null && options.minimumFractionDigits == null) {\n        options.maximumFractionDigits = 0;\n        options.minimumFractionDigits = 0;\n      } else if (options.maximumFractionDigits == null) {\n        options.maximumFractionDigits = options.minimumFractionDigits;\n      } else if (options.minimumFractionDigits == null) {\n        options.minimumFractionDigits = options.maximumFractionDigits;\n      }\n      // if both are specified, let the normal Range Error be thrown\n    }\n    this.formatter = new Intl.NumberFormat(locale, options);\n    this.options = this.formatter.resolvedOptions();\n    this.symbols = getSymbols(locale, this.formatter, this.options, options);\n    if (this.options.style === 'percent' && ((this.options.minimumFractionDigits ?? 0) > 18 || (this.options.maximumFractionDigits ?? 0) > 18)) {\n      console.warn('NumberParser cannot handle percentages with greater than 18 decimal places, please reduce the number in your options.');\n    }\n  }\n\n  parse(value: string) {\n    // to parse the number, we need to remove anything that isn't actually part of the number, for example we want '-10.40' not '-10.40 USD'\n    let fullySanitizedValue = this.sanitize(value);\n\n    if (this.symbols.group) {\n      // Remove group characters, and replace decimal points and numerals with ASCII values.\n      fullySanitizedValue = replaceAll(fullySanitizedValue, this.symbols.group, '');\n    }\n    if (this.symbols.decimal) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.decimal!, '.');\n    }\n    if (this.symbols.minusSign) {\n      fullySanitizedValue = fullySanitizedValue.replace(this.symbols.minusSign!, '-');\n    }\n    fullySanitizedValue = fullySanitizedValue.replace(this.symbols.numeral, this.symbols.index);\n\n    if (this.options.style === 'percent') {\n      // javascript is bad at dividing by 100 and maintaining the same significant figures, so perform it on the string before parsing\n      let isNegative = fullySanitizedValue.indexOf('-');\n      fullySanitizedValue = fullySanitizedValue.replace('-', '');\n      fullySanitizedValue = fullySanitizedValue.replace('+', '');\n      let index = fullySanitizedValue.indexOf('.');\n      if (index === -1) {\n        index = fullySanitizedValue.length;\n      }\n      fullySanitizedValue = fullySanitizedValue.replace('.', '');\n      if (index - 2 === 0) {\n        fullySanitizedValue = `0.${fullySanitizedValue}`;\n      } else if (index - 2 === -1) {\n        fullySanitizedValue = `0.0${fullySanitizedValue}`;\n      } else if (index - 2 === -2) {\n        fullySanitizedValue = '0.00';\n      } else {\n        fullySanitizedValue = `${fullySanitizedValue.slice(0, index - 2)}.${fullySanitizedValue.slice(index - 2)}`;\n      }\n      if (isNegative > -1) {\n        fullySanitizedValue = `-${fullySanitizedValue}`;\n      }\n    }\n\n    let newValue = fullySanitizedValue ? +fullySanitizedValue : NaN;\n    if (isNaN(newValue)) {\n      return NaN;\n    }\n\n    if (this.options.style === 'percent') {\n      // extra step for rounding percents to what our formatter would output\n      let options = {\n        ...this.options,\n        style: 'decimal' as const,\n        minimumFractionDigits: Math.min((this.options.minimumFractionDigits ?? 0) + 2, 20),\n        maximumFractionDigits: Math.min((this.options.maximumFractionDigits ?? 0) + 2, 20)\n      };\n      return (new NumberParser(this.locale, options)).parse(new NumberFormatter(this.locale, options).format(newValue));\n    }\n\n    // accounting will always be stripped to a positive number, so if it's accounting and has a () around everything, then we need to make it negative again\n    if (this.options.currencySign === 'accounting' && CURRENCY_SIGN_REGEX.test(value)) {\n      newValue = -1 * newValue;\n    }\n\n    return newValue;\n  }\n\n  sanitize(value: string) {\n    // Remove literals and whitespace, which are allowed anywhere in the string\n    value = value.replace(this.symbols.literals, '');\n\n    // Replace the ASCII minus sign with the minus sign used in the current locale\n    // so that both are allowed in case the user's keyboard doesn't have the locale's minus sign.\n    if (this.symbols.minusSign) {\n      value = value.replace('-', this.symbols.minusSign);\n    }\n\n    // In arab numeral system, their decimal character is 1643, but most keyboards don't type that\n    // instead they use the , (44) character or apparently the (1548) character.\n    if (this.options.numberingSystem === 'arab') {\n      if (this.symbols.decimal) {\n        value = value.replace(',', this.symbols.decimal);\n        value = value.replace(String.fromCharCode(1548), this.symbols.decimal);\n      }\n      if (this.symbols.group) {\n        value = replaceAll(value, '.', this.symbols.group);\n      }\n    }\n\n    // In some locale styles, such as swiss currency, the group character can be a special single quote\n    // that keyboards don't typically have. This expands the character to include the easier to type single quote.\n    if (this.symbols.group === '’' && value.includes(\"'\")) {\n      value = replaceAll(value, \"'\", this.symbols.group);\n    }\n\n    // fr-FR group character is narrow non-breaking space, char code 8239 (U+202F), but that's not a key on the french keyboard,\n    // so allow space and non-breaking space as a group char as well\n    if (this.options.locale === 'fr-FR' && this.symbols.group) {\n      value = replaceAll(value, ' ', this.symbols.group);\n      value = replaceAll(value, /\\u00A0/g, this.symbols.group);\n    }\n\n    return value;\n  }\n\n  isValidPartialNumber(value: string, minValue: number = -Infinity, maxValue: number = Infinity): boolean {\n    value = this.sanitize(value);\n\n    // Remove minus or plus sign, which must be at the start of the string.\n    if (this.symbols.minusSign && value.startsWith(this.symbols.minusSign) && minValue < 0) {\n      value = value.slice(this.symbols.minusSign.length);\n    } else if (this.symbols.plusSign && value.startsWith(this.symbols.plusSign) && maxValue > 0) {\n      value = value.slice(this.symbols.plusSign.length);\n    }\n\n    // Numbers cannot start with a group separator\n    if (this.symbols.group && value.startsWith(this.symbols.group)) {\n      return false;\n    }\n\n    // Numbers that can't have any decimal values fail if a decimal character is typed\n    if (this.symbols.decimal && value.indexOf(this.symbols.decimal) > -1 && this.options.maximumFractionDigits === 0) {\n      return false;\n    }\n\n    // Remove numerals, groups, and decimals\n    if (this.symbols.group) {\n      value = replaceAll(value, this.symbols.group, '');\n    }\n    value = value.replace(this.symbols.numeral, '');\n    if (this.symbols.decimal) {\n      value = value.replace(this.symbols.decimal, '');\n    }\n\n    // The number is valid if there are no remaining characters\n    return value.length === 0;\n  }\n}\n\nconst nonLiteralParts = new Set(['decimal', 'fraction', 'integer', 'minusSign', 'plusSign', 'group']);\n\n// This list is derived from https://www.unicode.org/cldr/charts/43/supplemental/language_plural_rules.html#comparison and includes\n// all unique numbers which we need to check in order to determine all the plural forms for a given locale.\n// See: https://github.com/adobe/react-spectrum/pull/5134/files#r1337037855 for used script\nconst pluralNumbers = [\n  0, 4, 2, 1, 11, 20, 3, 7, 100, 21, 0.1, 1.1\n];\n\nfunction getSymbols(locale: string, formatter: Intl.NumberFormat, intlOptions: Intl.ResolvedNumberFormatOptions, originalOptions: Intl.NumberFormatOptions): Symbols {\n  // formatter needs access to all decimal places in order to generate the correct literal strings for the plural set\n  let symbolFormatter = new Intl.NumberFormat(locale, {...intlOptions,\n    // Resets so we get the full range of symbols\n    minimumSignificantDigits: 1,\n    maximumSignificantDigits: 21,\n    roundingIncrement: 1,\n    roundingPriority: 'auto',\n    roundingMode: 'halfExpand'\n  });\n  // Note: some locale's don't add a group symbol until there is a ten thousands place\n  let allParts = symbolFormatter.formatToParts(-10000.111);\n  let posAllParts = symbolFormatter.formatToParts(10000.111);\n  let pluralParts = pluralNumbers.map(n => symbolFormatter.formatToParts(n));\n\n  let minusSign = allParts.find(p => p.type === 'minusSign')?.value ?? '-';\n  let plusSign = posAllParts.find(p => p.type === 'plusSign')?.value;\n\n  // Safari does not support the signDisplay option, but our number parser polyfills it.\n  // If no plus sign was returned, but the original options contained signDisplay, default to the '+' character.\n  if (!plusSign && (originalOptions?.signDisplay === 'exceptZero' || originalOptions?.signDisplay === 'always')) {\n    plusSign = '+';\n  }\n\n  // If maximumSignificantDigits is 1 (the minimum) then we won't get decimal characters out of the above formatters\n  // Percent also defaults to 0 fractionDigits, so we need to make a new one that isn't percent to get an accurate decimal\n  let decimalParts = new Intl.NumberFormat(locale, {...intlOptions, minimumFractionDigits: 2, maximumFractionDigits: 2}).formatToParts(0.001);\n\n  let decimal = decimalParts.find(p => p.type === 'decimal')?.value;\n  let group = allParts.find(p => p.type === 'group')?.value;\n\n  // this set is also for a regex, it's all literals that might be in the string we want to eventually parse that\n  // don't contribute to the numerical value\n  let allPartsLiterals = allParts.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value));\n  let pluralPartsLiterals = pluralParts.flatMap(p => p.filter(p => !nonLiteralParts.has(p.type)).map(p => escapeRegex(p.value)));\n  let sortedLiterals = [...new Set([...allPartsLiterals, ...pluralPartsLiterals])].sort((a, b) => b.length - a.length);\n\n  let literals = sortedLiterals.length === 0 ?\n      new RegExp('[\\\\p{White_Space}]', 'gu') :\n      new RegExp(`${sortedLiterals.join('|')}|[\\\\p{White_Space}]`, 'gu');\n\n  // These are for replacing non-latn characters with the latn equivalent\n  let numerals = [...new Intl.NumberFormat(intlOptions.locale, {useGrouping: false}).format(9876543210)].reverse();\n  let indexes = new Map(numerals.map((d, i) => [d, i]));\n  let numeral = new RegExp(`[${numerals.join('')}]`, 'g');\n  let index = d => String(indexes.get(d));\n\n  return {minusSign, plusSign, decimal, group, literals, numeral, index};\n}\n\nfunction replaceAll(str: string, find: string | RegExp, replace: string) {\n  if (str.replaceAll) {\n    return str.replaceAll(find, replace);\n  }\n\n  return str.split(find).join(replace);\n}\n\nfunction escapeRegex(string: string) {\n  return string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&');\n}\n"], "names": [], "mappings": ";;;;;AAAA;;;;;;;;;;CAUC,GAcD,MAAM,4CAAsB,IAAI,OAAO;AACvC,MAAM,0CAAoB;IAAC;IAAQ;IAAQ;IAAW;IAAQ;IAAQ;CAAW;AAQ1E,MAAM;IASX;;GAEC,GACD,MAAM,KAAa,EAAU;QAC3B,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,KAAK,CAAC;IACrE;IAEA;;;;GAIC,GACD,qBAAqB,KAAa,EAAE,QAAiB,EAAE,QAAiB,EAAW;QACjF,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,oBAAoB,CAAC,OAAO,UAAU;IACrG;IAEA;;;;GAIC,GACD,mBAAmB,KAAa,EAAU;QACxC,OAAO,0CAAoB,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,OAAO,EAAE,OAAO,OAAO,CAAC,eAAe;IACtF;IA5BA,YAAY,MAAc,EAAE,UAAoC,CAAC,CAAC,CAAE;QAClE,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,OAAO,GAAG;IACjB;AA0BF;AAEA,MAAM,0CAAoB,IAAI;AAC9B,SAAS,0CAAoB,MAAc,EAAE,OAAiC,EAAE,KAAa;IAC3F,iEAAiE;IACjE,IAAI,gBAAgB,4CAAsB,QAAQ;IAElD,uFAAuF;IACvF,oFAAoF;IACpF,IAAI,CAAC,OAAO,QAAQ,CAAC,WAAW,CAAC,cAAc,oBAAoB,CAAC,QAAQ;QAC1E,KAAK,IAAI,mBAAmB,wCAC1B,IAAI,oBAAoB,cAAc,OAAO,CAAC,eAAe,EAAE;YAC7D,IAAI,SAAS,4CAAsB,SAAU,CAAA,OAAO,QAAQ,CAAC,SAAS,SAAS,QAAO,IAAK,iBAAiB;YAC5G,IAAI,OAAO,oBAAoB,CAAC,QAC9B,OAAO;QAEX;IAEJ;IAEA,OAAO;AACT;AAEA,SAAS,4CAAsB,MAAc,EAAE,OAAiC;IAC9E,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAA,IAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,SAAS,wCAAkB,GAAG,CAAC;IACnC,IAAI,CAAC,QAAQ;QACX,SAAS,IAAI,uCAAiB,QAAQ;QACtC,wCAAkB,GAAG,CAAC,UAAU;IAClC;IAEA,OAAO;AACT;AAEA,8EAA8E;AAC9E,+DAA+D;AAC/D,MAAM;IA6BJ,MAAM,KAAa,EAAE;QACnB,wIAAwI;QACxI,IAAI,sBAAsB,IAAI,CAAC,QAAQ,CAAC;QAExC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EACpB,AACA,sBAAsB,iCAAW,qBAAqB,IAAI,CAAC,KAD2B,EACpB,CAAC,KAAK,EAAE;QAE5E,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EACtB,sBAAsB,oBAAoB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAG;QAE3E,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EACxB,sBAAsB,oBAAoB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,EAAG;QAE7E,sBAAsB,oBAAoB,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE,IAAI,CAAC,OAAO,CAAC,KAAK;QAE1F,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;YACpC,gIAAgI;YAChI,IAAI,aAAa,oBAAoB,OAAO,CAAC;YAC7C,sBAAsB,oBAAoB,OAAO,CAAC,KAAK;YACvD,sBAAsB,oBAAoB,OAAO,CAAC,KAAK;YACvD,IAAI,QAAQ,oBAAoB,OAAO,CAAC;YACxC,IAAI,UAAU,CAAA,GACZ,QAAQ,oBAAoB,MAAM;YAEpC,sBAAsB,oBAAoB,OAAO,CAAC,KAAK;YACvD,IAAI,QAAQ,MAAM,GAChB,sBAAsB,CAAC,EAAE,EAAE,qBAAqB;iBAC3C,IAAI,QAAQ,MAAM,CAAA,GACvB,sBAAsB,CAAC,GAAG,EAAE,qBAAqB;iBAC5C,IAAI,QAAQ,MAAM,CAAA,GACvB,sBAAsB;iBAEtB,sBAAsB,GAAG,oBAAoB,KAAK,CAAC,GAAG,QAAQ,GAAG,CAAC,EAAE,oBAAoB,KAAK,CAAC,QAAQ,IAAI;YAE5G,IAAI,aAAa,CAAA,GACf,sBAAsB,CAAC,CAAC,EAAE,qBAAqB;QAEnD;QAEA,IAAI,WAAW,sBAAsB,CAAC,sBAAsB;QAC5D,IAAI,MAAM,WACR,OAAO;QAGT,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,WAAW;gBAKD,qCACA;YALnC,sEAAsE;YACtE,IAAI,UAAU;gBACZ,GAAG,IAAI,CAAC,OAAO;gBACf,OAAO;gBACP,uBAAuB,KAAK,GAAG,CAAE,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,MAAA,QAAlC,wCAAA,KAAA,IAAA,sCAAsC,CAAA,IAAK,GAAG;gBAC/E,uBAAuB,KAAK,GAAG,CAAE,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,MAAA,QAAlC,wCAAA,KAAA,IAAA,sCAAsC,CAAA,IAAK,GAAG;YACjF;YACA,OAAQ,IAAI,0CAAa,IAAI,CAAC,MAAM,EAAE,SAAU,KAAK,CAAC,IAAI,CAAA,gLAAA,kBAAc,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,MAAM,CAAC;QACzG;QAEA,wJAAwJ;QACxJ,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,KAAK,gBAAgB,0CAAoB,IAAI,CAAC,QACzE,WAAW,CAAA,IAAK;QAGlB,OAAO;IACT;IAEA,SAAS,KAAa,EAAE;QACtB,2EAA2E;QAC3E,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE;QAE7C,8EAA8E;QAC9E,6FAA6F;QAC7F,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,EACxB,QAAQ,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,SAAS;QAGnD,8FAA8F;QAC9F,4EAA4E;QAC5E,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,KAAK,QAAQ;YAC3C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;gBACxB,QAAQ,MAAM,OAAO,CAAC,KAAK,IAAI,CAAC,OAAO,CAAC,OAAO;gBAC/C,QAAQ,MAAM,OAAO,CAAC,OAAO,YAAY,CAAC,OAAO,IAAI,CAAC,OAAO,CAAC,OAAO;YACvE;YACA,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EACpB,QAAQ,iCAAW,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;QAErD;QAEA,mGAAmG;QACnG,8GAA8G;QAC9G,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,YAAO,MAAM,QAAQ,CAAC,MAC/C,QAAQ,iCAAW,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;QAGnD,4HAA4H;QAC5H,gEAAgE;QAChE,IAAI,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;YACzD,QAAQ,iCAAW,OAAO,KAAK,IAAI,CAAC,OAAO,CAAC,KAAK;YACjD,QAAQ,iCAAW,OAAO,WAAW,IAAI,CAAC,OAAO,CAAC,KAAK;QACzD;QAEA,OAAO;IACT;IAEA,qBAAqB,KAAa,EAAE,WAAmB,CAAC,QAAQ,EAAE,WAAmB,QAAQ,EAAW;QACtG,QAAQ,IAAI,CAAC,QAAQ,CAAC;QAEtB,uEAAuE;QACvE,IAAI,IAAI,CAAC,OAAO,CAAC,SAAS,IAAI,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,KAAK,WAAW,GACnF,QAAQ,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,SAAS,CAAC,MAAM;aAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,QAAQ,IAAI,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,KAAK,WAAW,GACxF,QAAQ,MAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,MAAM;QAGlD,8CAA8C;QAC9C,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,IAAI,MAAM,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,GAC3D,OAAO;QAGT,kFAAkF;QAClF,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,IAAI,CAAA,KAAM,IAAI,CAAC,OAAO,CAAC,qBAAqB,KAAK,GAC7G,OAAO;QAGT,wCAAwC;QACxC,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,EACpB,QAAQ,iCAAW,OAAO,IAAI,CAAC,OAAO,CAAC,KAAK,EAAE;QAEhD,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;QAC5C,IAAI,IAAI,CAAC,OAAO,CAAC,OAAO,EACtB,QAAQ,MAAM,OAAO,CAAC,IAAI,CAAC,OAAO,CAAC,OAAO,EAAE;QAG9C,2DAA2D;QAC3D,OAAO,MAAM,MAAM,KAAK;IAC1B;IA5JA,YAAY,MAAc,EAAE,UAAoC,CAAC,CAAC,CAAE;QAClE,IAAI,CAAC,MAAM,GAAG;QACd,sJAAsJ;QACtJ,oFAAoF;QACpF,IAAI,QAAQ,iBAAiB,KAAK,KAAK,QAAQ,iBAAiB,IAAI,MAAM;YACxE,IAAI,QAAQ,qBAAqB,IAAI,QAAQ,QAAQ,qBAAqB,IAAI,MAAM;gBAClF,QAAQ,qBAAqB,GAAG;gBAChC,QAAQ,qBAAqB,GAAG;YAClC,OAAO,IAAI,QAAQ,qBAAqB,IAAI,MAC1C,QAAQ,qBAAqB,GAAG,QAAQ,qBAAqB;iBACxD,IAAI,QAAQ,qBAAqB,IAAI,MAC1C,QAAQ,qBAAqB,GAAG,QAAQ,qBAAqB;QAE/D,8DAA8D;QAChE;QACA,IAAI,CAAC,SAAS,GAAG,IAAI,KAAK,YAAY,CAAC,QAAQ;QAC/C,IAAI,CAAC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,eAAe;QAC7C,IAAI,CAAC,OAAO,GAAG,iCAAW,QAAQ,IAAI,CAAC,SAAS,EAAE,IAAI,CAAC,OAAO,EAAE;YACtB,qCAAkD;QAA5F,IAAI,IAAI,CAAC,OAAO,CAAC,KAAK,KAAK,aAAc,CAAC,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,MAAA,QAAlC,wCAAA,KAAA,IAAA,sCAAsC,CAAA,IAAK,MAAO,CAAA,CAAA,sCAAA,IAAI,CAAC,OAAO,CAAC,qBAAqB,MAAA,QAAlC,wCAAA,KAAA,IAAA,sCAAsC,CAAA,IAAK,EAAC,GACtI,QAAQ,IAAI,CAAC;IAEjB;AAwIF;AAEA,MAAM,wCAAkB,IAAI,IAAI;IAAC;IAAW;IAAY;IAAW;IAAa;IAAY;CAAQ;AAEpG,mIAAmI;AACnI,2GAA2G;AAC3G,2FAA2F;AAC3F,MAAM,sCAAgB;IACpB;IAAG;IAAG;IAAG;IAAG;IAAI;IAAI;IAAG;IAAG;IAAK;IAAI;IAAK;CACzC;AAED,SAAS,iCAAW,MAAc,EAAE,SAA4B,EAAE,WAA6C,EAAE,eAAyC;QAexI,gBACD,mBAYD,oBACF;IA5BZ,mHAAmH;IACnH,IAAI,kBAAkB,IAAI,KAAK,YAAY,CAAC,QAAQ;QAAC,GAAG,WAAW;QACjE,6CAA6C;QAC7C,0BAA0B;QAC1B,0BAA0B;QAC1B,mBAAmB;QACnB,kBAAkB;QAClB,cAAc;IAChB;IACA,oFAAoF;IACpF,IAAI,WAAW,gBAAgB,aAAa,CAAC,CAAA;IAC7C,IAAI,cAAc,gBAAgB,aAAa,CAAC;IAChD,IAAI,cAAc,oCAAc,GAAG,CAAC,CAAA,IAAK,gBAAgB,aAAa,CAAC;QAEvD;IAAhB,IAAI,YAAY,CAAA,uBAAA,CAAA,iBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,YAAA,MAAA,QAA9B,mBAAA,KAAA,IAAA,KAAA,IAAA,eAA4C,KAAK,MAAA,QAAjD,yBAAA,KAAA,IAAA,uBAAqD;IACrE,IAAI,WAAA,CAAW,oBAAA,YAAY,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,WAAA,MAAA,QAAjC,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAA8C,KAAK;IAElE,sFAAsF;IACtF,8GAA8G;IAC9G,IAAI,CAAC,YAAa,CAAA,CAAA,oBAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAiB,WAAW,MAAK,gBAAgB,CAAA,oBAAA,QAAA,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAiB,WAAW,MAAK,QAAO,GACzG,WAAW;IAGb,kHAAkH;IAClH,wHAAwH;IACxH,IAAI,eAAe,IAAI,KAAK,YAAY,CAAC,QAAQ;QAAC,GAAG,WAAW;QAAE,uBAAuB;QAAG,uBAAuB;IAAC,GAAG,aAAa,CAAC;IAErI,IAAI,UAAA,CAAU,qBAAA,aAAa,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,UAAA,MAAA,QAAlC,uBAAA,KAAA,IAAA,KAAA,IAAA,mBAA8C,KAAK;IACjE,IAAI,QAAA,CAAQ,kBAAA,SAAS,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAA,MAAA,QAA9B,oBAAA,KAAA,IAAA,KAAA,IAAA,gBAAwC,KAAK;IAEzD,+GAA+G;IAC/G,0CAA0C;IAC1C,IAAI,mBAAmB,SAAS,MAAM,CAAC,CAAA,IAAK,CAAC,sCAAgB,GAAG,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAA,IAAK,kCAAY,EAAE,KAAK;IACtG,IAAI,sBAAsB,YAAY,OAAO,CAAC,CAAA,IAAK,EAAE,MAAM,CAAC,CAAA,IAAK,CAAC,sCAAgB,GAAG,CAAC,EAAE,IAAI,GAAG,GAAG,CAAC,CAAA,IAAK,kCAAY,EAAE,KAAK;IAC3H,IAAI,iBAAiB;WAAI,IAAI,IAAI;eAAI;eAAqB;SAAoB;KAAE,CAAC,IAAI,CAAC,CAAC,GAAG,IAAM,EAAE,MAAM,GAAG,EAAE,MAAM;IAEnH,IAAI,WAAW,eAAe,MAAM,KAAK,IACrC,IAAI,OAAO,sBAAsB,QACjC,IAAI,OAAO,GAAG,eAAe,IAAI,CAAC,KAAK,mBAAmB,CAAC,EAAE;IAEjE,uEAAuE;IACvE,IAAI,WAAW;WAAI,IAAI,KAAK,YAAY,CAAC,YAAY,MAAM,EAAE;YAAC,aAAa;QAAK,GAAG,MAAM,CAAC;KAAY,CAAC,OAAO;IAC9G,IAAI,UAAU,IAAI,IAAI,SAAS,GAAG,CAAC,CAAC,GAAG,IAAM;YAAC;YAAG;SAAE;IACnD,IAAI,UAAU,IAAI,OAAO,CAAC,CAAC,EAAE,SAAS,IAAI,CAAC,IAAI,CAAC,CAAC,EAAE;IACnD,IAAI,QAAQ,CAAA,IAAK,OAAO,QAAQ,GAAG,CAAC;IAEpC,OAAO;mBAAC;kBAAW;iBAAU;eAAS;kBAAO;iBAAU;eAAS;IAAK;AACvE;AAEA,SAAS,iCAAW,GAAW,EAAE,IAAqB,EAAE,OAAe;IACrE,IAAI,IAAI,UAAU,EAChB,OAAO,IAAI,UAAU,CAAC,MAAM;IAG9B,OAAO,IAAI,KAAK,CAAC,MAAM,IAAI,CAAC;AAC9B;AAEA,SAAS,kCAAY,MAAc;IACjC,OAAO,OAAO,OAAO,CAAC,uBAAuB;AAC/C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1235, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/clsx/dist/clsx.mjs"], "sourcesContent": ["function r(e){var t,f,n=\"\";if(\"string\"==typeof e||\"number\"==typeof e)n+=e;else if(\"object\"==typeof e)if(Array.isArray(e)){var o=e.length;for(t=0;t<o;t++)e[t]&&(f=r(e[t]))&&(n&&(n+=\" \"),n+=f)}else for(f in e)e[f]&&(n&&(n+=\" \"),n+=f);return n}export function clsx(){for(var e,t,f=0,n=\"\",o=arguments.length;f<o;f++)(e=arguments[f])&&(t=r(e))&&(n&&(n+=\" \"),n+=t);return n}export default clsx;"], "names": [], "mappings": ";;;;AAAA,SAAS,EAAE,CAAC;IAAE,IAAI,GAAE,GAAE,IAAE;IAAG,IAAG,YAAU,OAAO,KAAG,YAAU,OAAO,GAAE,KAAG;SAAO,IAAG,YAAU,OAAO,GAAE,IAAG,MAAM,OAAO,CAAC,IAAG;QAAC,IAAI,IAAE,EAAE,MAAM;QAAC,IAAI,IAAE,GAAE,IAAE,GAAE,IAAI,CAAC,CAAC,EAAE,IAAE,CAAC,IAAE,EAAE,CAAC,CAAC,EAAE,CAAC,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAC,OAAM,IAAI,KAAK,EAAE,CAAC,CAAC,EAAE,IAAE,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;AAAQ,SAAS;IAAO,IAAI,IAAI,GAAE,GAAE,IAAE,GAAE,IAAE,IAAG,IAAE,UAAU,MAAM,EAAC,IAAE,GAAE,IAAI,CAAC,IAAE,SAAS,CAAC,EAAE,KAAG,CAAC,IAAE,EAAE,EAAE,KAAG,CAAC,KAAG,CAAC,KAAG,GAAG,GAAE,KAAG,CAAC;IAAE,OAAO;AAAC;uCAAgB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1259, "column": 0}, "map": {"version": 3, "file": "module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-stately/flags/dist/packages/%40react-stately/flags/src/index.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet _tableNestedRows = false;\nlet _shadowDOM = false;\n\nexport function enableTableNestedRows(): void {\n  _tableNestedRows = true;\n}\n\nexport function tableNestedRows(): boolean {\n  return _tableNestedRows;\n}\n\nexport function enableShadowDOM(): void {\n  _shadowDOM = true;\n}\n\nexport function shadowDOM(): boolean {\n  return _shadowDOM;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;;;;AAED,IAAI,yCAAmB;AACvB,IAAI,mCAAa;AAEV,SAAS;IACd,yCAAmB;AACrB;AAEO,SAAS;IACd,OAAO;AACT;AAEO,SAAS;IACd,mCAAa;AACf;AAEO,SAAS;IACd,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1297, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-stately/calendar/dist/packages/%40react-stately/calendar/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nimport {\n  CalendarDate,\n  DateDuration,\n  maxDate,\n  minDate,\n  startOfMonth,\n  startOfWeek,\n  startOfYear,\n  toCalendarDate\n} from '@internationalized/date';\nimport {DateValue} from '@react-types/calendar';\n\nexport function isInvalid(date: DateValue, minValue?: DateValue | null, maxValue?: DateValue | null): boolean {\n  return (minValue != null && date.compare(minValue) < 0) ||\n    (maxValue != null && date.compare(maxValue) > 0);\n}\n\nexport function alignCenter(date: CalendarDate, duration: DateDuration, locale: string, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  let halfDuration: DateDuration = {};\n  for (let key in duration) {\n    halfDuration[key] = Math.floor(duration[key] / 2);\n    if (halfDuration[key] > 0 && duration[key] % 2 === 0) {\n      halfDuration[key]--;\n    }\n  }\n\n  let aligned = alignStart(date, duration, locale).subtract(halfDuration);\n  return constrainStart(date, aligned, duration, locale, minValue, maxValue);\n}\n\nexport function alignStart(date: CalendarDate, duration: DateDuration, locale: string, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  // align to the start of the largest unit\n  let aligned = date;\n  if (duration.years) {\n    aligned = startOfYear(date);\n  } else if (duration.months) {\n    aligned = startOfMonth(date);\n  } else if (duration.weeks) {\n    aligned = startOfWeek(date, locale);\n  }\n\n  return constrainStart(date, aligned, duration, locale, minValue, maxValue);\n}\n\nexport function alignEnd(date: CalendarDate, duration: DateDuration, locale: string, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  let d = {...duration};\n  // subtract 1 from the smallest unit\n  if (d.days) {\n    d.days--;\n  } else if (d.weeks) {\n    d.weeks--;\n  } else if (d.months) {\n    d.months--;\n  } else if (d.years) {\n    d.years--;\n  }\n\n  let aligned = alignStart(date, duration, locale).subtract(d);\n  return constrainStart(date, aligned, duration, locale, minValue, maxValue);\n}\n\nexport function constrainStart(\n  date: CalendarDate,\n  aligned: CalendarDate,\n  duration: DateDuration,\n  locale: string,\n  minValue?: DateValue | null,\n  maxValue?: DateValue | null): CalendarDate {\n  if (minValue && date.compare(minValue) >= 0) {\n    let newDate = maxDate(\n      aligned,\n      alignStart(toCalendarDate(minValue), duration, locale)\n    );\n    if (newDate) {\n      aligned = newDate;\n    }\n  }\n\n  if (maxValue && date.compare(maxValue) <= 0) {\n    let newDate = minDate(\n      aligned,\n      alignEnd(toCalendarDate(maxValue), duration, locale)\n    );\n    if (newDate) {\n      aligned = newDate;\n    }\n  }\n\n  return aligned;\n}\n\nexport function constrainValue(date: CalendarDate, minValue?: DateValue | null, maxValue?: DateValue | null): CalendarDate {\n  if (minValue) {\n    let newDate = maxDate(date, toCalendarDate(minValue));\n    if (newDate) {\n      date = newDate;\n    }\n  }\n\n  if (maxValue) {\n    let newDate = minDate(date, toCalendarDate(maxValue));\n    if (newDate) {\n      date = newDate;\n    }\n  }\n\n  return date;\n}\n\nexport function previousAvailableDate(date: CalendarDate, minValue: DateValue, isDateUnavailable?: (date: CalendarDate) => boolean): CalendarDate | null {\n  if (!isDateUnavailable) {\n    return date;\n  }\n\n  while (date.compare(minValue) >= 0 && isDateUnavailable(date)) {\n    date = date.subtract({days: 1});\n  }\n\n  if (date.compare(minValue) >= 0) {\n    return date;\n  }\n  return null;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAaM,SAAS,0CAAU,IAAe,EAAE,QAA2B,EAAE,QAA2B;IACjG,OAAQ,YAAY,QAAQ,KAAK,OAAO,CAAC,YAAY,KAClD,YAAY,QAAQ,KAAK,OAAO,CAAC,YAAY;AAClD;AAEO,SAAS,0CAAY,IAAkB,EAAE,QAAsB,EAAE,MAAc,EAAE,QAA2B,EAAE,QAA2B;IAC9I,IAAI,eAA6B,CAAC;IAClC,IAAK,IAAI,OAAO,SAAU;QACxB,YAAY,CAAC,IAAI,GAAG,KAAK,KAAK,CAAC,QAAQ,CAAC,IAAI,GAAG;QAC/C,IAAI,YAAY,CAAC,IAAI,GAAG,KAAK,QAAQ,CAAC,IAAI,GAAG,MAAM,GACjD,YAAY,CAAC,IAAI;IAErB;IAEA,IAAI,UAAU,yCAAW,MAAM,UAAU,QAAQ,QAAQ,CAAC;IAC1D,OAAO,0CAAe,MAAM,SAAS,UAAU,QAAQ,UAAU;AACnE;AAEO,SAAS,yCAAW,IAAkB,EAAE,QAAsB,EAAE,MAAc,EAAE,QAA2B,EAAE,QAA2B;IAC7I,yCAAyC;IACzC,IAAI,UAAU;IACd,IAAI,SAAS,KAAK,EAChB,UAAU,CAAA,sKAAA,cAAU,EAAE;SACjB,IAAI,SAAS,MAAM,EACxB,UAAU,CAAA,sKAAA,eAAW,EAAE;SAClB,IAAI,SAAS,KAAK,EACvB,UAAU,CAAA,sKAAA,cAAU,EAAE,MAAM;IAG9B,OAAO,0CAAe,MAAM,SAAS,UAAU,QAAQ,UAAU;AACnE;AAEO,SAAS,0CAAS,IAAkB,EAAE,QAAsB,EAAE,MAAc,EAAE,QAA2B,EAAE,QAA2B;IAC3I,IAAI,IAAI;QAAC,GAAG,QAAQ;IAAA;IACpB,oCAAoC;IACpC,IAAI,EAAE,IAAI,EACR,EAAE,IAAI;SACD,IAAI,EAAE,KAAK,EAChB,EAAE,KAAK;SACF,IAAI,EAAE,MAAM,EACjB,EAAE,MAAM;SACH,IAAI,EAAE,KAAK,EAChB,EAAE,KAAK;IAGT,IAAI,UAAU,yCAAW,MAAM,UAAU,QAAQ,QAAQ,CAAC;IAC1D,OAAO,0CAAe,MAAM,SAAS,UAAU,QAAQ,UAAU;AACnE;AAEO,SAAS,0CACd,IAAkB,EAClB,OAAqB,EACrB,QAAsB,EACtB,MAAc,EACd,QAA2B,EAC3B,QAA2B;IAC3B,IAAI,YAAY,KAAK,OAAO,CAAC,aAAa,GAAG;QAC3C,IAAI,UAAU,CAAA,sKAAA,UAAM,EAClB,SACA,yCAAW,CAAA,yKAAA,iBAAa,EAAE,WAAW,UAAU;QAEjD,IAAI,SACF,UAAU;IAEd;IAEA,IAAI,YAAY,KAAK,OAAO,CAAC,aAAa,GAAG;QAC3C,IAAI,UAAU,CAAA,sKAAA,UAAM,EAClB,SACA,0CAAS,CAAA,yKAAA,iBAAa,EAAE,WAAW,UAAU;QAE/C,IAAI,SACF,UAAU;IAEd;IAEA,OAAO;AACT;AAEO,SAAS,0CAAe,IAAkB,EAAE,QAA2B,EAAE,QAA2B;IACzG,IAAI,UAAU;QACZ,IAAI,UAAU,CAAA,sKAAA,UAAM,EAAE,MAAM,CAAA,yKAAA,iBAAa,EAAE;QAC3C,IAAI,SACF,OAAO;IAEX;IAEA,IAAI,UAAU;QACZ,IAAI,UAAU,CAAA,sKAAA,UAAM,EAAE,MAAM,CAAA,yKAAA,iBAAa,EAAE;QAC3C,IAAI,SACF,OAAO;IAEX;IAEA,OAAO;AACT;AAEO,SAAS,0CAAsB,IAAkB,EAAE,QAAmB,EAAE,iBAAmD;IAChI,IAAI,CAAC,mBACH,OAAO;IAGT,MAAO,KAAK,OAAO,CAAC,aAAa,KAAK,kBAAkB,MACtD,OAAO,KAAK,QAAQ,CAAC;QAAC,MAAM;IAAC;IAG/B,IAAI,KAAK,OAAO,CAAC,aAAa,GAC5B,OAAO;IAET,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1389, "column": 0}, "map": {"version": 3, "file": "useCalendarState.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-stately/calendar/dist/packages/%40react-stately/calendar/src/useCalendarState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {alignCenter, alignEnd, alignStart, constrainStart, constrainValue, isInvalid, previousAvailableDate} from './utils';\nimport {\n  Calendar,\n  CalendarDate,\n  CalendarIdentifier,\n  DateDuration,\n  DateFormatter,\n  endOfMonth,\n  endOfWeek,\n  getDayOfWeek,\n  GregorianCalendar,\n  isEqualCalendar,\n  isSameDay,\n  startOfMonth,\n  startOfWeek,\n  toCalendar,\n  toCalendarDate,\n  today\n} from '@internationalized/date';\nimport {CalendarProps, DateValue, MappedDateValue} from '@react-types/calendar';\nimport {CalendarState} from './types';\nimport {useControlledState} from '@react-stately/utils';\nimport {useMemo, useState} from 'react';\nimport {ValidationState} from '@react-types/shared';\n\nexport interface CalendarStateOptions<T extends DateValue = DateValue> extends CalendarProps<T> {\n  /** The locale to display and edit the value according to. */\n  locale: string,\n  /**\n   * A function that creates a [Calendar](../internationalized/date/Calendar.html)\n   * object for a given calendar identifier. Such a function may be imported from the\n   * `@internationalized/date` package, or manually implemented to include support for\n   * only certain calendars.\n   */\n  createCalendar: (name: CalendarIdentifier) => Calendar,\n  /**\n   * The amount of days that will be displayed at once. This affects how pagination works.\n   * @default {months: 1}\n   */\n  visibleDuration?: DateDuration,\n  /** Determines how to align the initial selection relative to the visible date range. */\n  selectionAlignment?: 'start' | 'center' | 'end'\n}\n/**\n * Provides state management for a calendar component.\n * A calendar displays one or more date grids and allows users to select a single date.\n */\nexport function useCalendarState<T extends DateValue = DateValue>(props: CalendarStateOptions<T>): CalendarState {\n  let defaultFormatter = useMemo(() => new DateFormatter(props.locale), [props.locale]);\n  let resolvedOptions = useMemo(() => defaultFormatter.resolvedOptions(), [defaultFormatter]);\n  let {\n    locale,\n    createCalendar,\n    visibleDuration = {months: 1},\n    minValue,\n    maxValue,\n    selectionAlignment,\n    isDateUnavailable,\n    pageBehavior = 'visible',\n    firstDayOfWeek\n  } = props;\n  let calendar = useMemo(() => createCalendar(resolvedOptions.calendar as CalendarIdentifier), [createCalendar, resolvedOptions.calendar]);\n\n  let [value, setControlledValue] = useControlledState<DateValue | null, MappedDateValue<T>>(props.value!, props.defaultValue ?? null!, props.onChange);\n  let calendarDateValue = useMemo(() => value ? toCalendar(toCalendarDate(value), calendar) : null, [value, calendar]);\n  let timeZone = useMemo(() => value && 'timeZone' in value ? value.timeZone : resolvedOptions.timeZone, [value, resolvedOptions.timeZone]);\n  let focusedCalendarDate = useMemo(() => (\n    props.focusedValue\n      ? constrainValue(toCalendar(toCalendarDate(props.focusedValue), calendar), minValue, maxValue)\n      : undefined\n  ), [props.focusedValue, calendar, minValue, maxValue]);\n  let defaultFocusedCalendarDate = useMemo(() => (\n    constrainValue(\n      props.defaultFocusedValue\n        ? toCalendar(toCalendarDate(props.defaultFocusedValue), calendar)\n        : calendarDateValue || toCalendar(today(timeZone), calendar),\n      minValue,\n      maxValue\n    )\n  ), [props.defaultFocusedValue, calendarDateValue, timeZone, calendar, minValue, maxValue]);\n  let [focusedDate, setFocusedDate] = useControlledState(focusedCalendarDate, defaultFocusedCalendarDate, props.onFocusChange);\n  let [startDate, setStartDate] = useState(() => {\n    switch (selectionAlignment) {\n      case 'start':\n        return alignStart(focusedDate, visibleDuration, locale, minValue, maxValue);\n      case 'end':\n        return alignEnd(focusedDate, visibleDuration, locale, minValue, maxValue);\n      case 'center':\n      default:\n        return alignCenter(focusedDate, visibleDuration, locale, minValue, maxValue);\n    }\n  });\n  let [isFocused, setFocused] = useState(props.autoFocus || false);\n\n  let endDate = useMemo(() => {\n    let duration = {...visibleDuration};\n    if (duration.days) {\n      duration.days--;\n    } else {\n      duration.days = -1;\n    }\n    return startDate.add(duration);\n  }, [startDate, visibleDuration]);\n\n  // Reset focused date and visible range when calendar changes.\n  let [lastCalendar, setLastCalendar] = useState(calendar);\n  if (!isEqualCalendar(calendar, lastCalendar)) {\n    let newFocusedDate = toCalendar(focusedDate, calendar);\n    setStartDate(alignCenter(newFocusedDate, visibleDuration, locale, minValue, maxValue));\n    setFocusedDate(newFocusedDate);\n    setLastCalendar(calendar);\n  }\n\n  if (isInvalid(focusedDate, minValue, maxValue)) {\n    // If the focused date was moved to an invalid value, it can't be focused, so constrain it.\n    setFocusedDate(constrainValue(focusedDate, minValue, maxValue));\n  } else if (focusedDate.compare(startDate) < 0) {\n    setStartDate(alignEnd(focusedDate, visibleDuration, locale, minValue, maxValue));\n  } else if (focusedDate.compare(endDate) > 0) {\n    setStartDate(alignStart(focusedDate, visibleDuration, locale, minValue, maxValue));\n  }\n\n  // Sets focus to a specific cell date\n  function focusCell(date: CalendarDate) {\n    date = constrainValue(date, minValue, maxValue);\n    setFocusedDate(date);\n  }\n\n  function setValue(newValue: CalendarDate | null) {\n    if (!props.isDisabled && !props.isReadOnly) {\n      let localValue = newValue;\n      if (localValue === null) {\n        setControlledValue(null);\n        return;\n      }\n      localValue = constrainValue(localValue, minValue, maxValue);\n      localValue = previousAvailableDate(localValue, startDate, isDateUnavailable);\n      if (!localValue) {\n        return;\n      }\n\n      // The display calendar should not have any effect on the emitted value.\n      // Emit dates in the same calendar as the original value, if any, otherwise gregorian.\n      localValue = toCalendar(localValue, value?.calendar || new GregorianCalendar());\n\n      // Preserve time if the input value had one.\n      if (value && 'hour' in value) {\n        setControlledValue(value.set(localValue));\n      } else {\n        setControlledValue(localValue);\n      }\n    }\n  }\n\n  let isUnavailable = useMemo(() => {\n    if (!calendarDateValue) {\n      return false;\n    }\n\n    if (isDateUnavailable && isDateUnavailable(calendarDateValue)) {\n      return true;\n    }\n\n    return isInvalid(calendarDateValue, minValue, maxValue);\n  }, [calendarDateValue, isDateUnavailable, minValue, maxValue]);\n  let isValueInvalid = props.isInvalid || props.validationState === 'invalid' || isUnavailable;\n  let validationState: ValidationState | null = isValueInvalid ? 'invalid' : null;\n\n  let pageDuration = useMemo(() => {\n    if (pageBehavior === 'visible') {\n      return visibleDuration;\n    }\n\n    return unitDuration(visibleDuration);\n  }, [pageBehavior, visibleDuration]);\n\n  return {\n    isDisabled: props.isDisabled ?? false,\n    isReadOnly: props.isReadOnly ?? false,\n    value: calendarDateValue,\n    setValue,\n    visibleRange: {\n      start: startDate,\n      end: endDate\n    },\n    minValue,\n    maxValue,\n    focusedDate,\n    timeZone,\n    validationState,\n    isValueInvalid,\n    setFocusedDate(date) {\n      focusCell(date);\n      setFocused(true);\n    },\n    focusNextDay() {\n      focusCell(focusedDate.add({days: 1}));\n    },\n    focusPreviousDay() {\n      focusCell(focusedDate.subtract({days: 1}));\n    },\n    focusNextRow() {\n      if (visibleDuration.days) {\n        this.focusNextPage();\n      } else if (visibleDuration.weeks || visibleDuration.months || visibleDuration.years) {\n        focusCell(focusedDate.add({weeks: 1}));\n      }\n    },\n    focusPreviousRow() {\n      if (visibleDuration.days) {\n        this.focusPreviousPage();\n      } else if (visibleDuration.weeks || visibleDuration.months || visibleDuration.years) {\n        focusCell(focusedDate.subtract({weeks: 1}));\n      }\n    },\n    focusNextPage() {\n      let start = startDate.add(pageDuration);\n      setFocusedDate(constrainValue(focusedDate.add(pageDuration), minValue, maxValue));\n      setStartDate(\n        alignStart(\n          constrainStart(focusedDate, start, pageDuration, locale, minValue, maxValue),\n          pageDuration,\n          locale\n        )\n      );\n    },\n    focusPreviousPage() {\n      let start = startDate.subtract(pageDuration);\n      setFocusedDate(constrainValue(focusedDate.subtract(pageDuration), minValue, maxValue));\n      setStartDate(\n        alignStart(\n          constrainStart(focusedDate, start, pageDuration, locale, minValue, maxValue),\n          pageDuration,\n          locale\n        )\n      );\n    },\n    focusSectionStart() {\n      if (visibleDuration.days) {\n        focusCell(startDate);\n      } else if (visibleDuration.weeks) {\n        focusCell(startOfWeek(focusedDate, locale));\n      } else if (visibleDuration.months || visibleDuration.years) {\n        focusCell(startOfMonth(focusedDate));\n      }\n    },\n    focusSectionEnd() {\n      if (visibleDuration.days) {\n        focusCell(endDate);\n      } else if (visibleDuration.weeks) {\n        focusCell(endOfWeek(focusedDate, locale));\n      } else if (visibleDuration.months || visibleDuration.years) {\n        focusCell(endOfMonth(focusedDate));\n      }\n    },\n    focusNextSection(larger) {\n      if (!larger && !visibleDuration.days) {\n        focusCell(focusedDate.add(unitDuration(visibleDuration)));\n        return;\n      }\n\n      if (visibleDuration.days) {\n        this.focusNextPage();\n      } else if (visibleDuration.weeks) {\n        focusCell(focusedDate.add({months: 1}));\n      } else if (visibleDuration.months || visibleDuration.years) {\n        focusCell(focusedDate.add({years: 1}));\n      }\n    },\n    focusPreviousSection(larger) {\n      if (!larger && !visibleDuration.days) {\n        focusCell(focusedDate.subtract(unitDuration(visibleDuration)));\n        return;\n      }\n\n      if (visibleDuration.days) {\n        this.focusPreviousPage();\n      } else if (visibleDuration.weeks) {\n        focusCell(focusedDate.subtract({months: 1}));\n      } else if (visibleDuration.months || visibleDuration.years) {\n        focusCell(focusedDate.subtract({years: 1}));\n      }\n    },\n    selectFocusedDate() {\n      if (!(isDateUnavailable && isDateUnavailable(focusedDate))) {\n        setValue(focusedDate);\n      }\n    },\n    selectDate(date) {\n      setValue(date);\n    },\n    isFocused,\n    setFocused,\n    isInvalid(date) {\n      return isInvalid(date, minValue, maxValue);\n    },\n    isSelected(date) {\n      return calendarDateValue != null && isSameDay(date, calendarDateValue) && !this.isCellDisabled(date) && !this.isCellUnavailable(date);\n    },\n    isCellFocused(date) {\n      return isFocused && focusedDate && isSameDay(date, focusedDate);\n    },\n    isCellDisabled(date) {\n      return props.isDisabled || date.compare(startDate) < 0 || date.compare(endDate) > 0 || this.isInvalid(date);\n    },\n    isCellUnavailable(date) {\n      return props.isDateUnavailable ? props.isDateUnavailable(date) : false;\n    },\n    isPreviousVisibleRangeInvalid() {\n      let prev = startDate.subtract({days: 1});\n      return isSameDay(prev, startDate) || this.isInvalid(prev);\n    },\n    isNextVisibleRangeInvalid() {\n      // Adding may return the same date if we reached the end of time\n      // according to the calendar system (e.g. 9999-12-31).\n      let next = endDate.add({days: 1});\n      return isSameDay(next, endDate) || this.isInvalid(next);\n    },\n    getDatesInWeek(weekIndex, from = startDate) {\n      let date = from.add({weeks: weekIndex});\n      let dates: (CalendarDate | null)[] = [];\n\n      date = startOfWeek(date, locale, firstDayOfWeek);\n      \n      // startOfWeek will clamp dates within the calendar system's valid range, which may\n      // start in the middle of a week. In this case, add null placeholders.\n      let dayOfWeek = getDayOfWeek(date, locale, firstDayOfWeek);\n      for (let i = 0; i < dayOfWeek; i++) {\n        dates.push(null);\n      }\n\n      while (dates.length < 7) {\n        dates.push(date);\n        let nextDate = date.add({days: 1});\n        if (isSameDay(date, nextDate)) {\n          // If the next day is the same, we have hit the end of the calendar system.\n          break;\n        }\n        date = nextDate;\n      }\n\n      // Add null placeholders if at the end of the calendar system.\n      while (dates.length < 7) {\n        dates.push(null);\n      }\n\n      return dates;\n    }\n  };\n}\n\nfunction unitDuration(duration: DateDuration) {\n  let unit = {...duration};\n  for (let key in duration) {\n    unit[key] = 1;\n  }\n  return unit;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAiDM,SAAS,0CAAkD,KAA8B;IAC9F,IAAI,mBAAmB,CAAA,iKAAA,UAAM,EAAE,IAAM,IAAI,CAAA,4KAAA,gBAAY,EAAE,MAAM,MAAM,GAAG;QAAC,MAAM,MAAM;KAAC;IACpF,IAAI,kBAAkB,CAAA,iKAAA,UAAM,EAAE,IAAM,iBAAiB,eAAe,IAAI;QAAC;KAAiB;IAC1F,IAAI,EAAA,QACF,MAAM,EAAA,gBACN,cAAc,EAAA,iBACd,kBAAkB;QAAC,QAAQ;IAAC,CAAA,EAAA,UAC5B,QAAQ,EAAA,UACR,QAAQ,EAAA,oBACR,kBAAkB,EAAA,mBAClB,iBAAiB,EAAA,cACjB,eAAe,SAAA,EAAA,gBACf,cAAc,EACf,GAAG;IACJ,IAAI,WAAW,CAAA,iKAAA,UAAM,EAAE,IAAM,eAAe,gBAAgB,QAAQ,GAAyB;QAAC;QAAgB,gBAAgB,QAAQ;KAAC;QAE9B;IAAzG,IAAI,CAAC,OAAO,mBAAmB,GAAG,CAAA,iLAAA,qBAAiB,EAAwC,MAAM,KAAK,EAAG,CAAA,sBAAA,MAAM,YAAY,MAAA,QAAlB,wBAAA,KAAA,IAAA,sBAAsB,MAAO,MAAM,QAAQ;IACpJ,IAAI,oBAAoB,CAAA,iKAAA,UAAM,EAAE,IAAM,QAAQ,CAAA,yKAAA,aAAS,EAAE,CAAA,yKAAA,iBAAa,EAAE,QAAQ,YAAY,MAAM;QAAC;QAAO;KAAS;IACnH,IAAI,WAAW,CAAA,iKAAA,UAAM,EAAE,IAAM,SAAS,cAAc,QAAQ,MAAM,QAAQ,GAAG,gBAAgB,QAAQ,EAAE;QAAC;QAAO,gBAAgB,QAAQ;KAAC;IACxI,IAAI,sBAAsB,CAAA,iKAAA,UAAM,EAAE,IAChC,MAAM,YAAY,GACd,CAAA,uKAAA,iBAAa,EAAE,CAAA,yKAAA,aAAS,EAAE,CAAA,yKAAA,iBAAa,EAAE,MAAM,YAAY,GAAG,WAAW,UAAU,YACnF,WACH;QAAC,MAAM,YAAY;QAAE;QAAU;QAAU;KAAS;IACrD,IAAI,6BAA6B,CAAA,iKAAA,UAAM,EAAE,IACvC,CAAA,uKAAA,iBAAa,EACX,MAAM,mBAAmB,GACrB,CAAA,yKAAA,aAAS,EAAE,CAAA,yKAAA,iBAAa,EAAE,MAAM,mBAAmB,GAAG,YACtD,qBAAqB,CAAA,yKAAA,aAAS,EAAE,CAAA,sKAAA,QAAI,EAAE,WAAW,WACrD,UACA,WAED;QAAC,MAAM,mBAAmB;QAAE;QAAmB;QAAU;QAAU;QAAU;KAAS;IACzF,IAAI,CAAC,aAAa,eAAe,GAAG,CAAA,iLAAA,qBAAiB,EAAE,qBAAqB,4BAA4B,MAAM,aAAa;IAC3H,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,iKAAA,WAAO,EAAE;QACvC,OAAQ;YACN,KAAK;gBACH,OAAO,CAAA,uKAAA,aAAS,EAAE,aAAa,iBAAiB,QAAQ,UAAU;YACpE,KAAK;gBACH,OAAO,CAAA,uKAAA,WAAO,EAAE,aAAa,iBAAiB,QAAQ,UAAU;YAClE,KAAK;YACL;gBACE,OAAO,CAAA,uKAAA,cAAU,EAAE,aAAa,iBAAiB,QAAQ,UAAU;QACvE;IACF;IACA,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,iKAAA,WAAO,EAAE,MAAM,SAAS,IAAI;IAE1D,IAAI,UAAU,CAAA,iKAAA,UAAM,EAAE;QACpB,IAAI,WAAW;YAAC,GAAG,eAAe;QAAA;QAClC,IAAI,SAAS,IAAI,EACf,SAAS,IAAI;aAEb,SAAS,IAAI,GAAG,CAAA;QAElB,OAAO,UAAU,GAAG,CAAC;IACvB,GAAG;QAAC;QAAW;KAAgB;IAE/B,8DAA8D;IAC9D,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAC/C,IAAI,CAAC,CAAA,sKAAA,kBAAc,EAAE,UAAU,eAAe;QAC5C,IAAI,iBAAiB,CAAA,yKAAA,aAAS,EAAE,aAAa;QAC7C,aAAa,CAAA,uKAAA,cAAU,EAAE,gBAAgB,iBAAiB,QAAQ,UAAU;QAC5E,eAAe;QACf,gBAAgB;IAClB;IAEA,IAAI,CAAA,uKAAA,YAAQ,EAAE,aAAa,UAAU,WACnC,AACA,eAAe,CAAA,2EAD4E,4FAC5E,iBAAa,EAAE,aAAa,UAAU;SAChD,IAAI,YAAY,OAAO,CAAC,aAAa,GAC1C,aAAa,CAAA,uKAAA,WAAO,EAAE,aAAa,iBAAiB,QAAQ,UAAU;SACjE,IAAI,YAAY,OAAO,CAAC,WAAW,GACxC,aAAa,CAAA,uKAAA,aAAS,EAAE,aAAa,iBAAiB,QAAQ,UAAU;IAG1E,qCAAqC;IACrC,SAAS,UAAU,IAAkB;QACnC,OAAO,CAAA,uKAAA,iBAAa,EAAE,MAAM,UAAU;QACtC,eAAe;IACjB;IAEA,SAAS,SAAS,QAA6B;QAC7C,IAAI,CAAC,MAAM,UAAU,IAAI,CAAC,MAAM,UAAU,EAAE;YAC1C,IAAI,aAAa;YACjB,IAAI,eAAe,MAAM;gBACvB,mBAAmB;gBACnB;YACF;YACA,aAAa,CAAA,uKAAA,iBAAa,EAAE,YAAY,UAAU;YAClD,aAAa,CAAA,uKAAA,wBAAoB,EAAE,YAAY,WAAW;YAC1D,IAAI,CAAC,YACH;YAGF,wEAAwE;YACxE,sFAAsF;YACtF,aAAa,CAAA,yKAAA,aAAS,EAAE,YAAY,CAAA,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,QAAQ,KAAI,IAAI,CAAA,gLAAA,oBAAgB;YAE3E,4CAA4C;YAC5C,IAAI,SAAS,UAAU,OACrB,mBAAmB,MAAM,GAAG,CAAC;iBAE7B,mBAAmB;QAEvB;IACF;IAEA,IAAI,gBAAgB,CAAA,iKAAA,UAAM,EAAE;QAC1B,IAAI,CAAC,mBACH,OAAO;QAGT,IAAI,qBAAqB,kBAAkB,oBACzC,OAAO;QAGT,OAAO,CAAA,uKAAA,YAAQ,EAAE,mBAAmB,UAAU;IAChD,GAAG;QAAC;QAAmB;QAAmB;QAAU;KAAS;IAC7D,IAAI,iBAAiB,MAAM,SAAS,IAAI,MAAM,eAAe,KAAK,aAAa;IAC/E,IAAI,kBAA0C,iBAAiB,YAAY;IAE3E,IAAI,eAAe,CAAA,iKAAA,UAAM,EAAE;QACzB,IAAI,iBAAiB,WACnB,OAAO;QAGT,OAAO,mCAAa;IACtB,GAAG;QAAC;QAAc;KAAgB;QAGpB,mBACA;IAFd,OAAO;QACL,YAAY,CAAA,oBAAA,MAAM,UAAU,MAAA,QAAhB,sBAAA,KAAA,IAAA,oBAAoB;QAChC,YAAY,CAAA,oBAAA,MAAM,UAAU,MAAA,QAAhB,sBAAA,KAAA,IAAA,oBAAoB;QAChC,OAAO;kBACP;QACA,cAAc;YACZ,OAAO;YACP,KAAK;QACP;kBACA;kBACA;qBACA;kBACA;yBACA;wBACA;QACA,gBAAe,IAAI;YACjB,UAAU;YACV,WAAW;QACb;QACA;YACE,UAAU,YAAY,GAAG,CAAC;gBAAC,MAAM;YAAC;QACpC;QACA;YACE,UAAU,YAAY,QAAQ,CAAC;gBAAC,MAAM;YAAC;QACzC;QACA;YACE,IAAI,gBAAgB,IAAI,EACtB,IAAI,CAAC,aAAa;iBACb,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,KAAK,EACjF,UAAU,YAAY,GAAG,CAAC;gBAAC,OAAO;YAAC;QAEvC;QACA;YACE,IAAI,gBAAgB,IAAI,EACtB,IAAI,CAAC,iBAAiB;iBACjB,IAAI,gBAAgB,KAAK,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,KAAK,EACjF,UAAU,YAAY,QAAQ,CAAC;gBAAC,OAAO;YAAC;QAE5C;QACA;YACE,IAAI,QAAQ,UAAU,GAAG,CAAC;YAC1B,eAAe,CAAA,uKAAA,iBAAa,EAAE,YAAY,GAAG,CAAC,eAAe,UAAU;YACvE,aACE,CAAA,uKAAA,aAAS,EACP,CAAA,uKAAA,iBAAa,EAAE,aAAa,OAAO,cAAc,QAAQ,UAAU,WACnE,cACA;QAGN;QACA;YACE,IAAI,QAAQ,UAAU,QAAQ,CAAC;YAC/B,eAAe,CAAA,uKAAA,iBAAa,EAAE,YAAY,QAAQ,CAAC,eAAe,UAAU;YAC5E,aACE,CAAA,uKAAA,aAAS,EACP,CAAA,uKAAA,iBAAa,EAAE,aAAa,OAAO,cAAc,QAAQ,UAAU,WACnE,cACA;QAGN;QACA;YACE,IAAI,gBAAgB,IAAI,EACtB,UAAU;iBACL,IAAI,gBAAgB,KAAK,EAC9B,UAAU,CAAA,sKAAA,cAAU,EAAE,aAAa;iBAC9B,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,KAAK,EACxD,UAAU,CAAA,sKAAA,eAAW,EAAE;QAE3B;QACA;YACE,IAAI,gBAAgB,IAAI,EACtB,UAAU;iBACL,IAAI,gBAAgB,KAAK,EAC9B,UAAU,CAAA,sKAAA,YAAQ,EAAE,aAAa;iBAC5B,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,KAAK,EACxD,UAAU,CAAA,sKAAA,aAAS,EAAE;QAEzB;QACA,kBAAiB,MAAM;YACrB,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,EAAE;gBACpC,UAAU,YAAY,GAAG,CAAC,mCAAa;gBACvC;YACF;YAEA,IAAI,gBAAgB,IAAI,EACtB,IAAI,CAAC,aAAa;iBACb,IAAI,gBAAgB,KAAK,EAC9B,UAAU,YAAY,GAAG,CAAC;gBAAC,QAAQ;YAAC;iBAC/B,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,KAAK,EACxD,UAAU,YAAY,GAAG,CAAC;gBAAC,OAAO;YAAC;QAEvC;QACA,sBAAqB,MAAM;YACzB,IAAI,CAAC,UAAU,CAAC,gBAAgB,IAAI,EAAE;gBACpC,UAAU,YAAY,QAAQ,CAAC,mCAAa;gBAC5C;YACF;YAEA,IAAI,gBAAgB,IAAI,EACtB,IAAI,CAAC,iBAAiB;iBACjB,IAAI,gBAAgB,KAAK,EAC9B,UAAU,YAAY,QAAQ,CAAC;gBAAC,QAAQ;YAAC;iBACpC,IAAI,gBAAgB,MAAM,IAAI,gBAAgB,KAAK,EACxD,UAAU,YAAY,QAAQ,CAAC;gBAAC,OAAO;YAAC;QAE5C;QACA;YACE,IAAI,CAAE,CAAA,qBAAqB,kBAAkB,YAAW,GACtD,SAAS;QAEb;QACA,YAAW,IAAI;YACb,SAAS;QACX;mBACA;oBACA;QACA,WAAU,IAAI;YACZ,OAAO,CAAA,uKAAA,YAAQ,EAAE,MAAM,UAAU;QACnC;QACA,YAAW,IAAI;YACb,OAAO,qBAAqB,QAAQ,CAAA,sKAAA,YAAQ,EAAE,MAAM,sBAAsB,CAAC,IAAI,CAAC,cAAc,CAAC,SAAS,CAAC,IAAI,CAAC,iBAAiB,CAAC;QAClI;QACA,eAAc,IAAI;YAChB,OAAO,aAAa,eAAe,CAAA,sKAAA,YAAQ,EAAE,MAAM;QACrD;QACA,gBAAe,IAAI;YACjB,OAAO,MAAM,UAAU,IAAI,KAAK,OAAO,CAAC,aAAa,KAAK,KAAK,OAAO,CAAC,WAAW,KAAK,IAAI,CAAC,SAAS,CAAC;QACxG;QACA,mBAAkB,IAAI;YACpB,OAAO,MAAM,iBAAiB,GAAG,MAAM,iBAAiB,CAAC,QAAQ;QACnE;QACA;YACE,IAAI,OAAO,UAAU,QAAQ,CAAC;gBAAC,MAAM;YAAC;YACtC,OAAO,CAAA,sKAAA,YAAQ,EAAE,MAAM,cAAc,IAAI,CAAC,SAAS,CAAC;QACtD;QACA;YACE,gEAAgE;YAChE,sDAAsD;YACtD,IAAI,OAAO,QAAQ,GAAG,CAAC;gBAAC,MAAM;YAAC;YAC/B,OAAO,CAAA,sKAAA,YAAQ,EAAE,MAAM,YAAY,IAAI,CAAC,SAAS,CAAC;QACpD;QACA,gBAAe,SAAS,EAAE,OAAO,SAAS;YACxC,IAAI,OAAO,KAAK,GAAG,CAAC;gBAAC,OAAO;YAAS;YACrC,IAAI,QAAiC,EAAE;YAEvC,OAAO,CAAA,sKAAA,cAAU,EAAE,MAAM,QAAQ;YAEjC,mFAAmF;YACnF,sEAAsE;YACtE,IAAI,YAAY,CAAA,sKAAA,eAAW,EAAE,MAAM,QAAQ;YAC3C,IAAK,IAAI,IAAI,GAAG,IAAI,WAAW,IAC7B,MAAM,IAAI,CAAC;YAGb,MAAO,MAAM,MAAM,GAAG,EAAG;gBACvB,MAAM,IAAI,CAAC;gBACX,IAAI,WAAW,KAAK,GAAG,CAAC;oBAAC,MAAM;gBAAC;gBAChC,IAAI,CAAA,sKAAA,YAAQ,EAAE,MAAM,WAElB;gBAEF,OAAO;YACT;YAEA,8DAA8D;YAC9D,MAAO,MAAM,MAAM,GAAG,EACpB,MAAM,IAAI,CAAC;YAGb,OAAO;QACT;IACF;AACF;AAEA,SAAS,mCAAa,QAAsB;IAC1C,IAAI,OAAO;QAAC,GAAG,QAAQ;IAAA;IACvB,IAAK,IAAI,OAAO,SACd,IAAI,CAAC,IAAI,GAAG;IAEd,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1692, "column": 0}, "map": {"version": 3, "file": "useRangeCalendarState.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-stately/calendar/dist/packages/%40react-stately/calendar/src/useRangeCalendarState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {alignCenter, constrainValue, isInvalid, previousAvailableDate} from './utils';\nimport {Calendar, CalendarDate, CalendarIdentifier, DateDuration, GregorianCalendar, isEqualDay, maxDate, minDate, toCalendar, toCalendarDate} from '@internationalized/date';\nimport {CalendarState, RangeCalendarState} from './types';\nimport {DateValue, MappedDateValue, RangeCalendarProps} from '@react-types/calendar';\nimport {RangeValue, ValidationState} from '@react-types/shared';\nimport {useCalendarState} from './useCalendarState';\nimport {useControlledState} from '@react-stately/utils';\nimport {useMemo, useRef, useState} from 'react';\n\nexport interface RangeCalendarStateOptions<T extends DateValue = DateValue> extends RangeCalendarProps<T> {\n  /** The locale to display and edit the value according to. */\n  locale: string,\n  /**\n   * A function that creates a [Calendar](../internationalized/date/Calendar.html)\n   * object for a given calendar identifier. Such a function may be imported from the\n   * `@internationalized/date` package, or manually implemented to include support for\n   * only certain calendars.\n   */\n  createCalendar: (name: CalendarIdentifier) => Calendar,\n  /**\n   * The amount of days that will be displayed at once. This affects how pagination works.\n   * @default {months: 1}\n   */\n  visibleDuration?: DateDuration\n}\n\n/**\n * Provides state management for a range calendar component.\n * A range calendar displays one or more date grids and allows users to select a contiguous range of dates.\n */\nexport function useRangeCalendarState<T extends DateValue = DateValue>(props: RangeCalendarStateOptions<T>): RangeCalendarState {\n  let {value: valueProp, defaultValue, onChange, createCalendar, locale, visibleDuration = {months: 1}, minValue, maxValue, ...calendarProps} = props;\n  let [value, setValue] = useControlledState<RangeValue<T> | null, RangeValue<MappedDateValue<T>>>(\n    valueProp!,\n    defaultValue || null!,\n    onChange\n  );\n\n  let [anchorDate, setAnchorDateState] = useState<CalendarDate | null>(null);\n  let alignment: 'center' | 'start' = 'center';\n  if (value && value.start && value.end) {\n    let start = alignCenter(toCalendarDate(value.start), visibleDuration, locale, minValue, maxValue);\n    let end = start.add(visibleDuration).subtract({days: 1});\n\n    if (value.end.compare(end) > 0) {\n      alignment = 'start';\n    }\n  }\n\n  // Available range must be stored in a ref so we have access to the updated version immediately in `isInvalid`.\n  let availableRangeRef = useRef<Partial<RangeValue<DateValue>> | null>(null);\n  let [availableRange, setAvailableRange] = useState<Partial<RangeValue<DateValue>>|null>(null);\n  let min = useMemo(() => maxDate(minValue, availableRange?.start), [minValue, availableRange]);\n  let max = useMemo(() => minDate(maxValue, availableRange?.end), [maxValue, availableRange]);\n\n  let calendar = useCalendarState({\n    ...calendarProps,\n    value: value && value.start,\n    createCalendar,\n    locale,\n    visibleDuration,\n    minValue: min,\n    maxValue: max,\n    selectionAlignment: alignment\n  });\n\n  let updateAvailableRange = (date) => {\n    if (date && props.isDateUnavailable && !props.allowsNonContiguousRanges) {\n      const nextAvailableStartDate = nextUnavailableDate(date, calendar, -1);\n      const nextAvailableEndDate = nextUnavailableDate(date, calendar, 1);\n      availableRangeRef.current = {\n        start: nextAvailableStartDate,\n        end: nextAvailableEndDate\n      };\n      setAvailableRange(availableRangeRef.current);\n    } else {\n      availableRangeRef.current = null;\n      setAvailableRange(null);\n    }\n  };\n\n  // If the visible range changes, we need to update the available range.\n  let [lastVisibleRange, setLastVisibleRange] = useState(calendar.visibleRange);\n  if (!isEqualDay(calendar.visibleRange.start, lastVisibleRange.start) || !isEqualDay(calendar.visibleRange.end, lastVisibleRange.end)) {\n    updateAvailableRange(anchorDate);\n    setLastVisibleRange(calendar.visibleRange);\n  }\n\n  let setAnchorDate = (date: CalendarDate | null) => {\n    if (date) {\n      setAnchorDateState(date);\n      updateAvailableRange(date);\n    } else {\n      setAnchorDateState(null);\n      updateAvailableRange(null);\n    }\n  };\n\n  let highlightedRange = anchorDate ? makeRange(anchorDate, calendar.focusedDate) : value && makeRange(value.start, value.end);\n  let selectDate = (date: CalendarDate) => {\n    if (props.isReadOnly) {\n      return;\n    }\n\n    const constrainedDate = constrainValue(date, min, max);\n    const previousAvailableConstrainedDate = previousAvailableDate(constrainedDate, calendar.visibleRange.start, props.isDateUnavailable);\n    if (!previousAvailableConstrainedDate) {\n      return;\n    }\n\n    if (!anchorDate) {\n      setAnchorDate(previousAvailableConstrainedDate);\n    } else {\n      let range = makeRange(anchorDate, previousAvailableConstrainedDate);\n      if (range) {\n        setValue({\n          start: convertValue(range.start, value?.start) as T,\n          end: convertValue(range.end, value?.end) as T\n        });\n      }\n      setAnchorDate(null);\n    }\n  };\n\n  let [isDragging, setDragging] = useState(false);\n\n  let {isDateUnavailable} = props;\n  let isInvalidSelection = useMemo(() => {\n    if (!value || anchorDate) {\n      return false;\n    }\n\n    if (isDateUnavailable && (isDateUnavailable(value.start) || isDateUnavailable(value.end))) {\n      return true;\n    }\n\n    return isInvalid(value.start, minValue, maxValue) || isInvalid(value.end, minValue, maxValue);\n  }, [isDateUnavailable, value, anchorDate, minValue, maxValue]);\n\n  let isValueInvalid = props.isInvalid || props.validationState === 'invalid' || isInvalidSelection;\n  let validationState: ValidationState | null = isValueInvalid ? 'invalid' : null;\n\n  return {\n    ...calendar,\n    value,\n    setValue,\n    anchorDate,\n    setAnchorDate,\n    highlightedRange,\n    validationState,\n    isValueInvalid,\n    selectFocusedDate() {\n      selectDate(calendar.focusedDate);\n    },\n    selectDate,\n    highlightDate(date) {\n      if (anchorDate) {\n        calendar.setFocusedDate(date);\n      }\n    },\n    isSelected(date) {\n      return Boolean(highlightedRange && date.compare(highlightedRange.start) >= 0 && date.compare(highlightedRange.end) <= 0 && !calendar.isCellDisabled(date) && !calendar.isCellUnavailable(date));\n    },\n    isInvalid(date) {\n      return calendar.isInvalid(date) || isInvalid(date, availableRangeRef.current?.start, availableRangeRef.current?.end);\n    },\n    isDragging,\n    setDragging\n  };\n}\n\nfunction makeRange(start: DateValue, end: DateValue): RangeValue<CalendarDate> | null {\n  if (!start || !end) {\n    return null;\n  }\n\n  if (end.compare(start) < 0) {\n    [start, end] = [end, start];\n  }\n\n  return {start: toCalendarDate(start), end: toCalendarDate(end)};\n}\n\nfunction convertValue(newValue: CalendarDate, oldValue?: DateValue): DateValue {\n  // The display calendar should not have any effect on the emitted value.\n  // Emit dates in the same calendar as the original value, if any, otherwise gregorian.\n  newValue = toCalendar(newValue, oldValue?.calendar || new GregorianCalendar());\n\n  // Preserve time if the input value had one.\n  if (oldValue && 'hour' in oldValue) {\n    return oldValue.set(newValue);\n  }\n\n  return newValue;\n}\n\nfunction nextUnavailableDate(anchorDate: CalendarDate, state: CalendarState, dir: number): CalendarDate | undefined {\n  let nextDate = anchorDate.add({days: dir});\n  while (\n    (dir < 0 ? nextDate.compare(state.visibleRange.start) >= 0 : nextDate.compare(state.visibleRange.end) <= 0) &&\n    !state.isCellUnavailable(nextDate)\n  ) {\n    nextDate = nextDate.add({days: dir});\n  }\n\n  if (state.isCellUnavailable(nextDate)) {\n    return nextDate.add({days: -dir});\n  }\n\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAgCM,SAAS,0CAAuD,KAAmC;IACxG,IAAI,EAAC,OAAO,SAAS,EAAA,cAAE,YAAY,EAAA,UAAE,QAAQ,EAAA,gBAAE,cAAc,EAAA,QAAE,MAAM,EAAA,iBAAE,kBAAkB;QAAC,QAAQ;IAAC,CAAA,EAAA,UAAG,QAAQ,EAAA,UAAE,QAAQ,EAAE,GAAG,eAAc,GAAG;IAC9I,IAAI,CAAC,OAAO,SAAS,GAAG,CAAA,iLAAA,qBAAiB,EACvC,WACA,gBAAgB,MAChB;IAGF,IAAI,CAAC,YAAY,mBAAmB,GAAG,CAAA,iKAAA,WAAO,EAAuB;IACrE,IAAI,YAAgC;IACpC,IAAI,SAAS,MAAM,KAAK,IAAI,MAAM,GAAG,EAAE;QACrC,IAAI,QAAQ,CAAA,uKAAA,cAAU,EAAE,CAAA,yKAAA,iBAAa,EAAE,MAAM,KAAK,GAAG,iBAAiB,QAAQ,UAAU;QACxF,IAAI,MAAM,MAAM,GAAG,CAAC,iBAAiB,QAAQ,CAAC;YAAC,MAAM;QAAC;QAEtD,IAAI,MAAM,GAAG,CAAC,OAAO,CAAC,OAAO,GAC3B,YAAY;IAEhB;IAEA,+GAA+G;IAC/G,IAAI,oBAAoB,CAAA,iKAAA,SAAK,EAAyC;IACtE,IAAI,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,iKAAA,WAAO,EAAuC;IACxF,IAAI,MAAM,CAAA,iKAAA,UAAM,EAAE,IAAM,CAAA,sKAAA,UAAM,EAAE,UAAU,mBAAA,QAAA,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAgB,KAAK,GAAG;QAAC;QAAU;KAAe;IAC5F,IAAI,MAAM,CAAA,iKAAA,UAAM,EAAE,IAAM,CAAA,sKAAA,UAAM,EAAE,UAAU,mBAAA,QAAA,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAgB,GAAG,GAAG;QAAC;QAAU;KAAe;IAE1F,IAAI,WAAW,CAAA,kLAAA,mBAAe,EAAE;QAC9B,GAAG,aAAa;QAChB,OAAO,SAAS,MAAM,KAAK;wBAC3B;gBACA;yBACA;QACA,UAAU;QACV,UAAU;QACV,oBAAoB;IACtB;IAEA,IAAI,uBAAuB,CAAC;QAC1B,IAAI,QAAQ,MAAM,iBAAiB,IAAI,CAAC,MAAM,yBAAyB,EAAE;YACvE,MAAM,yBAAyB,0CAAoB,MAAM,UAAU,CAAA;YACnE,MAAM,uBAAuB,0CAAoB,MAAM,UAAU;YACjE,kBAAkB,OAAO,GAAG;gBAC1B,OAAO;gBACP,KAAK;YACP;YACA,kBAAkB,kBAAkB,OAAO;QAC7C,OAAO;YACL,kBAAkB,OAAO,GAAG;YAC5B,kBAAkB;QACpB;IACF;IAEA,uEAAuE;IACvE,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,iKAAA,WAAO,EAAE,SAAS,YAAY;IAC5E,IAAI,CAAC,CAAA,sKAAA,aAAS,EAAE,SAAS,YAAY,CAAC,KAAK,EAAE,iBAAiB,KAAK,KAAK,CAAC,CAAA,sKAAA,aAAS,EAAE,SAAS,YAAY,CAAC,GAAG,EAAE,iBAAiB,GAAG,GAAG;QACpI,qBAAqB;QACrB,oBAAoB,SAAS,YAAY;IAC3C;IAEA,IAAI,gBAAgB,CAAC;QACnB,IAAI,MAAM;YACR,mBAAmB;YACnB,qBAAqB;QACvB,OAAO;YACL,mBAAmB;YACnB,qBAAqB;QACvB;IACF;IAEA,IAAI,mBAAmB,aAAa,gCAAU,YAAY,SAAS,WAAW,IAAI,SAAS,gCAAU,MAAM,KAAK,EAAE,MAAM,GAAG;IAC3H,IAAI,aAAa,CAAC;QAChB,IAAI,MAAM,UAAU,EAClB;QAGF,MAAM,kBAAkB,CAAA,uKAAA,iBAAa,EAAE,MAAM,KAAK;QAClD,MAAM,mCAAmC,CAAA,uKAAA,wBAAoB,EAAE,iBAAiB,SAAS,YAAY,CAAC,KAAK,EAAE,MAAM,iBAAiB;QACpI,IAAI,CAAC,kCACH;QAGF,IAAI,CAAC,YACH,cAAc;aACT;YACL,IAAI,QAAQ,gCAAU,YAAY;YAClC,IAAI,OACF,SAAS;gBACP,OAAO,mCAAa,MAAM,KAAK,EAAE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,KAAK;gBAC7C,KAAK,mCAAa,MAAM,GAAG,EAAE,UAAA,QAAA,UAAA,KAAA,IAAA,KAAA,IAAA,MAAO,GAAG;YACzC;YAEF,cAAc;QAChB;IACF;IAEA,IAAI,CAAC,YAAY,YAAY,GAAG,CAAA,iKAAA,WAAO,EAAE;IAEzC,IAAI,EAAA,mBAAC,iBAAiB,EAAC,GAAG;IAC1B,IAAI,qBAAqB,CAAA,iKAAA,UAAM,EAAE;QAC/B,IAAI,CAAC,SAAS,YACZ,OAAO;QAGT,IAAI,qBAAsB,CAAA,kBAAkB,MAAM,KAAK,KAAK,kBAAkB,MAAM,GAAG,CAAA,GACrF,OAAO;QAGT,OAAO,CAAA,uKAAA,YAAQ,EAAE,MAAM,KAAK,EAAE,UAAU,aAAa,CAAA,uKAAA,YAAQ,EAAE,MAAM,GAAG,EAAE,UAAU;IACtF,GAAG;QAAC;QAAmB;QAAO;QAAY;QAAU;KAAS;IAE7D,IAAI,iBAAiB,MAAM,SAAS,IAAI,MAAM,eAAe,KAAK,aAAa;IAC/E,IAAI,kBAA0C,iBAAiB,YAAY;IAE3E,OAAO;QACL,GAAG,QAAQ;eACX;kBACA;oBACA;uBACA;0BACA;yBACA;wBACA;QACA;YACE,WAAW,SAAS,WAAW;QACjC;oBACA;QACA,eAAc,IAAI;YAChB,IAAI,YACF,SAAS,cAAc,CAAC;QAE5B;QACA,YAAW,IAAI;YACb,OAAO,QAAQ,oBAAoB,KAAK,OAAO,CAAC,iBAAiB,KAAK,KAAK,KAAK,KAAK,OAAO,CAAC,iBAAiB,GAAG,KAAK,KAAK,CAAC,SAAS,cAAc,CAAC,SAAS,CAAC,SAAS,iBAAiB,CAAC;QAC3L;QACA,WAAU,IAAI;gBACuC,4BAAkC;YAArF,OAAO,SAAS,SAAS,CAAC,SAAS,CAAA,uKAAA,YAAQ,EAAE,MAAA,CAAM,6BAAA,kBAAkB,OAAO,MAAA,QAAzB,+BAAA,KAAA,IAAA,KAAA,IAAA,2BAA2B,KAAK,EAAA,CAAE,8BAAA,kBAAkB,OAAO,MAAA,QAAzB,gCAAA,KAAA,IAAA,KAAA,IAAA,4BAA2B,GAAG;QACrH;oBACA;qBACA;IACF;AACF;AAEA,SAAS,gCAAU,KAAgB,EAAE,GAAc;IACjD,IAAI,CAAC,SAAS,CAAC,KACb,OAAO;IAGT,IAAI,IAAI,OAAO,CAAC,SAAS,GACvB,CAAC,OAAO,IAAI,GAAG;QAAC;QAAK;KAAM;IAG7B,OAAO;QAAC,OAAO,CAAA,yKAAA,iBAAa,EAAE;QAAQ,KAAK,CAAA,yKAAA,iBAAa,EAAE;IAAI;AAChE;AAEA,SAAS,mCAAa,QAAsB,EAAE,QAAoB;IAChE,wEAAwE;IACxE,sFAAsF;IACtF,WAAW,CAAA,yKAAA,aAAS,EAAE,UAAU,CAAA,aAAA,QAAA,aAAA,KAAA,IAAA,KAAA,IAAA,SAAU,QAAQ,KAAI,IAAI,CAAA,gLAAA,oBAAgB;IAE1E,4CAA4C;IAC5C,IAAI,YAAY,UAAU,UACxB,OAAO,SAAS,GAAG,CAAC;IAGtB,OAAO;AACT;AAEA,SAAS,0CAAoB,UAAwB,EAAE,KAAoB,EAAE,GAAW;IACtF,IAAI,WAAW,WAAW,GAAG,CAAC;QAAC,MAAM;IAAG;IACxC,MACG,CAAA,MAAM,IAAI,SAAS,OAAO,CAAC,MAAM,YAAY,CAAC,KAAK,KAAK,IAAI,SAAS,OAAO,CAAC,MAAM,YAAY,CAAC,GAAG,KAAK,CAAA,KACzG,CAAC,MAAM,iBAAiB,CAAC,UAEzB,WAAW,SAAS,GAAG,CAAC;QAAC,MAAM;IAAG;IAGpC,IAAI,MAAM,iBAAiB,CAAC,WAC1B,OAAO,SAAS,GAAG,CAAC;QAAC,MAAM,CAAC;IAAG;AAGnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1877, "column": 0}, "map": {"version": 3, "file": "useControlledState.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-stately/utils/dist/packages/%40react-stately/utils/src/useControlledState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {useCallback, useEffect, useRef, useState} from 'react';\n\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined>, defaultValue: Exclude<T, undefined> | undefined, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: Exclude<T, undefined> | undefined, defaultValue: Exclude<T, undefined>, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void];\nexport function useControlledState<T, C = T>(value: T, defaultValue: T, onChange?: (v: C, ...args: any[]) => void): [T, (value: T, ...args: any[]) => void] {\n  let [stateValue, setStateValue] = useState(value || defaultValue);\n\n  let isControlledRef = useRef(value !== undefined);\n  let isControlled = value !== undefined;\n  useEffect(() => {\n    let wasControlled = isControlledRef.current;\n    if (wasControlled !== isControlled && process.env.NODE_ENV !== 'production') {\n      console.warn(`WARN: A component changed from ${wasControlled ? 'controlled' : 'uncontrolled'} to ${isControlled ? 'controlled' : 'uncontrolled'}.`);\n    }\n    isControlledRef.current = isControlled;\n  }, [isControlled]);\n\n  let currentValue = isControlled ? value : stateValue;\n  let setValue = useCallback((value, ...args) => {\n    let onChangeCaller = (value, ...onChangeArgs) => {\n      if (onChange) {\n        if (!Object.is(currentValue, value)) {\n          onChange(value, ...onChangeArgs);\n        }\n      }\n      if (!isControlled) {\n        // If uncontrolled, mutate the currentValue local variable so that\n        // calling setState multiple times with the same value only emits onChange once.\n        // We do not use a ref for this because we specifically _do_ want the value to\n        // reset every render, and assigning to a ref in render breaks aborted suspended renders.\n        // eslint-disable-next-line react-hooks/exhaustive-deps\n        currentValue = value;\n      }\n    };\n\n    if (typeof value === 'function') {\n      if (process.env.NODE_ENV !== 'production') {\n        console.warn('We can not support a function callback. See Github Issues for details https://github.com/adobe/react-spectrum/issues/2320');\n      }\n      // this supports functional updates https://reactjs.org/docs/hooks-reference.html#functional-updates\n      // when someone using useControlledState calls setControlledState(myFunc)\n      // this will call our useState setState with a function as well which invokes myFunc and calls onChange with the value from myFunc\n      // if we're in an uncontrolled state, then we also return the value of myFunc which to setState looks as though it was just called with myFunc from the beginning\n      // otherwise we just return the controlled value, which won't cause a rerender because React knows to bail out when the value is the same\n      let updateFunction = (oldValue, ...functionArgs) => {\n        let interceptedValue = value(isControlled ? currentValue : oldValue, ...functionArgs);\n        onChangeCaller(interceptedValue, ...args);\n        if (!isControlled) {\n          return interceptedValue;\n        }\n        return oldValue;\n      };\n      setStateValue(updateFunction);\n    } else {\n      if (!isControlled) {\n        setStateValue(value);\n      }\n      onChangeCaller(value, ...args);\n    }\n  }, [isControlled, currentValue, onChange]);\n\n  return [currentValue, setValue];\n}\n"], "names": [], "mappings": ";;;AAuB0C,QAAQ,GAAG,CAAC,QAAQ;;;AAvB9D;;;;;;;;;;CAUC,GAMM,SAAS,0CAA6B,KAAQ,EAAE,YAAe,EAAE,QAAyC;IAC/G,IAAI,CAAC,YAAY,cAAc,GAAG,CAAA,iKAAA,WAAO,EAAE,SAAS;IAEpD,IAAI,kBAAkB,CAAA,GAAA,uKAAK,EAAE,UAAU;IACvC,IAAI,eAAe,UAAU;IAC7B,CAAA,iKAAA,YAAQ,EAAE;QACR,IAAI,gBAAgB,gBAAgB,OAAO;QAC3C,IAAI,kBAAkB,oEAAyC,cAC7D,QAAQ,IAAI,CAAC,CAAC,+BAA+B,EAAE,gBAAgB,eAAe,eAAe,IAAI,EAAE,eAAe,eAAe,eAAe,CAAC,CAAC;QAEpJ,gBAAgB,OAAO,GAAG;IAC5B,GAAG;QAAC;KAAa;IAEjB,IAAI,eAAe,eAAe,QAAQ;IAC1C,IAAI,WAAW,CAAA,iKAAA,cAAU,EAAE,CAAC,OAAO,GAAG;QACpC,IAAI,iBAAiB,CAAC,OAAO,GAAG;YAC9B,IAAI,UACF;gBAAA,IAAI,CAAC,OAAO,EAAE,CAAC,cAAc,QAC3B,SAAS,UAAU;YACrB;YAEF,IAAI,CAAC,cACH,AACA,kEADkE,cACc;YAChF,8EAA8E;YAC9E,yFAAyF;YACzF,uDAAuD;YACvD,eAAe;QAEnB;QAEA,IAAI,OAAO,UAAU,YAAY;YAC/B,IAAI,QAAQ,GAAG,CAAC,QAAQ,KAAK,WAC3B,QAAQ,IAAI,CAAC;YAEf,oGAAoG;YACpG,yEAAyE;YACzE,kIAAkI;YAClI,iKAAiK;YACjK,yIAAyI;YACzI,IAAI,iBAAiB,CAAC,UAAU,GAAG;gBACjC,IAAI,mBAAmB,MAAM,eAAe,eAAe,aAAa;gBACxE,eAAe,qBAAqB;gBACpC,IAAI,CAAC,cACH,OAAO;gBAET,OAAO;YACT;YACA,cAAc;QAChB,OAAO;YACL,IAAI,CAAC,cACH,cAAc;YAEhB,eAAe,UAAU;QAC3B;IACF,GAAG;QAAC;QAAc;QAAc;KAAS;IAEzC,OAAO;QAAC;QAAc;KAAS;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1952, "column": 0}, "map": {"version": 3, "file": "useFormValidationState.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-stately/form/dist/packages/%40react-stately/form/src/useFormValidationState.ts"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {Context, createContext, useContext, useEffect, useMemo, useRef, useState} from 'react';\nimport {Validation, ValidationErrors, ValidationFunction, ValidationResult} from '@react-types/shared';\n\nexport const VALID_VALIDITY_STATE: ValidityState = {\n  badInput: false,\n  customError: false,\n  patternMismatch: false,\n  rangeOverflow: false,\n  rangeUnderflow: false,\n  stepMismatch: false,\n  tooLong: false,\n  tooShort: false,\n  typeMismatch: false,\n  valueMissing: false,\n  valid: true\n};\n\nconst CUSTOM_VALIDITY_STATE: ValidityState = {\n  ...VALID_VALIDITY_STATE,\n  customError: true,\n  valid: false\n};\n\nexport const DEFAULT_VALIDATION_RESULT: ValidationResult = {\n  isInvalid: false,\n  validationDetails: VALID_VALIDITY_STATE,\n  validationErrors: []\n};\n\nexport const FormValidationContext: Context<ValidationErrors> = createContext<ValidationErrors>({});\n\nexport const privateValidationStateProp: string = '__formValidationState' + Date.now();\n\ninterface FormValidationProps<T> extends Validation<T> {\n  builtinValidation?: ValidationResult,\n  name?: string | string[],\n  value: T | null\n}\n\nexport interface FormValidationState {\n  /** Realtime validation results, updated as the user edits the value. */\n  realtimeValidation: ValidationResult,\n  /** Currently displayed validation results, updated when the user commits their changes. */\n  displayValidation: ValidationResult,\n  /** Updates the current validation result. Not displayed to the user until `commitValidation` is called. */\n  updateValidation(result: ValidationResult): void,\n  /** Resets the displayed validation state to valid when the user resets the form. */\n  resetValidation(): void,\n  /** Commits the realtime validation so it is displayed to the user. */\n  commitValidation(): void\n}\n\nexport function useFormValidationState<T>(props: FormValidationProps<T>): FormValidationState {\n  // Private prop for parent components to pass state to children.\n  if (props[privateValidationStateProp]) {\n    let {realtimeValidation, displayValidation, updateValidation, resetValidation, commitValidation} = props[privateValidationStateProp] as FormValidationState;\n    return {realtimeValidation, displayValidation, updateValidation, resetValidation, commitValidation};\n  }\n\n  // eslint-disable-next-line react-hooks/rules-of-hooks\n  return useFormValidationStateImpl(props);\n}\n\nfunction useFormValidationStateImpl<T>(props: FormValidationProps<T>): FormValidationState {\n  let {isInvalid, validationState, name, value, builtinValidation, validate, validationBehavior = 'aria'} = props;\n\n  // backward compatibility.\n  if (validationState) {\n    isInvalid ||= validationState === 'invalid';\n  }\n\n  // If the isInvalid prop is controlled, update validation result in realtime.\n  let controlledError: ValidationResult | null = isInvalid !== undefined ? {\n    isInvalid,\n    validationErrors: [],\n    validationDetails: CUSTOM_VALIDITY_STATE\n  } : null;\n\n  // Perform custom client side validation.\n  let clientError: ValidationResult | null = useMemo(() => {\n    if (!validate || value == null) {\n      return null;\n    }\n    let validateErrors = runValidate(validate, value);\n    return getValidationResult(validateErrors);\n  }, [validate, value]);\n\n  if (builtinValidation?.validationDetails.valid) {\n    builtinValidation = undefined;\n  }\n\n  // Get relevant server errors from the form.\n  let serverErrors = useContext(FormValidationContext);\n  let serverErrorMessages = useMemo(() => {\n    if (name) {\n      return Array.isArray(name) ? name.flatMap(name => asArray(serverErrors[name])) : asArray(serverErrors[name]);\n    }\n    return [];\n  }, [serverErrors, name]);\n\n  // Show server errors when the form gets a new value, and clear when the user changes the value.\n  let [lastServerErrors, setLastServerErrors] = useState(serverErrors);\n  let [isServerErrorCleared, setServerErrorCleared] = useState(false);\n  if (serverErrors !== lastServerErrors) {\n    setLastServerErrors(serverErrors);\n    setServerErrorCleared(false);\n  }\n\n  let serverError: ValidationResult | null = useMemo(() =>\n    getValidationResult(isServerErrorCleared ? [] : serverErrorMessages),\n    [isServerErrorCleared, serverErrorMessages]\n  );\n\n  // Track the next validation state in a ref until commitValidation is called.\n  let nextValidation = useRef(DEFAULT_VALIDATION_RESULT);\n  let [currentValidity, setCurrentValidity] = useState(DEFAULT_VALIDATION_RESULT);\n\n  let lastError = useRef(DEFAULT_VALIDATION_RESULT);\n  let commitValidation = () => {\n    if (!commitQueued) {\n      return;\n    }\n\n    setCommitQueued(false);\n    let error = clientError || builtinValidation || nextValidation.current;\n    if (!isEqualValidation(error, lastError.current)) {\n      lastError.current = error;\n      setCurrentValidity(error);\n    }\n  };\n\n  let [commitQueued, setCommitQueued] = useState(false);\n  useEffect(commitValidation);\n\n  // realtimeValidation is used to update the native input element's state based on custom validation logic.\n  // displayValidation is the currently displayed validation state that the user sees (e.g. on input change/form submit).\n  // With validationBehavior=\"aria\", all errors are displayed in realtime rather than on submit.\n  let realtimeValidation = controlledError || serverError || clientError || builtinValidation || DEFAULT_VALIDATION_RESULT;\n  let displayValidation = validationBehavior === 'native'\n    ? controlledError || serverError || currentValidity\n    : controlledError || serverError || clientError || builtinValidation || currentValidity;\n\n  return {\n    realtimeValidation,\n    displayValidation,\n    updateValidation(value) {\n      // If validationBehavior is 'aria', update in realtime. Otherwise, store in a ref until commit.\n      if (validationBehavior === 'aria' && !isEqualValidation(currentValidity, value)) {\n        setCurrentValidity(value);\n      } else {\n        nextValidation.current = value;\n      }\n    },\n    resetValidation() {\n      // Update the currently displayed validation state to valid on form reset,\n      // even if the native validity says it isn't. It'll show again on the next form submit.\n      let error = DEFAULT_VALIDATION_RESULT;\n      if (!isEqualValidation(error, lastError.current)) {\n        lastError.current = error;\n        setCurrentValidity(error);\n      }\n\n      // Do not commit validation after the next render. This avoids a condition where\n      // useSelect calls commitValidation inside an onReset handler.\n      if (validationBehavior === 'native') {\n        setCommitQueued(false);\n      }\n\n      setServerErrorCleared(true);\n    },\n    commitValidation() {\n      // Commit validation state so the user sees it on blur/change/submit. Also clear any server errors.\n      // Wait until after the next render to commit so that the latest value has been validated.\n      if (validationBehavior === 'native') {\n        setCommitQueued(true);\n      }\n      setServerErrorCleared(true);\n    }\n  };\n}\n\nfunction asArray<T>(v: T | T[]): T[] {\n  if (!v) {\n    return [];\n  }\n\n  return Array.isArray(v) ? v : [v];\n}\n\nfunction runValidate<T>(validate: ValidationFunction<T>, value: T): string[] {\n  if (typeof validate === 'function') {\n    let e = validate(value);\n    if (e && typeof e !== 'boolean') {\n      return asArray(e);\n    }\n  }\n\n  return [];\n}\n\nfunction getValidationResult(errors: string[]): ValidationResult | null {\n  return errors.length ? {\n    isInvalid: true,\n    validationErrors: errors,\n    validationDetails: CUSTOM_VALIDITY_STATE\n  } : null;\n}\n\nfunction isEqualValidation(a: ValidationResult | null, b: ValidationResult | null): boolean {\n  if (a === b) {\n    return true;\n  }\n\n  return !!a && !!b\n    && a.isInvalid === b.isInvalid\n    && a.validationErrors.length === b.validationErrors.length\n    && a.validationErrors.every((a, i) => a === b.validationErrors[i])\n    && Object.entries(a.validationDetails).every(([k, v]) => b.validationDetails[k] === v);\n}\n\nexport function mergeValidation(...results: ValidationResult[]): ValidationResult {\n  let errors = new Set<string>();\n  let isInvalid = false;\n  let validationDetails = {\n    ...VALID_VALIDITY_STATE\n  };\n\n  for (let v of results) {\n    for (let e of v.validationErrors) {\n      errors.add(e);\n    }\n\n    // Only these properties apply for checkboxes.\n    isInvalid ||= v.isInvalid;\n    for (let key in validationDetails) {\n      validationDetails[key] ||= v.validationDetails[key];\n    }\n  }\n\n  validationDetails.valid = !isInvalid;\n  return {\n    isInvalid,\n    validationErrors: [...errors],\n    validationDetails\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAKM,MAAM,4CAAsC;IACjD,UAAU;IACV,aAAa;IACb,iBAAiB;IACjB,eAAe;IACf,gBAAgB;IAChB,cAAc;IACd,SAAS;IACT,UAAU;IACV,cAAc;IACd,cAAc;IACd,OAAO;AACT;AAEA,MAAM,8CAAuC;IAC3C,GAAG,yCAAoB;IACvB,aAAa;IACb,OAAO;AACT;AAEO,MAAM,4CAA8C;IACzD,WAAW;IACX,mBAAmB;IACnB,kBAAkB,EAAE;AACtB;AAEO,MAAM,4CAAmD,CAAA,iKAAA,gBAAY,EAAoB,CAAC;AAE1F,MAAM,2CAAqC,0BAA0B,KAAK,GAAG;AAqB7E,SAAS,0CAA0B,KAA6B;IACrE,gEAAgE;IAChE,IAAI,KAAK,CAAC,yCAA2B,EAAE;QACrC,IAAI,EAAA,oBAAC,kBAAkB,EAAA,mBAAE,iBAAiB,EAAA,kBAAE,gBAAgB,EAAA,iBAAE,eAAe,EAAA,kBAAE,gBAAgB,EAAC,GAAG,KAAK,CAAC,yCAA2B;QACpI,OAAO;gCAAC;+BAAoB;8BAAmB;6BAAkB;8BAAiB;QAAgB;IACpG;IAEA,sDAAsD;IACtD,OAAO,iDAA2B;AACpC;AAEA,SAAS,iDAA8B,KAA6B;IAClE,IAAI,EAAA,WAAC,SAAS,EAAA,iBAAE,eAAe,EAAA,MAAE,IAAI,EAAA,OAAE,KAAK,EAAA,mBAAE,iBAAiB,EAAA,UAAE,QAAQ,EAAA,oBAAE,qBAAqB,MAAA,EAAO,GAAG;IAE1G,0BAA0B;IAC1B,IAAI,iBACF,aAAA,CAAA,YAAc,oBAAoB,SAAA;IAGpC,6EAA6E;IAC7E,IAAI,kBAA2C,cAAc,YAAY;mBACvE;QACA,kBAAkB,EAAE;QACpB,mBAAmB;IACrB,IAAI;IAEJ,yCAAyC;IACzC,IAAI,cAAuC,CAAA,iKAAA,UAAM,EAAE;QACjD,IAAI,CAAC,YAAY,SAAS,MACxB,OAAO;QAET,IAAI,iBAAiB,kCAAY,UAAU;QAC3C,OAAO,0CAAoB;IAC7B,GAAG;QAAC;QAAU;KAAM;IAEpB,IAAI,sBAAA,QAAA,sBAAA,KAAA,IAAA,KAAA,IAAA,kBAAmB,iBAAiB,CAAC,KAAK,EAC5C,oBAAoB;IAGtB,4CAA4C;IAC5C,IAAI,eAAe,CAAA,iKAAA,aAAS,EAAE;IAC9B,IAAI,sBAAsB,CAAA,iKAAA,UAAM,EAAE;QAChC,IAAI,MACF,OAAO,MAAM,OAAO,CAAC,QAAQ,KAAK,OAAO,CAAC,CAAA,OAAQ,8BAAQ,YAAY,CAAC,KAAK,KAAK,8BAAQ,YAAY,CAAC,KAAK;QAE7G,OAAO,EAAE;IACX,GAAG;QAAC;QAAc;KAAK;IAEvB,gGAAgG;IAChG,IAAI,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,iKAAA,WAAO,EAAE;IACvD,IAAI,CAAC,sBAAsB,sBAAsB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAC7D,IAAI,iBAAiB,kBAAkB;QACrC,oBAAoB;QACpB,sBAAsB;IACxB;IAEA,IAAI,cAAuC,CAAA,iKAAA,UAAM,EAAE,IACjD,0CAAoB,uBAAuB,EAAE,GAAG,sBAChD;QAAC;QAAsB;KAAoB;IAG7C,6EAA6E;IAC7E,IAAI,iBAAiB,CAAA,iKAAA,SAAK,EAAE;IAC5B,IAAI,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAErD,IAAI,YAAY,CAAA,iKAAA,SAAK,EAAE;IACvB,IAAI,mBAAmB;QACrB,IAAI,CAAC,cACH;QAGF,gBAAgB;QAChB,IAAI,QAAQ,eAAe,qBAAqB,eAAe,OAAO;QACtE,IAAI,CAAC,wCAAkB,OAAO,UAAU,OAAO,GAAG;YAChD,UAAU,OAAO,GAAG;YACpB,mBAAmB;QACrB;IACF;IAEA,IAAI,CAAC,cAAc,gBAAgB,GAAG,CAAA,iKAAA,WAAO,EAAE;IAC/C,CAAA,iKAAA,YAAQ,EAAE;IAEV,0GAA0G;IAC1G,uHAAuH;IACvH,8FAA8F;IAC9F,IAAI,qBAAqB,mBAAmB,eAAe,eAAe,qBAAqB;IAC/F,IAAI,oBAAoB,uBAAuB,WAC3C,mBAAmB,eAAe,kBAClC,mBAAmB,eAAe,eAAe,qBAAqB;IAE1E,OAAO;4BACL;2BACA;QACA,kBAAiB,KAAK;YACpB,+FAA+F;YAC/F,IAAI,uBAAuB,UAAU,CAAC,wCAAkB,iBAAiB,QACvE,mBAAmB;iBAEnB,eAAe,OAAO,GAAG;QAE7B;QACA;YACE,0EAA0E;YAC1E,uFAAuF;YACvF,IAAI,QAAQ;YACZ,IAAI,CAAC,wCAAkB,OAAO,UAAU,OAAO,GAAG;gBAChD,UAAU,OAAO,GAAG;gBACpB,mBAAmB;YACrB;YAEA,gFAAgF;YAChF,8DAA8D;YAC9D,IAAI,uBAAuB,UACzB,gBAAgB;YAGlB,sBAAsB;QACxB;QACA;YACE,mGAAmG;YACnG,0FAA0F;YAC1F,IAAI,uBAAuB,UACzB,gBAAgB;YAElB,sBAAsB;QACxB;IACF;AACF;AAEA,SAAS,8BAAW,CAAU;IAC5B,IAAI,CAAC,GACH,OAAO,EAAE;IAGX,OAAO,MAAM,OAAO,CAAC,KAAK,IAAI;QAAC;KAAE;AACnC;AAEA,SAAS,kCAAe,QAA+B,EAAE,KAAQ;IAC/D,IAAI,OAAO,aAAa,YAAY;QAClC,IAAI,IAAI,SAAS;QACjB,IAAI,KAAK,OAAO,MAAM,WACpB,OAAO,8BAAQ;IAEnB;IAEA,OAAO,EAAE;AACX;AAEA,SAAS,0CAAoB,MAAgB;IAC3C,OAAO,OAAO,MAAM,GAAG;QACrB,WAAW;QACX,kBAAkB;QAClB,mBAAmB;IACrB,IAAI;AACN;AAEA,SAAS,wCAAkB,CAA0B,EAAE,CAA0B;IAC/E,IAAI,MAAM,GACR,OAAO;IAGT,OAAO,CAAC,CAAC,KAAK,CAAC,CAAC,KACX,EAAE,SAAS,KAAK,EAAE,SAAS,IAC3B,EAAE,gBAAgB,CAAC,MAAM,KAAK,EAAE,gBAAgB,CAAC,MAAM,IACvD,EAAE,gBAAgB,CAAC,KAAK,CAAC,CAAC,GAAG,IAAM,MAAM,EAAE,gBAAgB,CAAC,EAAE,KAC9D,OAAO,OAAO,CAAC,EAAE,iBAAiB,EAAE,KAAK,CAAC,CAAC,CAAC,GAAG,EAAE,GAAK,EAAE,iBAAiB,CAAC,EAAE,KAAK;AACxF;AAEO,SAAS,0CAAgB,GAAG,OAA2B;IAC5D,IAAI,SAAS,IAAI;IACjB,IAAI,YAAY;IAChB,IAAI,oBAAoB;QACtB,GAAG,yCAAoB;IACzB;IAEA,KAAK,IAAI,KAAK,QAAS;YAQnB,oBAAkB;QAPpB,KAAK,IAAI,KAAK,EAAE,gBAAgB,CAC9B,OAAO,GAAG,CAAC;QAGb,8CAA8C;QAC9C,aAAA,CAAA,YAAc,EAAE,SAAS;QACzB,IAAK,IAAI,OAAO,kBACd,CAAA,qBAAA,iBAAA,CAAiB,CAAC,OAAA,IAAI,IAAA,CAAtB,kBAAiB,CAAC,KAAI,GAAK,EAAE,iBAAiB,CAAC,IAAI;IAEvD;IAEA,kBAAkB,KAAK,GAAG,CAAC;IAC3B,OAAO;mBACL;QACA,kBAAkB;eAAI;SAAO;2BAC7B;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2155, "column": 0}, "map": {"version": 3, "file": "useOverlayTriggerState.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40react-stately/overlays/dist/packages/%40react-stately/overlays/src/useOverlayTriggerState.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {OverlayTriggerProps} from '@react-types/overlays';\nimport {useCallback} from 'react';\nimport {useControlledState} from '@react-stately/utils';\n\nexport interface OverlayTriggerState {\n  /** Whether the overlay is currently open. */\n  readonly isOpen: boolean,\n  /** Sets whether the overlay is open. */\n  setOpen(isOpen: boolean): void,\n  /** Opens the overlay. */\n  open(): void,\n  /** Closes the overlay. */\n  close(): void,\n  /** Toggles the overlay's visibility. */\n  toggle(): void\n}\n\n/**\n * Manages state for an overlay trigger. Tracks whether the overlay is open, and provides\n * methods to toggle this state.\n */\nexport function useOverlayTriggerState(props: OverlayTriggerProps): OverlayTriggerState  {\n  let [isOpen, setOpen] = useControlledState(props.isOpen, props.defaultOpen || false, props.onOpenChange);\n\n  const open = useCallback(() => {\n    setOpen(true);\n  }, [setOpen]);\n\n  const close = useCallback(() => {\n    setOpen(false);\n  }, [setOpen]);\n\n  const toggle = useCallback(() => {\n    setOpen(!isOpen);\n  }, [setOpen, isOpen]);\n\n  return {\n    isOpen,\n    setOpen,\n    open,\n    close,\n    toggle\n  };\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAuBM,SAAS,0CAAuB,KAA0B;IAC/D,IAAI,CAAC,QAAQ,QAAQ,GAAG,CAAA,iLAAA,qBAAiB,EAAE,MAAM,MAAM,EAAE,MAAM,WAAW,IAAI,OAAO,MAAM,YAAY;IAEvG,MAAM,OAAO,CAAA,iKAAA,cAAU,EAAE;QACvB,QAAQ;IACV,GAAG;QAAC;KAAQ;IAEZ,MAAM,QAAQ,CAAA,iKAAA,cAAU,EAAE;QACxB,QAAQ;IACV,GAAG;QAAC;KAAQ;IAEZ,MAAM,SAAS,CAAA,iKAAA,cAAU,EAAE;QACzB,QAAQ,CAAC;IACX,GAAG;QAAC;QAAS;KAAO;IAEpB,OAAO;gBACL;iBACA;cACA;eACA;gBACA;IACF;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2206, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/aria-hidden/dist/es2015/index.js"], "sourcesContent": ["var getDefaultParent = function (originalTarget) {\n    if (typeof document === 'undefined') {\n        return null;\n    }\n    var sampleTarget = Array.isArray(originalTarget) ? originalTarget[0] : originalTarget;\n    return sampleTarget.ownerDocument.body;\n};\nvar counterMap = new WeakMap();\nvar uncontrolledNodes = new WeakMap();\nvar markerMap = {};\nvar lockCount = 0;\nvar unwrapHost = function (node) {\n    return node && (node.host || unwrapHost(node.parentNode));\n};\nvar correctTargets = function (parent, targets) {\n    return targets\n        .map(function (target) {\n        if (parent.contains(target)) {\n            return target;\n        }\n        var correctedTarget = unwrapHost(target);\n        if (correctedTarget && parent.contains(correctedTarget)) {\n            return correctedTarget;\n        }\n        console.error('aria-hidden', target, 'in not contained inside', parent, '. Doing nothing');\n        return null;\n    })\n        .filter(function (x) { return Boolean(x); });\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @param {String} [controlAttribute] - html Attribute to control\n * @return {Undo} undo command\n */\nvar applyAttributeToOthers = function (originalTarget, parentNode, markerName, controlAttribute) {\n    var targets = correctTargets(parentNode, Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    if (!markerMap[markerName]) {\n        markerMap[markerName] = new WeakMap();\n    }\n    var markerCounter = markerMap[markerName];\n    var hiddenNodes = [];\n    var elementsToKeep = new Set();\n    var elementsToStop = new Set(targets);\n    var keep = function (el) {\n        if (!el || elementsToKeep.has(el)) {\n            return;\n        }\n        elementsToKeep.add(el);\n        keep(el.parentNode);\n    };\n    targets.forEach(keep);\n    var deep = function (parent) {\n        if (!parent || elementsToStop.has(parent)) {\n            return;\n        }\n        Array.prototype.forEach.call(parent.children, function (node) {\n            if (elementsToKeep.has(node)) {\n                deep(node);\n            }\n            else {\n                try {\n                    var attr = node.getAttribute(controlAttribute);\n                    var alreadyHidden = attr !== null && attr !== 'false';\n                    var counterValue = (counterMap.get(node) || 0) + 1;\n                    var markerValue = (markerCounter.get(node) || 0) + 1;\n                    counterMap.set(node, counterValue);\n                    markerCounter.set(node, markerValue);\n                    hiddenNodes.push(node);\n                    if (counterValue === 1 && alreadyHidden) {\n                        uncontrolledNodes.set(node, true);\n                    }\n                    if (markerValue === 1) {\n                        node.setAttribute(markerName, 'true');\n                    }\n                    if (!alreadyHidden) {\n                        node.setAttribute(controlAttribute, 'true');\n                    }\n                }\n                catch (e) {\n                    console.error('aria-hidden: cannot operate on ', node, e);\n                }\n            }\n        });\n    };\n    deep(parentNode);\n    elementsToKeep.clear();\n    lockCount++;\n    return function () {\n        hiddenNodes.forEach(function (node) {\n            var counterValue = counterMap.get(node) - 1;\n            var markerValue = markerCounter.get(node) - 1;\n            counterMap.set(node, counterValue);\n            markerCounter.set(node, markerValue);\n            if (!counterValue) {\n                if (!uncontrolledNodes.has(node)) {\n                    node.removeAttribute(controlAttribute);\n                }\n                uncontrolledNodes.delete(node);\n            }\n            if (!markerValue) {\n                node.removeAttribute(markerName);\n            }\n        });\n        lockCount--;\n        if (!lockCount) {\n            // clear\n            counterMap = new WeakMap();\n            counterMap = new WeakMap();\n            uncontrolledNodes = new WeakMap();\n            markerMap = {};\n        }\n    };\n};\n/**\n * Marks everything except given node(or nodes) as aria-hidden\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var hideOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-aria-hidden'; }\n    var targets = Array.from(Array.isArray(originalTarget) ? originalTarget : [originalTarget]);\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    // we should not hide aria-live elements - https://github.com/theKashey/aria-hidden/issues/10\n    // and script elements, as they have no impact on accessibility.\n    targets.push.apply(targets, Array.from(activeParentNode.querySelectorAll('[aria-live], script')));\n    return applyAttributeToOthers(targets, activeParentNode, markerName, 'aria-hidden');\n};\n/**\n * Marks everything except given node(or nodes) as inert\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var inertOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-inert-ed'; }\n    var activeParentNode = parentNode || getDefaultParent(originalTarget);\n    if (!activeParentNode) {\n        return function () { return null; };\n    }\n    return applyAttributeToOthers(originalTarget, activeParentNode, markerName, 'inert');\n};\n/**\n * @returns if current browser supports inert\n */\nexport var supportsInert = function () {\n    return typeof HTMLElement !== 'undefined' && HTMLElement.prototype.hasOwnProperty('inert');\n};\n/**\n * Automatic function to \"suppress\" DOM elements - _hide_ or _inert_ in the best possible way\n * @param {Element | Element[]} originalTarget - elements to keep on the page\n * @param [parentNode] - top element, defaults to document.body\n * @param {String} [markerName] - a special attribute to mark every node\n * @return {Undo} undo command\n */\nexport var suppressOthers = function (originalTarget, parentNode, markerName) {\n    if (markerName === void 0) { markerName = 'data-suppressed'; }\n    return (supportsInert() ? inertOthers : hideOthers)(originalTarget, parentNode, markerName);\n};\n"], "names": [], "mappings": ";;;;;;AAAA,IAAI,mBAAmB,SAAU,cAAc;IAC3C,IAAI,OAAO,aAAa,aAAa;QACjC,OAAO;IACX;IACA,IAAI,eAAe,MAAM,OAAO,CAAC,kBAAkB,cAAc,CAAC,EAAE,GAAG;IACvE,OAAO,aAAa,aAAa,CAAC,IAAI;AAC1C;AACA,IAAI,aAAa,IAAI;AACrB,IAAI,oBAAoB,IAAI;AAC5B,IAAI,YAAY,CAAC;AACjB,IAAI,YAAY;AAChB,IAAI,aAAa,SAAU,IAAI;IAC3B,OAAO,QAAQ,CAAC,KAAK,IAAI,IAAI,WAAW,KAAK,UAAU,CAAC;AAC5D;AACA,IAAI,iBAAiB,SAAU,MAAM,EAAE,OAAO;IAC1C,OAAO,QACF,GAAG,CAAC,SAAU,MAAM;QACrB,IAAI,OAAO,QAAQ,CAAC,SAAS;YACzB,OAAO;QACX;QACA,IAAI,kBAAkB,WAAW;QACjC,IAAI,mBAAmB,OAAO,QAAQ,CAAC,kBAAkB;YACrD,OAAO;QACX;QACA,QAAQ,KAAK,CAAC,eAAe,QAAQ,2BAA2B,QAAQ;QACxE,OAAO;IACX,GACK,MAAM,CAAC,SAAU,CAAC;QAAI,OAAO,QAAQ;IAAI;AAClD;AACA;;;;;;;CAOC,GACD,IAAI,yBAAyB,SAAU,cAAc,EAAE,UAAU,EAAE,UAAU,EAAE,gBAAgB;IAC3F,IAAI,UAAU,eAAe,YAAY,MAAM,OAAO,CAAC,kBAAkB,iBAAiB;QAAC;KAAe;IAC1G,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE;QACxB,SAAS,CAAC,WAAW,GAAG,IAAI;IAChC;IACA,IAAI,gBAAgB,SAAS,CAAC,WAAW;IACzC,IAAI,cAAc,EAAE;IACpB,IAAI,iBAAiB,IAAI;IACzB,IAAI,iBAAiB,IAAI,IAAI;IAC7B,IAAI,OAAO,SAAU,EAAE;QACnB,IAAI,CAAC,MAAM,eAAe,GAAG,CAAC,KAAK;YAC/B;QACJ;QACA,eAAe,GAAG,CAAC;QACnB,KAAK,GAAG,UAAU;IACtB;IACA,QAAQ,OAAO,CAAC;IAChB,IAAI,OAAO,SAAU,MAAM;QACvB,IAAI,CAAC,UAAU,eAAe,GAAG,CAAC,SAAS;YACvC;QACJ;QACA,MAAM,SAAS,CAAC,OAAO,CAAC,IAAI,CAAC,OAAO,QAAQ,EAAE,SAAU,IAAI;YACxD,IAAI,eAAe,GAAG,CAAC,OAAO;gBAC1B,KAAK;YACT,OACK;gBACD,IAAI;oBACA,IAAI,OAAO,KAAK,YAAY,CAAC;oBAC7B,IAAI,gBAAgB,SAAS,QAAQ,SAAS;oBAC9C,IAAI,eAAe,CAAC,WAAW,GAAG,CAAC,SAAS,CAAC,IAAI;oBACjD,IAAI,cAAc,CAAC,cAAc,GAAG,CAAC,SAAS,CAAC,IAAI;oBACnD,WAAW,GAAG,CAAC,MAAM;oBACrB,cAAc,GAAG,CAAC,MAAM;oBACxB,YAAY,IAAI,CAAC;oBACjB,IAAI,iBAAiB,KAAK,eAAe;wBACrC,kBAAkB,GAAG,CAAC,MAAM;oBAChC;oBACA,IAAI,gBAAgB,GAAG;wBACnB,KAAK,YAAY,CAAC,YAAY;oBAClC;oBACA,IAAI,CAAC,eAAe;wBAChB,KAAK,YAAY,CAAC,kBAAkB;oBACxC;gBACJ,EACA,OAAO,GAAG;oBACN,QAAQ,KAAK,CAAC,mCAAmC,MAAM;gBAC3D;YACJ;QACJ;IACJ;IACA,KAAK;IACL,eAAe,KAAK;IACpB;IACA,OAAO;QACH,YAAY,OAAO,CAAC,SAAU,IAAI;YAC9B,IAAI,eAAe,WAAW,GAAG,CAAC,QAAQ;YAC1C,IAAI,cAAc,cAAc,GAAG,CAAC,QAAQ;YAC5C,WAAW,GAAG,CAAC,MAAM;YACrB,cAAc,GAAG,CAAC,MAAM;YACxB,IAAI,CAAC,cAAc;gBACf,IAAI,CAAC,kBAAkB,GAAG,CAAC,OAAO;oBAC9B,KAAK,eAAe,CAAC;gBACzB;gBACA,kBAAkB,MAAM,CAAC;YAC7B;YACA,IAAI,CAAC,aAAa;gBACd,KAAK,eAAe,CAAC;YACzB;QACJ;QACA;QACA,IAAI,CAAC,WAAW;YACZ,QAAQ;YACR,aAAa,IAAI;YACjB,aAAa,IAAI;YACjB,oBAAoB,IAAI;YACxB,YAAY,CAAC;QACjB;IACJ;AACJ;AAQO,IAAI,aAAa,SAAU,cAAc,EAAE,UAAU,EAAE,UAAU;IACpE,IAAI,eAAe,KAAK,GAAG;QAAE,aAAa;IAAoB;IAC9D,IAAI,UAAU,MAAM,IAAI,CAAC,MAAM,OAAO,CAAC,kBAAkB,iBAAiB;QAAC;KAAe;IAC1F,IAAI,mBAAmB,cAAc,iBAAiB;IACtD,IAAI,CAAC,kBAAkB;QACnB,OAAO;YAAc,OAAO;QAAM;IACtC;IACA,6FAA6F;IAC7F,gEAAgE;IAChE,QAAQ,IAAI,CAAC,KAAK,CAAC,SAAS,MAAM,IAAI,CAAC,iBAAiB,gBAAgB,CAAC;IACzE,OAAO,uBAAuB,SAAS,kBAAkB,YAAY;AACzE;AAQO,IAAI,cAAc,SAAU,cAAc,EAAE,UAAU,EAAE,UAAU;IACrE,IAAI,eAAe,KAAK,GAAG;QAAE,aAAa;IAAiB;IAC3D,IAAI,mBAAmB,cAAc,iBAAiB;IACtD,IAAI,CAAC,kBAAkB;QACnB,OAAO;YAAc,OAAO;QAAM;IACtC;IACA,OAAO,uBAAuB,gBAAgB,kBAAkB,YAAY;AAChF;AAIO,IAAI,gBAAgB;IACvB,OAAO,OAAO,gBAAgB,eAAe,YAAY,SAAS,CAAC,cAAc,CAAC;AACtF;AAQO,IAAI,iBAAiB,SAAU,cAAc,EAAE,UAAU,EAAE,UAAU;IACxE,IAAI,eAAe,KAAK,GAAG;QAAE,aAAa;IAAmB;IAC7D,OAAO,CAAC,kBAAkB,cAAc,UAAU,EAAE,gBAAgB,YAAY;AACpF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2372, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/tslib/tslib.es6.mjs"], "sourcesContent": ["/******************************************************************************\nCopyright (c) Microsoft Corporation.\n\nPermission to use, copy, modify, and/or distribute this software for any\npurpose with or without fee is hereby granted.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\" AND THE AUTHOR DISCLAIMS ALL WARRANTIES WITH\nREGARD TO THIS SOFTWARE INCLUDING ALL IMPLIED WARRANTIES OF MERCHANTABILITY\nAND FITNESS. IN NO EVENT SHALL THE AUTHOR BE LIABLE FOR ANY SPECIAL, DIRECT,\nINDIRECT, OR CONSEQUENTIAL DAMAGES OR ANY DAMAGES WHATSOEVER RESULTING FROM\nLOSS OF USE, DATA OR PROFITS, WHETHER IN AN ACTION OF CONTRACT, NEGLIGENCE OR\nOTHER TORTIOUS ACTION, ARISING OUT OF OR IN CONNECTION WITH THE USE OR\nPERFORMANCE OF THIS SOFTWARE.\n***************************************************************************** */\n/* global Reflect, Promise, SuppressedError, Symbol, Iterator */\n\nvar extendStatics = function(d, b) {\n  extendStatics = Object.setPrototypeOf ||\n      ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n      function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n  return extendStatics(d, b);\n};\n\nexport function __extends(d, b) {\n  if (typeof b !== \"function\" && b !== null)\n      throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n  extendStatics(d, b);\n  function __() { this.constructor = d; }\n  d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n}\n\nexport var __assign = function() {\n  __assign = Object.assign || function __assign(t) {\n      for (var s, i = 1, n = arguments.length; i < n; i++) {\n          s = arguments[i];\n          for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p)) t[p] = s[p];\n      }\n      return t;\n  }\n  return __assign.apply(this, arguments);\n}\n\nexport function __rest(s, e) {\n  var t = {};\n  for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)\n      t[p] = s[p];\n  if (s != null && typeof Object.getOwnPropertySymbols === \"function\")\n      for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {\n          if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))\n              t[p[i]] = s[p[i]];\n      }\n  return t;\n}\n\nexport function __decorate(decorators, target, key, desc) {\n  var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;\n  if (typeof Reflect === \"object\" && typeof Reflect.decorate === \"function\") r = Reflect.decorate(decorators, target, key, desc);\n  else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;\n  return c > 3 && r && Object.defineProperty(target, key, r), r;\n}\n\nexport function __param(paramIndex, decorator) {\n  return function (target, key) { decorator(target, key, paramIndex); }\n}\n\nexport function __esDecorate(ctor, descriptorIn, decorators, contextIn, initializers, extraInitializers) {\n  function accept(f) { if (f !== void 0 && typeof f !== \"function\") throw new TypeError(\"Function expected\"); return f; }\n  var kind = contextIn.kind, key = kind === \"getter\" ? \"get\" : kind === \"setter\" ? \"set\" : \"value\";\n  var target = !descriptorIn && ctor ? contextIn[\"static\"] ? ctor : ctor.prototype : null;\n  var descriptor = descriptorIn || (target ? Object.getOwnPropertyDescriptor(target, contextIn.name) : {});\n  var _, done = false;\n  for (var i = decorators.length - 1; i >= 0; i--) {\n      var context = {};\n      for (var p in contextIn) context[p] = p === \"access\" ? {} : contextIn[p];\n      for (var p in contextIn.access) context.access[p] = contextIn.access[p];\n      context.addInitializer = function (f) { if (done) throw new TypeError(\"Cannot add initializers after decoration has completed\"); extraInitializers.push(accept(f || null)); };\n      var result = (0, decorators[i])(kind === \"accessor\" ? { get: descriptor.get, set: descriptor.set } : descriptor[key], context);\n      if (kind === \"accessor\") {\n          if (result === void 0) continue;\n          if (result === null || typeof result !== \"object\") throw new TypeError(\"Object expected\");\n          if (_ = accept(result.get)) descriptor.get = _;\n          if (_ = accept(result.set)) descriptor.set = _;\n          if (_ = accept(result.init)) initializers.unshift(_);\n      }\n      else if (_ = accept(result)) {\n          if (kind === \"field\") initializers.unshift(_);\n          else descriptor[key] = _;\n      }\n  }\n  if (target) Object.defineProperty(target, contextIn.name, descriptor);\n  done = true;\n};\n\nexport function __runInitializers(thisArg, initializers, value) {\n  var useValue = arguments.length > 2;\n  for (var i = 0; i < initializers.length; i++) {\n      value = useValue ? initializers[i].call(thisArg, value) : initializers[i].call(thisArg);\n  }\n  return useValue ? value : void 0;\n};\n\nexport function __propKey(x) {\n  return typeof x === \"symbol\" ? x : \"\".concat(x);\n};\n\nexport function __setFunctionName(f, name, prefix) {\n  if (typeof name === \"symbol\") name = name.description ? \"[\".concat(name.description, \"]\") : \"\";\n  return Object.defineProperty(f, \"name\", { configurable: true, value: prefix ? \"\".concat(prefix, \" \", name) : name });\n};\n\nexport function __metadata(metadataKey, metadataValue) {\n  if (typeof Reflect === \"object\" && typeof Reflect.metadata === \"function\") return Reflect.metadata(metadataKey, metadataValue);\n}\n\nexport function __awaiter(thisArg, _arguments, P, generator) {\n  function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }\n  return new (P || (P = Promise))(function (resolve, reject) {\n      function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }\n      function rejected(value) { try { step(generator[\"throw\"](value)); } catch (e) { reject(e); } }\n      function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }\n      step((generator = generator.apply(thisArg, _arguments || [])).next());\n  });\n}\n\nexport function __generator(thisArg, body) {\n  var _ = { label: 0, sent: function() { if (t[0] & 1) throw t[1]; return t[1]; }, trys: [], ops: [] }, f, y, t, g = Object.create((typeof Iterator === \"function\" ? Iterator : Object).prototype);\n  return g.next = verb(0), g[\"throw\"] = verb(1), g[\"return\"] = verb(2), typeof Symbol === \"function\" && (g[Symbol.iterator] = function() { return this; }), g;\n  function verb(n) { return function (v) { return step([n, v]); }; }\n  function step(op) {\n      if (f) throw new TypeError(\"Generator is already executing.\");\n      while (g && (g = 0, op[0] && (_ = 0)), _) try {\n          if (f = 1, y && (t = op[0] & 2 ? y[\"return\"] : op[0] ? y[\"throw\"] || ((t = y[\"return\"]) && t.call(y), 0) : y.next) && !(t = t.call(y, op[1])).done) return t;\n          if (y = 0, t) op = [op[0] & 2, t.value];\n          switch (op[0]) {\n              case 0: case 1: t = op; break;\n              case 4: _.label++; return { value: op[1], done: false };\n              case 5: _.label++; y = op[1]; op = [0]; continue;\n              case 7: op = _.ops.pop(); _.trys.pop(); continue;\n              default:\n                  if (!(t = _.trys, t = t.length > 0 && t[t.length - 1]) && (op[0] === 6 || op[0] === 2)) { _ = 0; continue; }\n                  if (op[0] === 3 && (!t || (op[1] > t[0] && op[1] < t[3]))) { _.label = op[1]; break; }\n                  if (op[0] === 6 && _.label < t[1]) { _.label = t[1]; t = op; break; }\n                  if (t && _.label < t[2]) { _.label = t[2]; _.ops.push(op); break; }\n                  if (t[2]) _.ops.pop();\n                  _.trys.pop(); continue;\n          }\n          op = body.call(thisArg, _);\n      } catch (e) { op = [6, e]; y = 0; } finally { f = t = 0; }\n      if (op[0] & 5) throw op[1]; return { value: op[0] ? op[1] : void 0, done: true };\n  }\n}\n\nexport var __createBinding = Object.create ? (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  var desc = Object.getOwnPropertyDescriptor(m, k);\n  if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n  }\n  Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n  if (k2 === undefined) k2 = k;\n  o[k2] = m[k];\n});\n\nexport function __exportStar(m, o) {\n  for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(o, p)) __createBinding(o, m, p);\n}\n\nexport function __values(o) {\n  var s = typeof Symbol === \"function\" && Symbol.iterator, m = s && o[s], i = 0;\n  if (m) return m.call(o);\n  if (o && typeof o.length === \"number\") return {\n      next: function () {\n          if (o && i >= o.length) o = void 0;\n          return { value: o && o[i++], done: !o };\n      }\n  };\n  throw new TypeError(s ? \"Object is not iterable.\" : \"Symbol.iterator is not defined.\");\n}\n\nexport function __read(o, n) {\n  var m = typeof Symbol === \"function\" && o[Symbol.iterator];\n  if (!m) return o;\n  var i = m.call(o), r, ar = [], e;\n  try {\n      while ((n === void 0 || n-- > 0) && !(r = i.next()).done) ar.push(r.value);\n  }\n  catch (error) { e = { error: error }; }\n  finally {\n      try {\n          if (r && !r.done && (m = i[\"return\"])) m.call(i);\n      }\n      finally { if (e) throw e.error; }\n  }\n  return ar;\n}\n\n/** @deprecated */\nexport function __spread() {\n  for (var ar = [], i = 0; i < arguments.length; i++)\n      ar = ar.concat(__read(arguments[i]));\n  return ar;\n}\n\n/** @deprecated */\nexport function __spreadArrays() {\n  for (var s = 0, i = 0, il = arguments.length; i < il; i++) s += arguments[i].length;\n  for (var r = Array(s), k = 0, i = 0; i < il; i++)\n      for (var a = arguments[i], j = 0, jl = a.length; j < jl; j++, k++)\n          r[k] = a[j];\n  return r;\n}\n\nexport function __spreadArray(to, from, pack) {\n  if (pack || arguments.length === 2) for (var i = 0, l = from.length, ar; i < l; i++) {\n      if (ar || !(i in from)) {\n          if (!ar) ar = Array.prototype.slice.call(from, 0, i);\n          ar[i] = from[i];\n      }\n  }\n  return to.concat(ar || Array.prototype.slice.call(from));\n}\n\nexport function __await(v) {\n  return this instanceof __await ? (this.v = v, this) : new __await(v);\n}\n\nexport function __asyncGenerator(thisArg, _arguments, generator) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var g = generator.apply(thisArg, _arguments || []), i, q = [];\n  return i = Object.create((typeof AsyncIterator === \"function\" ? AsyncIterator : Object).prototype), verb(\"next\"), verb(\"throw\"), verb(\"return\", awaitReturn), i[Symbol.asyncIterator] = function () { return this; }, i;\n  function awaitReturn(f) { return function (v) { return Promise.resolve(v).then(f, reject); }; }\n  function verb(n, f) { if (g[n]) { i[n] = function (v) { return new Promise(function (a, b) { q.push([n, v, a, b]) > 1 || resume(n, v); }); }; if (f) i[n] = f(i[n]); } }\n  function resume(n, v) { try { step(g[n](v)); } catch (e) { settle(q[0][3], e); } }\n  function step(r) { r.value instanceof __await ? Promise.resolve(r.value.v).then(fulfill, reject) : settle(q[0][2], r); }\n  function fulfill(value) { resume(\"next\", value); }\n  function reject(value) { resume(\"throw\", value); }\n  function settle(f, v) { if (f(v), q.shift(), q.length) resume(q[0][0], q[0][1]); }\n}\n\nexport function __asyncDelegator(o) {\n  var i, p;\n  return i = {}, verb(\"next\"), verb(\"throw\", function (e) { throw e; }), verb(\"return\"), i[Symbol.iterator] = function () { return this; }, i;\n  function verb(n, f) { i[n] = o[n] ? function (v) { return (p = !p) ? { value: __await(o[n](v)), done: false } : f ? f(v) : v; } : f; }\n}\n\nexport function __asyncValues(o) {\n  if (!Symbol.asyncIterator) throw new TypeError(\"Symbol.asyncIterator is not defined.\");\n  var m = o[Symbol.asyncIterator], i;\n  return m ? m.call(o) : (o = typeof __values === \"function\" ? __values(o) : o[Symbol.iterator](), i = {}, verb(\"next\"), verb(\"throw\"), verb(\"return\"), i[Symbol.asyncIterator] = function () { return this; }, i);\n  function verb(n) { i[n] = o[n] && function (v) { return new Promise(function (resolve, reject) { v = o[n](v), settle(resolve, reject, v.done, v.value); }); }; }\n  function settle(resolve, reject, d, v) { Promise.resolve(v).then(function(v) { resolve({ value: v, done: d }); }, reject); }\n}\n\nexport function __makeTemplateObject(cooked, raw) {\n  if (Object.defineProperty) { Object.defineProperty(cooked, \"raw\", { value: raw }); } else { cooked.raw = raw; }\n  return cooked;\n};\n\nvar __setModuleDefault = Object.create ? (function(o, v) {\n  Object.defineProperty(o, \"default\", { enumerable: true, value: v });\n}) : function(o, v) {\n  o[\"default\"] = v;\n};\n\nvar ownKeys = function(o) {\n  ownKeys = Object.getOwnPropertyNames || function (o) {\n    var ar = [];\n    for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;\n    return ar;\n  };\n  return ownKeys(o);\n};\n\nexport function __importStar(mod) {\n  if (mod && mod.__esModule) return mod;\n  var result = {};\n  if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== \"default\") __createBinding(result, mod, k[i]);\n  __setModuleDefault(result, mod);\n  return result;\n}\n\nexport function __importDefault(mod) {\n  return (mod && mod.__esModule) ? mod : { default: mod };\n}\n\nexport function __classPrivateFieldGet(receiver, state, kind, f) {\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a getter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n  return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\n\nexport function __classPrivateFieldSet(receiver, state, value, kind, f) {\n  if (kind === \"m\") throw new TypeError(\"Private method is not writable\");\n  if (kind === \"a\" && !f) throw new TypeError(\"Private accessor was defined without a setter\");\n  if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver)) throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n  return (kind === \"a\" ? f.call(receiver, value) : f ? f.value = value : state.set(receiver, value)), value;\n}\n\nexport function __classPrivateFieldIn(state, receiver) {\n  if (receiver === null || (typeof receiver !== \"object\" && typeof receiver !== \"function\")) throw new TypeError(\"Cannot use 'in' operator on non-object\");\n  return typeof state === \"function\" ? receiver === state : state.has(receiver);\n}\n\nexport function __addDisposableResource(env, value, async) {\n  if (value !== null && value !== void 0) {\n    if (typeof value !== \"object\" && typeof value !== \"function\") throw new TypeError(\"Object expected.\");\n    var dispose, inner;\n    if (async) {\n      if (!Symbol.asyncDispose) throw new TypeError(\"Symbol.asyncDispose is not defined.\");\n      dispose = value[Symbol.asyncDispose];\n    }\n    if (dispose === void 0) {\n      if (!Symbol.dispose) throw new TypeError(\"Symbol.dispose is not defined.\");\n      dispose = value[Symbol.dispose];\n      if (async) inner = dispose;\n    }\n    if (typeof dispose !== \"function\") throw new TypeError(\"Object not disposable.\");\n    if (inner) dispose = function() { try { inner.call(this); } catch (e) { return Promise.reject(e); } };\n    env.stack.push({ value: value, dispose: dispose, async: async });\n  }\n  else if (async) {\n    env.stack.push({ async: true });\n  }\n  return value;\n}\n\nvar _SuppressedError = typeof SuppressedError === \"function\" ? SuppressedError : function (error, suppressed, message) {\n  var e = new Error(message);\n  return e.name = \"SuppressedError\", e.error = error, e.suppressed = suppressed, e;\n};\n\nexport function __disposeResources(env) {\n  function fail(e) {\n    env.error = env.hasError ? new _SuppressedError(e, env.error, \"An error was suppressed during disposal.\") : e;\n    env.hasError = true;\n  }\n  var r, s = 0;\n  function next() {\n    while (r = env.stack.pop()) {\n      try {\n        if (!r.async && s === 1) return s = 0, env.stack.push(r), Promise.resolve().then(next);\n        if (r.dispose) {\n          var result = r.dispose.call(r.value);\n          if (r.async) return s |= 2, Promise.resolve(result).then(next, function(e) { fail(e); return next(); });\n        }\n        else s |= 1;\n      }\n      catch (e) {\n        fail(e);\n      }\n    }\n    if (s === 1) return env.hasError ? Promise.reject(env.error) : Promise.resolve();\n    if (env.hasError) throw env.error;\n  }\n  return next();\n}\n\nexport function __rewriteRelativeImportExtension(path, preserveJsx) {\n  if (typeof path === \"string\" && /^\\.\\.?\\//.test(path)) {\n      return path.replace(/\\.(tsx)$|((?:\\.d)?)((?:\\.[^./]+?)?)\\.([cm]?)ts$/i, function (m, tsx, d, ext, cm) {\n          return tsx ? preserveJsx ? \".jsx\" : \".js\" : d && (!ext || !cm) ? m : (d + ext + \".\" + cm.toLowerCase() + \"js\");\n      });\n  }\n  return path;\n}\n\nexport default {\n  __extends,\n  __assign,\n  __rest,\n  __decorate,\n  __param,\n  __esDecorate,\n  __runInitializers,\n  __propKey,\n  __setFunctionName,\n  __metadata,\n  __awaiter,\n  __generator,\n  __createBinding,\n  __exportStar,\n  __values,\n  __read,\n  __spread,\n  __spreadArrays,\n  __spreadArray,\n  __await,\n  __asyncGenerator,\n  __asyncDelegator,\n  __asyncValues,\n  __makeTemplateObject,\n  __importStar,\n  __importDefault,\n  __classPrivateFieldGet,\n  __classPrivateFieldSet,\n  __classPrivateFieldIn,\n  __addDisposableResource,\n  __disposeResources,\n  __rewriteRelativeImportExtension,\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;;8EAa8E,GAC9E,8DAA8D;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAE9D,IAAI,gBAAgB,SAAS,CAAC,EAAE,CAAC;IAC/B,gBAAgB,OAAO,cAAc,IAChC,CAAA;QAAE,WAAW,EAAE;IAAC,CAAA,aAAa,SAAS,SAAU,CAAC,EAAE,CAAC;QAAI,EAAE,SAAS,GAAG;IAAG,KAC1E,SAAU,CAAC,EAAE,CAAC;QAAI,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IAAE;IACpG,OAAO,cAAc,GAAG;AAC1B;AAEO,SAAS,UAAU,CAAC,EAAE,CAAC;IAC5B,IAAI,OAAO,MAAM,cAAc,MAAM,MACjC,MAAM,IAAI,UAAU,yBAAyB,OAAO,KAAK;IAC7D,cAAc,GAAG;IACjB,SAAS;QAAO,IAAI,CAAC,WAAW,GAAG;IAAG;IACtC,EAAE,SAAS,GAAG,MAAM,OAAO,OAAO,MAAM,CAAC,KAAK,CAAC,GAAG,SAAS,GAAG,EAAE,SAAS,EAAE,IAAI,IAAI;AACrF;AAEO,IAAI,WAAW;IACpB,WAAW,OAAO,MAAM,IAAI,SAAS,SAAS,CAAC;QAC3C,IAAK,IAAI,GAAG,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAAI,GAAG,IAAK;YACjD,IAAI,SAAS,CAAC,EAAE;YAChB,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;QAChF;QACA,OAAO;IACX;IACA,OAAO,SAAS,KAAK,CAAC,IAAI,EAAE;AAC9B;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,CAAC;IACT,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,MAAM,EAAE,OAAO,CAAC,KAAK,GAC9E,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACf,IAAI,KAAK,QAAQ,OAAO,OAAO,qBAAqB,KAAK,YACrD,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,qBAAqB,CAAC,IAAI,IAAI,EAAE,MAAM,EAAE,IAAK;QACpE,IAAI,EAAE,OAAO,CAAC,CAAC,CAAC,EAAE,IAAI,KAAK,OAAO,SAAS,CAAC,oBAAoB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,GACzE,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC;IACzB;IACJ,OAAO;AACT;AAEO,SAAS,WAAW,UAAU,EAAE,MAAM,EAAE,GAAG,EAAE,IAAI;IACtD,IAAI,IAAI,UAAU,MAAM,EAAE,IAAI,IAAI,IAAI,SAAS,SAAS,OAAO,OAAO,OAAO,wBAAwB,CAAC,QAAQ,OAAO,MAAM;IAC3H,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,IAAI,QAAQ,QAAQ,CAAC,YAAY,QAAQ,KAAK;SACpH,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK,IAAI,IAAI,UAAU,CAAC,EAAE,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE,KAAK,IAAI,IAAI,EAAE,QAAQ,KAAK,KAAK,EAAE,QAAQ,IAAI,KAAK;IAChJ,OAAO,IAAI,KAAK,KAAK,OAAO,cAAc,CAAC,QAAQ,KAAK,IAAI;AAC9D;AAEO,SAAS,QAAQ,UAAU,EAAE,SAAS;IAC3C,OAAO,SAAU,MAAM,EAAE,GAAG;QAAI,UAAU,QAAQ,KAAK;IAAa;AACtE;AAEO,SAAS,aAAa,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,SAAS,EAAE,YAAY,EAAE,iBAAiB;IACrG,SAAS,OAAO,CAAC;QAAI,IAAI,MAAM,KAAK,KAAK,OAAO,MAAM,YAAY,MAAM,IAAI,UAAU;QAAsB,OAAO;IAAG;IACtH,IAAI,OAAO,UAAU,IAAI,EAAE,MAAM,SAAS,WAAW,QAAQ,SAAS,WAAW,QAAQ;IACzF,IAAI,SAAS,CAAC,gBAAgB,OAAO,SAAS,CAAC,SAAS,GAAG,OAAO,KAAK,SAAS,GAAG;IACnF,IAAI,aAAa,gBAAgB,CAAC,SAAS,OAAO,wBAAwB,CAAC,QAAQ,UAAU,IAAI,IAAI,CAAC,CAAC;IACvG,IAAI,GAAG,OAAO;IACd,IAAK,IAAI,IAAI,WAAW,MAAM,GAAG,GAAG,KAAK,GAAG,IAAK;QAC7C,IAAI,UAAU,CAAC;QACf,IAAK,IAAI,KAAK,UAAW,OAAO,CAAC,EAAE,GAAG,MAAM,WAAW,CAAC,IAAI,SAAS,CAAC,EAAE;QACxE,IAAK,IAAI,KAAK,UAAU,MAAM,CAAE,QAAQ,MAAM,CAAC,EAAE,GAAG,UAAU,MAAM,CAAC,EAAE;QACvE,QAAQ,cAAc,GAAG,SAAU,CAAC;YAAI,IAAI,MAAM,MAAM,IAAI,UAAU;YAA2D,kBAAkB,IAAI,CAAC,OAAO,KAAK;QAAQ;QAC5K,IAAI,SAAS,CAAC,GAAG,UAAU,CAAC,EAAE,EAAE,SAAS,aAAa;YAAE,KAAK,WAAW,GAAG;YAAE,KAAK,WAAW,GAAG;QAAC,IAAI,UAAU,CAAC,IAAI,EAAE;QACtH,IAAI,SAAS,YAAY;YACrB,IAAI,WAAW,KAAK,GAAG;YACvB,IAAI,WAAW,QAAQ,OAAO,WAAW,UAAU,MAAM,IAAI,UAAU;YACvE,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,GAAG,GAAG,WAAW,GAAG,GAAG;YAC7C,IAAI,IAAI,OAAO,OAAO,IAAI,GAAG,aAAa,OAAO,CAAC;QACtD,OACK,IAAI,IAAI,OAAO,SAAS;YACzB,IAAI,SAAS,SAAS,aAAa,OAAO,CAAC;iBACtC,UAAU,CAAC,IAAI,GAAG;QAC3B;IACJ;IACA,IAAI,QAAQ,OAAO,cAAc,CAAC,QAAQ,UAAU,IAAI,EAAE;IAC1D,OAAO;AACT;;AAEO,SAAS,kBAAkB,OAAO,EAAE,YAAY,EAAE,KAAK;IAC5D,IAAI,WAAW,UAAU,MAAM,GAAG;IAClC,IAAK,IAAI,IAAI,GAAG,IAAI,aAAa,MAAM,EAAE,IAAK;QAC1C,QAAQ,WAAW,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,SAAS,YAAY,CAAC,EAAE,CAAC,IAAI,CAAC;IACnF;IACA,OAAO,WAAW,QAAQ,KAAK;AACjC;;AAEO,SAAS,UAAU,CAAC;IACzB,OAAO,OAAO,MAAM,WAAW,IAAI,GAAG,MAAM,CAAC;AAC/C;;AAEO,SAAS,kBAAkB,CAAC,EAAE,IAAI,EAAE,MAAM;IAC/C,IAAI,OAAO,SAAS,UAAU,OAAO,KAAK,WAAW,GAAG,IAAI,MAAM,CAAC,KAAK,WAAW,EAAE,OAAO;IAC5F,OAAO,OAAO,cAAc,CAAC,GAAG,QAAQ;QAAE,cAAc;QAAM,OAAO,SAAS,GAAG,MAAM,CAAC,QAAQ,KAAK,QAAQ;IAAK;AACpH;;AAEO,SAAS,WAAW,WAAW,EAAE,aAAa;IACnD,IAAI,OAAO,YAAY,YAAY,OAAO,QAAQ,QAAQ,KAAK,YAAY,OAAO,QAAQ,QAAQ,CAAC,aAAa;AAClH;AAEO,SAAS,UAAU,OAAO,EAAE,UAAU,EAAE,CAAC,EAAE,SAAS;IACzD,SAAS,MAAM,KAAK;QAAI,OAAO,iBAAiB,IAAI,QAAQ,IAAI,EAAE,SAAU,OAAO;YAAI,QAAQ;QAAQ;IAAI;IAC3G,OAAO,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,CAAC,EAAE,SAAU,OAAO,EAAE,MAAM;QACrD,SAAS,UAAU,KAAK;YAAI,IAAI;gBAAE,KAAK,UAAU,IAAI,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC1F,SAAS,SAAS,KAAK;YAAI,IAAI;gBAAE,KAAK,SAAS,CAAC,QAAQ,CAAC;YAAS,EAAE,OAAO,GAAG;gBAAE,OAAO;YAAI;QAAE;QAC7F,SAAS,KAAK,MAAM;YAAI,OAAO,IAAI,GAAG,QAAQ,OAAO,KAAK,IAAI,MAAM,OAAO,KAAK,EAAE,IAAI,CAAC,WAAW;QAAW;QAC7G,KAAK,CAAC,YAAY,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,CAAC,EAAE,IAAI;IACtE;AACF;AAEO,SAAS,YAAY,OAAO,EAAE,IAAI;IACvC,IAAI,IAAI;QAAE,OAAO;QAAG,MAAM;YAAa,IAAI,CAAC,CAAC,EAAE,GAAG,GAAG,MAAM,CAAC,CAAC,EAAE;YAAE,OAAO,CAAC,CAAC,EAAE;QAAE;QAAG,MAAM,EAAE;QAAE,KAAK,EAAE;IAAC,GAAG,GAAG,GAAG,GAAG,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,aAAa,aAAa,WAAW,MAAM,EAAE,SAAS;IAC/L,OAAO,EAAE,IAAI,GAAG,KAAK,IAAI,CAAC,CAAC,QAAQ,GAAG,KAAK,IAAI,CAAC,CAAC,SAAS,GAAG,KAAK,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAa,OAAO,IAAI;IAAE,CAAC,GAAG;;IAC1J,SAAS,KAAK,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,KAAK;gBAAC;gBAAG;aAAE;QAAG;IAAG;IACjE,SAAS,KAAK,EAAE;QACZ,IAAI,GAAG,MAAM,IAAI,UAAU;QAC3B,MAAO,KAAK,CAAC,IAAI,GAAG,EAAE,CAAC,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC,GAAG,EAAG,IAAI;YAC1C,IAAI,IAAI,GAAG,KAAK,CAAC,IAAI,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,QAAQ,IAAI,CAAC,CAAC,IAAI,CAAC,CAAC,SAAS,KAAK,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE,IAAI,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,EAAE,OAAO;YAC3J,IAAI,IAAI,GAAG,GAAG,KAAK;gBAAC,EAAE,CAAC,EAAE,GAAG;gBAAG,EAAE,KAAK;aAAC;YACvC,OAAQ,EAAE,CAAC,EAAE;gBACT,KAAK;gBAAG,KAAK;oBAAG,IAAI;oBAAI;gBACxB,KAAK;oBAAG,EAAE,KAAK;oBAAI,OAAO;wBAAE,OAAO,EAAE,CAAC,EAAE;wBAAE,MAAM;oBAAM;gBACtD,KAAK;oBAAG,EAAE,KAAK;oBAAI,IAAI,EAAE,CAAC,EAAE;oBAAE,KAAK;wBAAC;qBAAE;oBAAE;gBACxC,KAAK;oBAAG,KAAK,EAAE,GAAG,CAAC,GAAG;oBAAI,EAAE,IAAI,CAAC,GAAG;oBAAI;gBACxC;oBACI,IAAI,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,MAAM,GAAG,KAAK,CAAC,CAAC,EAAE,MAAM,GAAG,EAAE,KAAK,CAAC,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,CAAC,EAAE,KAAK,CAAC,GAAG;wBAAE,IAAI;wBAAG;oBAAU;oBAC3G,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,CAAC,CAAC,KAAM,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,AAAC,GAAG;wBAAE,EAAE,KAAK,GAAG,EAAE,CAAC,EAAE;wBAAE;oBAAO;oBACrF,IAAI,EAAE,CAAC,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,IAAI;wBAAI;oBAAO;oBACpE,IAAI,KAAK,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE,EAAE;wBAAE,EAAE,KAAK,GAAG,CAAC,CAAC,EAAE;wBAAE,EAAE,GAAG,CAAC,IAAI,CAAC;wBAAK;oBAAO;oBAClE,IAAI,CAAC,CAAC,EAAE,EAAE,EAAE,GAAG,CAAC,GAAG;oBACnB,EAAE,IAAI,CAAC,GAAG;oBAAI;YACtB;YACA,KAAK,KAAK,IAAI,CAAC,SAAS;QAC5B,EAAE,OAAO,GAAG;YAAE,KAAK;gBAAC;gBAAG;aAAE;YAAE,IAAI;QAAG,SAAU;YAAE,IAAI,IAAI;QAAG;QACzD,IAAI,EAAE,CAAC,EAAE,GAAG,GAAG,MAAM,EAAE,CAAC,EAAE;QAAE,OAAO;YAAE,OAAO,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,KAAK;YAAG,MAAM;QAAK;IACnF;AACF;AAEO,IAAI,kBAAkB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IAChE,IAAI,OAAO,WAAW,KAAK;IAC3B,IAAI,OAAO,OAAO,wBAAwB,CAAC,GAAG;IAC9C,IAAI,CAAC,QAAQ,CAAC,SAAS,OAAO,CAAC,EAAE,UAAU,GAAG,KAAK,QAAQ,IAAI,KAAK,YAAY,GAAG;QAC/E,OAAO;YAAE,YAAY;YAAM,KAAK;gBAAa,OAAO,CAAC,CAAC,EAAE;YAAE;QAAE;IAChE;IACA,OAAO,cAAc,CAAC,GAAG,IAAI;AAC/B,IAAM,SAAS,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE;IACxB,IAAI,OAAO,WAAW,KAAK;IAC3B,CAAC,CAAC,GAAG,GAAG,CAAC,CAAC,EAAE;AACd;AAEO,SAAS,aAAa,CAAC,EAAE,CAAC;IAC/B,IAAK,IAAI,KAAK,EAAG,IAAI,MAAM,aAAa,CAAC,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,gBAAgB,GAAG,GAAG;AAC7G;AAEO,SAAS,SAAS,CAAC;IACxB,IAAI,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,EAAE,IAAI,KAAK,CAAC,CAAC,EAAE,EAAE,IAAI;IAC5E,IAAI,GAAG,OAAO,EAAE,IAAI,CAAC;IACrB,IAAI,KAAK,OAAO,EAAE,MAAM,KAAK,UAAU,OAAO;QAC1C,MAAM;YACF,IAAI,KAAK,KAAK,EAAE,MAAM,EAAE,IAAI,KAAK;YACjC,OAAO;gBAAE,OAAO,KAAK,CAAC,CAAC,IAAI;gBAAE,MAAM,CAAC;YAAE;QAC1C;IACJ;IACA,MAAM,IAAI,UAAU,IAAI,4BAA4B;AACtD;AAEO,SAAS,OAAO,CAAC,EAAE,CAAC;IACzB,IAAI,IAAI,OAAO,WAAW,cAAc,CAAC,CAAC,OAAO,QAAQ,CAAC;IAC1D,IAAI,CAAC,GAAG,OAAO;IACf,IAAI,IAAI,EAAE,IAAI,CAAC,IAAI,GAAG,KAAK,EAAE,EAAE;IAC/B,IAAI;QACA,MAAO,CAAC,MAAM,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,EAAE,IAAI,CAAE,GAAG,IAAI,CAAC,EAAE,KAAK;IAC7E,EACA,OAAO,OAAO;QAAE,IAAI;YAAE,OAAO;QAAM;IAAG,SAC9B;QACJ,IAAI;YACA,IAAI,KAAK,CAAC,EAAE,IAAI,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,GAAG,EAAE,IAAI,CAAC;QAClD,SACQ;YAAE,IAAI,GAAG,MAAM,EAAE,KAAK;QAAE;IACpC;IACA,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,KAAK,EAAE,EAAE,IAAI,GAAG,IAAI,UAAU,MAAM,EAAE,IAC3C,KAAK,GAAG,MAAM,CAAC,OAAO,SAAS,CAAC,EAAE;IACtC,OAAO;AACT;AAGO,SAAS;IACd,IAAK,IAAI,IAAI,GAAG,IAAI,GAAG,KAAK,UAAU,MAAM,EAAE,IAAI,IAAI,IAAK,KAAK,SAAS,CAAC,EAAE,CAAC,MAAM;IACnF,IAAK,IAAI,IAAI,MAAM,IAAI,IAAI,GAAG,IAAI,GAAG,IAAI,IAAI,IACzC,IAAK,IAAI,IAAI,SAAS,CAAC,EAAE,EAAE,IAAI,GAAG,KAAK,EAAE,MAAM,EAAE,IAAI,IAAI,KAAK,IAC1D,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE;IACnB,OAAO;AACT;AAEO,SAAS,cAAc,EAAE,EAAE,IAAI,EAAE,IAAI;IAC1C,IAAI,QAAQ,UAAU,MAAM,KAAK,GAAG,IAAK,IAAI,IAAI,GAAG,IAAI,KAAK,MAAM,EAAE,IAAI,IAAI,GAAG,IAAK;QACjF,IAAI,MAAM,CAAC,CAAC,KAAK,IAAI,GAAG;YACpB,IAAI,CAAC,IAAI,KAAK,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC,MAAM,GAAG;YAClD,EAAE,CAAC,EAAE,GAAG,IAAI,CAAC,EAAE;QACnB;IACJ;IACA,OAAO,GAAG,MAAM,CAAC,MAAM,MAAM,SAAS,CAAC,KAAK,CAAC,IAAI,CAAC;AACpD;AAEO,SAAS,QAAQ,CAAC;IACvB,OAAO,IAAI,YAAY,UAAU,CAAC,IAAI,CAAC,CAAC,GAAG,GAAG,IAAI,IAAI,IAAI,QAAQ;AACpE;AAEO,SAAS,iBAAiB,OAAO,EAAE,UAAU,EAAE,SAAS;IAC7D,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,UAAU,KAAK,CAAC,SAAS,cAAc,EAAE,GAAG,GAAG,IAAI,EAAE;IAC7D,OAAO,IAAI,OAAO,MAAM,CAAC,CAAC,OAAO,kBAAkB,aAAa,gBAAgB,MAAM,EAAE,SAAS,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,UAAU,cAAc,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IACtN,SAAS,YAAY,CAAC;QAAI,OAAO,SAAU,CAAC;YAAI,OAAO,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,GAAG;QAAS;IAAG;IAC9F,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,IAAI,CAAC,CAAC,EAAE,EAAE;YAAE,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;gBAAI,OAAO,IAAI,QAAQ,SAAU,CAAC,EAAE,CAAC;oBAAI,EAAE,IAAI,CAAC;wBAAC;wBAAG;wBAAG;wBAAG;qBAAE,IAAI,KAAK,OAAO,GAAG;gBAAI;YAAI;YAAG,IAAI,GAAG,CAAC,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,EAAE;QAAG;IAAE;IACvK,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI;YAAE,KAAK,CAAC,CAAC,EAAE,CAAC;QAAK,EAAE,OAAO,GAAG;YAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;QAAI;IAAE;IACjF,SAAS,KAAK,CAAC;QAAI,EAAE,KAAK,YAAY,UAAU,QAAQ,OAAO,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,SAAS,UAAU,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE;IAAI;IACvH,SAAS,QAAQ,KAAK;QAAI,OAAO,QAAQ;IAAQ;IACjD,SAAS,OAAO,KAAK;QAAI,OAAO,SAAS;IAAQ;IACjD,SAAS,OAAO,CAAC,EAAE,CAAC;QAAI,IAAI,EAAE,IAAI,EAAE,KAAK,IAAI,EAAE,MAAM,EAAE,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,EAAE,CAAC,EAAE;IAAG;AACnF;AAEO,SAAS,iBAAiB,CAAC;IAChC,IAAI,GAAG;IACP,OAAO,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,SAAS,SAAU,CAAC;QAAI,MAAM;IAAG,IAAI,KAAK,WAAW,CAAC,CAAC,OAAO,QAAQ,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG;;IAC1I,SAAS,KAAK,CAAC,EAAE,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,SAAU,CAAC;YAAI,OAAO,CAAC,IAAI,CAAC,CAAC,IAAI;gBAAE,OAAO,QAAQ,CAAC,CAAC,EAAE,CAAC;gBAAK,MAAM;YAAM,IAAI,IAAI,EAAE,KAAK;QAAG,IAAI;IAAG;AACvI;AAEO,SAAS,cAAc,CAAC;IAC7B,IAAI,CAAC,OAAO,aAAa,EAAE,MAAM,IAAI,UAAU;IAC/C,IAAI,IAAI,CAAC,CAAC,OAAO,aAAa,CAAC,EAAE;IACjC,OAAO,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC,IAAI,OAAO,aAAa,aAAa,SAAS,KAAK,CAAC,CAAC,OAAO,QAAQ,CAAC,IAAI,IAAI,CAAC,GAAG,KAAK,SAAS,KAAK,UAAU,KAAK,WAAW,CAAC,CAAC,OAAO,aAAa,CAAC,GAAG;QAAc,OAAO,IAAI;IAAE,GAAG,CAAC;;IAC/M,SAAS,KAAK,CAAC;QAAI,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,IAAI,SAAU,CAAC;YAAI,OAAO,IAAI,QAAQ,SAAU,OAAO,EAAE,MAAM;gBAAI,IAAI,CAAC,CAAC,EAAE,CAAC,IAAI,OAAO,SAAS,QAAQ,EAAE,IAAI,EAAE,EAAE,KAAK;YAAG;QAAI;IAAG;IAC/J,SAAS,OAAO,OAAO,EAAE,MAAM,EAAE,CAAC,EAAE,CAAC;QAAI,QAAQ,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC;YAAI,QAAQ;gBAAE,OAAO;gBAAG,MAAM;YAAE;QAAI,GAAG;IAAS;AAC7H;AAEO,SAAS,qBAAqB,MAAM,EAAE,GAAG;IAC9C,IAAI,OAAO,cAAc,EAAE;QAAE,OAAO,cAAc,CAAC,QAAQ,OAAO;YAAE,OAAO;QAAI;IAAI,OAAO;QAAE,OAAO,GAAG,GAAG;IAAK;IAC9G,OAAO;AACT;;AAEA,IAAI,qBAAqB,OAAO,MAAM,GAAI,SAAS,CAAC,EAAE,CAAC;IACrD,OAAO,cAAc,CAAC,GAAG,WAAW;QAAE,YAAY;QAAM,OAAO;IAAE;AACnE,IAAK,SAAS,CAAC,EAAE,CAAC;IAChB,CAAC,CAAC,UAAU,GAAG;AACjB;AAEA,IAAI,UAAU,SAAS,CAAC;IACtB,UAAU,OAAO,mBAAmB,IAAI,SAAU,CAAC;QACjD,IAAI,KAAK,EAAE;QACX,IAAK,IAAI,KAAK,EAAG,IAAI,OAAO,SAAS,CAAC,cAAc,CAAC,IAAI,CAAC,GAAG,IAAI,EAAE,CAAC,GAAG,MAAM,CAAC,GAAG;QACjF,OAAO;IACT;IACA,OAAO,QAAQ;AACjB;AAEO,SAAS,aAAa,GAAG;IAC9B,IAAI,OAAO,IAAI,UAAU,EAAE,OAAO;IAClC,IAAI,SAAS,CAAC;IACd,IAAI,OAAO,MAAM;QAAA,IAAK,IAAI,IAAI,QAAQ,MAAM,IAAI,GAAG,IAAI,EAAE,MAAM,EAAE,IAAK,IAAI,CAAC,CAAC,EAAE,KAAK,WAAW,gBAAgB,QAAQ,KAAK,CAAC,CAAC,EAAE;IAAC;IAChI,mBAAmB,QAAQ;IAC3B,OAAO;AACT;AAEO,SAAS,gBAAgB,GAAG;IACjC,OAAO,AAAC,OAAO,IAAI,UAAU,GAAI,MAAM;QAAE,SAAS;IAAI;AACxD;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IAC7D,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,IAAI,CAAC,YAAY,IAAI,EAAE,KAAK,GAAG,MAAM,GAAG,CAAC;AACtF;AAEO,SAAS,uBAAuB,QAAQ,EAAE,KAAK,EAAE,KAAK,EAAE,IAAI,EAAE,CAAC;IACpE,IAAI,SAAS,KAAK,MAAM,IAAI,UAAU;IACtC,IAAI,SAAS,OAAO,CAAC,GAAG,MAAM,IAAI,UAAU;IAC5C,IAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,WAAW,MAAM,IAAI,UAAU;IACvG,OAAO,AAAC,SAAS,MAAM,EAAE,IAAI,CAAC,UAAU,SAAS,IAAI,EAAE,KAAK,GAAG,QAAQ,MAAM,GAAG,CAAC,UAAU,QAAS;AACtG;AAEO,SAAS,sBAAsB,KAAK,EAAE,QAAQ;IACnD,IAAI,aAAa,QAAS,OAAO,aAAa,YAAY,OAAO,aAAa,YAAa,MAAM,IAAI,UAAU;IAC/G,OAAO,OAAO,UAAU,aAAa,aAAa,QAAQ,MAAM,GAAG,CAAC;AACtE;AAEO,SAAS,wBAAwB,GAAG,EAAE,KAAK,EAAE,KAAK;IACvD,IAAI,UAAU,QAAQ,UAAU,KAAK,GAAG;QACtC,IAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,MAAM,IAAI,UAAU;QAClF,IAAI,SAAS;QACb,IAAI,OAAO;YACT,IAAI,CAAC,OAAO,YAAY,EAAE,MAAM,IAAI,UAAU;YAC9C,UAAU,KAAK,CAAC,OAAO,YAAY,CAAC;QACtC;QACA,IAAI,YAAY,KAAK,GAAG;YACtB,IAAI,CAAC,OAAO,OAAO,EAAE,MAAM,IAAI,UAAU;YACzC,UAAU,KAAK,CAAC,OAAO,OAAO,CAAC;YAC/B,IAAI,OAAO,QAAQ;QACrB;QACA,IAAI,OAAO,YAAY,YAAY,MAAM,IAAI,UAAU;QACvD,IAAI,OAAO,UAAU;YAAa,IAAI;gBAAE,MAAM,IAAI,CAAC,IAAI;YAAG,EAAE,OAAO,GAAG;gBAAE,OAAO,QAAQ,MAAM,CAAC;YAAI;QAAE;QACpG,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;YAAO,SAAS;YAAS,OAAO;QAAM;IAChE,OACK,IAAI,OAAO;QACd,IAAI,KAAK,CAAC,IAAI,CAAC;YAAE,OAAO;QAAK;IAC/B;IACA,OAAO;AACT;AAEA,IAAI,mBAAmB,OAAO,oBAAoB,aAAa,kBAAkB,SAAU,KAAK,EAAE,UAAU,EAAE,OAAO;IACnH,IAAI,IAAI,IAAI,MAAM;IAClB,OAAO,EAAE,IAAI,GAAG,mBAAmB,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,GAAG,YAAY;AACjF;AAEO,SAAS,mBAAmB,GAAG;IACpC,SAAS,KAAK,CAAC;QACb,IAAI,KAAK,GAAG,IAAI,QAAQ,GAAG,IAAI,iBAAiB,GAAG,IAAI,KAAK,EAAE,8CAA8C;QAC5G,IAAI,QAAQ,GAAG;IACjB;IACA,IAAI,GAAG,IAAI;IACX,SAAS;QACP,MAAO,IAAI,IAAI,KAAK,CAAC,GAAG,GAAI;YAC1B,IAAI;gBACF,IAAI,CAAC,EAAE,KAAK,IAAI,MAAM,GAAG,OAAO,IAAI,GAAG,IAAI,KAAK,CAAC,IAAI,CAAC,IAAI,QAAQ,OAAO,GAAG,IAAI,CAAC;gBACjF,IAAI,EAAE,OAAO,EAAE;oBACb,IAAI,SAAS,EAAE,OAAO,CAAC,IAAI,CAAC,EAAE,KAAK;oBACnC,IAAI,EAAE,KAAK,EAAE,OAAO,KAAK,GAAG,QAAQ,OAAO,CAAC,QAAQ,IAAI,CAAC,MAAM,SAAS,CAAC;wBAAI,KAAK;wBAAI,OAAO;oBAAQ;gBACvG,OACK,KAAK;YACZ,EACA,OAAO,GAAG;gBACR,KAAK;YACP;QACF;QACA,IAAI,MAAM,GAAG,OAAO,IAAI,QAAQ,GAAG,QAAQ,MAAM,CAAC,IAAI,KAAK,IAAI,QAAQ,OAAO;QAC9E,IAAI,IAAI,QAAQ,EAAE,MAAM,IAAI,KAAK;IACnC;IACA,OAAO;AACT;AAEO,SAAS,iCAAiC,IAAI,EAAE,WAAW;IAChE,IAAI,OAAO,SAAS,YAAY,WAAW,IAAI,CAAC,OAAO;QACnD,OAAO,KAAK,OAAO,CAAC,oDAAoD,SAAU,CAAC,EAAE,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE;YAChG,OAAO,MAAM,cAAc,SAAS,QAAQ,KAAK,CAAC,CAAC,OAAO,CAAC,EAAE,IAAI,IAAK,IAAI,MAAM,MAAM,GAAG,WAAW,KAAK;QAC7G;IACJ;IACA,OAAO;AACT;uCAEe;IACb;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2972, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll-bar/dist/es2015/constants.js"], "sourcesContent": ["export var zeroRightClassName = 'right-scroll-bar-position';\nexport var fullWidthClassName = 'width-before-scroll-bar';\nexport var noScrollbarsClassName = 'with-scroll-bars-hidden';\n/**\n * Name of a CSS variable containing the amount of \"hidden\" scrollbar\n * ! might be undefined ! use will fallback!\n */\nexport var removedBarSizeVariable = '--removed-body-scroll-bar-size';\n"], "names": [], "mappings": ";;;;;;AAAO,IAAI,qBAAqB;AACzB,IAAI,qBAAqB;AACzB,IAAI,wBAAwB;AAK5B,IAAI,yBAAyB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2988, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll-bar/dist/es2015/utils.js"], "sourcesContent": ["export var zeroGap = {\n    left: 0,\n    top: 0,\n    right: 0,\n    gap: 0,\n};\nvar parse = function (x) { return parseInt(x || '', 10) || 0; };\nvar getOffset = function (gapMode) {\n    var cs = window.getComputedStyle(document.body);\n    var left = cs[gapMode === 'padding' ? 'paddingLeft' : 'marginLeft'];\n    var top = cs[gapMode === 'padding' ? 'paddingTop' : 'marginTop'];\n    var right = cs[gapMode === 'padding' ? 'paddingRight' : 'marginRight'];\n    return [parse(left), parse(top), parse(right)];\n};\nexport var getGapWidth = function (gapMode) {\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    if (typeof window === 'undefined') {\n        return zeroGap;\n    }\n    var offsets = getOffset(gapMode);\n    var documentWidth = document.documentElement.clientWidth;\n    var windowWidth = window.innerWidth;\n    return {\n        left: offsets[0],\n        top: offsets[1],\n        right: offsets[2],\n        gap: Math.max(0, windowWidth - documentWidth + offsets[2] - offsets[0]),\n    };\n};\n"], "names": [], "mappings": ";;;;AAAO,IAAI,UAAU;IACjB,MAAM;IACN,KAAK;IACL,OAAO;IACP,KAAK;AACT;AACA,IAAI,QAAQ,SAAU,CAAC;IAAI,OAAO,SAAS,KAAK,IAAI,OAAO;AAAG;AAC9D,IAAI,YAAY,SAAU,OAAO;IAC7B,IAAI,KAAK,OAAO,gBAAgB,CAAC,SAAS,IAAI;IAC9C,IAAI,OAAO,EAAE,CAAC,YAAY,YAAY,gBAAgB,aAAa;IACnE,IAAI,MAAM,EAAE,CAAC,YAAY,YAAY,eAAe,YAAY;IAChE,IAAI,QAAQ,EAAE,CAAC,YAAY,YAAY,iBAAiB,cAAc;IACtE,OAAO;QAAC,MAAM;QAAO,MAAM;QAAM,MAAM;KAAO;AAClD;AACO,IAAI,cAAc,SAAU,OAAO;IACtC,IAAI,YAAY,KAAK,GAAG;QAAE,UAAU;IAAU;IAC9C,IAAI,OAAO,WAAW,aAAa;QAC/B,OAAO;IACX;IACA,IAAI,UAAU,UAAU;IACxB,IAAI,gBAAgB,SAAS,eAAe,CAAC,WAAW;IACxD,IAAI,cAAc,OAAO,UAAU;IACnC,OAAO;QACH,MAAM,OAAO,CAAC,EAAE;QAChB,KAAK,OAAO,CAAC,EAAE;QACf,OAAO,OAAO,CAAC,EAAE;QACjB,KAAK,KAAK,GAAG,CAAC,GAAG,cAAc,gBAAgB,OAAO,CAAC,EAAE,GAAG,OAAO,CAAC,EAAE;IAC1E;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3035, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll-bar/dist/es2015/component.js"], "sourcesContent": ["import * as React from 'react';\nimport { styleSingleton } from 'react-style-singleton';\nimport { fullWidthClassName, zeroRightClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nvar Style = styleSingleton();\nexport var lockAttribute = 'data-scroll-locked';\n// important tip - once we measure scrollBar width and remove them\n// we could not repeat this operation\n// thus we are using style-singleton - only the first \"yet correct\" style will be applied.\nvar getStyles = function (_a, allowRelative, gapMode, important) {\n    var left = _a.left, top = _a.top, right = _a.right, gap = _a.gap;\n    if (gapMode === void 0) { gapMode = 'margin'; }\n    return \"\\n  .\".concat(noScrollbarsClassName, \" {\\n   overflow: hidden \").concat(important, \";\\n   padding-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  body[\").concat(lockAttribute, \"] {\\n    overflow: hidden \").concat(important, \";\\n    overscroll-behavior: contain;\\n    \").concat([\n        allowRelative && \"position: relative \".concat(important, \";\"),\n        gapMode === 'margin' &&\n            \"\\n    padding-left: \".concat(left, \"px;\\n    padding-top: \").concat(top, \"px;\\n    padding-right: \").concat(right, \"px;\\n    margin-left:0;\\n    margin-top:0;\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n    \"),\n        gapMode === 'padding' && \"padding-right: \".concat(gap, \"px \").concat(important, \";\"),\n    ]\n        .filter(Boolean)\n        .join(''), \"\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" {\\n    right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" {\\n    margin-right: \").concat(gap, \"px \").concat(important, \";\\n  }\\n  \\n  .\").concat(zeroRightClassName, \" .\").concat(zeroRightClassName, \" {\\n    right: 0 \").concat(important, \";\\n  }\\n  \\n  .\").concat(fullWidthClassName, \" .\").concat(fullWidthClassName, \" {\\n    margin-right: 0 \").concat(important, \";\\n  }\\n  \\n  body[\").concat(lockAttribute, \"] {\\n    \").concat(removedBarSizeVariable, \": \").concat(gap, \"px;\\n  }\\n\");\n};\nvar getCurrentUseCounter = function () {\n    var counter = parseInt(document.body.getAttribute(lockAttribute) || '0', 10);\n    return isFinite(counter) ? counter : 0;\n};\nexport var useLockAttribute = function () {\n    React.useEffect(function () {\n        document.body.setAttribute(lockAttribute, (getCurrentUseCounter() + 1).toString());\n        return function () {\n            var newCounter = getCurrentUseCounter() - 1;\n            if (newCounter <= 0) {\n                document.body.removeAttribute(lockAttribute);\n            }\n            else {\n                document.body.setAttribute(lockAttribute, newCounter.toString());\n            }\n        };\n    }, []);\n};\n/**\n * Removes page scrollbar and blocks page scroll when mounted\n */\nexport var RemoveScrollBar = function (_a) {\n    var noRelative = _a.noRelative, noImportant = _a.noImportant, _b = _a.gapMode, gapMode = _b === void 0 ? 'margin' : _b;\n    useLockAttribute();\n    /*\n     gap will be measured on every component mount\n     however it will be used only by the \"first\" invocation\n     due to singleton nature of <Style\n     */\n    var gap = React.useMemo(function () { return getGapWidth(gapMode); }, [gapMode]);\n    return React.createElement(Style, { styles: getStyles(gap, !noRelative, gapMode, !noImportant ? '!important' : '') });\n};\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AAAA;AACA;AACA;;;;;AACA,IAAI,QAAQ,CAAA,GAAA,6KAAA,CAAA,iBAAc,AAAD;AAClB,IAAI,gBAAgB;AAC3B,kEAAkE;AAClE,qCAAqC;AACrC,0FAA0F;AAC1F,IAAI,YAAY,SAAU,EAAE,EAAE,aAAa,EAAE,OAAO,EAAE,SAAS;IAC3D,IAAI,OAAO,GAAG,IAAI,EAAE,MAAM,GAAG,GAAG,EAAE,QAAQ,GAAG,KAAK,EAAE,MAAM,GAAG,GAAG;IAChE,IAAI,YAAY,KAAK,GAAG;QAAE,UAAU;IAAU;IAC9C,OAAO,QAAQ,MAAM,CAAC,kLAAA,CAAA,wBAAqB,EAAE,4BAA4B,MAAM,CAAC,WAAW,yBAAyB,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,WAAW,mBAAmB,MAAM,CAAC,eAAe,8BAA8B,MAAM,CAAC,WAAW,8CAA8C,MAAM,CAAC;QACnS,iBAAiB,sBAAsB,MAAM,CAAC,WAAW;QACzD,YAAY,YACR,uBAAuB,MAAM,CAAC,MAAM,0BAA0B,MAAM,CAAC,KAAK,4BAA4B,MAAM,CAAC,OAAO,kEAAkE,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,WAAW;QAC/N,YAAY,aAAa,kBAAkB,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,WAAW;KACnF,CACI,MAAM,CAAC,SACP,IAAI,CAAC,KAAK,kBAAkB,MAAM,CAAC,kLAAA,CAAA,qBAAkB,EAAE,mBAAmB,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,WAAW,mBAAmB,MAAM,CAAC,kLAAA,CAAA,qBAAkB,EAAE,0BAA0B,MAAM,CAAC,KAAK,OAAO,MAAM,CAAC,WAAW,mBAAmB,MAAM,CAAC,kLAAA,CAAA,qBAAkB,EAAE,MAAM,MAAM,CAAC,kLAAA,CAAA,qBAAkB,EAAE,qBAAqB,MAAM,CAAC,WAAW,mBAAmB,MAAM,CAAC,kLAAA,CAAA,qBAAkB,EAAE,MAAM,MAAM,CAAC,kLAAA,CAAA,qBAAkB,EAAE,4BAA4B,MAAM,CAAC,WAAW,uBAAuB,MAAM,CAAC,eAAe,aAAa,MAAM,CAAC,kLAAA,CAAA,yBAAsB,EAAE,MAAM,MAAM,CAAC,KAAK;AACnkB;AACA,IAAI,uBAAuB;IACvB,IAAI,UAAU,SAAS,SAAS,IAAI,CAAC,YAAY,CAAC,kBAAkB,KAAK;IACzE,OAAO,SAAS,WAAW,UAAU;AACzC;AACO,IAAI,mBAAmB;IAC1B,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;sCAAE;YACZ,SAAS,IAAI,CAAC,YAAY,CAAC,eAAe,CAAC,yBAAyB,CAAC,EAAE,QAAQ;YAC/E;8CAAO;oBACH,IAAI,aAAa,yBAAyB;oBAC1C,IAAI,cAAc,GAAG;wBACjB,SAAS,IAAI,CAAC,eAAe,CAAC;oBAClC,OACK;wBACD,SAAS,IAAI,CAAC,YAAY,CAAC,eAAe,WAAW,QAAQ;oBACjE;gBACJ;;QACJ;qCAAG,EAAE;AACT;AAIO,IAAI,kBAAkB,SAAU,EAAE;IACrC,IAAI,aAAa,GAAG,UAAU,EAAE,cAAc,GAAG,WAAW,EAAE,KAAK,GAAG,OAAO,EAAE,UAAU,OAAO,KAAK,IAAI,WAAW;IACpH;IACA;;;;KAIC,GACD,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,UAAa,AAAD;wCAAE;YAAc,OAAO,CAAA,GAAA,8KAAA,CAAA,cAAW,AAAD,EAAE;QAAU;uCAAG;QAAC;KAAQ;IAC/E,OAAO,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QAAE,QAAQ,UAAU,KAAK,CAAC,YAAY,SAAS,CAAC,cAAc,eAAe;IAAI;AACvH", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3110, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll-bar/dist/es2015/index.js"], "sourcesContent": ["import { RemoveScrollBar } from './component';\nimport { zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable } from './constants';\nimport { getGapWidth } from './utils';\nexport { RemoveScrollBar, zeroRightClassName, fullWidthClassName, noScrollbarsClassName, removedBarSizeVariable, getGapWidth, };\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3135, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/use-callback-ref/dist/es2015/assignRef.js"], "sourcesContent": ["/**\n * Assigns a value for a given ref, no matter of the ref format\n * @param {RefObject} ref - a callback function or ref object\n * @param value - a new value\n *\n * @see https://github.com/theKashey/use-callback-ref#assignref\n * @example\n * const refObject = useRef();\n * const refFn = (ref) => {....}\n *\n * assignRef(refObject, \"refValue\");\n * assignRef(refFn, \"refValue\");\n */\nexport function assignRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    }\n    else if (ref) {\n        ref.current = value;\n    }\n    return ref;\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;;;CAYC;;;AACM,SAAS,UAAU,GAAG,EAAE,KAAK;IAChC,IAAI,OAAO,QAAQ,YAAY;QAC3B,IAAI;IACR,OACK,IAAI,KAAK;QACV,IAAI,OAAO,GAAG;IAClB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3164, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/use-callback-ref/dist/es2015/useRef.js"], "sourcesContent": ["import { useState } from 'react';\n/**\n * creates a MutableRef with ref change callback\n * @param initialValue - initial ref value\n * @param {Function} callback - a callback to run when value changes\n *\n * @example\n * const ref = useCallbackRef(0, (newValue, oldValue) => console.log(oldValue, '->', newValue);\n * ref.current = 1;\n * // prints 0 -> 1\n *\n * @see https://reactjs.org/docs/hooks-reference.html#useref\n * @see https://github.com/theKashey/use-callback-ref#usecallbackref---to-replace-reactuseref\n * @returns {MutableRefObject}\n */\nexport function useCallbackRef(initialValue, callback) {\n    var ref = useState(function () { return ({\n        // value\n        value: initialValue,\n        // last callback\n        callback: callback,\n        // \"memoized\" public interface\n        facade: {\n            get current() {\n                return ref.value;\n            },\n            set current(value) {\n                var last = ref.value;\n                if (last !== value) {\n                    ref.value = value;\n                    ref.callback(value, last);\n                }\n            },\n        },\n    }); })[0];\n    // update callback\n    ref.callback = callback;\n    return ref.facade;\n}\n"], "names": [], "mappings": ";;;AAAA;;AAeO,SAAS,eAAe,YAAY,EAAE,QAAQ;IACjD,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD;mCAAE;YAAc,OAAQ;gBACrC,QAAQ;gBACR,OAAO;gBACP,gBAAgB;gBAChB,UAAU;gBACV,8BAA8B;gBAC9B,QAAQ;oBACJ,IAAI,WAAU;wBACV,OAAO,IAAI,KAAK;oBACpB;oBACA,IAAI,SAAQ,MAAO;wBACf,IAAI,OAAO,IAAI,KAAK;wBACpB,IAAI,SAAS,OAAO;4BAChB,IAAI,KAAK,GAAG;4BACZ,IAAI,QAAQ,CAAC,OAAO;wBACxB;oBACJ;gBACJ;YACJ;QAAI;iCAAE,CAAC,EAAE;IACT,kBAAkB;IAClB,IAAI,QAAQ,GAAG;IACf,OAAO,IAAI,MAAM;AACrB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3203, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/use-callback-ref/dist/es2015/useMergeRef.js"], "sourcesContent": ["import * as React from 'react';\nimport { assignRef } from './assignRef';\nimport { useCallbackRef } from './useRef';\nvar useIsomorphicLayoutEffect = typeof window !== 'undefined' ? React.useLayoutEffect : React.useEffect;\nvar currentValues = new WeakMap();\n/**\n * Merges two or more refs together providing a single interface to set their value\n * @param {RefObject|Ref} refs\n * @returns {MutableRefObject} - a new ref, which translates all changes to {refs}\n *\n * @see {@link mergeRefs} a version without buit-in memoization\n * @see https://github.com/theKashey/use-callback-ref#usemergerefs\n * @example\n * const Component = React.forwardRef((props, ref) => {\n *   const ownRef = useRef();\n *   const domRef = useMergeRefs([ref, ownRef]); // 👈 merge together\n *   return <div ref={domRef}>...</div>\n * }\n */\nexport function useMergeRefs(refs, defaultValue) {\n    var callbackRef = useCallbackRef(defaultValue || null, function (newValue) {\n        return refs.forEach(function (ref) { return assignRef(ref, newValue); });\n    });\n    // handle refs changes - added or removed\n    useIsomorphicLayoutEffect(function () {\n        var oldValue = currentValues.get(callbackRef);\n        if (oldValue) {\n            var prevRefs_1 = new Set(oldValue);\n            var nextRefs_1 = new Set(refs);\n            var current_1 = callbackRef.current;\n            prevRefs_1.forEach(function (ref) {\n                if (!nextRefs_1.has(ref)) {\n                    assignRef(ref, null);\n                }\n            });\n            nextRefs_1.forEach(function (ref) {\n                if (!prevRefs_1.has(ref)) {\n                    assignRef(ref, current_1);\n                }\n            });\n        }\n        currentValues.set(callbackRef, refs);\n    }, [refs]);\n    return callbackRef;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;AACA,IAAI,4BAA4B,OAAO,WAAW,cAAc,6JAAA,CAAA,kBAAqB,GAAG,6JAAA,CAAA,YAAe;AACvG,IAAI,gBAAgB,IAAI;AAejB,SAAS,aAAa,IAAI,EAAE,YAAY;IAC3C,IAAI,cAAc,CAAA,GAAA,qKAAA,CAAA,iBAAc,AAAD,EAAE,gBAAgB;oDAAM,SAAU,QAAQ;YACrE,OAAO,KAAK,OAAO;4DAAC,SAAU,GAAG;oBAAI,OAAO,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE,KAAK;gBAAW;;QAC1E;;IACA,yCAAyC;IACzC;kDAA0B;YACtB,IAAI,WAAW,cAAc,GAAG,CAAC;YACjC,IAAI,UAAU;gBACV,IAAI,aAAa,IAAI,IAAI;gBACzB,IAAI,aAAa,IAAI,IAAI;gBACzB,IAAI,YAAY,YAAY,OAAO;gBACnC,WAAW,OAAO;8DAAC,SAAU,GAAG;wBAC5B,IAAI,CAAC,WAAW,GAAG,CAAC,MAAM;4BACtB,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE,KAAK;wBACnB;oBACJ;;gBACA,WAAW,OAAO;8DAAC,SAAU,GAAG;wBAC5B,IAAI,CAAC,WAAW,GAAG,CAAC,MAAM;4BACtB,CAAA,GAAA,wKAAA,CAAA,YAAS,AAAD,EAAE,KAAK;wBACnB;oBACJ;;YACJ;YACA,cAAc,GAAG,CAAC,aAAa;QACnC;iDAAG;QAAC;KAAK;IACT,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3260, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/use-sidecar/dist/es2015/medium.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nfunction ItoI(a) {\n    return a;\n}\nfunction innerCreateMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    var buffer = [];\n    var assigned = false;\n    var medium = {\n        read: function () {\n            if (assigned) {\n                throw new Error('Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.');\n            }\n            if (buffer.length) {\n                return buffer[buffer.length - 1];\n            }\n            return defaults;\n        },\n        useMedium: function (data) {\n            var item = middleware(data, assigned);\n            buffer.push(item);\n            return function () {\n                buffer = buffer.filter(function (x) { return x !== item; });\n            };\n        },\n        assignSyncMedium: function (cb) {\n            assigned = true;\n            while (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n            }\n            buffer = {\n                push: function (x) { return cb(x); },\n                filter: function () { return buffer; },\n            };\n        },\n        assignMedium: function (cb) {\n            assigned = true;\n            var pendingQueue = [];\n            if (buffer.length) {\n                var cbs = buffer;\n                buffer = [];\n                cbs.forEach(cb);\n                pendingQueue = buffer;\n            }\n            var executeQueue = function () {\n                var cbs = pendingQueue;\n                pendingQueue = [];\n                cbs.forEach(cb);\n            };\n            var cycle = function () { return Promise.resolve().then(executeQueue); };\n            cycle();\n            buffer = {\n                push: function (x) {\n                    pendingQueue.push(x);\n                    cycle();\n                },\n                filter: function (filter) {\n                    pendingQueue = pendingQueue.filter(filter);\n                    return buffer;\n                },\n            };\n        },\n    };\n    return medium;\n}\nexport function createMedium(defaults, middleware) {\n    if (middleware === void 0) { middleware = ItoI; }\n    return innerCreateMedium(defaults, middleware);\n}\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport function createSidecarMedium(options) {\n    if (options === void 0) { options = {}; }\n    var medium = innerCreateMedium(null);\n    medium.options = __assign({ async: true, ssr: false }, options);\n    return medium;\n}\n"], "names": [], "mappings": ";;;;AAAA;;AACA,SAAS,KAAK,CAAC;IACX,OAAO;AACX;AACA,SAAS,kBAAkB,QAAQ,EAAE,UAAU;IAC3C,IAAI,eAAe,KAAK,GAAG;QAAE,aAAa;IAAM;IAChD,IAAI,SAAS,EAAE;IACf,IAAI,WAAW;IACf,IAAI,SAAS;QACT,MAAM;YACF,IAAI,UAAU;gBACV,MAAM,IAAI,MAAM;YACpB;YACA,IAAI,OAAO,MAAM,EAAE;gBACf,OAAO,MAAM,CAAC,OAAO,MAAM,GAAG,EAAE;YACpC;YACA,OAAO;QACX;QACA,WAAW,SAAU,IAAI;YACrB,IAAI,OAAO,WAAW,MAAM;YAC5B,OAAO,IAAI,CAAC;YACZ,OAAO;gBACH,SAAS,OAAO,MAAM,CAAC,SAAU,CAAC;oBAAI,OAAO,MAAM;gBAAM;YAC7D;QACJ;QACA,kBAAkB,SAAU,EAAE;YAC1B,WAAW;YACX,MAAO,OAAO,MAAM,CAAE;gBAClB,IAAI,MAAM;gBACV,SAAS,EAAE;gBACX,IAAI,OAAO,CAAC;YAChB;YACA,SAAS;gBACL,MAAM,SAAU,CAAC;oBAAI,OAAO,GAAG;gBAAI;gBACnC,QAAQ;oBAAc,OAAO;gBAAQ;YACzC;QACJ;QACA,cAAc,SAAU,EAAE;YACtB,WAAW;YACX,IAAI,eAAe,EAAE;YACrB,IAAI,OAAO,MAAM,EAAE;gBACf,IAAI,MAAM;gBACV,SAAS,EAAE;gBACX,IAAI,OAAO,CAAC;gBACZ,eAAe;YACnB;YACA,IAAI,eAAe;gBACf,IAAI,MAAM;gBACV,eAAe,EAAE;gBACjB,IAAI,OAAO,CAAC;YAChB;YACA,IAAI,QAAQ;gBAAc,OAAO,QAAQ,OAAO,GAAG,IAAI,CAAC;YAAe;YACvE;YACA,SAAS;gBACL,MAAM,SAAU,CAAC;oBACb,aAAa,IAAI,CAAC;oBAClB;gBACJ;gBACA,QAAQ,SAAU,MAAM;oBACpB,eAAe,aAAa,MAAM,CAAC;oBACnC,OAAO;gBACX;YACJ;QACJ;IACJ;IACA,OAAO;AACX;AACO,SAAS,aAAa,QAAQ,EAAE,UAAU;IAC7C,IAAI,eAAe,KAAK,GAAG;QAAE,aAAa;IAAM;IAChD,OAAO,kBAAkB,UAAU;AACvC;AAEO,SAAS,oBAAoB,OAAO;IACvC,IAAI,YAAY,KAAK,GAAG;QAAE,UAAU,CAAC;IAAG;IACxC,IAAI,SAAS,kBAAkB;IAC/B,OAAO,OAAO,GAAG,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE;QAAE,OAAO;QAAM,KAAK;IAAM,GAAG;IACvD,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3365, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/use-sidecar/dist/es2015/exports.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nvar SideCar = function (_a) {\n    var sideCar = _a.sideCar, rest = __rest(_a, [\"sideCar\"]);\n    if (!sideCar) {\n        throw new Error('Sidecar: please provide `sideCar` property to import the right car');\n    }\n    var Target = sideCar.read();\n    if (!Target) {\n        throw new Error('Sidecar medium not found');\n    }\n    return React.createElement(Target, __assign({}, rest));\n};\nSideCar.isSideCarExport = true;\nexport function exportSidecar(medium, exported) {\n    medium.useMedium(exported);\n    return SideCar;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACA,IAAI,UAAU,SAAU,EAAE;IACtB,IAAI,UAAU,GAAG,OAAO,EAAE,OAAO,CAAA,GAAA,yIAAA,CAAA,SAAM,AAAD,EAAE,IAAI;QAAC;KAAU;IACvD,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,IAAI,SAAS,QAAQ,IAAI;IACzB,IAAI,CAAC,QAAQ;QACT,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,QAAQ,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG;AACpD;AACA,QAAQ,eAAe,GAAG;AACnB,SAAS,cAAc,MAAM,EAAE,QAAQ;IAC1C,OAAO,SAAS,CAAC;IACjB,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3396, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll/dist/es2015/medium.js"], "sourcesContent": ["import { createSidecarMedium } from 'use-sidecar';\nexport var effectCar = createSidecarMedium();\n"], "names": [], "mappings": ";;;AAAA;;AACO,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,sBAAmB,AAAD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3408, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll/dist/es2015/UI.js"], "sourcesContent": ["import { __assign, __rest } from \"tslib\";\nimport * as React from 'react';\nimport { fullWidthClassName, zeroRightClassName } from 'react-remove-scroll-bar/constants';\nimport { useMergeRefs } from 'use-callback-ref';\nimport { effectCar } from './medium';\nvar nothing = function () {\n    return;\n};\n/**\n * Removes scrollbar from the page and contain the scroll within the Lock\n */\nvar RemoveScroll = React.forwardRef(function (props, parentRef) {\n    var ref = React.useRef(null);\n    var _a = React.useState({\n        onScrollCapture: nothing,\n        onWheelCapture: nothing,\n        onTouchMoveCapture: nothing,\n    }), callbacks = _a[0], setCallbacks = _a[1];\n    var forwardProps = props.forwardProps, children = props.children, className = props.className, removeScrollBar = props.removeScrollBar, enabled = props.enabled, shards = props.shards, sideCar = props.sideCar, noRelative = props.noRelative, noIsolation = props.noIsolation, inert = props.inert, allowPinchZoom = props.allowPinchZoom, _b = props.as, Container = _b === void 0 ? 'div' : _b, gapMode = props.gapMode, rest = __rest(props, [\"forwardProps\", \"children\", \"className\", \"removeScrollBar\", \"enabled\", \"shards\", \"sideCar\", \"noRelative\", \"noIsolation\", \"inert\", \"allowPinchZoom\", \"as\", \"gapMode\"]);\n    var SideCar = sideCar;\n    var containerRef = useMergeRefs([ref, parentRef]);\n    var containerProps = __assign(__assign({}, rest), callbacks);\n    return (React.createElement(React.Fragment, null,\n        enabled && (React.createElement(SideCar, { sideCar: effectCar, removeScrollBar: removeScrollBar, shards: shards, noRelative: noRelative, noIsolation: noIsolation, inert: inert, setCallbacks: setCallbacks, allowPinchZoom: !!allowPinchZoom, lockRef: ref, gapMode: gapMode })),\n        forwardProps ? (React.cloneElement(React.Children.only(children), __assign(__assign({}, containerProps), { ref: containerRef }))) : (React.createElement(Container, __assign({}, containerProps, { className: className, ref: containerRef }), children))));\n});\nRemoveScroll.defaultProps = {\n    enabled: true,\n    removeScrollBar: true,\n    inert: false,\n};\nRemoveScroll.classNames = {\n    fullWidth: fullWidthClassName,\n    zeroRight: zeroRightClassName,\n};\nexport { RemoveScroll };\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;;;;;;AACA,IAAI,UAAU;IACV;AACJ;AACA;;CAEC,GACD,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,SAAU,KAAK,EAAE,SAAS;IAC1D,IAAI,MAAM,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IACvB,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE;QACpB,iBAAiB;QACjB,gBAAgB;QAChB,oBAAoB;IACxB,IAAI,YAAY,EAAE,CAAC,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;IAC3C,IAAI,eAAe,MAAM,YAAY,EAAE,WAAW,MAAM,QAAQ,EAAE,YAAY,MAAM,SAAS,EAAE,kBAAkB,MAAM,eAAe,EAAE,UAAU,MAAM,OAAO,EAAE,SAAS,MAAM,MAAM,EAAE,UAAU,MAAM,OAAO,EAAE,aAAa,MAAM,UAAU,EAAE,cAAc,MAAM,WAAW,EAAE,QAAQ,MAAM,KAAK,EAAE,iBAAiB,MAAM,cAAc,EAAE,KAAK,MAAM,EAAE,EAAE,YAAY,OAAO,KAAK,IAAI,QAAQ,IAAI,UAAU,MAAM,OAAO,EAAE,OAAO,CAAA,GAAA,yIAAA,CAAA,SAAM,AAAD,EAAE,OAAO;QAAC;QAAgB;QAAY;QAAa;QAAmB;QAAW;QAAU;QAAW;QAAc;QAAe;QAAS;QAAkB;QAAM;KAAU;IACvlB,IAAI,UAAU;IACd,IAAI,eAAe,CAAA,GAAA,0KAAA,CAAA,eAAY,AAAD,EAAE;QAAC;QAAK;KAAU;IAChD,IAAI,iBAAiB,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;IAClD,OAAQ,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,6JAAA,CAAA,WAAc,EAAE,MACxC,WAAY,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,SAAS;QAAE,SAAS,wKAAA,CAAA,YAAS;QAAE,iBAAiB;QAAiB,QAAQ;QAAQ,YAAY;QAAY,aAAa;QAAa,OAAO;QAAO,cAAc;QAAc,gBAAgB,CAAC,CAAC;QAAgB,SAAS;QAAK,SAAS;IAAQ,IAC9Q,eAAgB,CAAA,GAAA,6JAAA,CAAA,eAAkB,AAAD,EAAE,6JAAA,CAAA,WAAc,CAAC,IAAI,CAAC,WAAW,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,iBAAiB;QAAE,KAAK;IAAa,MAAQ,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,WAAW,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,gBAAgB;QAAE,WAAW;QAAW,KAAK;IAAa,IAAI;AACvP;AACA,aAAa,YAAY,GAAG;IACxB,SAAS;IACT,iBAAiB;IACjB,OAAO;AACX;AACA,aAAa,UAAU,GAAG;IACtB,WAAW,kLAAA,CAAA,qBAAkB;IAC7B,WAAW,kLAAA,CAAA,qBAAkB;AACjC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll/dist/es2015/aggresiveCapture.js"], "sourcesContent": ["var passiveSupported = false;\nif (typeof window !== 'undefined') {\n    try {\n        var options = Object.defineProperty({}, 'passive', {\n            get: function () {\n                passiveSupported = true;\n                return true;\n            },\n        });\n        // @ts-ignore\n        window.addEventListener('test', options, options);\n        // @ts-ignore\n        window.removeEventListener('test', options, options);\n    }\n    catch (err) {\n        passiveSupported = false;\n    }\n}\nexport var nonPassive = passiveSupported ? { passive: false } : false;\n"], "names": [], "mappings": ";;;AAAA,IAAI,mBAAmB;AACvB,IAAI,OAAO,WAAW,aAAa;IAC/B,IAAI;QACA,IAAI,UAAU,OAAO,cAAc,CAAC,CAAC,GAAG,WAAW;YAC/C,KAAK;gBACD,mBAAmB;gBACnB,OAAO;YACX;QACJ;QACA,aAAa;QACb,OAAO,gBAAgB,CAAC,QAAQ,SAAS;QACzC,aAAa;QACb,OAAO,mBAAmB,CAAC,QAAQ,SAAS;IAChD,EACA,OAAO,KAAK;QACR,mBAAmB;IACvB;AACJ;AACO,IAAI,aAAa,mBAAmB;IAAE,SAAS;AAAM,IAAI", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3517, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll/dist/es2015/handleScroll.js"], "sourcesContent": ["var alwaysContainsScroll = function (node) {\n    // textarea will always _contain_ scroll inside self. It only can be hidden\n    return node.tagName === 'TEXTAREA';\n};\nvar elementCanBeScrolled = function (node, overflow) {\n    if (!(node instanceof Element)) {\n        return false;\n    }\n    var styles = window.getComputedStyle(node);\n    return (\n    // not-not-scrollable\n    styles[overflow] !== 'hidden' &&\n        // contains scroll inside self\n        !(styles.overflowY === styles.overflowX && !alwaysContainsScroll(node) && styles[overflow] === 'visible'));\n};\nvar elementCouldBeVScrolled = function (node) { return elementCanBeScrolled(node, 'overflowY'); };\nvar elementCouldBeHScrolled = function (node) { return elementCanBeScrolled(node, 'overflowX'); };\nexport var locationCouldBeScrolled = function (axis, node) {\n    var ownerDocument = node.ownerDocument;\n    var current = node;\n    do {\n        // Skip over shadow root\n        if (typeof ShadowRoot !== 'undefined' && current instanceof ShadowRoot) {\n            current = current.host;\n        }\n        var isScrollable = elementCouldBeScrolled(axis, current);\n        if (isScrollable) {\n            var _a = getScrollVariables(axis, current), scrollHeight = _a[1], clientHeight = _a[2];\n            if (scrollHeight > clientHeight) {\n                return true;\n            }\n        }\n        current = current.parentNode;\n    } while (current && current !== ownerDocument.body);\n    return false;\n};\nvar getVScrollVariables = function (_a) {\n    var scrollTop = _a.scrollTop, scrollHeight = _a.scrollHeight, clientHeight = _a.clientHeight;\n    return [\n        scrollTop,\n        scrollHeight,\n        clientHeight,\n    ];\n};\nvar getHScrollVariables = function (_a) {\n    var scrollLeft = _a.scrollLeft, scrollWidth = _a.scrollWidth, clientWidth = _a.clientWidth;\n    return [\n        scrollLeft,\n        scrollWidth,\n        clientWidth,\n    ];\n};\nvar elementCouldBeScrolled = function (axis, node) {\n    return axis === 'v' ? elementCouldBeVScrolled(node) : elementCouldBeHScrolled(node);\n};\nvar getScrollVariables = function (axis, node) {\n    return axis === 'v' ? getVScrollVariables(node) : getHScrollVariables(node);\n};\nvar getDirectionFactor = function (axis, direction) {\n    /**\n     * If the element's direction is rtl (right-to-left), then scrollLeft is 0 when the scrollbar is at its rightmost position,\n     * and then increasingly negative as you scroll towards the end of the content.\n     * @see https://developer.mozilla.org/en-US/docs/Web/API/Element/scrollLeft\n     */\n    return axis === 'h' && direction === 'rtl' ? -1 : 1;\n};\nexport var handleScroll = function (axis, endTarget, event, sourceDelta, noOverscroll) {\n    var directionFactor = getDirectionFactor(axis, window.getComputedStyle(endTarget).direction);\n    var delta = directionFactor * sourceDelta;\n    // find scrollable target\n    var target = event.target;\n    var targetInLock = endTarget.contains(target);\n    var shouldCancelScroll = false;\n    var isDeltaPositive = delta > 0;\n    var availableScroll = 0;\n    var availableScrollTop = 0;\n    do {\n        if (!target) {\n            break;\n        }\n        var _a = getScrollVariables(axis, target), position = _a[0], scroll_1 = _a[1], capacity = _a[2];\n        var elementScroll = scroll_1 - capacity - directionFactor * position;\n        if (position || elementScroll) {\n            if (elementCouldBeScrolled(axis, target)) {\n                availableScroll += elementScroll;\n                availableScrollTop += position;\n            }\n        }\n        var parent_1 = target.parentNode;\n        // we will \"bubble\" from ShadowDom in case we are, or just to the parent in normal case\n        // this is the same logic used in focus-lock\n        target = (parent_1 && parent_1.nodeType === Node.DOCUMENT_FRAGMENT_NODE ? parent_1.host : parent_1);\n    } while (\n    // portaled content\n    (!targetInLock && target !== document.body) ||\n        // self content\n        (targetInLock && (endTarget.contains(target) || endTarget === target)));\n    // handle epsilon around 0 (non standard zoom levels)\n    if (isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScroll) < 1) || (!noOverscroll && delta > availableScroll))) {\n        shouldCancelScroll = true;\n    }\n    else if (!isDeltaPositive &&\n        ((noOverscroll && Math.abs(availableScrollTop) < 1) || (!noOverscroll && -delta > availableScrollTop))) {\n        shouldCancelScroll = true;\n    }\n    return shouldCancelScroll;\n};\n"], "names": [], "mappings": ";;;;AAAA,IAAI,uBAAuB,SAAU,IAAI;IACrC,2EAA2E;IAC3E,OAAO,KAAK,OAAO,KAAK;AAC5B;AACA,IAAI,uBAAuB,SAAU,IAAI,EAAE,QAAQ;IAC/C,IAAI,CAAC,CAAC,gBAAgB,OAAO,GAAG;QAC5B,OAAO;IACX;IACA,IAAI,SAAS,OAAO,gBAAgB,CAAC;IACrC,OACA,qBAAqB;IACrB,MAAM,CAAC,SAAS,KAAK,YACjB,8BAA8B;IAC9B,CAAC,CAAC,OAAO,SAAS,KAAK,OAAO,SAAS,IAAI,CAAC,qBAAqB,SAAS,MAAM,CAAC,SAAS,KAAK,SAAS;AAChH;AACA,IAAI,0BAA0B,SAAU,IAAI;IAAI,OAAO,qBAAqB,MAAM;AAAc;AAChG,IAAI,0BAA0B,SAAU,IAAI;IAAI,OAAO,qBAAqB,MAAM;AAAc;AACzF,IAAI,0BAA0B,SAAU,IAAI,EAAE,IAAI;IACrD,IAAI,gBAAgB,KAAK,aAAa;IACtC,IAAI,UAAU;IACd,GAAG;QACC,wBAAwB;QACxB,IAAI,OAAO,eAAe,eAAe,mBAAmB,YAAY;YACpE,UAAU,QAAQ,IAAI;QAC1B;QACA,IAAI,eAAe,uBAAuB,MAAM;QAChD,IAAI,cAAc;YACd,IAAI,KAAK,mBAAmB,MAAM,UAAU,eAAe,EAAE,CAAC,EAAE,EAAE,eAAe,EAAE,CAAC,EAAE;YACtF,IAAI,eAAe,cAAc;gBAC7B,OAAO;YACX;QACJ;QACA,UAAU,QAAQ,UAAU;IAChC,QAAS,WAAW,YAAY,cAAc,IAAI,CAAE;IACpD,OAAO;AACX;AACA,IAAI,sBAAsB,SAAU,EAAE;IAClC,IAAI,YAAY,GAAG,SAAS,EAAE,eAAe,GAAG,YAAY,EAAE,eAAe,GAAG,YAAY;IAC5F,OAAO;QACH;QACA;QACA;KACH;AACL;AACA,IAAI,sBAAsB,SAAU,EAAE;IAClC,IAAI,aAAa,GAAG,UAAU,EAAE,cAAc,GAAG,WAAW,EAAE,cAAc,GAAG,WAAW;IAC1F,OAAO;QACH;QACA;QACA;KACH;AACL;AACA,IAAI,yBAAyB,SAAU,IAAI,EAAE,IAAI;IAC7C,OAAO,SAAS,MAAM,wBAAwB,QAAQ,wBAAwB;AAClF;AACA,IAAI,qBAAqB,SAAU,IAAI,EAAE,IAAI;IACzC,OAAO,SAAS,MAAM,oBAAoB,QAAQ,oBAAoB;AAC1E;AACA,IAAI,qBAAqB,SAAU,IAAI,EAAE,SAAS;IAC9C;;;;KAIC,GACD,OAAO,SAAS,OAAO,cAAc,QAAQ,CAAC,IAAI;AACtD;AACO,IAAI,eAAe,SAAU,IAAI,EAAE,SAAS,EAAE,KAAK,EAAE,WAAW,EAAE,YAAY;IACjF,IAAI,kBAAkB,mBAAmB,MAAM,OAAO,gBAAgB,CAAC,WAAW,SAAS;IAC3F,IAAI,QAAQ,kBAAkB;IAC9B,yBAAyB;IACzB,IAAI,SAAS,MAAM,MAAM;IACzB,IAAI,eAAe,UAAU,QAAQ,CAAC;IACtC,IAAI,qBAAqB;IACzB,IAAI,kBAAkB,QAAQ;IAC9B,IAAI,kBAAkB;IACtB,IAAI,qBAAqB;IACzB,GAAG;QACC,IAAI,CAAC,QAAQ;YACT;QACJ;QACA,IAAI,KAAK,mBAAmB,MAAM,SAAS,WAAW,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE,EAAE,WAAW,EAAE,CAAC,EAAE;QAC/F,IAAI,gBAAgB,WAAW,WAAW,kBAAkB;QAC5D,IAAI,YAAY,eAAe;YAC3B,IAAI,uBAAuB,MAAM,SAAS;gBACtC,mBAAmB;gBACnB,sBAAsB;YAC1B;QACJ;QACA,IAAI,WAAW,OAAO,UAAU;QAChC,uFAAuF;QACvF,4CAA4C;QAC5C,SAAU,YAAY,SAAS,QAAQ,KAAK,KAAK,sBAAsB,GAAG,SAAS,IAAI,GAAG;IAC9F,QACA,mBAAmB;IAClB,CAAC,gBAAgB,WAAW,SAAS,IAAI,IAErC,gBAAgB,CAAC,UAAU,QAAQ,CAAC,WAAW,cAAc,MAAM,EAAI;IAC5E,qDAAqD;IACrD,IAAI,mBACA,CAAC,AAAC,gBAAgB,KAAK,GAAG,CAAC,mBAAmB,KAAO,CAAC,gBAAgB,QAAQ,eAAgB,GAAG;QACjG,qBAAqB;IACzB,OACK,IAAI,CAAC,mBACN,CAAC,AAAC,gBAAgB,KAAK,GAAG,CAAC,sBAAsB,KAAO,CAAC,gBAAgB,CAAC,QAAQ,kBAAmB,GAAG;QACxG,qBAAqB;IACzB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3630, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll/dist/es2015/SideEffect.js"], "sourcesContent": ["import { __spreadArray } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScrollBar } from 'react-remove-scroll-bar';\nimport { styleSingleton } from 'react-style-singleton';\nimport { nonPassive } from './aggresiveCapture';\nimport { handleScroll, locationCouldBeScrolled } from './handleScroll';\nexport var getTouchXY = function (event) {\n    return 'changedTouches' in event ? [event.changedTouches[0].clientX, event.changedTouches[0].clientY] : [0, 0];\n};\nexport var getDeltaXY = function (event) { return [event.deltaX, event.deltaY]; };\nvar extractRef = function (ref) {\n    return ref && 'current' in ref ? ref.current : ref;\n};\nvar deltaCompare = function (x, y) { return x[0] === y[0] && x[1] === y[1]; };\nvar generateStyle = function (id) { return \"\\n  .block-interactivity-\".concat(id, \" {pointer-events: none;}\\n  .allow-interactivity-\").concat(id, \" {pointer-events: all;}\\n\"); };\nvar idCounter = 0;\nvar lockStack = [];\nexport function RemoveScrollSideCar(props) {\n    var shouldPreventQueue = React.useRef([]);\n    var touchStartRef = React.useRef([0, 0]);\n    var activeAxis = React.useRef();\n    var id = React.useState(idCounter++)[0];\n    var Style = React.useState(styleSingleton)[0];\n    var lastProps = React.useRef(props);\n    React.useEffect(function () {\n        lastProps.current = props;\n    }, [props]);\n    React.useEffect(function () {\n        if (props.inert) {\n            document.body.classList.add(\"block-interactivity-\".concat(id));\n            var allow_1 = __spreadArray([props.lockRef.current], (props.shards || []).map(extractRef), true).filter(Boolean);\n            allow_1.forEach(function (el) { return el.classList.add(\"allow-interactivity-\".concat(id)); });\n            return function () {\n                document.body.classList.remove(\"block-interactivity-\".concat(id));\n                allow_1.forEach(function (el) { return el.classList.remove(\"allow-interactivity-\".concat(id)); });\n            };\n        }\n        return;\n    }, [props.inert, props.lockRef.current, props.shards]);\n    var shouldCancelEvent = React.useCallback(function (event, parent) {\n        if (('touches' in event && event.touches.length === 2) || (event.type === 'wheel' && event.ctrlKey)) {\n            return !lastProps.current.allowPinchZoom;\n        }\n        var touch = getTouchXY(event);\n        var touchStart = touchStartRef.current;\n        var deltaX = 'deltaX' in event ? event.deltaX : touchStart[0] - touch[0];\n        var deltaY = 'deltaY' in event ? event.deltaY : touchStart[1] - touch[1];\n        var currentAxis;\n        var target = event.target;\n        var moveDirection = Math.abs(deltaX) > Math.abs(deltaY) ? 'h' : 'v';\n        // allow horizontal touch move on Range inputs. They will not cause any scroll\n        if ('touches' in event && moveDirection === 'h' && target.type === 'range') {\n            return false;\n        }\n        var canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n        if (!canBeScrolledInMainDirection) {\n            return true;\n        }\n        if (canBeScrolledInMainDirection) {\n            currentAxis = moveDirection;\n        }\n        else {\n            currentAxis = moveDirection === 'v' ? 'h' : 'v';\n            canBeScrolledInMainDirection = locationCouldBeScrolled(moveDirection, target);\n            // other axis might be not scrollable\n        }\n        if (!canBeScrolledInMainDirection) {\n            return false;\n        }\n        if (!activeAxis.current && 'changedTouches' in event && (deltaX || deltaY)) {\n            activeAxis.current = currentAxis;\n        }\n        if (!currentAxis) {\n            return true;\n        }\n        var cancelingAxis = activeAxis.current || currentAxis;\n        return handleScroll(cancelingAxis, parent, event, cancelingAxis === 'h' ? deltaX : deltaY, true);\n    }, []);\n    var shouldPrevent = React.useCallback(function (_event) {\n        var event = _event;\n        if (!lockStack.length || lockStack[lockStack.length - 1] !== Style) {\n            // not the last active\n            return;\n        }\n        var delta = 'deltaY' in event ? getDeltaXY(event) : getTouchXY(event);\n        var sourceEvent = shouldPreventQueue.current.filter(function (e) { return e.name === event.type && (e.target === event.target || event.target === e.shadowParent) && deltaCompare(e.delta, delta); })[0];\n        // self event, and should be canceled\n        if (sourceEvent && sourceEvent.should) {\n            if (event.cancelable) {\n                event.preventDefault();\n            }\n            return;\n        }\n        // outside or shard event\n        if (!sourceEvent) {\n            var shardNodes = (lastProps.current.shards || [])\n                .map(extractRef)\n                .filter(Boolean)\n                .filter(function (node) { return node.contains(event.target); });\n            var shouldStop = shardNodes.length > 0 ? shouldCancelEvent(event, shardNodes[0]) : !lastProps.current.noIsolation;\n            if (shouldStop) {\n                if (event.cancelable) {\n                    event.preventDefault();\n                }\n            }\n        }\n    }, []);\n    var shouldCancel = React.useCallback(function (name, delta, target, should) {\n        var event = { name: name, delta: delta, target: target, should: should, shadowParent: getOutermostShadowParent(target) };\n        shouldPreventQueue.current.push(event);\n        setTimeout(function () {\n            shouldPreventQueue.current = shouldPreventQueue.current.filter(function (e) { return e !== event; });\n        }, 1);\n    }, []);\n    var scrollTouchStart = React.useCallback(function (event) {\n        touchStartRef.current = getTouchXY(event);\n        activeAxis.current = undefined;\n    }, []);\n    var scrollWheel = React.useCallback(function (event) {\n        shouldCancel(event.type, getDeltaXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    var scrollTouchMove = React.useCallback(function (event) {\n        shouldCancel(event.type, getTouchXY(event), event.target, shouldCancelEvent(event, props.lockRef.current));\n    }, []);\n    React.useEffect(function () {\n        lockStack.push(Style);\n        props.setCallbacks({\n            onScrollCapture: scrollWheel,\n            onWheelCapture: scrollWheel,\n            onTouchMoveCapture: scrollTouchMove,\n        });\n        document.addEventListener('wheel', shouldPrevent, nonPassive);\n        document.addEventListener('touchmove', shouldPrevent, nonPassive);\n        document.addEventListener('touchstart', scrollTouchStart, nonPassive);\n        return function () {\n            lockStack = lockStack.filter(function (inst) { return inst !== Style; });\n            document.removeEventListener('wheel', shouldPrevent, nonPassive);\n            document.removeEventListener('touchmove', shouldPrevent, nonPassive);\n            document.removeEventListener('touchstart', scrollTouchStart, nonPassive);\n        };\n    }, []);\n    var removeScrollBar = props.removeScrollBar, inert = props.inert;\n    return (React.createElement(React.Fragment, null,\n        inert ? React.createElement(Style, { styles: generateStyle(id) }) : null,\n        removeScrollBar ? React.createElement(RemoveScrollBar, { noRelative: props.noRelative, gapMode: props.gapMode }) : null));\n}\nfunction getOutermostShadowParent(node) {\n    var shadowParent = null;\n    while (node !== null) {\n        if (node instanceof ShadowRoot) {\n            shadowParent = node.host;\n            node = node.host;\n        }\n        node = node.parentNode;\n    }\n    return shadowParent;\n}\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAAA;AACA;AAAA;AACA;AACA;;;;;;;AACO,IAAI,aAAa,SAAU,KAAK;IACnC,OAAO,oBAAoB,QAAQ;QAAC,MAAM,cAAc,CAAC,EAAE,CAAC,OAAO;QAAE,MAAM,cAAc,CAAC,EAAE,CAAC,OAAO;KAAC,GAAG;QAAC;QAAG;KAAE;AAClH;AACO,IAAI,aAAa,SAAU,KAAK;IAAI,OAAO;QAAC,MAAM,MAAM;QAAE,MAAM,MAAM;KAAC;AAAE;AAChF,IAAI,aAAa,SAAU,GAAG;IAC1B,OAAO,OAAO,aAAa,MAAM,IAAI,OAAO,GAAG;AACnD;AACA,IAAI,eAAe,SAAU,CAAC,EAAE,CAAC;IAAI,OAAO,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE,IAAI,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,EAAE;AAAE;AAC5E,IAAI,gBAAgB,SAAU,EAAE;IAAI,OAAO,4BAA4B,MAAM,CAAC,IAAI,qDAAqD,MAAM,CAAC,IAAI;AAA8B;AAChL,IAAI,YAAY;AAChB,IAAI,YAAY,EAAE;AACX,SAAS,oBAAoB,KAAK;IACrC,IAAI,qBAAqB,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE,EAAE;IACxC,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;QAAC;QAAG;KAAE;IACvC,IAAI,aAAa,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD;IAC5B,IAAI,KAAK,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,YAAY,CAAC,EAAE;IACvC,IAAI,QAAQ,CAAA,GAAA,6JAAA,CAAA,WAAc,AAAD,EAAE,6KAAA,CAAA,iBAAc,CAAC,CAAC,EAAE;IAC7C,IAAI,YAAY,CAAA,GAAA,6JAAA,CAAA,SAAY,AAAD,EAAE;IAC7B,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;yCAAE;YACZ,UAAU,OAAO,GAAG;QACxB;wCAAG;QAAC;KAAM;IACV,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;yCAAE;YACZ,IAAI,MAAM,KAAK,EAAE;gBACb,SAAS,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC;gBAC1D,IAAI,UAAU,CAAA,GAAA,yIAAA,CAAA,gBAAa,AAAD,EAAE;oBAAC,MAAM,OAAO,CAAC,OAAO;iBAAC,EAAE,CAAC,MAAM,MAAM,IAAI,EAAE,EAAE,GAAG,CAAC,aAAa,MAAM,MAAM,CAAC;gBACxG,QAAQ,OAAO;qDAAC,SAAU,EAAE;wBAAI,OAAO,GAAG,SAAS,CAAC,GAAG,CAAC,uBAAuB,MAAM,CAAC;oBAAM;;gBAC5F;qDAAO;wBACH,SAAS,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,uBAAuB,MAAM,CAAC;wBAC7D,QAAQ,OAAO;6DAAC,SAAU,EAAE;gCAAI,OAAO,GAAG,SAAS,CAAC,MAAM,CAAC,uBAAuB,MAAM,CAAC;4BAAM;;oBACnG;;YACJ;YACA;QACJ;wCAAG;QAAC,MAAM,KAAK;QAAE,MAAM,OAAO,CAAC,OAAO;QAAE,MAAM,MAAM;KAAC;IACrD,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;8DAAE,SAAU,KAAK,EAAE,MAAM;YAC7D,IAAI,AAAC,aAAa,SAAS,MAAM,OAAO,CAAC,MAAM,KAAK,KAAO,MAAM,IAAI,KAAK,WAAW,MAAM,OAAO,EAAG;gBACjG,OAAO,CAAC,UAAU,OAAO,CAAC,cAAc;YAC5C;YACA,IAAI,QAAQ,WAAW;YACvB,IAAI,aAAa,cAAc,OAAO;YACtC,IAAI,SAAS,YAAY,QAAQ,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;YACxE,IAAI,SAAS,YAAY,QAAQ,MAAM,MAAM,GAAG,UAAU,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE;YACxE,IAAI;YACJ,IAAI,SAAS,MAAM,MAAM;YACzB,IAAI,gBAAgB,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU,MAAM;YAChE,8EAA8E;YAC9E,IAAI,aAAa,SAAS,kBAAkB,OAAO,OAAO,IAAI,KAAK,SAAS;gBACxE,OAAO;YACX;YACA,IAAI,+BAA+B,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE,eAAe;YAC1E,IAAI,CAAC,8BAA8B;gBAC/B,OAAO;YACX;YACA,IAAI,8BAA8B;gBAC9B,cAAc;YAClB,OACK;gBACD,cAAc,kBAAkB,MAAM,MAAM;gBAC5C,+BAA+B,CAAA,GAAA,8KAAA,CAAA,0BAAuB,AAAD,EAAE,eAAe;YACtE,qCAAqC;YACzC;YACA,IAAI,CAAC,8BAA8B;gBAC/B,OAAO;YACX;YACA,IAAI,CAAC,WAAW,OAAO,IAAI,oBAAoB,SAAS,CAAC,UAAU,MAAM,GAAG;gBACxE,WAAW,OAAO,GAAG;YACzB;YACA,IAAI,CAAC,aAAa;gBACd,OAAO;YACX;YACA,IAAI,gBAAgB,WAAW,OAAO,IAAI;YAC1C,OAAO,CAAA,GAAA,8KAAA,CAAA,eAAY,AAAD,EAAE,eAAe,QAAQ,OAAO,kBAAkB,MAAM,SAAS,QAAQ;QAC/F;6DAAG,EAAE;IACL,IAAI,gBAAgB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;0DAAE,SAAU,MAAM;YAClD,IAAI,QAAQ;YACZ,IAAI,CAAC,UAAU,MAAM,IAAI,SAAS,CAAC,UAAU,MAAM,GAAG,EAAE,KAAK,OAAO;gBAChE,sBAAsB;gBACtB;YACJ;YACA,IAAI,QAAQ,YAAY,QAAQ,WAAW,SAAS,WAAW;YAC/D,IAAI,cAAc,mBAAmB,OAAO,CAAC,MAAM;kEAAC,SAAU,CAAC;oBAAI,OAAO,EAAE,IAAI,KAAK,MAAM,IAAI,IAAI,CAAC,EAAE,MAAM,KAAK,MAAM,MAAM,IAAI,MAAM,MAAM,KAAK,EAAE,YAAY,KAAK,aAAa,EAAE,KAAK,EAAE;gBAAQ;gEAAE,CAAC,EAAE;YACxM,qCAAqC;YACrC,IAAI,eAAe,YAAY,MAAM,EAAE;gBACnC,IAAI,MAAM,UAAU,EAAE;oBAClB,MAAM,cAAc;gBACxB;gBACA;YACJ;YACA,yBAAyB;YACzB,IAAI,CAAC,aAAa;gBACd,IAAI,aAAa,CAAC,UAAU,OAAO,CAAC,MAAM,IAAI,EAAE,EAC3C,GAAG,CAAC,YACJ,MAAM,CAAC,SACP,MAAM;iFAAC,SAAU,IAAI;wBAAI,OAAO,KAAK,QAAQ,CAAC,MAAM,MAAM;oBAAG;;gBAClE,IAAI,aAAa,WAAW,MAAM,GAAG,IAAI,kBAAkB,OAAO,UAAU,CAAC,EAAE,IAAI,CAAC,UAAU,OAAO,CAAC,WAAW;gBACjH,IAAI,YAAY;oBACZ,IAAI,MAAM,UAAU,EAAE;wBAClB,MAAM,cAAc;oBACxB;gBACJ;YACJ;QACJ;yDAAG,EAAE;IACL,IAAI,eAAe,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;yDAAE,SAAU,IAAI,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM;YACtE,IAAI,QAAQ;gBAAE,MAAM;gBAAM,OAAO;gBAAO,QAAQ;gBAAQ,QAAQ;gBAAQ,cAAc,yBAAyB;YAAQ;YACvH,mBAAmB,OAAO,CAAC,IAAI,CAAC;YAChC;iEAAW;oBACP,mBAAmB,OAAO,GAAG,mBAAmB,OAAO,CAAC,MAAM;yEAAC,SAAU,CAAC;4BAAI,OAAO,MAAM;wBAAO;;gBACtG;gEAAG;QACP;wDAAG,EAAE;IACL,IAAI,mBAAmB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;6DAAE,SAAU,KAAK;YACpD,cAAc,OAAO,GAAG,WAAW;YACnC,WAAW,OAAO,GAAG;QACzB;4DAAG,EAAE;IACL,IAAI,cAAc,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;wDAAE,SAAU,KAAK;YAC/C,aAAa,MAAM,IAAI,EAAE,WAAW,QAAQ,MAAM,MAAM,EAAE,kBAAkB,OAAO,MAAM,OAAO,CAAC,OAAO;QAC5G;uDAAG,EAAE;IACL,IAAI,kBAAkB,CAAA,GAAA,6JAAA,CAAA,cAAiB,AAAD;4DAAE,SAAU,KAAK;YACnD,aAAa,MAAM,IAAI,EAAE,WAAW,QAAQ,MAAM,MAAM,EAAE,kBAAkB,OAAO,MAAM,OAAO,CAAC,OAAO;QAC5G;2DAAG,EAAE;IACL,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;yCAAE;YACZ,UAAU,IAAI,CAAC;YACf,MAAM,YAAY,CAAC;gBACf,iBAAiB;gBACjB,gBAAgB;gBAChB,oBAAoB;YACxB;YACA,SAAS,gBAAgB,CAAC,SAAS,eAAe,kLAAA,CAAA,aAAU;YAC5D,SAAS,gBAAgB,CAAC,aAAa,eAAe,kLAAA,CAAA,aAAU;YAChE,SAAS,gBAAgB,CAAC,cAAc,kBAAkB,kLAAA,CAAA,aAAU;YACpE;iDAAO;oBACH,YAAY,UAAU,MAAM;yDAAC,SAAU,IAAI;4BAAI,OAAO,SAAS;wBAAO;;oBACtE,SAAS,mBAAmB,CAAC,SAAS,eAAe,kLAAA,CAAA,aAAU;oBAC/D,SAAS,mBAAmB,CAAC,aAAa,eAAe,kLAAA,CAAA,aAAU;oBACnE,SAAS,mBAAmB,CAAC,cAAc,kBAAkB,kLAAA,CAAA,aAAU;gBAC3E;;QACJ;wCAAG,EAAE;IACL,IAAI,kBAAkB,MAAM,eAAe,EAAE,QAAQ,MAAM,KAAK;IAChE,OAAQ,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,6JAAA,CAAA,WAAc,EAAE,MACxC,QAAQ,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,OAAO;QAAE,QAAQ,cAAc;IAAI,KAAK,MACpE,kBAAkB,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,kLAAA,CAAA,kBAAe,EAAE;QAAE,YAAY,MAAM,UAAU;QAAE,SAAS,MAAM,OAAO;IAAC,KAAK;AAC3H;AACA,SAAS,yBAAyB,IAAI;IAClC,IAAI,eAAe;IACnB,MAAO,SAAS,KAAM;QAClB,IAAI,gBAAgB,YAAY;YAC5B,eAAe,KAAK,IAAI;YACxB,OAAO,KAAK,IAAI;QACpB;QACA,OAAO,KAAK,UAAU;IAC1B;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3885, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll/dist/es2015/sidecar.js"], "sourcesContent": ["import { exportSidecar } from 'use-sidecar';\nimport { RemoveScrollSideCar } from './SideEffect';\nimport { effectCar } from './medium';\nexport default exportSidecar(effectCar, RemoveScrollSideCar);\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;;;;uCACe,CAAA,GAAA,8JAAA,CAAA,gBAAa,AAAD,EAAE,wKAAA,CAAA,YAAS,EAAE,4KAAA,CAAA,sBAAmB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3901, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-remove-scroll/dist/es2015/Combination.js"], "sourcesContent": ["import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { RemoveScroll } from './UI';\nimport SideCar from './sidecar';\nvar ReactRemoveScroll = React.forwardRef(function (props, ref) { return (React.createElement(RemoveScroll, __assign({}, props, { ref: ref, sideCar: SideCar }))); });\nReactRemoveScroll.classNames = RemoveScroll.classNames;\nexport default ReactRemoveScroll;\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;;;;;AACA,IAAI,oBAAoB,CAAA,GAAA,6JAAA,CAAA,aAAgB,AAAD,EAAE,SAAU,KAAK,EAAE,GAAG;IAAI,OAAQ,CAAA,GAAA,6JAAA,CAAA,gBAAmB,AAAD,EAAE,oKAAA,CAAA,eAAY,EAAE,CAAA,GAAA,yIAAA,CAAA,WAAQ,AAAD,EAAE,CAAC,GAAG,OAAO;QAAE,KAAK;QAAK,SAAS,yKAAA,CAAA,UAAO;IAAC;AAAM;AAClK,kBAAkB,UAAU,GAAG,oKAAA,CAAA,eAAY,CAAC,UAAU;uCACvC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3936, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/get-nonce/dist/es2015/index.js"], "sourcesContent": ["var currentNonce;\nexport var setNonce = function (nonce) {\n    currentNonce = nonce;\n};\nexport var getNonce = function () {\n    if (currentNonce) {\n        return currentNonce;\n    }\n    if (typeof __webpack_nonce__ !== 'undefined') {\n        return __webpack_nonce__;\n    }\n    return undefined;\n};\n"], "names": [], "mappings": ";;;;AAAA,IAAI;AACG,IAAI,WAAW,SAAU,KAAK;IACjC,eAAe;AACnB;AACO,IAAI,WAAW;IAClB,IAAI,cAAc;QACd,OAAO;IACX;IACA,IAAI,OAAO,sBAAsB,aAAa;QAC1C,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 3959, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-style-singleton/dist/es2015/singleton.js"], "sourcesContent": ["import { getNonce } from 'get-nonce';\nfunction makeStyleTag() {\n    if (!document)\n        return null;\n    var tag = document.createElement('style');\n    tag.type = 'text/css';\n    var nonce = getNonce();\n    if (nonce) {\n        tag.setAttribute('nonce', nonce);\n    }\n    return tag;\n}\nfunction injectStyles(tag, css) {\n    // @ts-ignore\n    if (tag.styleSheet) {\n        // @ts-ignore\n        tag.styleSheet.cssText = css;\n    }\n    else {\n        tag.appendChild(document.createTextNode(css));\n    }\n}\nfunction insertStyleTag(tag) {\n    var head = document.head || document.getElementsByTagName('head')[0];\n    head.appendChild(tag);\n}\nexport var stylesheetSingleton = function () {\n    var counter = 0;\n    var stylesheet = null;\n    return {\n        add: function (style) {\n            if (counter == 0) {\n                if ((stylesheet = makeStyleTag())) {\n                    injectStyles(stylesheet, style);\n                    insertStyleTag(stylesheet);\n                }\n            }\n            counter++;\n        },\n        remove: function () {\n            counter--;\n            if (!counter && stylesheet) {\n                stylesheet.parentNode && stylesheet.parentNode.removeChild(stylesheet);\n                stylesheet = null;\n            }\n        },\n    };\n};\n"], "names": [], "mappings": ";;;AAAA;;AACA,SAAS;IACL,IAAI,CAAC,UACD,OAAO;IACX,IAAI,MAAM,SAAS,aAAa,CAAC;IACjC,IAAI,IAAI,GAAG;IACX,IAAI,QAAQ,CAAA,GAAA,0JAAA,CAAA,WAAQ,AAAD;IACnB,IAAI,OAAO;QACP,IAAI,YAAY,CAAC,SAAS;IAC9B;IACA,OAAO;AACX;AACA,SAAS,aAAa,GAAG,EAAE,GAAG;IAC1B,aAAa;IACb,IAAI,IAAI,UAAU,EAAE;QAChB,aAAa;QACb,IAAI,UAAU,CAAC,OAAO,GAAG;IAC7B,OACK;QACD,IAAI,WAAW,CAAC,SAAS,cAAc,CAAC;IAC5C;AACJ;AACA,SAAS,eAAe,GAAG;IACvB,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;IACpE,KAAK,WAAW,CAAC;AACrB;AACO,IAAI,sBAAsB;IAC7B,IAAI,UAAU;IACd,IAAI,aAAa;IACjB,OAAO;QACH,KAAK,SAAU,KAAK;YAChB,IAAI,WAAW,GAAG;gBACd,IAAK,aAAa,gBAAiB;oBAC/B,aAAa,YAAY;oBACzB,eAAe;gBACnB;YACJ;YACA;QACJ;QACA,QAAQ;YACJ;YACA,IAAI,CAAC,WAAW,YAAY;gBACxB,WAAW,UAAU,IAAI,WAAW,UAAU,CAAC,WAAW,CAAC;gBAC3D,aAAa;YACjB;QACJ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4015, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-style-singleton/dist/es2015/hook.js"], "sourcesContent": ["import * as React from 'react';\nimport { stylesheetSingleton } from './singleton';\n/**\n * creates a hook to control style singleton\n * @see {@link styleSingleton} for a safer component version\n * @example\n * ```tsx\n * const useStyle = styleHookSingleton();\n * ///\n * useStyle('body { overflow: hidden}');\n */\nexport var styleHookSingleton = function () {\n    var sheet = stylesheetSingleton();\n    return function (styles, isDynamic) {\n        React.useEffect(function () {\n            sheet.add(styles);\n            return function () {\n                sheet.remove();\n            };\n        }, [styles && isDynamic]);\n    };\n};\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAUO,IAAI,qBAAqB;IAC5B,IAAI,QAAQ,CAAA,GAAA,6KAAA,CAAA,sBAAmB,AAAD;IAC9B,OAAO,SAAU,MAAM,EAAE,SAAS;QAC9B,CAAA,GAAA,6JAAA,CAAA,YAAe,AAAD;4CAAE;gBACZ,MAAM,GAAG,CAAC;gBACV;oDAAO;wBACH,MAAM,MAAM;oBAChB;;YACJ;2CAAG;YAAC,UAAU;SAAU;IAC5B;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4045, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-style-singleton/dist/es2015/component.js"], "sourcesContent": ["import { styleHook<PERSON>ingleton } from './hook';\n/**\n * create a Component to add styles on demand\n * - styles are added when first instance is mounted\n * - styles are removed when the last instance is unmounted\n * - changing styles in runtime does nothing unless dynamic is set. But with multiple components that can lead to the undefined behavior\n */\nexport var styleSingleton = function () {\n    var useStyle = styleHookSingleton();\n    var Sheet = function (_a) {\n        var styles = _a.styles, dynamic = _a.dynamic;\n        useStyle(styles, dynamic);\n        return null;\n    };\n    return Sheet;\n};\n"], "names": [], "mappings": ";;;AAAA;;AAOO,IAAI,iBAAiB;IACxB,IAAI,WAAW,CAAA,GAAA,wKAAA,CAAA,qBAAkB,AAAD;IAChC,IAAI,QAAQ,SAAU,EAAE;QACpB,IAAI,SAAS,GAAG,MAAM,EAAE,UAAU,GAAG,OAAO;QAC5C,SAAS,QAAQ;QACjB,OAAO;IACX;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4065, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/react-style-singleton/dist/es2015/index.js"], "sourcesContent": ["export { styleSingleton } from './component';\nexport { stylesheetSingleton } from './singleton';\nexport { styleHookSingleton } from './hook';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 4089, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/input-otp/src/input.tsx", "file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/input-otp/src/sync-timeouts.ts", "file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/input-otp/src/use-previous.ts", "file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/input-otp/src/use-pwm-badge.tsx", "file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/input-otp/src/regexp.ts"], "sourcesContent": ["'use client'\n\nimport * as React from 'react'\n\nimport { syncTimeouts } from './sync-timeouts'\nimport { OTPInputProps, RenderProps } from './types'\nimport { usePrevious } from './use-previous'\nimport { usePasswordManagerBadge } from './use-pwm-badge'\n\nexport const OTPInputContext = React.createContext<RenderProps>(\n  {} as RenderProps,\n)\n\nexport const OTPInput = React.forwardRef<HTMLInputElement, OTPInputProps>(\n  (\n    {\n      value: uncheckedValue,\n      onChange: uncheckedOnChange,\n      maxLength,\n      textAlign = 'left',\n      pattern,\n      placeholder,\n      inputMode = 'numeric',\n      onComplete,\n      pushPasswordManagerStrategy = 'increase-width',\n      pasteTransformer,\n      containerClassName,\n      noScriptCSSFallback = NOSCRIPT_CSS_FALLBACK,\n\n      render,\n      children,\n\n      ...props\n    },\n    ref,\n  ) => {\n    // Only used when `value` state is not provided\n    const [internalValue, setInternalValue] = React.useState(\n      typeof props.defaultValue === 'string' ? props.defaultValue : '',\n    )\n\n    // Definitions\n    const value = uncheckedValue ?? internalValue\n    const previousValue = usePrevious(value)\n    const onChange = React.useCallback(\n      (newValue: string) => {\n        uncheckedOnChange?.(newValue)\n        setInternalValue(newValue)\n      },\n      [uncheckedOnChange],\n    )\n    const regexp = React.useMemo(\n      () =>\n        pattern\n          ? typeof pattern === 'string'\n            ? new RegExp(pattern)\n            : pattern\n          : null,\n      [pattern],\n    )\n\n    /** useRef */\n    const inputRef = React.useRef<HTMLInputElement>(null)\n    const containerRef = React.useRef<HTMLDivElement>(null)\n    const initialLoadRef = React.useRef({\n      value,\n      onChange,\n      isIOS:\n        typeof window !== 'undefined' &&\n        window?.CSS?.supports?.('-webkit-touch-callout', 'none'),\n    })\n    const inputMetadataRef = React.useRef<{\n      prev: [number | null, number | null, 'none' | 'forward' | 'backward']\n    }>({\n      prev: [\n        inputRef.current?.selectionStart,\n        inputRef.current?.selectionEnd,\n        inputRef.current?.selectionDirection,\n      ],\n    })\n    React.useImperativeHandle(ref, () => inputRef.current, [])\n    React.useEffect(() => {\n      const input = inputRef.current\n      const container = containerRef.current\n\n      if (!input || !container) {\n        return\n      }\n\n      // Sync input value\n      if (initialLoadRef.current.value !== input.value) {\n        initialLoadRef.current.onChange(input.value)\n      }\n\n      // Previous selection\n      inputMetadataRef.current.prev = [\n        input.selectionStart,\n        input.selectionEnd,\n        input.selectionDirection,\n      ]\n      function onDocumentSelectionChange() {\n        if (document.activeElement !== input) {\n          setMirrorSelectionStart(null)\n          setMirrorSelectionEnd(null)\n          return\n        }\n\n        // Aliases\n        const _s = input.selectionStart\n        const _e = input.selectionEnd\n        const _dir = input.selectionDirection\n        const _ml = input.maxLength\n        const _val = input.value\n        const _prev = inputMetadataRef.current.prev\n\n        // Algorithm\n        let start = -1\n        let end = -1\n        let direction: 'forward' | 'backward' | 'none' = undefined\n        if (_val.length !== 0 && _s !== null && _e !== null) {\n          const isSingleCaret = _s === _e\n          const isInsertMode = _s === _val.length && _val.length < _ml\n\n          if (isSingleCaret && !isInsertMode) {\n            const c = _s\n            if (c === 0) {\n              start = 0\n              end = 1\n              direction = 'forward'\n            } else if (c === _ml) {\n              start = c - 1\n              end = c\n              direction = 'backward'\n            } else if (_ml > 1 && _val.length > 1) {\n              let offset = 0\n              if (_prev[0] !== null && _prev[1] !== null) {\n                direction = c < _prev[1] ? 'backward' : 'forward'\n                const wasPreviouslyInserting =\n                  _prev[0] === _prev[1] && _prev[0] < _ml\n                if (direction === 'backward' && !wasPreviouslyInserting) {\n                  offset = -1\n                }\n              }\n\n              start = offset + c\n              end = offset + c + 1\n            }\n          }\n\n          if (start !== -1 && end !== -1 && start !== end) {\n            inputRef.current.setSelectionRange(start, end, direction)\n          }\n        }\n\n        // Finally, update the state\n        const s = start !== -1 ? start : _s\n        const e = end !== -1 ? end : _e\n        const dir = direction ?? _dir\n        setMirrorSelectionStart(s)\n        setMirrorSelectionEnd(e)\n        // Store the previous selection value\n        inputMetadataRef.current.prev = [s, e, dir]\n      }\n      document.addEventListener('selectionchange', onDocumentSelectionChange, {\n        capture: true,\n      })\n\n      // Set initial mirror state\n      onDocumentSelectionChange()\n      document.activeElement === input && setIsFocused(true)\n\n      // Apply needed styles\n      if (!document.getElementById('input-otp-style')) {\n        const styleEl = document.createElement('style')\n        styleEl.id = 'input-otp-style'\n        document.head.appendChild(styleEl)\n\n        if (styleEl.sheet) {\n          const autofillStyles =\n            'background: transparent !important; color: transparent !important; border-color: transparent !important; opacity: 0 !important; box-shadow: none !important; -webkit-box-shadow: none !important; -webkit-text-fill-color: transparent !important;'\n\n          safeInsertRule(\n            styleEl.sheet,\n            '[data-input-otp]::selection { background: transparent !important; color: transparent !important; }',\n          )\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp]:autofill { ${autofillStyles} }`,\n          )\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp]:-webkit-autofill { ${autofillStyles} }`,\n          )\n          // iOS\n          safeInsertRule(\n            styleEl.sheet,\n            `@supports (-webkit-touch-callout: none) { [data-input-otp] { letter-spacing: -.6em !important; font-weight: 100 !important; font-stretch: ultra-condensed; font-optical-sizing: none !important; left: -1px !important; right: 1px !important; } }`,\n          )\n          // PWM badges\n          safeInsertRule(\n            styleEl.sheet,\n            `[data-input-otp] + * { pointer-events: all !important; }`,\n          )\n        }\n      }\n      // Track root height\n      const updateRootHeight = () => {\n        if (container) {\n          container.style.setProperty(\n            '--root-height',\n            `${input.clientHeight}px`,\n          )\n        }\n      }\n      updateRootHeight()\n      const resizeObserver = new ResizeObserver(updateRootHeight)\n      resizeObserver.observe(input)\n\n      return () => {\n        document.removeEventListener(\n          'selectionchange',\n          onDocumentSelectionChange,\n          { capture: true },\n        )\n        resizeObserver.disconnect()\n      }\n    }, [])\n\n    /** Mirrors for UI rendering purpose only */\n    const [isHoveringInput, setIsHoveringInput] = React.useState(false)\n    const [isFocused, setIsFocused] = React.useState(false)\n    const [mirrorSelectionStart, setMirrorSelectionStart] = React.useState<\n      number | null\n    >(null)\n    const [mirrorSelectionEnd, setMirrorSelectionEnd] = React.useState<\n      number | null\n    >(null)\n\n    /** Effects */\n    React.useEffect(() => {\n      syncTimeouts(() => {\n        // Forcefully remove :autofill state\n        inputRef.current?.dispatchEvent(new Event('input'))\n\n        // Update the selection state\n        const s = inputRef.current?.selectionStart\n        const e = inputRef.current?.selectionEnd\n        const dir = inputRef.current?.selectionDirection\n        if (s !== null && e !== null) {\n          setMirrorSelectionStart(s)\n          setMirrorSelectionEnd(e)\n          inputMetadataRef.current.prev = [s, e, dir]\n        }\n      })\n    }, [value, isFocused])\n\n    React.useEffect(() => {\n      if (previousValue === undefined) {\n        return\n      }\n\n      if (\n        value !== previousValue &&\n        previousValue.length < maxLength &&\n        value.length === maxLength\n      ) {\n        onComplete?.(value)\n      }\n    }, [maxLength, onComplete, previousValue, value])\n\n    const pwmb = usePasswordManagerBadge({\n      containerRef,\n      inputRef,\n      pushPasswordManagerStrategy,\n      isFocused,\n    })\n\n    /** Event handlers */\n    const _changeListener = React.useCallback(\n      (e: React.ChangeEvent<HTMLInputElement>) => {\n        const newValue = e.currentTarget.value.slice(0, maxLength)\n        if (newValue.length > 0 && regexp && !regexp.test(newValue)) {\n          e.preventDefault()\n          return\n        }\n        const maybeHasDeleted =\n          typeof previousValue === 'string' &&\n          newValue.length < previousValue.length\n        if (maybeHasDeleted) {\n          // Since cutting/deleting text doesn't trigger\n          // selectionchange event, we'll have to dispatch it manually.\n          // NOTE: The following line also triggers when cmd+A then pasting\n          // a value with smaller length, which is not ideal for performance.\n          document.dispatchEvent(new Event('selectionchange'))\n        }\n        onChange(newValue)\n      },\n      [maxLength, onChange, previousValue, regexp],\n    )\n    const _focusListener = React.useCallback(() => {\n      if (inputRef.current) {\n        const start = Math.min(inputRef.current.value.length, maxLength - 1)\n        const end = inputRef.current.value.length\n        inputRef.current?.setSelectionRange(start, end)\n        setMirrorSelectionStart(start)\n        setMirrorSelectionEnd(end)\n      }\n      setIsFocused(true)\n    }, [maxLength])\n    // Fix iOS pasting\n    const _pasteListener = React.useCallback(\n      (e: React.ClipboardEvent<HTMLInputElement>) => {\n        const input = inputRef.current\n        if (!pasteTransformer && (!initialLoadRef.current.isIOS || !e.clipboardData || !input)) {\n          return\n        }\n        \n        const _content = e.clipboardData.getData('text/plain')\n        const content = pasteTransformer\n          ? pasteTransformer(_content)\n          : _content\n        e.preventDefault()\n\n        const start = inputRef.current?.selectionStart\n        const end = inputRef.current?.selectionEnd\n\n        const isReplacing = start !== end\n\n        const newValueUncapped = isReplacing\n          ? value.slice(0, start) + content + value.slice(end) // Replacing\n          : value.slice(0, start) + content + value.slice(start) // Inserting\n        const newValue = newValueUncapped.slice(0, maxLength)\n\n        if (newValue.length > 0 && regexp && !regexp.test(newValue)) {\n          return\n        }\n\n        input.value = newValue\n        onChange(newValue)\n\n        const _start = Math.min(newValue.length, maxLength - 1)\n        const _end = newValue.length\n\n        input.setSelectionRange(_start, _end)\n        setMirrorSelectionStart(_start)\n        setMirrorSelectionEnd(_end)\n      },\n      [maxLength, onChange, regexp, value],\n    )\n\n    /** Styles */\n    const rootStyle = React.useMemo<React.CSSProperties>(\n      () => ({\n        position: 'relative',\n        cursor: props.disabled ? 'default' : 'text',\n        userSelect: 'none',\n        WebkitUserSelect: 'none',\n        pointerEvents: 'none',\n      }),\n      [props.disabled],\n    )\n\n    const inputStyle = React.useMemo<React.CSSProperties>(\n      () => ({\n        position: 'absolute',\n        inset: 0,\n        width: pwmb.willPushPWMBadge\n          ? `calc(100% + ${pwmb.PWM_BADGE_SPACE_WIDTH})`\n          : '100%',\n        clipPath: pwmb.willPushPWMBadge\n          ? `inset(0 ${pwmb.PWM_BADGE_SPACE_WIDTH} 0 0)`\n          : undefined,\n        height: '100%',\n        display: 'flex',\n        textAlign,\n        opacity: '1', // Mandatory for iOS hold-paste\n        color: 'transparent',\n        pointerEvents: 'all',\n        background: 'transparent',\n        caretColor: 'transparent',\n        border: '0 solid transparent',\n        outline: '0 solid transparent',\n        boxShadow: 'none',\n        lineHeight: '1',\n        letterSpacing: '-.5em',\n        fontSize: 'var(--root-height)',\n        fontFamily: 'monospace',\n        fontVariantNumeric: 'tabular-nums',\n        // letterSpacing: '-1em',\n        // transform: 'scale(1.5)',\n        // paddingRight: '100%',\n        // paddingBottom: '100%',\n        // debugging purposes\n        // inset: undefined,\n        // position: undefined,\n        // color: 'black',\n        // background: 'white',\n        // opacity: '1',\n        // caretColor: 'black',\n        // padding: '0',\n        // letterSpacing: 'unset',\n        // fontSize: 'unset',\n        // paddingInline: '.5rem',\n      }),\n      [pwmb.PWM_BADGE_SPACE_WIDTH, pwmb.willPushPWMBadge, textAlign],\n    )\n\n    /** Rendering */\n    const renderedInput = React.useMemo(\n      () => (\n        <input\n          autoComplete={props.autoComplete || 'one-time-code'}\n          {...props}\n          data-input-otp\n          data-input-otp-placeholder-shown={value.length === 0 || undefined}\n          data-input-otp-mss={mirrorSelectionStart}\n          data-input-otp-mse={mirrorSelectionEnd}\n          inputMode={inputMode}\n          pattern={regexp?.source}\n          aria-placeholder={placeholder}\n          style={inputStyle}\n          maxLength={maxLength}\n          value={value}\n          ref={inputRef}\n          onPaste={e => {\n            _pasteListener(e)\n            props.onPaste?.(e)\n          }}\n          onChange={_changeListener}\n          onMouseOver={e => {\n            setIsHoveringInput(true)\n            props.onMouseOver?.(e)\n          }}\n          onMouseLeave={e => {\n            setIsHoveringInput(false)\n            props.onMouseLeave?.(e)\n          }}\n          onFocus={e => {\n            _focusListener()\n            props.onFocus?.(e)\n          }}\n          onBlur={e => {\n            setIsFocused(false)\n            props.onBlur?.(e)\n          }}\n        />\n      ),\n      [\n        _changeListener,\n        _focusListener,\n        _pasteListener,\n        inputMode,\n        inputStyle,\n        maxLength,\n        mirrorSelectionEnd,\n        mirrorSelectionStart,\n        props,\n        regexp?.source,\n        value,\n      ],\n    )\n\n    const contextValue = React.useMemo<RenderProps>(() => {\n      return {\n        slots: Array.from({ length: maxLength }).map((_, slotIdx) => {\n          const isActive =\n            isFocused &&\n            mirrorSelectionStart !== null &&\n            mirrorSelectionEnd !== null &&\n            ((mirrorSelectionStart === mirrorSelectionEnd &&\n              slotIdx === mirrorSelectionStart) ||\n              (slotIdx >= mirrorSelectionStart && slotIdx < mirrorSelectionEnd))\n\n          const char = value[slotIdx] !== undefined ? value[slotIdx] : null\n          const placeholderChar = value[0] !== undefined ? null : placeholder?.[slotIdx] ?? null\n\n          return {\n            char,\n            placeholderChar,\n            isActive,\n            hasFakeCaret: isActive && char === null,\n          }\n        }),\n        isFocused,\n        isHovering: !props.disabled && isHoveringInput,\n      }\n    }, [\n      isFocused,\n      isHoveringInput,\n      maxLength,\n      mirrorSelectionEnd,\n      mirrorSelectionStart,\n      props.disabled,\n      value,\n    ])\n\n    const renderedChildren = React.useMemo(() => {\n      if (render) {\n        return render(contextValue)\n      }\n      return (\n        <OTPInputContext.Provider value={contextValue}>\n          {children}\n        </OTPInputContext.Provider>\n      )\n    }, [children, contextValue, render])\n\n    return (\n      <>\n        {noScriptCSSFallback !== null && (\n          <noscript>\n            <style>{noScriptCSSFallback}</style>\n          </noscript>\n        )}\n\n        <div\n          ref={containerRef}\n          data-input-otp-container\n          style={rootStyle}\n          className={containerClassName}\n        >\n          {renderedChildren}\n\n          <div\n            style={{\n              position: 'absolute',\n              inset: 0,\n              pointerEvents: 'none',\n            }}\n          >\n            {renderedInput}\n          </div>\n        </div>\n      </>\n    )\n  },\n)\nOTPInput.displayName = 'Input'\n\nfunction safeInsertRule(sheet: CSSStyleSheet, rule: string) {\n  try {\n    sheet.insertRule(rule)\n  } catch {\n    console.error('input-otp could not insert CSS rule:', rule)\n  }\n}\n\n// Decided to go with <noscript>\n// instead of `scripting` CSS media query\n// because it's a fallback for initial page load\n// and the <script> tag won't be loaded\n// unless the user has JS disabled.\nconst NOSCRIPT_CSS_FALLBACK = `\n[data-input-otp] {\n  --nojs-bg: white !important;\n  --nojs-fg: black !important;\n\n  background-color: var(--nojs-bg) !important;\n  color: var(--nojs-fg) !important;\n  caret-color: var(--nojs-fg) !important;\n  letter-spacing: .25em !important;\n  text-align: center !important;\n  border: 1px solid var(--nojs-fg) !important;\n  border-radius: 4px !important;\n  width: 100% !important;\n}\n@media (prefers-color-scheme: dark) {\n  [data-input-otp] {\n    --nojs-bg: black !important;\n    --nojs-fg: white !important;\n  }\n}`\n", "export function syncTimeouts(cb: (...args: any[]) => unknown): number[] {\n  const t1 = setTimeout(cb, 0) // For faster machines\n  const t2 = setTimeout(cb, 1_0)\n  const t3 = setTimeout(cb, 5_0)\n  return [t1, t2, t3]\n}\n", "import * as React from 'react'\n\nexport function usePrevious<T>(value: T) {\n  const ref = React.useRef<T>()\n  React.useEffect(() => {\n    ref.current = value\n  })\n  return ref.current\n}\n", "import * as React from 'react'\nimport { OTPInputProps } from './types'\n\nconst PWM_BADGE_MARGIN_RIGHT = 18\nconst PWM_BADGE_SPACE_WIDTH_PX = 40\nconst PWM_BADGE_SPACE_WIDTH = `${PWM_BADGE_SPACE_WIDTH_PX}px` as const\n\nconst PASSWORD_MANAGERS_SELECTORS = [\n  '[data-lastpass-icon-root]', // LastPass\n  'com-1password-button', // 1Password\n  '[data-dashlanecreated]', // Dashlane\n  '[style$=\"2147483647 !important;\"]', // Bitwarden\n].join(',')\n\nexport function usePasswordManagerBadge({\n  containerRef,\n  inputRef,\n  pushPasswordManagerStrategy,\n  isFocused,\n}: {\n  containerRef: React.RefObject<HTMLDivElement>\n  inputRef: React.RefObject<HTMLInputElement>\n  pushPasswordManagerStrategy: OTPInputProps['pushPasswordManagerStrategy']\n  isFocused: boolean\n}) {\n  /** Password managers have a badge\n   *  and I'll use this state to push them\n   *  outside the input */\n  const [hasPWMBadge, setHasPWMBadge] = React.useState(false)\n  const [hasPWMBadgeSpace, setHasPWMBadgeSpace] = React.useState(false)\n  const [done, setDone] = React.useState(false)\n\n  const willPushPWMBadge = React.useMemo(() => {\n    if (pushPasswordManagerStrategy === 'none') {\n      return false\n    }\n\n    const increaseWidthCase =\n      (pushPasswordManagerStrategy === 'increase-width' ||\n        // TODO: remove 'experimental-no-flickering' support in 2.0.0\n        pushPasswordManagerStrategy === 'experimental-no-flickering') &&\n      hasPWMBadge &&\n      hasPWMBadgeSpace\n\n    return increaseWidthCase\n  }, [hasPWMBadge, hasPWMBadgeSpace, pushPasswordManagerStrategy])\n\n  const trackPWMBadge = React.useCallback(() => {\n    const container = containerRef.current\n    const input = inputRef.current\n    if (\n      !container ||\n      !input ||\n      done ||\n      pushPasswordManagerStrategy === 'none'\n    ) {\n      return\n    }\n\n    const elementToCompare = container\n\n    // Get the top right-center point of the container.\n    // That is usually where most password managers place their badge.\n    const rightCornerX =\n      elementToCompare.getBoundingClientRect().left +\n      elementToCompare.offsetWidth\n    const centereredY =\n      elementToCompare.getBoundingClientRect().top +\n      elementToCompare.offsetHeight / 2\n    const x = rightCornerX - PWM_BADGE_MARGIN_RIGHT\n    const y = centereredY\n\n    // Do an extra search to check for famous password managers\n    const pmws = document.querySelectorAll(PASSWORD_MANAGERS_SELECTORS)\n\n    // If no password manager is automatically detect,\n    // we'll try to dispatch document.elementFromPoint\n    // to identify badges\n    if (pmws.length === 0) {\n      const maybeBadgeEl = document.elementFromPoint(x, y)\n\n      // If the found element is the input itself,\n      // then we assume it's not a password manager badge.\n      // We are not sure. Most times that means there isn't a badge.\n      if (maybeBadgeEl === container) {\n        return\n      }\n    }\n\n    setHasPWMBadge(true)\n    setDone(true)\n  }, [containerRef, inputRef, done, pushPasswordManagerStrategy])\n\n  React.useEffect(() => {\n    const container = containerRef.current\n    if (!container || pushPasswordManagerStrategy === 'none') {\n      return\n    }\n\n    // Check if the PWM area is 100% visible\n    function checkHasSpace() {\n      const viewportWidth = window.innerWidth\n      const distanceToRightEdge =\n        viewportWidth - container.getBoundingClientRect().right\n      setHasPWMBadgeSpace(distanceToRightEdge >= PWM_BADGE_SPACE_WIDTH_PX)\n    }\n\n    checkHasSpace()\n    const interval = setInterval(checkHasSpace, 1000)\n\n    return () => {\n      clearInterval(interval)\n    }\n  }, [containerRef, pushPasswordManagerStrategy])\n\n  React.useEffect(() => {\n    const _isFocused = isFocused || document.activeElement === inputRef.current\n\n    if (pushPasswordManagerStrategy === 'none' || !_isFocused) {\n      return\n    }\n    const t1 = setTimeout(trackPWMBadge, 0)\n    const t2 = setTimeout(trackPWMBadge, 2000)\n    const t3 = setTimeout(trackPWMBadge, 5000)\n    const t4 = setTimeout(() => {\n      setDone(true)\n    }, 6000)\n    return () => {\n      clearTimeout(t1)\n      clearTimeout(t2)\n      clearTimeout(t3)\n      clearTimeout(t4)\n    }\n  }, [inputRef, isFocused, pushPasswordManagerStrategy, trackPWMBadge])\n\n  return { hasPWMBadge, willPushPWMBadge, PWM_BADGE_SPACE_WIDTH }\n}\n", "export const REGEXP_ONLY_DIGITS = '^\\\\d+$'\nexport const REGEXP_ONLY_CHARS = '^[a-zA-Z]+$'\nexport const REGEXP_ONLY_DIGITS_AND_CHARS = '^[a-zA-Z0-9]+$'\n"], "names": ["React", "syncTimeouts", "cb", "t1", "t2", "t3", "React", "usePrevious", "value", "ref", "React", "PWM_BADGE_MARGIN_RIGHT", "PWM_BADGE_SPACE_WIDTH_PX", "PWM_BADGE_SPACE_WIDTH", "PASSWORD_MANAGERS_SELECTORS", "usePasswordManagerBadge", "containerRef", "inputRef", "pushPasswordManagerStrategy", "isFocused", "hasPWMBadge", "setHasPWMBadge", "hasPWMBadgeSpace", "setHasPWMBadgeSpace", "done", "setDone", "willPushPWMBadge", "trackPWMBadge", "container", "input", "elementToCompare", "rightCornerX", "centereredY", "x", "y", "checkHasSpace", "distanceToRightEdge", "interval", "_isFocused", "t1", "t2", "t3", "t4", "OTPInputContext", "OTPInput", "_a", "ref", "_b", "uncheckedValue", "uncheckedOnChange", "max<PERSON><PERSON><PERSON>", "textAlign", "pattern", "placeholder", "inputMode", "onComplete", "pushPasswordManagerStrategy", "pasteTransformer", "containerClassName", "noScriptCSSFallback", "NOSCRIPT_CSS_FALLBACK", "render", "children", "props", "__objRest", "_c", "_d", "_e", "internalValue", "setInternalValue", "value", "previousValue", "usePrevious", "onChange", "newValue", "regexp", "inputRef", "containerRef", "initialLoadRef", "inputMetadataRef", "input", "container", "onDocumentSelectionChange", "setMirrorSelectionStart", "setMirrorSelectionEnd", "_s", "_dir", "_ml", "_val", "_prev", "start", "end", "direction", "isSingleCaret", "isInsertMode", "c", "offset", "wasPreviouslyInserting", "s", "e", "dir", "setIsFocused", "styleEl", "autofillStyles", "safeInsertRule", "updateRootHeight", "resizeObserver", "isHoveringInput", "setIsHoveringInput", "isFocused", "mirrorSelectionStart", "mirrorSelectionEnd", "syncTimeouts", "pwmb", "usePasswordManagerBadge", "_changeListener", "_focusListener", "_pasteListener", "_content", "content", "_start", "_end", "rootStyle", "inputStyle", "renderedInput", "__spreadProps", "__spreadValues", "contextValue", "_", "slotIdx", "isActive", "char", "placeholder<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "sheet", "rule", "REGEXP_ONLY_DIGITS", "REGEXP_ONLY_CHARS", "REGEXP_ONLY_DIGITS_AND_CHARS"], "mappings": ";;;;;;;AAEA,UAAYA,MAAW;;;;;;;;;;;;;;;;;;;;;;ACFhB,SAASC,GAAaC,CAAAA,CAA2C;IACtE,IAAMC,IAAK,WAAWD,GAAI,CAAC,GACrBE,IAAK,WAAWF,GAAI,EAAG,GACvBG,IAAK,WAAWH,GAAI,EAAG;IAC7B,OAAO;QAACC;QAAIC;QAAIC,CAAE;;AACpB,CCLA,UAAYC,MAAW;;AAEhB,SAASC,GAAeC,CAAAA,CAAU;IACvC,IAAMC,qKAAY,UAAA,CAAU;IAC5B,yKAAM,YAAA;wBAAU,IAAM;YACpBA,EAAI,OAAA,GAAUD;QAChB,CAAC;wBACMC,EAAI,OACb,CCRA,UAAYC,MAAW;;;AAGvB,IAAMC,KAAyB,IACzBC,KAA2B,IAC3BC,KAAwB,GAAGD,EAAwB,CAAA,EAAA,CAAA,EAEnDE,KAA8B;IAClC;IACA;IACA;IACA,mCACF;CAAA,CAAE,IAAA,CAAK,GAAG;AAEH,SAASC,GAAwB,EACtC,cAAAC,CAAAA,EACA,UAAAC,CAAAA,EACA,6BAAAC,CAAAA,EACA,WAAAC,CACF,EAAA,CAKG;IAID,IAAM,CAACC,GAAaC,CAAc,CAAA,qKAAU,WAAA,EAAS,CAAA,CAAK,GACpD,CAACC,GAAkBC,CAAmB,CAAA,OAAU,yKAAA,EAAS,CAAA,CAAK,GAC9D,CAACC,GAAMC,CAAO,CAAA,qKAAU,WAAA,EAAS,CAAA,CAAK,GAEtCC,sKAAyB,UAAA;yBAAQ,IACjCR,MAAgC,SAC3B,CAAA,IAAA,CAINA,MAAgC,oBAE/BA,MAAgC,4BAAA,KAClCE,KACAE;wBAGD;QAACF;QAAaE;QAAkBJ,CAA2B;KAAC,GAEzDS,sKAAsB,cAAA;6BAAY,IAAM;YAC5C,IAAMC,IAAYZ,EAAa,OAAA,EACzBa,IAAQZ,EAAS,OAAA;YACvB,IACE,CAACW,KACD,CAACC,KACDL,KACAN,MAAgC,QAEhC;YAGF,IAAMY,IAAmBF,GAInBG,IACJD,EAAiB,qBAAA,CAAsB,EAAE,IAAA,GACzCA,EAAiB,WAAA,EACbE,IACJF,EAAiB,qBAAA,CAAsB,EAAE,GAAA,GACzCA,EAAiB,YAAA,GAAe,GAC5BG,IAAIF,IAAepB,IACnBuB,IAAIF;YAGG,SAAS,gBAAA,CAAiBlB,EAA2B,EAKzD,MAAA,KAAW,KACG,SAAS,gBAAA,CAAiBmB,GAAGC,CAAC,MAK9BN,KAAAA,CAKvBP,EAAe,CAAA,CAAI,GACnBI,EAAQ,CAAA,CAAI,CAAA;QACd;4BAAG;QAACT;QAAcC;QAAUO;QAAMN,CAA2B;KAAC;IAE9D,yKAAM,YAAA;wBAAU,IAAM;YACpB,IAAMU,IAAYZ,EAAa,OAAA;YAC/B,IAAI,CAACY,KAAaV,MAAgC,QAChD;YAIF,SAASiB,GAAgB;gBAEvB,IAAMC,IADgB,OAAO,UAAA,GAEXR,EAAU,qBAAA,CAAsB,EAAE,KAAA;gBACpDL,EAAoBa,KAAuBxB,EAAwB;YACrE;YAEAuB,EAAc;YACd,IAAME,IAAW,YAAYF,GAAe,GAAI;YAEhD;gCAAO,IAAM;oBACX,cAAcE,CAAQ;gBACxB;;QACF;uBAAG;QAACrB;QAAcE,CAA2B;KAAC,GAExC,8KAAA;wBAAU,IAAM;YACpB,IAAMoB,IAAanB,KAAa,SAAS,aAAA,KAAkBF,EAAS,OAAA;YAEpE,IAAIC,MAAgC,UAAU,CAACoB,GAC7C;YAEF,IAAMC,IAAK,WAAWZ,GAAe,CAAC,GAChCa,IAAK,WAAWb,GAAe,GAAI,GACnCc,IAAK,WAAWd,GAAe,GAAI,GACnCe,IAAK;kCAAW,IAAM;oBAC1BjB,EAAQ,CAAA,CAAI;gBACd;iCAAG,GAAI;YACP;gCAAO,IAAM;oBACX,aAAac,CAAE,GACf,aAAaC,CAAE,GACf,aAAaC,CAAE,GACf,aAAaC,CAAE;gBACjB;;QACF;uBAAG;QAACzB;QAAUE;QAAWD;QAA6BS,CAAa;KAAC,GAE7D;QAAE,aAAAP;QAAa,kBAAAM;QAAkB,uBAAAb;IAAsB;AAChE;AH/HO,IAAM8B,uKAAwB,gBAAA,EACnC,CAAC,CACH,GAEaC,SAAiB,2KAAA,EAC5B,CACEC,GAmBAC,IACG;IApBH,IAAAC,IAAAF,GACE,EAAA,OAAOG,CAAAA,EACP,UAAUC,CAAAA,EACV,WAAAC,CAAAA,EACA,WAAAC,IAAY,MAAA,EACZ,SAAAC,CAAAA,EACA,aAAAC,CAAAA,EACA,WAAAC,IAAY,SAAA,EACZ,YAAAC,CAAAA,EACA,6BAAAC,IAA8B,gBAAA,EAC9B,kBAAAC,CAAAA,EACA,oBAAAC,CAAAA,EACA,qBAAAC,IAAsBC,EAAAA,EAEtB,QAAAC,CAAAA,EACA,UAAAC,CA9BN,EAAA,GAeIf,GAiBKgB,IAAAC,GAjBLjB,GAiBK;QAhBH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QAEA;QACA;KAAA;IA9BN,IAAAF,GAAAE,IAAAkB,IAAAC,IAAAC;IAqCI,IAAM,CAACC,GAAeC,EAAgB,CAAA,qKAAU,WAAA,EAC9C,OAAON,EAAM,YAAA,IAAiB,WAAWA,EAAM,YAAA,GAAe,EAChE,GAGMO,IAAQtB,KAAA,OAAAA,IAAkBoB,GAC1BG,IAAgBC,GAAYF,CAAK,GACjCG,sKAAiB,cAAA;8BACpBC,GAAqB;YACpBzB,KAAA,QAAAA,EAAoByB,IACpBL,GAAiBK,CAAQ;QAC3B;4BACA;QAACzB,CAAiB;KACpB,GACM0B,sKAAe,UAAA;yBACnB,IACEvB,IACI,OAAOA,KAAY,WACjB,IAAI,OAAOA,CAAO,IAClBA,IACF;wBACN;QAACA,CAAO;KACV,GAGMwB,sKAAiB,SAAA,EAAyB,IAAI,GAC9CC,sKAAqB,SAAA,EAAuB,IAAI,GAChDC,IAAuB,2KAAA,EAAO;QAClC,OAAAR;QACA,UAAAG;QACA,OACE,OAAO,UAAW,eAAA,CAAA,CAClB1B,KAAAA,CAAAF,IAAA,UAAA,OAAA,KAAA,IAAA,OAAQ,GAAA,KAAR,OAAA,KAAA,IAAAA,EAAa,QAAA,KAAb,OAAA,KAAA,IAAAE,GAAA,IAAA,CAAAF,GAAwB,yBAAyB,OAAA;IACrD,CAAC,GACKkC,sKAAyB,SAAA,EAE5B;QACD,MAAM;YAAA,CACJd,KAAAW,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAX,GAAkB,cAAA;YAAA,CAClBC,KAAAU,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAV,GAAkB,YAAA;YAAA,CAClBC,KAAAS,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAT,GAAkB,kBACpB;;IACF,CAAC;IACK,wLAAA,EAAoBrB;kCAAK,IAAM8B,EAAS,OAAA;iCAAS,CAAC,CAAC,qKACnD,YAAA;wBAAU,IAAM;YACpB,IAAMI,IAAQJ,EAAS,OAAA,EACjBK,IAAYJ,EAAa,OAAA;YAE/B,IAAI,CAACG,KAAS,CAACC,GACb;YAIEH,EAAe,OAAA,CAAQ,KAAA,KAAUE,EAAM,KAAA,IACzCF,EAAe,OAAA,CAAQ,QAAA,CAASE,EAAM,KAAK,GAI7CD,EAAiB,OAAA,CAAQ,IAAA,GAAO;gBAC9BC,EAAM,cAAA;gBACNA,EAAM,YAAA;gBACNA,EAAM,kBACR;aAAA;YACA,SAASE,GAA4B;gBACnC,IAAI,SAAS,aAAA,KAAkBF,GAAO;oBACpCG,EAAwB,IAAI,GAC5BC,EAAsB,IAAI;oBAC1B;gBACF;gBAGA,IAAMC,IAAKL,EAAM,cAAA,EACXb,IAAKa,EAAM,YAAA,EACXM,KAAON,EAAM,kBAAA,EACbO,IAAMP,EAAM,SAAA,EACZQ,IAAOR,EAAM,KAAA,EACbS,IAAQV,EAAiB,OAAA,CAAQ,IAAA,EAGnCW,IAAQ,CAAA,GACRC,IAAM,CAAA,GACNC;gBACJ,IAAIJ,EAAK,MAAA,KAAW,KAAKH,MAAO,QAAQlB,MAAO,MAAM;oBACnD,IAAM0B,KAAgBR,MAAOlB,GACvB2B,KAAeT,MAAOG,EAAK,MAAA,IAAUA,EAAK,MAAA,GAASD;oBAEzD,IAAIM,MAAiB,CAACC,IAAc;wBAClC,IAAMC,IAAIV;wBACV,IAAIU,MAAM,GACRL,IAAQ,GACRC,IAAM,GACNC,IAAY;6BAAA,IACHG,MAAMR,GACfG,IAAQK,IAAI,GACZJ,IAAMI,GACNH,IAAY;6BAAA,IACHL,IAAM,KAAKC,EAAK,MAAA,GAAS,GAAG;4BACrC,IAAIQ,KAAS;4BACb,IAAIP,CAAAA,CAAM,CAAC,CAAA,KAAM,QAAQA,CAAAA,CAAM,CAAC,CAAA,KAAM,MAAM;gCAC1CG,IAAYG,IAAIN,CAAAA,CAAM,CAAC,CAAA,GAAI,aAAa;gCACxC,IAAMQ,KACJR,CAAAA,CAAM,CAAC,CAAA,KAAMA,CAAAA,CAAM,CAAC,CAAA,IAAKA,CAAAA,CAAM,CAAC,CAAA,GAAIF;gCAClCK,MAAc,cAAc,CAACK,MAAAA,CAC/BD,KAAS,CAAA,CAAA;4BAEb;4BAEAN,IAAQM,KAASD,GACjBJ,IAAMK,KAASD,IAAI;wBACrB;oBACF;oBAEIL,MAAU,CAAA,KAAMC,MAAQ,CAAA,KAAMD,MAAUC,KAC1Cf,EAAS,OAAA,CAAQ,iBAAA,CAAkBc,GAAOC,GAAKC,CAAS;gBAE5D;gBAGA,IAAMM,KAAIR,MAAU,CAAA,IAAKA,IAAQL,GAC3Bc,KAAIR,MAAQ,CAAA,IAAKA,IAAMxB,GACvBiC,KAAMR,KAAA,OAAAA,IAAaN;gBACzBH,EAAwBe,EAAC,GACzBd,EAAsBe,EAAC,GAEvBpB,EAAiB,OAAA,CAAQ,IAAA,GAAO;oBAACmB;oBAAGC;oBAAGC,EAAG;;YAC5C;YAUA,IATA,SAAS,gBAAA,CAAiB,mBAAmBlB,GAA2B;gBACtE,SAAS,CAAA;YACX,CAAC,GAGDA,EAA0B,GAC1B,SAAS,aAAA,KAAkBF,KAASqB,EAAa,CAAA,CAAI,GAGjD,CAAC,SAAS,cAAA,CAAe,iBAAiB,GAAG;gBAC/C,IAAMC,IAAU,SAAS,aAAA,CAAc,OAAO;gBAI9C,IAHAA,EAAQ,EAAA,GAAK,mBACb,SAAS,IAAA,CAAK,WAAA,CAAYA,CAAO,GAE7BA,EAAQ,KAAA,EAAO;oBACjB,IAAMC,IACJ;oBAEFC,EACEF,EAAQ,KAAA,EACR,oGACF,GACAE,EACEF,EAAQ,KAAA,EACR,CAAA,4BAAA,EAA+BC,CAAc,CAAA,EAAA,CAC/C,GACAC,EACEF,EAAQ,KAAA,EACR,CAAA,oCAAA,EAAuCC,CAAc,CAAA,EAAA,CACvD,GAEAC,EACEF,EAAQ,KAAA,EACR,oPACF,GAEAE,EACEF,EAAQ,KAAA,EACR,0DACF;gBACF;YACF;YAEA,IAAMG;kCAAmB,IAAM;oBACzBxB,KACFA,EAAU,KAAA,CAAM,WAAA,CACd,iBACA,GAAGD,EAAM,YAAY,CAAA,EAAA,CACvB;gBAEJ;;YACAyB,EAAiB;YACjB,IAAMC,IAAiB,IAAI,eAAeD,CAAgB;YAC1D,OAAAC,EAAe,OAAA,CAAQ1B,CAAK;gCAErB,IAAM;oBACX,SAAS,mBAAA,CACP,mBACAE,GACA;wBAAE,SAAS,CAAA;oBAAK,CAClB,GACAwB,EAAe,UAAA,CAAW;gBAC5B;;QACF;uBAAG,CAAC,CAAC;IAGL,IAAM,CAACC,IAAiBC,EAAkB,CAAA,qKAAU,WAAA,EAAS,CAAA,CAAK,GAC5D,CAACC,GAAWR,CAAY,CAAA,IAAU,4KAAA,EAAS,CAAA,CAAK,GAChD,CAACS,GAAsB3B,CAAuB,CAAA,qKAAU,WAAA,EAE5D,IAAI,GACA,CAAC4B,GAAoB3B,CAAqB,CAAA,qKAAU,WAAA,EAExD,IAAI;IAGA,8KAAA;wBAAU,IAAM;YACpB4B;gCAAa,IAAM;oBAhPzB,IAAAnE,GAAAE,GAAAkB,GAAAC;oBAAAA,CAkPQrB,IAAA+B,EAAS,OAAA,KAAT,QAAA/B,EAAkB,aAAA,CAAc,IAAI,MAAM,OAAO;oBAGjD,IAAMqD,IAAAA,CAAInD,IAAA6B,EAAS,OAAA,KAAT,OAAA,KAAA,IAAA7B,EAAkB,cAAA,EACtBoD,IAAAA,CAAIlC,IAAAW,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAX,EAAkB,YAAA,EACtBmC,IAAAA,CAAMlC,IAAAU,EAAS,OAAA,KAAT,OAAA,KAAA,IAAAV,EAAkB,kBAAA;oBAC1BgC,MAAM,QAAQC,MAAM,QAAA,CACtBhB,EAAwBe,CAAC,GACzBd,EAAsBe,CAAC,GACvBpB,EAAiB,OAAA,CAAQ,IAAA,GAAO;wBAACmB;wBAAGC;wBAAGC,CAAG;qBAAA;gBAE9C,CAAC;;QACH;uBAAG;QAAC9B;QAAOuC,CAAS;KAAC,oKAEf,aAAA;wBAAU,IAAM;YAChBtC,MAAkB,KAAA,KAKpBD,MAAUC,KACVA,EAAc,MAAA,GAASrB,KACvBoB,EAAM,MAAA,KAAWpB,KAAAA,CAEjBK,KAAA,QAAAA,EAAae,EAAAA;QAEjB;uBAAG;QAACpB;QAAWK;QAAYgB;QAAeD,CAAK;KAAC;IAEhD,IAAM2C,IAAOC,GAAwB;QACnC,cAAArC;QACA,UAAAD;QACA,6BAAApB;QACA,WAAAqD;IACF,CAAC,GAGKM,KAAwB,gLAAA;+BAC3BhB,GAA2C;YAC1C,IAAMzB,IAAWyB,EAAE,aAAA,CAAc,KAAA,CAAM,KAAA,CAAM,GAAGjD,CAAS;YACzD,IAAIwB,EAAS,MAAA,GAAS,KAAKC,KAAU,CAACA,EAAO,IAAA,CAAKD,CAAQ,GAAG;gBAC3DyB,EAAE,cAAA,CAAe;gBACjB;YACF;YAEE,OAAO5B,KAAkB,YACzBG,EAAS,MAAA,GAASH,EAAc,MAAA,IAMhC,SAAS,aAAA,CAAc,IAAI,MAAM,iBAAiB,CAAC,GAErDE,EAASC,CAAQ;QACnB;6BACA;QAACxB;QAAWuB;QAAUF;QAAeI,CAAM;KAC7C,GACMyC,SAAuB,4KAAA;8BAAY,IAAM;YA3SnD,IAAAvE;YA4SM,IAAI+B,EAAS,OAAA,EAAS;gBACpB,IAAMc,IAAQ,KAAK,GAAA,CAAId,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA,EAAQ1B,IAAY,CAAC,GAC7DyC,IAAMf,EAAS,OAAA,CAAQ,KAAA,CAAM,MAAA;gBAAA,CACnC/B,IAAA+B,EAAS,OAAA,KAAT,QAAA/B,EAAkB,iBAAA,CAAkB6C,GAAOC,IAC3CR,EAAwBO,CAAK,GAC7BN,EAAsBO,CAAG;YAC3B;YACAU,EAAa,CAAA,CAAI;QACnB;6BAAG;QAACnD,CAAS;KAAC,GAERmE,uKAAuB,cAAA;+BAC1BlB,GAA8C;YAvTrD,IAAAtD,GAAAE;YAwTQ,IAAMiC,IAAQJ,EAAS,OAAA;YACvB,IAAI,CAACnB,KAAAA,CAAqB,CAACqB,EAAe,OAAA,CAAQ,KAAA,IAAS,CAACqB,EAAE,aAAA,IAAiB,CAACnB,CAAAA,GAC9E;YAGF,IAAMsC,IAAWnB,EAAE,aAAA,CAAc,OAAA,CAAQ,YAAY,GAC/CoB,IAAU9D,IACZA,EAAiB6D,CAAQ,IACzBA;YACJnB,EAAE,cAAA,CAAe;YAEjB,IAAMT,IAAAA,CAAQ7C,IAAA+B,EAAS,OAAA,KAAT,OAAA,KAAA,IAAA/B,EAAkB,cAAA,EAC1B8C,IAAAA,CAAM5C,IAAA6B,EAAS,OAAA,KAAT,OAAA,KAAA,IAAA7B,EAAkB,YAAA,EAOxB2B,IAAAA,CALcgB,MAAUC,IAG1BrB,EAAM,KAAA,CAAM,GAAGoB,CAAK,IAAI6B,IAAUjD,EAAM,KAAA,CAAMqB,CAAG,IACjDrB,EAAM,KAAA,CAAM,GAAGoB,CAAK,IAAI6B,IAAUjD,EAAM,KAAA,CAAMoB,CAAK,CAAA,EACrB,KAAA,CAAM,GAAGxC,CAAS;YAEpD,IAAIwB,EAAS,MAAA,GAAS,KAAKC,KAAU,CAACA,EAAO,IAAA,CAAKD,CAAQ,GACxD;YAGFM,EAAM,KAAA,GAAQN,GACdD,EAASC,CAAQ;YAEjB,IAAM8C,IAAS,KAAK,GAAA,CAAI9C,EAAS,MAAA,EAAQxB,IAAY,CAAC,GAChDuE,IAAO/C,EAAS,MAAA;YAEtBM,EAAM,iBAAA,CAAkBwC,GAAQC,CAAI,GACpCtC,EAAwBqC,CAAM,GAC9BpC,EAAsBqC,CAAI;QAC5B;6BACA;QAACvE;QAAWuB;QAAUE;QAAQL,CAAK;KACrC,GAGMoD,MAAkB,2KAAA;0BACtB,IAAA,CAAO;gBACL,UAAU;gBACV,QAAQ3D,EAAM,QAAA,GAAW,YAAY;gBACrC,YAAY;gBACZ,kBAAkB;gBAClB,eAAe;YACjB,CAAA;yBACA;QAACA,EAAM,QAAQ;KACjB,GAEM4D,uKAAmB,UAAA;0BACvB,IAAA,CAAO;gBACL,UAAU;gBACV,OAAO;gBACP,OAAOV,EAAK,gBAAA,GACR,CAAA,YAAA,EAAeA,EAAK,qBAAqB,CAAA,CAAA,CAAA,GACzC;gBACJ,UAAUA,EAAK,gBAAA,GACX,CAAA,QAAA,EAAWA,EAAK,qBAAqB,CAAA,KAAA,CAAA,GACrC,KAAA;gBACJ,QAAQ;gBACR,SAAS;gBACT,WAAA9D;gBACA,SAAS;gBACT,OAAO;gBACP,eAAe;gBACf,YAAY;gBACZ,YAAY;gBACZ,QAAQ;gBACR,SAAS;gBACT,WAAW;gBACX,YAAY;gBACZ,eAAe;gBACf,UAAU;gBACV,YAAY;gBACZ,oBAAoB;YAgBtB,CAAA;yBACA;QAAC8D,EAAK,qBAAA;QAAuBA,EAAK,gBAAA;QAAkB9D,CAAS;KAC/D,GAGMyE,uKAAsB,UAAA;0BAC1B,sKACE,gBAAA,EAAC,SAAAC,GAAAC,GAAA;gBACC,cAAc/D,EAAM,YAAA,IAAgB;YAAA,GAChCA,IAFL;gBAGC,kBAAc,CAAA;gBACd,oCAAkCO,EAAM,MAAA,KAAW,KAAK,KAAA;gBACxD,sBAAoBwC;gBACpB,sBAAoBC;gBACpB,WAAWzD;gBACX,SAASqB,KAAA,OAAA,KAAA,IAAAA,EAAQ,MAAA;gBACjB,oBAAkBtB;gBAClB,OAAOsE;gBACP,WAAWzE;gBACX,OAAOoB;gBACP,KAAKM;gBACL,OAAA;uCAASuB,GAAK;wBAxaxB,IAAAtD;wBAyaYwE,GAAelB,CAAC,GAAA,CAChBtD,IAAAkB,EAAM,OAAA,KAAN,QAAAlB,EAAA,IAAA,CAAAkB,GAAgBoC;oBAClB;;gBACA,UAAUgB;gBACV,WAAA;uCAAahB,GAAK;wBA7a5B,IAAAtD;wBA8aY+D,GAAmB,CAAA,CAAI,GAAA,CACvB/D,IAAAkB,EAAM,WAAA,KAAN,QAAAlB,EAAA,IAAA,CAAAkB,GAAoBoC;oBACtB;;gBACA,YAAA;uCAAcA,GAAK;wBAjb7B,IAAAtD;wBAkbY+D,GAAmB,CAAA,CAAK,GAAA,CACxB/D,IAAAkB,EAAM,YAAA,KAAN,QAAAlB,EAAA,IAAA,CAAAkB,GAAqBoC;oBACvB;;gBACA,OAAA;uCAASA,GAAK;wBArbxB,IAAAtD;wBAsbYuE,GAAe,GAAA,CACfvE,IAAAkB,EAAM,OAAA,KAAN,QAAAlB,EAAA,IAAA,CAAAkB,GAAgBoC;oBAClB;;gBACA,MAAA;sCAAQA,GAAK;wBAzbvB,IAAAtD;wBA0bYwD,EAAa,CAAA,CAAK,GAAA,CAClBxD,IAAAkB,EAAM,MAAA,KAAN,QAAAlB,EAAA,IAAA,CAAAkB,GAAeoC;oBACjB;;YAAA,EACF;yBAEF;QACEgB;QACAC;QACAC;QACA/D;QACAqE;QACAzE;QACA6D;QACAD;QACA/C;QACAY,KAAA,OAAA,KAAA,IAAAA,EAAQ,MAAA;QACRL,CACF;KACF,GAEMyD,uKAAqB,UAAA;0BAAqB,IAAA,CACvC;gBACL,OAAO,MAAM,IAAA,CAAK;oBAAE,QAAQ7E;gBAAU,CAAC,EAAE,GAAA;sCAAI,CAAC8E,GAAGC,IAAY;wBAhdrE,IAAApF;wBAidU,IAAMqF,IACJrB,KACAC,MAAyB,QACzBC,MAAuB,QAAA,CACrBD,MAAyBC,KACzBkB,MAAYnB,KACXmB,KAAWnB,KAAwBmB,IAAUlB,CAAAA,GAE5CoB,IAAO7D,CAAAA,CAAM2D,CAAO,CAAA,KAAM,KAAA,IAAY3D,CAAAA,CAAM2D,CAAO,CAAA,GAAI,MACvDG,IAAkB9D,CAAAA,CAAM,CAAC,CAAA,KAAM,KAAA,IAAY,OAAA,CAAOzB,IAAAQ,KAAA,OAAA,KAAA,IAAAA,CAAAA,CAAc4E,EAAAA,KAAd,OAAApF,IAA0B;wBAElF,OAAO;4BACL,MAAAsF;4BACA,iBAAAC;4BACA,UAAAF;4BACA,cAAcA,KAAYC,MAAS;wBACrC;oBACF,CAAC;;gBACD,WAAAtB;gBACA,YAAY,CAAC9C,EAAM,QAAA,IAAY4C;YACjC,CAAA;yBACC;QACDE;QACAF;QACAzD;QACA6D;QACAD;QACA/C,EAAM,QAAA;QACNO,CACF;KAAC,GAEK+D,uKAAyB,UAAA;0BAAQ,IACjCxE,IACKA,EAAOkE,EAAY,KAG1B,iLAAA,EAACpF,GAAgB,QAAA,EAAhB;gBAAyB,OAAOoF;YAAAA,GAC9BjE,CACH;yBAED;QAACA;QAAUiE;QAAclE,CAAM;KAAC;IAEnC,OACE,kLAAA,EAAA,6JAAA,CAAA,WAAA,EAAA,MACGF,MAAwB,0KACvB,gBAAA,EAAC,YAAA,OACC,iLAAA,EAAC,SAAA,MAAOA,CAAoB,CAC9B,qKAGF,gBAAA,EAAC,OAAA;QACC,KAAKkB;QACL,4BAAwB,CAAA;QACxB,OAAO6C;QACP,WAAWhE;IAAAA,GAEV2E,sKAED,gBAAA,EAAC,OAAA;QACC,OAAO;YACL,UAAU;YACV,OAAO;YACP,eAAe;QACjB;IAAA,GAECT,EACH,CACF,CACF;AAEJ,CACF;AACAhF,GAAS,WAAA,GAAc;AAEvB,SAAS4D,EAAe8B,CAAAA,EAAsBC,CAAAA,CAAc;IAC1D,IAAI;QACFD,EAAM,UAAA,CAAWC,CAAI;IACvB,EAAA,OAAQ,GAAA;QACN,QAAQ,KAAA,CAAM,wCAAwCA,CAAI;IAC5D;AACF;AAOA,IAAM3E,KAAwB,CAAA;;;;;;;;;;;;;;;;;;;;AIxiBvB,IAAM4E,KAAqB,UACrBC,KAAoB,eACpBC,KAA+B", "ignoreList": [0, 1, 2, 3, 4], "debugId": null}}, {"offset": {"line": 4572, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/tailwindcss/dist/plugin.mjs"], "sourcesContent": ["function g(i,n){return{handler:i,config:n}}g.withOptions=function(i,n=()=>({})){function t(o){return{handler:i(o),config:n(o)}}return t.__isOptionsFunction=!0,t};var u=g;export{u as default};\n"], "names": [], "mappings": ";;;AAAA,SAAS,EAAE,CAAC,EAAC,CAAC;IAAE,OAAM;QAAC,SAAQ;QAAE,QAAO;IAAC;AAAC;AAAC,EAAE,WAAW,GAAC,SAAS,CAAC,EAAC,IAAE,IAAI,CAAC,CAAC,CAAC,CAAC;IAAE,SAAS,EAAE,CAAC;QAAE,OAAM;YAAC,SAAQ,EAAE;YAAG,QAAO,EAAE;QAAE;IAAC;IAAC,OAAO,EAAE,mBAAmB,GAAC,CAAC,GAAE;AAAC;AAAE,IAAI,IAAE", "ignoreList": [0], "debugId": null}}]}