{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Heading, Text, But<PERSON>, Card, Separator } from '@whop/react/components';\n\nexport default function Page() {\n\tconst [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');\n\n\treturn (\n\t\t<div className=\"min-h-screen bg-gradient-to-br from-gray-1 to-green-1 p-4\">\n\t\t\t{/* Navigation */}\n\t\t\t<div className=\"max-w-6xl mx-auto mb-8\">\n\t\t\t\t<Card className=\"p-6 shadow-lg bg-white/80 backdrop-blur-sm border border-green-3\">\n\t\t\t\t\t<div className=\"flex items-center justify-between mb-6\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Heading size=\"7\" className=\"text-gray-12 mb-1\">\n\t\t\t\t\t\t\t\tWhop Leaderboards & Competitions\n\t\t\t\t\t\t\t</Heading>\n\t\t\t\t\t\t\t<Text size=\"3\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\tCompete, climb, and conquer the leaderboards\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t<div className=\"w-2 h-2 bg-green-9 rounded-full animate-pulse\"></div>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">Live</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t<div className=\"flex gap-2\">\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tvariant={activeTab === 'leaderboards' ? 'solid' : 'soft'}\n\t\t\t\t\t\t\tcolor={activeTab === 'leaderboards' ? 'green' : 'gray'}\n\t\t\t\t\t\t\tonClick={() => setActiveTab('leaderboards')}\n\t\t\t\t\t\t\tclassName=\"px-8 py-3 font-medium transition-all duration-200\"\n\t\t\t\t\t\t\tsize=\"3\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🏆 Leaderboards\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tvariant={activeTab === 'competitions' ? 'solid' : 'soft'}\n\t\t\t\t\t\t\tcolor={activeTab === 'competitions' ? 'green' : 'gray'}\n\t\t\t\t\t\t\tonClick={() => setActiveTab('competitions')}\n\t\t\t\t\t\t\tclassName=\"px-8 py-3 font-medium transition-all duration-200\"\n\t\t\t\t\t\t\tsize=\"3\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t⚔️ Competitions\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\n\t\t\t{/* Content */}\n\t\t\t<div className=\"max-w-6xl mx-auto\">\n\t\t\t\t{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction LeaderboardsPage() {\n\t// Sample leaderboard data\n\tconst leaderboardData = [\n\t\t{ rank: 1, name: \"Alex Thompson\", score: 2847, change: \"+12\", avatar: \"AT\", streak: 7, country: \"🇺🇸\" },\n\t\t{ rank: 2, name: \"Sarah Chen\", score: 2756, change: \"+8\", avatar: \"SC\", streak: 5, country: \"🇨🇦\" },\n\t\t{ rank: 3, name: \"Marcus Johnson\", score: 2698, change: \"-3\", avatar: \"MJ\", streak: 3, country: \"🇬🇧\" },\n\t\t{ rank: 4, name: \"Emma Rodriguez\", score: 2634, change: \"+15\", avatar: \"ER\", streak: 12, country: \"🇪🇸\" },\n\t\t{ rank: 5, name: \"David Kim\", score: 2589, change: \"+5\", avatar: \"DK\", streak: 2, country: \"🇰🇷\" },\n\t\t{ rank: 6, name: \"Lisa Wang\", score: 2543, change: \"-2\", avatar: \"LW\", streak: 8, country: \"🇨🇳\" },\n\t\t{ rank: 7, name: \"James Wilson\", score: 2498, change: \"+7\", avatar: \"JW\", streak: 4, country: \"🇦🇺\" },\n\t\t{ rank: 8, name: \"Maya Patel\", score: 2456, change: \"+3\", avatar: \"MP\", streak: 6, country: \"🇮🇳\" },\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* Header */}\n\t\t\t<Card className=\"p-6 bg-gradient-to-r from-green-2 to-green-3 border-green-6\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<Heading size=\"8\" className=\"text-green-12 mb-2\">\n\t\t\t\t\t\tGlobal Leaderboard\n\t\t\t\t\t</Heading>\n\t\t\t\t\t<Text size=\"4\" className=\"text-green-11\">\n\t\t\t\t\t\tCompete with the best and climb to the top\n\t\t\t\t\t</Text>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Leaderboard */}\n\t\t\t<Card className=\"overflow-hidden shadow-lg\">\n\t\t\t\t<div className=\"p-6 bg-gradient-to-r from-green-1 to-green-2 border-b border-green-4\">\n\t\t\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Heading size=\"5\" className=\"text-green-12\">\n\t\t\t\t\t\t\t\tTop Performers\n\t\t\t\t\t\t\t</Heading>\n\t\t\t\t\t\t\t<Text size=\"3\" className=\"text-green-11 mt-1\">\n\t\t\t\t\t\t\t\tUpdated in real-time • Last update: 2 minutes ago\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Button variant=\"soft\" color=\"green\" size=\"2\">\n\t\t\t\t\t\t\tView All\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<div className=\"divide-y divide-gray-4\">\n\t\t\t\t\t{leaderboardData.map((player, index) => (\n\t\t\t\t\t\t<div key={player.rank} className=\"p-5 hover:bg-green-1 transition-all duration-200 hover:shadow-sm\">\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t\t\t\t\t{/* Rank Badge */}\n\t\t\t\t\t\t\t\t\t<div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold shadow-sm ${\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 1 ? 'bg-gradient-to-br from-yellow-9 to-yellow-10 text-yellow-1' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 2 ? 'bg-gradient-to-br from-gray-9 to-gray-10 text-gray-1' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 3 ? 'bg-gradient-to-br from-orange-9 to-orange-10 text-orange-1' :\n\t\t\t\t\t\t\t\t\t\t'bg-gradient-to-br from-green-3 to-green-4 text-green-11'\n\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t{player.rank}\n\t\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t\t{/* Player Info */}\n\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t\t\t\t<div className=\"w-8 h-8 rounded-full bg-gray-3 flex items-center justify-center text-xs font-semibold text-gray-11\">\n\t\t\t\t\t\t\t\t\t\t\t{player.avatar}\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"4\" weight=\"medium\" className=\"text-gray-12\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{player.name}\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t\t<span className=\"text-sm\">{player.country}</span>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2 mt-1\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{player.streak} day streak\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"w-1 h-1 bg-gray-6 rounded-full\"></div>\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\t\t\tRank #{player.rank}\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-6\">\n\t\t\t\t\t\t\t\t\t<div className=\"text-right\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-gray-12 block\">\n\t\t\t\t\t\t\t\t\t\t\t{player.score.toLocaleString()}\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\tpoints\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div className={`px-3 py-1 rounded-full text-xs font-semibold shadow-sm ${\n\t\t\t\t\t\t\t\t\t\tplayer.change.startsWith('+') ? 'bg-green-3 text-green-11 border border-green-6' : 'bg-red-3 text-red-11 border border-red-6'\n\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t{player.change}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\n\t\t\t\t{/* View More */}\n\t\t\t\t<div className=\"p-4 bg-gray-1 border-t border-gray-4\">\n\t\t\t\t\t<Button variant=\"soft\" color=\"gray\" className=\"w-full\">\n\t\t\t\t\t\tView Full Leaderboard\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Stats Cards */}\n\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n\t\t\t\t<Card className=\"p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"6\" weight=\"bold\" className=\"text-green-12 block mb-1\">\n\t\t\t\t\t\t\t1,247\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"3\" className=\"text-green-11 mb-2\">\n\t\t\t\t\t\t\tTotal Players\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\t+23 this week\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"6\" weight=\"bold\" className=\"text-green-12 block mb-1\">\n\t\t\t\t\t\t\t89.2%\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"3\" className=\"text-green-11 mb-2\">\n\t\t\t\t\t\t\tActivity Rate\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\t+2.1% from last month\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"6\" weight=\"bold\" className=\"text-green-12 block mb-1\">\n\t\t\t\t\t\t\t2,847\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"3\" className=\"text-green-11 mb-2\">\n\t\t\t\t\t\t\tTop Score\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\tAlex Thompson\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"6\" weight=\"bold\" className=\"text-green-12 block mb-1\">\n\t\t\t\t\t\t\t156\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"3\" className=\"text-green-11 mb-2\">\n\t\t\t\t\t\t\tAvg Score\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\tDaily average\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction CompetitionsPage() {\n\tconst competitions = [\n\t\t{\n\t\t\tid: 1,\n\t\t\ttitle: \"Weekly Challenge\",\n\t\t\tdescription: \"Complete daily tasks to earn points and climb the weekly leaderboard.\",\n\t\t\ttimeLeft: \"3 days, 14 hours\",\n\t\t\tparticipants: 342,\n\t\t\tprize: \"$500\",\n\t\t\tstatus: \"active\",\n\t\t\tdifficulty: \"Medium\"\n\t\t},\n\t\t{\n\t\t\tid: 2,\n\t\t\ttitle: \"Monthly Tournament\",\n\t\t\tdescription: \"The ultimate test of skill. Top 10 players win exclusive rewards.\",\n\t\t\ttimeLeft: \"Starts in 5 days\",\n\t\t\tparticipants: 89,\n\t\t\tprize: \"$2,000\",\n\t\t\tstatus: \"upcoming\",\n\t\t\tdifficulty: \"Hard\"\n\t\t},\n\t\t{\n\t\t\tid: 3,\n\t\t\ttitle: \"Speed Run Challenge\",\n\t\t\tdescription: \"Race against time in this fast-paced competition.\",\n\t\t\ttimeLeft: \"2 hours left\",\n\t\t\tparticipants: 156,\n\t\t\tprize: \"$250\",\n\t\t\tstatus: \"active\",\n\t\t\tdifficulty: \"Easy\"\n\t\t},\n\t\t{\n\t\t\tid: 4,\n\t\t\ttitle: \"Elite Championship\",\n\t\t\tdescription: \"Invitation-only tournament for top 50 players.\",\n\t\t\ttimeLeft: \"Registration closed\",\n\t\t\tparticipants: 50,\n\t\t\tprize: \"$5,000\",\n\t\t\tstatus: \"closed\",\n\t\t\tdifficulty: \"Expert\"\n\t\t}\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-6\">\n\t\t\t{/* Header */}\n\t\t\t<Card className=\"p-6 bg-gradient-to-r from-green-2 to-green-3 border-green-6\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<Heading size=\"8\" className=\"text-green-12 mb-2\">\n\t\t\t\t\t\tCompetitions\n\t\t\t\t\t</Heading>\n\t\t\t\t\t<Text size=\"4\" className=\"text-green-11\">\n\t\t\t\t\t\tJoin exciting competitions and win amazing prizes\n\t\t\t\t\t</Text>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Active Competitions */}\n\t\t\t<div>\n\t\t\t\t<Heading size=\"5\" className=\"text-gray-12 mb-4\">\n\t\t\t\t\tActive & Upcoming Competitions\n\t\t\t\t</Heading>\n\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n\t\t\t\t\t{competitions.map((comp) => (\n\t\t\t\t\t\t<Card key={comp.id} className=\"p-6 hover:shadow-lg transition-shadow\">\n\t\t\t\t\t\t\t<div className=\"flex justify-between items-start mb-4\">\n\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t<Heading size=\"5\" className=\"text-gray-12 mb-2\">\n\t\t\t\t\t\t\t\t\t\t{comp.title}\n\t\t\t\t\t\t\t\t\t</Heading>\n\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2 mb-2\">\n\t\t\t\t\t\t\t\t\t\t<div className={`px-2 py-1 rounded-full text-xs font-medium ${\n\t\t\t\t\t\t\t\t\t\t\tcomp.status === 'active' ? 'bg-green-3 text-green-11' :\n\t\t\t\t\t\t\t\t\t\t\tcomp.status === 'upcoming' ? 'bg-blue-3 text-blue-11' :\n\t\t\t\t\t\t\t\t\t\t\t'bg-gray-3 text-gray-11'\n\t\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t\t{comp.status.charAt(0).toUpperCase() + comp.status.slice(1)}\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div className={`px-2 py-1 rounded-full text-xs font-medium ${\n\t\t\t\t\t\t\t\t\t\t\tcomp.difficulty === 'Easy' ? 'bg-green-3 text-green-11' :\n\t\t\t\t\t\t\t\t\t\t\tcomp.difficulty === 'Medium' ? 'bg-yellow-3 text-yellow-11' :\n\t\t\t\t\t\t\t\t\t\t\tcomp.difficulty === 'Hard' ? 'bg-orange-3 text-orange-11' :\n\t\t\t\t\t\t\t\t\t\t\t'bg-red-3 text-red-11'\n\t\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t\t{comp.difficulty}\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-11\">\n\t\t\t\t\t\t\t\t\t{comp.prize}\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<Text size=\"3\" className=\"text-gray-10 mb-4\">\n\t\t\t\t\t\t\t\t{comp.description}\n\t\t\t\t\t\t\t</Text>\n\n\t\t\t\t\t\t\t<div className=\"space-y-2 mb-4\">\n\t\t\t\t\t\t\t\t<div className=\"flex justify-between\">\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-11\">Time:</Text>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-12\">{comp.timeLeft}</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div className=\"flex justify-between\">\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-11\">Participants:</Text>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-12\">{comp.participants}</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tcolor=\"green\"\n\t\t\t\t\t\t\t\tvariant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}\n\t\t\t\t\t\t\t\tclassName=\"w-full\"\n\t\t\t\t\t\t\t\tdisabled={comp.status === 'closed'}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{comp.status === 'active' ? 'Join Now' :\n\t\t\t\t\t\t\t\t comp.status === 'upcoming' ? 'Register' :\n\t\t\t\t\t\t\t\t 'Closed'}\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Competition Stats */}\n\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-4 gap-4\">\n\t\t\t\t<Card className=\"p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"5\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t12\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\t\tActive Competitions\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"5\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t$15K\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\t\tTotal Prize Pool\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"5\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t847\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\t\tTotal Participants\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"5\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t24h\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\t\tAvg Duration\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,qBACC,6LAAC;QAAI,WAAU;;0BAEd,6LAAC;gBAAI,WAAU;0BACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oBAAC,WAAU;;sCACf,6LAAC;4BAAI,WAAU;;8CACd,6LAAC;;sDACA,6LAAC,mLAAA,CAAA,UAAO;4CAAC,MAAK;4CAAI,WAAU;sDAAoB;;;;;;sDAGhD,6LAAC,6KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAe;;;;;;;;;;;;8CAIzC,6LAAC;oCAAI,WAAU;;sDACd,6LAAC;4CAAI,WAAU;;;;;;sDACf,6LAAC,6KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAI3C,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,iLAAA,CAAA,SAAM;oCACN,SAAS,cAAc,iBAAiB,UAAU;oCAClD,OAAO,cAAc,iBAAiB,UAAU;oCAChD,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,MAAK;8CACL;;;;;;8CAGD,6LAAC,iLAAA,CAAA,SAAM;oCACN,SAAS,cAAc,iBAAiB,UAAU;oCAClD,OAAO,cAAc,iBAAiB,UAAU;oCAChD,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,MAAK;8CACL;;;;;;;;;;;;;;;;;;;;;;;0BAQJ,6LAAC;gBAAI,WAAU;0BACb,cAAc,+BAAiB,6LAAC;;;;yCAAsB,6LAAC;;;;;;;;;;;;;;;;AAI5D;GApDwB;KAAA;AAsDxB,SAAS;IACR,0BAA0B;IAC1B,MAAM,kBAAkB;QACvB;YAAE,MAAM;YAAG,MAAM;YAAiB,OAAO;YAAM,QAAQ;YAAO,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACvG;YAAE,MAAM;YAAG,MAAM;YAAc,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACnG;YAAE,MAAM;YAAG,MAAM;YAAkB,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACvG;YAAE,MAAM;YAAG,MAAM;YAAkB,OAAO;YAAM,QAAQ;YAAO,QAAQ;YAAM,QAAQ;YAAI,SAAS;QAAO;QACzG;YAAE,MAAM;YAAG,MAAM;YAAa,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QAClG;YAAE,MAAM;YAAG,MAAM;YAAa,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QAClG;YAAE,MAAM;YAAG,MAAM;YAAgB,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACrG;YAAE,MAAM;YAAG,MAAM;YAAc,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;KACnG;IAED,qBACC,6LAAC;QAAI,WAAU;;0BAEd,6LAAC,6KAAA,CAAA,OAAI;gBAAC,WAAU;0BACf,cAAA,6LAAC;oBAAI,WAAU;;sCACd,6LAAC,mLAAA,CAAA,UAAO;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAGjD,6LAAC,6KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,6KAAA,CAAA,OAAI;gBAAC,WAAU;;kCACf,6LAAC;wBAAI,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC;;sDACA,6LAAC,mLAAA,CAAA,UAAO;4CAAC,MAAK;4CAAI,WAAU;sDAAgB;;;;;;sDAG5C,6LAAC,6KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAqB;;;;;;;;;;;;8CAI/C,6LAAC,iLAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAO,OAAM;oCAAQ,MAAK;8CAAI;;;;;;;;;;;;;;;;;kCAMhD,6LAAC;wBAAI,WAAU;kCACb,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;gCAAsB,WAAU;0CAChC,cAAA,6LAAC;oCAAI,WAAU;;sDACd,6LAAC;4CAAI,WAAU;;8DAEd,6LAAC;oDAAI,WAAW,CAAC,oFAAoF,EACpG,OAAO,IAAI,KAAK,IAAI,+DACpB,OAAO,IAAI,KAAK,IAAI,yDACpB,OAAO,IAAI,KAAK,IAAI,+DACpB,2DACC;8DACA,OAAO,IAAI;;;;;;8DAIb,6LAAC;oDAAI,WAAU;;sEACd,6LAAC;4DAAI,WAAU;sEACb,OAAO,MAAM;;;;;;sEAEf,6LAAC;;8EACA,6LAAC;oEAAI,WAAU;;sFACd,6LAAC,6KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,QAAO;4EAAS,WAAU;sFACvC,OAAO,IAAI;;;;;;sFAEb,6LAAC;4EAAK,WAAU;sFAAW,OAAO,OAAO;;;;;;;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;;sFACd,6LAAC,6KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,WAAU;;gFACvB,OAAO,MAAM;gFAAC;;;;;;;sFAEhB,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC,6KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,WAAU;;gFAAe;gFAChC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOvB,6LAAC;4CAAI,WAAU;;8DACd,6LAAC;oDAAI,WAAU;;sEACd,6LAAC,6KAAA,CAAA,OAAI;4DAAC,MAAK;4DAAI,QAAO;4DAAO,WAAU;sEACrC,OAAO,KAAK,CAAC,cAAc;;;;;;sEAE7B,6LAAC,6KAAA,CAAA,OAAI;4DAAC,MAAK;4DAAI,WAAU;sEAAe;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAW,CAAC,uDAAuD,EACvE,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,mDAAmD,4CAClF;8DACA,OAAO,MAAM;;;;;;;;;;;;;;;;;;+BAlDR,OAAO,IAAI;;;;;;;;;;kCA2DvB,6LAAC;wBAAI,WAAU;kCACd,cAAA,6LAAC,iLAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAO,OAAM;4BAAO,WAAU;sCAAS;;;;;;;;;;;;;;;;;0BAOzD,6LAAC;gBAAI,WAAU;;kCACd,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA2B;;;;;;8CAGlE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA2B;;;;;;8CAGlE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA2B;;;;;;8CAGlE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA2B;;;;;;8CAGlE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;MA7KS;AA+KT,SAAS;IACR,MAAM,eAAe;QACpB;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;QACb;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;QACb;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;QACb;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;QACb;KACA;IAED,qBACC,6LAAC;QAAI,WAAU;;0BAEd,6LAAC,6KAAA,CAAA,OAAI;gBAAC,WAAU;0BACf,cAAA,6LAAC;oBAAI,WAAU;;sCACd,6LAAC,mLAAA,CAAA,UAAO;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAGjD,6LAAC,6KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAO3C,6LAAC;;kCACA,6LAAC,mLAAA,CAAA,UAAO;wBAAC,MAAK;wBAAI,WAAU;kCAAoB;;;;;;kCAGhD,6LAAC;wBAAI,WAAU;kCACb,aAAa,GAAG,CAAC,CAAC,qBAClB,6LAAC,6KAAA,CAAA,OAAI;gCAAe,WAAU;;kDAC7B,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;;kEACA,6LAAC,mLAAA,CAAA,UAAO;wDAAC,MAAK;wDAAI,WAAU;kEAC1B,KAAK,KAAK;;;;;;kEAEZ,6LAAC;wDAAI,WAAU;;0EACd,6LAAC;gEAAI,WAAW,CAAC,2CAA2C,EAC3D,KAAK,MAAM,KAAK,WAAW,6BAC3B,KAAK,MAAM,KAAK,aAAa,2BAC7B,0BACC;0EACA,KAAK,MAAM,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,KAAK,MAAM,CAAC,KAAK,CAAC;;;;;;0EAE1D,6LAAC;gEAAI,WAAW,CAAC,2CAA2C,EAC3D,KAAK,UAAU,KAAK,SAAS,6BAC7B,KAAK,UAAU,KAAK,WAAW,+BAC/B,KAAK,UAAU,KAAK,SAAS,+BAC7B,wBACC;0EACA,KAAK,UAAU;;;;;;;;;;;;;;;;;;0DAInB,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAO,WAAU;0DACrC,KAAK,KAAK;;;;;;;;;;;;kDAIb,6LAAC,6KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,KAAK,WAAW;;;;;;kDAGlB,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACd,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAe;;;;;;kEACxC,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAgB,KAAK,QAAQ;;;;;;;;;;;;0DAEvD,6LAAC;gDAAI,WAAU;;kEACd,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAe;;;;;;kEACxC,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAgB,KAAK,YAAY;;;;;;;;;;;;;;;;;;kDAI5D,6LAAC,iLAAA,CAAA,SAAM;wCACN,OAAM;wCACN,SAAS,KAAK,MAAM,KAAK,WAAW,UAAU,KAAK,MAAM,KAAK,aAAa,SAAS;wCACpF,WAAU;wCACV,UAAU,KAAK,MAAM,KAAK;kDAEzB,KAAK,MAAM,KAAK,WAAW,aAC3B,KAAK,MAAM,KAAK,aAAa,aAC7B;;;;;;;+BApDQ,KAAK,EAAE;;;;;;;;;;;;;;;;0BA4DrB,6LAAC;gBAAI,WAAU;;kCACd,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;MA5KS", "debugId": null}}, {"offset": {"line": 1149, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js"], "sourcesContent": ["/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\"use strict\";\n\"production\" !== process.env.NODE_ENV &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = require(\"next/dist/compiled/react\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n"], "names": [], "mappings": "AAAA;;;;;;;;CAQC,GAGgB;AADjB;AACA,oEACE,AAAC;IACC,SAAS,yBAAyB,IAAI;QACpC,IAAI,QAAQ,MAAM,OAAO;QACzB,IAAI,eAAe,OAAO,MACxB,OAAO,KAAK,QAAQ,KAAK,yBACrB,OACA,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI;QACvC,IAAI,aAAa,OAAO,MAAM,OAAO;QACrC,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;QACX;QACA,IAAI,aAAa,OAAO,MACtB,OACG,aAAa,OAAO,KAAK,GAAG,IAC3B,QAAQ,KAAK,CACX,sHAEJ,KAAK,QAAQ;YAEb,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO,CAAC,KAAK,WAAW,IAAI,SAAS,IAAI;YAC3C,KAAK;gBACH,OAAO,CAAC,KAAK,QAAQ,CAAC,WAAW,IAAI,SAAS,IAAI;YACpD,KAAK;gBACH,IAAI,YAAY,KAAK,MAAM;gBAC3B,OAAO,KAAK,WAAW;gBACvB,QACE,CAAC,AAAC,OAAO,UAAU,WAAW,IAAI,UAAU,IAAI,IAAI,IACnD,OAAO,OAAO,OAAO,gBAAgB,OAAO,MAAM,YAAa;gBAClE,OAAO;YACT,KAAK;gBACH,OACE,AAAC,YAAY,KAAK,WAAW,IAAI,MACjC,SAAS,YACL,YACA,yBAAyB,KAAK,IAAI,KAAK;YAE/C,KAAK;gBACH,YAAY,KAAK,QAAQ;gBACzB,OAAO,KAAK,KAAK;gBACjB,IAAI;oBACF,OAAO,yBAAyB,KAAK;gBACvC,EAAE,OAAO,GAAG,CAAC;QACjB;QACF,OAAO;IACT;IACA,SAAS,mBAAmB,KAAK;QAC/B,OAAO,KAAK;IACd;IACA,SAAS,uBAAuB,KAAK;QACnC,IAAI;YACF,mBAAmB;YACnB,IAAI,2BAA2B,CAAC;QAClC,EAAE,OAAO,GAAG;YACV,2BAA2B,CAAC;QAC9B;QACA,IAAI,0BAA0B;YAC5B,2BAA2B;YAC3B,IAAI,wBAAwB,yBAAyB,KAAK;YAC1D,IAAI,oCACF,AAAC,eAAe,OAAO,UACrB,OAAO,WAAW,IAClB,KAAK,CAAC,OAAO,WAAW,CAAC,IAC3B,MAAM,WAAW,CAAC,IAAI,IACtB;YACF,sBAAsB,IAAI,CACxB,0BACA,4GACA;YAEF,OAAO,mBAAmB;QAC5B;IACF;IACA,SAAS,YAAY,IAAI;QACvB,IAAI,SAAS,qBAAqB,OAAO;QACzC,IACE,aAAa,OAAO,QACpB,SAAS,QACT,KAAK,QAAQ,KAAK,iBAElB,OAAO;QACT,IAAI;YACF,IAAI,OAAO,yBAAyB;YACpC,OAAO,OAAO,MAAM,OAAO,MAAM;QACnC,EAAE,OAAO,GAAG;YACV,OAAO;QACT;IACF;IACA,SAAS;QACP,IAAI,aAAa,qBAAqB,CAAC;QACvC,OAAO,SAAS,aAAa,OAAO,WAAW,QAAQ;IACzD;IACA,SAAS;QACP,OAAO,MAAM;IACf;IACA,SAAS,YAAY,MAAM;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,IAAI,SAAS,OAAO,wBAAwB,CAAC,QAAQ,OAAO,GAAG;YAC/D,IAAI,UAAU,OAAO,cAAc,EAAE,OAAO,CAAC;QAC/C;QACA,OAAO,KAAK,MAAM,OAAO,GAAG;IAC9B;IACA,SAAS,2BAA2B,KAAK,EAAE,WAAW;QACpD,SAAS;YACP,8BACE,CAAC,AAAC,6BAA6B,CAAC,GAChC,QAAQ,KAAK,CACX,2OACA,YACD;QACL;QACA,sBAAsB,cAAc,GAAG,CAAC;QACxC,OAAO,cAAc,CAAC,OAAO,OAAO;YAClC,KAAK;YACL,cAAc,CAAC;QACjB;IACF;IACA,SAAS;QACP,IAAI,gBAAgB,yBAAyB,IAAI,CAAC,IAAI;QACtD,sBAAsB,CAAC,cAAc,IACnC,CAAC,AAAC,sBAAsB,CAAC,cAAc,GAAG,CAAC,GAC3C,QAAQ,KAAK,CACX,8IACD;QACH,gBAAgB,IAAI,CAAC,KAAK,CAAC,GAAG;QAC9B,OAAO,KAAK,MAAM,gBAAgB,gBAAgB;IACpD;IACA,SAAS,aACP,IAAI,EACJ,GAAG,EACH,IAAI,EACJ,MAAM,EACN,KAAK,EACL,KAAK,EACL,UAAU,EACV,SAAS;QAET,OAAO,MAAM,GAAG;QAChB,OAAO;YACL,UAAU;YACV,MAAM;YACN,KAAK;YACL,OAAO;YACP,QAAQ;QACV;QACA,SAAS,CAAC,KAAK,MAAM,OAAO,OAAO,IAAI,IACnC,OAAO,cAAc,CAAC,MAAM,OAAO;YACjC,YAAY,CAAC;YACb,KAAK;QACP,KACA,OAAO,cAAc,CAAC,MAAM,OAAO;YAAE,YAAY,CAAC;YAAG,OAAO;QAAK;QACrE,KAAK,MAAM,GAAG,CAAC;QACf,OAAO,cAAc,CAAC,KAAK,MAAM,EAAE,aAAa;YAC9C,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,eAAe;YACzC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,cAAc,CAAC,MAAM,cAAc;YACxC,cAAc,CAAC;YACf,YAAY,CAAC;YACb,UAAU,CAAC;YACX,OAAO;QACT;QACA,OAAO,MAAM,IAAI,CAAC,OAAO,MAAM,CAAC,KAAK,KAAK,GAAG,OAAO,MAAM,CAAC,KAAK;QAChE,OAAO;IACT;IACA,SAAS,WACP,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI,EACJ,UAAU,EACV,SAAS;QAET,IAAI,WAAW,OAAO,QAAQ;QAC9B,IAAI,KAAK,MAAM,UACb,IAAI,kBACF,IAAI,YAAY,WAAW;YACzB,IACE,mBAAmB,GACnB,mBAAmB,SAAS,MAAM,EAClC,mBAEA,kBAAkB,QAAQ,CAAC,iBAAiB;YAC9C,OAAO,MAAM,IAAI,OAAO,MAAM,CAAC;QACjC,OACE,QAAQ,KAAK,CACX;aAED,kBAAkB;QACzB,IAAI,eAAe,IAAI,CAAC,QAAQ,QAAQ;YACtC,WAAW,yBAAyB;YACpC,IAAI,OAAO,OAAO,IAAI,CAAC,QAAQ,MAAM,CAAC,SAAU,CAAC;gBAC/C,OAAO,UAAU;YACnB;YACA,mBACE,IAAI,KAAK,MAAM,GACX,oBAAoB,KAAK,IAAI,CAAC,aAAa,WAC3C;YACN,qBAAqB,CAAC,WAAW,iBAAiB,IAChD,CAAC,AAAC,OACA,IAAI,KAAK,MAAM,GAAG,MAAM,KAAK,IAAI,CAAC,aAAa,WAAW,MAC5D,QAAQ,KAAK,CACX,mOACA,kBACA,UACA,MACA,WAED,qBAAqB,CAAC,WAAW,iBAAiB,GAAG,CAAC,CAAE;QAC7D;QACA,WAAW;QACX,KAAK,MAAM,YACT,CAAC,uBAAuB,WAAY,WAAW,KAAK,QAAS;QAC/D,YAAY,WACV,CAAC,uBAAuB,OAAO,GAAG,GAAI,WAAW,KAAK,OAAO,GAAG,AAAC;QACnE,IAAI,SAAS,QAAQ;YACnB,WAAW,CAAC;YACZ,IAAK,IAAI,YAAY,OACnB,UAAU,YAAY,CAAC,QAAQ,CAAC,SAAS,GAAG,MAAM,CAAC,SAAS;QAChE,OAAO,WAAW;QAClB,YACE,2BACE,UACA,eAAe,OAAO,OAClB,KAAK,WAAW,IAAI,KAAK,IAAI,IAAI,YACjC;QAER,OAAO,aACL,MACA,UACA,MACA,QACA,YACA,UACA,YACA;IAEJ;IACA,SAAS,kBAAkB,IAAI;QAC7B,aAAa,OAAO,QAClB,SAAS,QACT,KAAK,QAAQ,KAAK,sBAClB,KAAK,MAAM,IACX,CAAC,KAAK,MAAM,CAAC,SAAS,GAAG,CAAC;IAC9B;IACA,IAAI,uHACF,qBAAqB,OAAO,GAAG,CAAC,+BAChC,oBAAoB,OAAO,GAAG,CAAC,iBAC/B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC;IACnC,OAAO,GAAG,CAAC;IACX,IAAI,sBAAsB,OAAO,GAAG,CAAC,mBACnC,qBAAqB,OAAO,GAAG,CAAC,kBAChC,yBAAyB,OAAO,GAAG,CAAC,sBACpC,sBAAsB,OAAO,GAAG,CAAC,mBACjC,2BAA2B,OAAO,GAAG,CAAC,wBACtC,kBAAkB,OAAO,GAAG,CAAC,eAC7B,kBAAkB,OAAO,GAAG,CAAC,eAC7B,sBAAsB,OAAO,GAAG,CAAC,mBACjC,yBAAyB,OAAO,GAAG,CAAC,2BACpC,uBACE,MAAM,+DAA+D,EACvE,iBAAiB,OAAO,SAAS,CAAC,cAAc,EAChD,cAAc,MAAM,OAAO,EAC3B,aAAa,QAAQ,UAAU,GAC3B,QAAQ,UAAU,GAClB;QACE,OAAO;IACT;IACN,QAAQ;QACN,4BAA4B,SAAU,iBAAiB;YACrD,OAAO;QACT;IACF;IACA,IAAI;IACJ,IAAI,yBAAyB,CAAC;IAC9B,IAAI,yBAAyB,KAAK,CAAC,2BAA2B,CAAC,IAAI,CACjE,OACA;IAEF,IAAI,wBAAwB,WAAW,YAAY;IACnD,IAAI,wBAAwB,CAAC;IAC7B,QAAQ,QAAQ,GAAG;IACnB,QAAQ,MAAM,GAAG,SACf,IAAI,EACJ,MAAM,EACN,QAAQ,EACR,gBAAgB,EAChB,MAAM,EACN,IAAI;QAEJ,IAAI,mBACF,MAAM,qBAAqB,0BAA0B;QACvD,OAAO,WACL,MACA,QACA,UACA,kBACA,QACA,MACA,mBACI,MAAM,2BACN,wBACJ,mBAAmB,WAAW,YAAY,SAAS;IAEvD;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1357, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/dist/compiled/react/jsx-dev-runtime.js"], "sourcesContent": ["'use strict';\n\nif (process.env.NODE_ENV === 'production') {\n  module.exports = require('./cjs/react-jsx-dev-runtime.production.js');\n} else {\n  module.exports = require('./cjs/react-jsx-dev-runtime.development.js');\n}\n"], "names": [], "mappings": "AAEI;AAFJ;AAEA,uCAA2C;;AAE3C,OAAO;IACL,OAAO,OAAO;AAChB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1370, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/iframe/use-iframe-sdk.mjs"], "sourcesContent": ["import { use } from \"react\";\nimport { WhopIframeSdkContext } from \"./context.mjs\";\nexport function useIframeSdk() {\n    const sdk = use(WhopIframeSdkContext);\n    if (!sdk) {\n        throw new Error(\"useIframeSdk must be used within a WhopIframeSdkProvider\");\n    }\n    return sdk;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS;IACZ,MAAM,MAAM,CAAA,GAAA,6JAAA,CAAA,MAAG,AAAD,EAAE,gKAAA,CAAA,uBAAoB;IACpC,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1390, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/iframe/index.mjs"], "sourcesContent": ["export * from \"./context.mjs\";\nexport * from \"./provider.mjs\";\nexport * from \"./use-iframe-sdk.mjs\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1414, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/theme/script.mjs"], "sourcesContent": ["export function script() {\n    const cookie = document.cookie.match(/whop-frosted-theme=appearance:(?<appearance>light|dark)/)?.groups;\n    const el = document.documentElement;\n    const classes = [\n        \"light\",\n        \"dark\"\n    ];\n    const theme = cookie ? cookie.appearance : getSystemTheme();\n    function updateDOM(theme) {\n        el.classList.remove(...classes);\n        el.classList.add(theme);\n        el.style.colorScheme = theme;\n    }\n    function getSystemTheme() {\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    }\n    updateDOM(theme);\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS;IACZ,MAAM,SAAS,SAAS,MAAM,CAAC,KAAK,CAAC,4DAA4D;IACjG,MAAM,KAAK,SAAS,eAAe;IACnC,MAAM,UAAU;QACZ;QACA;KACH;IACD,MAAM,QAAQ,SAAS,OAAO,UAAU,GAAG;IAC3C,SAAS,UAAU,KAAK;QACpB,GAAG,SAAS,CAAC,MAAM,IAAI;QACvB,GAAG,SAAS,CAAC,GAAG,CAAC;QACjB,GAAG,KAAK,CAAC,WAAW,GAAG;IAC3B;IACA,SAAS;QACL,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;IAChF;IACA,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1441, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/theme/index.mjs"], "sourcesContent": ["import React from \"react\";\nimport { script } from \"./script.mjs\";\nexport function WhopThemeScript() {\n    const scriptString = `(${script.toString()})()`;\n    return React.createElement(React.Fragment, null, React.createElement(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: scriptString\n        }\n    }));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS;IACZ,MAAM,eAAe,CAAC,CAAC,EAAE,8JAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;IAC/C,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAC3E,yBAAyB;YACrB,QAAQ;QACZ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1462, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/components/whop-app.mjs"], "sourcesContent": ["import React from \"react\";\nimport { Theme } from \"./index.mjs\";\nimport { WhopIframeSdkProvider } from \"../iframe/index.mjs\";\nimport { WhopThemeScript } from \"../theme/index.mjs\";\nexport function WhopApp({ children, sdkOptions, ...themeProps }) {\n    return React.createElement(React.Fragment, null, React.createElement(WhopThemeScript, null), React.createElement(WhopIframeSdkProvider, {\n        options: sdkOptions\n    }, React.createElement(Theme, themeProps, children)));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAAA;AACA;;;;;AACO,SAAS,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,YAAY;IAC3D,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,6JAAA,CAAA,kBAAe,EAAE,OAAO,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,iKAAA,CAAA,wBAAqB,EAAE;QACpI,SAAS;IACb,GAAG,6JAAA,CAAA,UAAK,CAAC,aAAa,CAAC,wJAAA,CAAA,QAAK,EAAE,YAAY;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/components/index.mjs"], "sourcesContent": ["export * from \"frosted-ui\";\nexport { WhopApp } from \"./whop-app.mjs\";\n"], "names": [], "mappings": ";AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1505, "column": 0}, "map": {"version": 3, "file": "text-align.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/text-align.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,WAAW,GAAG;IAAC,MAAM;IAAE,QAAQ;IAAE,OAAO;CAAU,CAAC;AAEzD,MAAM,SAAS,GAAG;IAChB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,WAAW;IACnB,OAAO,EAAE,SAAS;CAC6B,CAAC", "debugId": null}}, {"offset": {"line": 1526, "column": 0}, "map": {"version": 3, "file": "color.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/color.prop.ts"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;;AAEpE,MAAM,wBAAwB,GAAG,CAAC;2KAAG,iBAAc,EAAE;2KAAG,gBAAa,CAAC,WAAW,CAAC,MAAM;CAAC,CAAC;AAC1F,MAAM,SAAS,GAAG;IAChB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,wBAAwB;IAChC,OAAO,EAAE,SAAkE;CACf,CAAC", "debugId": null}}, {"offset": {"line": 1548, "column": 0}, "map": {"version": 3, "file": "high-contrast.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/high-contrast.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,gBAAgB,GAAG;IACvB,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,SAAS;CACD,CAAC", "debugId": null}}, {"offset": {"line": 1563, "column": 0}, "map": {"version": 3, "file": "leading-trim.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/leading-trim.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,UAAU,GAAG;IAAC,QAAQ;IAAE,OAAO;IAAE,KAAK;IAAE,MAAM;CAAU,CAAC;AAE/D,MAAM,QAAQ,GAAG;IACf,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,SAAS;CAC4B,CAAC", "debugId": null}}, {"offset": {"line": 1585, "column": 0}, "map": {"version": 3, "file": "weight.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/weight.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,OAAO,GAAG;IAAC,OAAO;IAAE,SAAS;IAAE,QAAQ;IAAE,WAAW;IAAE,MAAM;CAAU,CAAC;AAE7E,MAAM,UAAU,GAAG;IACjB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,OAAO;IACf,OAAO,EAAE,SAAS;CACyB,CAAC", "debugId": null}}, {"offset": {"line": 1608, "column": 0}, "map": {"version": 3, "file": "heading.props.js", "sourceRoot": "", "sources": ["../../../../src/components/heading/heading.props.ts"], "names": [], "mappings": ";;;;;;;AACA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;AAE7F,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AAC1E,MAAM,OAAO,yLAAG,aAAU,CAAC,MAAM,CAAC;AAElC,MAAM,eAAe,GAAG;IACtB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,GAAG;IAAA,CAAE;IACnD,MAAM,EAAE;QAAE,yLAAG,aAAU;QAAE,OAAO,EAAE,MAAM;IAAA,CAAE;IAC1C,KAAK,+LAAE,YAAS;IAChB,IAAI,iMAAE,WAAQ;IACd,KAAK,uLAAE,YAAS;IAChB,YAAY,kMAAE,mBAAgB;CAQ/B,CAAC", "debugId": null}}, {"offset": {"line": 1653, "column": 0}, "map": {"version": 3, "file": "heading.js", "sourceRoot": "", "sources": ["../../../../src/components/heading/heading.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;;;;;AAgBlD,MAAM,OAAO,GAAG,CAAC,KAAmB,EAAE,EAAE;IACtC,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,GAAG,KAAK,EACf,EAAE,EAAE,GAAG,GAAG,IAAI,EACd,IAAI,gMAAG,kBAAe,CAAC,IAAI,CAAC,OAAO,EACnC,MAAM,gMAAG,kBAAe,CAAC,MAAM,CAAC,OAAO,EACvC,KAAK,gMAAG,kBAAe,CAAC,KAAK,CAAC,OAAO,EACrC,IAAI,gMAAG,kBAAe,CAAC,IAAI,CAAC,OAAO,EACnC,KAAK,gMAAG,kBAAe,CAAC,KAAK,CAAC,OAAO,EACrC,YAAY,+LAAG,mBAAe,CAAC,YAAY,CAAC,OAAO,EACnD,GAAG,YAAY,EAChB,GAAG,KAAK,CAAC;IACV,OAAO,kKACL,gBAAA,EAAC,2MAAI,CAAC,IAAI,EAAA;QAAA,qBACW,KAAK;QAAA,GACpB,YAAY;QAChB,SAAS,6IAAE,UAAA,AAAU,EACnB,aAAa,EACb,SAAS,EACT,IAAI,CAAC,CAAC,CAAC,CAAA,WAAA,EAAc,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,EACvC,MAAM,CAAC,CAAC,CAAC,CAAA,aAAA,EAAgB,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,EAC7C,KAAK,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,EACvC,IAAI,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,EACrC;YAAE,mBAAmB,EAAE,YAAY;QAAA,CAAE,CACtC;IAAA,GAEA,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,mKAAC,gBAAA,EAAC,GAAG,EAAA,MAAE,QAAQ,CAAO,CACjC,CACb,CAAC;AACJ,CAAC,CAAC;AACF,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC", "debugId": null}}, {"offset": {"line": 1683, "column": 0}, "map": {"version": 3, "file": "text.props.js", "sourceRoot": "", "sources": ["../../../../src/components/text/text.props.ts"], "names": [], "mappings": ";;;;;;;AACA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;AAE7F,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AAE1E,MAAM,YAAY,GAAG;IACnB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,SAAS;IAAA,CAAE;IACzD,MAAM,wLAAE,aAAU;IAClB,KAAK,+LAAE,YAAS;IAChB,IAAI,iMAAE,WAAQ;IACd,KAAK,uLAAE,YAAS;IAChB,YAAY,kMAAE,mBAAgB;CAQ/B,CAAC", "debugId": null}}, {"offset": {"line": 1724, "column": 0}, "map": {"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../../../src/components/text/text.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;;;;;AAyB5C,MAAM,IAAI,GAAG,CAAC,KAAgB,EAAE,EAAE;IAChC,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,GAAG,KAAK,EACf,EAAE,EAAE,GAAG,GAAG,MAAM,EAChB,IAAI,0LAAG,eAAY,CAAC,IAAI,CAAC,OAAO,EAChC,MAAM,0LAAG,eAAY,CAAC,MAAM,CAAC,OAAO,EACpC,KAAK,0LAAG,eAAY,CAAC,KAAK,CAAC,OAAO,EAClC,IAAI,0LAAG,eAAY,CAAC,IAAI,CAAC,OAAO,EAChC,KAAK,0LAAG,eAAY,CAAC,KAAK,CAAC,OAAO,EAClC,YAAY,yLAAG,gBAAY,CAAC,YAAY,CAAC,OAAO,EAChD,GAAG,SAAS,EACb,GAAG,KAAK,CAAC;IACV,OAAO,kKACL,gBAAA,EAAC,2MAAI,CAAC,IAAI,EAAA;QAAA,qBACW,KAAK;QAAA,GACpB,SAAS;QACb,SAAS,6IAAE,UAAA,AAAU,EACnB,UAAU,EACV,SAAS,EACT,IAAI,CAAC,CAAC,CAAC,CAAA,WAAA,EAAc,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,EACvC,MAAM,CAAC,CAAC,CAAC,CAAA,aAAA,EAAgB,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,EAC7C,KAAK,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,EACvC,IAAI,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,EACrC;YAAE,mBAAmB,EAAE,YAAY;QAAA,CAAE,CACtC;IAAA,GAEA,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,mKAAC,gBAAA,EAAC,GAAG,EAAA,MAAE,QAAQ,CAAO,CACjC,CACb,CAAC;AACJ,CAAC,CAAC;AACF,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC", "debugId": null}}, {"offset": {"line": 1754, "column": 0}, "map": {"version": 3, "file": "base-button.props.js", "sourceRoot": "", "sources": ["../../../../src/components/base-button/base-button.props.ts"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;;AAE5D,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AAC5C,MAAM,QAAQ,GAAG;IAAC,SAAS;IAAE,OAAO;IAAE,MAAM;IAAE,SAAS;IAAE,OAAO;CAAU,CAAC;AAE3E,MAAM,kBAAkB,GAAG;IACzB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,GAAG;IAAA,CAAE;IACnD,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,QAAQ;QAAE,OAAO,EAAE,SAAS;IAAA,CAAE;IAC/D,KAAK,uLAAE,YAAS;IAChB,YAAY,kMAAE,mBAAgB;CAM/B,CAAC", "debugId": null}}, {"offset": {"line": 1795, "column": 0}, "map": {"version": 3, "file": "map-prop-values.js", "sourceRoot": "", "sources": ["../../../src/helpers/map-prop-values.ts"], "names": [], "mappings": ";;;;AAIA,SAAS,iBAAiB,CACxB,SAAwC,EACxC,QAAkC;IAElC,IAAI,SAAS,KAAK,SAAS,EAAE,OAAO,SAAS,CAAC;IAC9C,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IACD,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAG,CAAD;YAAE,GAAG;YAAE,QAAQ,CAAC,KAAK,CAAC;SAAC,CAAC,CAAC,CAAC;AACrG,CAAC;AAED,SAAS,0BAA0B,CACjC,IAAqD;IAErD,OAAQ,IAAI,EAAE,CAAC;QACb,KAAK,GAAG;YACN,OAAO,GAAG,CAAC;QACb,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACN,OAAO,GAAG,CAAC;QACb,KAAK,GAAG;YACN,OAAO,GAAG,CAAC;IACf,CAAC;AACH,CAAC", "debugId": null}}, {"offset": {"line": 1828, "column": 0}, "map": {"version": 3, "file": "spinner.props.js", "sourceRoot": "", "sources": ["../../../../src/components/spinner/spinner.props.ts"], "names": [], "mappings": ";;;AAEA,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AAEtD,MAAM,eAAe,GAAG;IACtB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,GAAG;IAAA,CAAE;IACnD,OAAO,EAAE;QAAE,IAAI,EAAE,SAAS;QAAE,OAAO,EAAE,IAAI;IAAA,CAAE;CAI5C,CAAC", "debugId": null}}, {"offset": {"line": 1858, "column": 0}, "map": {"version": 3, "file": "spinner.js", "sourceRoot": "", "sources": ["../../../../src/components/spinner/spinner.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;;;;AAOlD,MAAM,OAAO,GAAG,CAAC,KAAmB,EAAE,EAAE;IACtC,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,gMAAG,kBAAe,CAAC,OAAO,CAAC,OAAO,EACzC,IAAI,gMAAG,kBAAe,CAAC,IAAI,CAAC,OAAO,EACnC,GAAG,YAAY,EAChB,GAAG,KAAK,CAAC;IAEV,IAAI,CAAC,OAAO,EAAE,OAAO,QAAQ,CAAC;IAE9B,MAAM,OAAO,GAAG,kKACd,gBAAA,EAAA,QAAA;QAAA,GAAU,YAAY;QAAE,SAAS,6IAAE,UAAA,AAAU,EAAC,aAAa,EAAE,CAAA,WAAA,EAAc,IAAI,EAAE,EAAE,SAAS,CAAC;IAAA,IAC3F,iLAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,oKACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,oKACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,oKACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,oKACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,GACpC,iLAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,oKACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,oKACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,CAC/B,CACR,CAAC;IAEF,IAAI,QAAQ,KAAK,SAAS,EAAE,OAAO,OAAO,CAAC;IAE3C,OAAO,kKACL,gBAAA,EAAA,QAAA;QACE,KAAK,EAAE;YACL,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,QAAQ;YACpB,cAAc,EAAE,QAAQ;SACzB;IAAA,qKAMD,gBAAA,EAAA,QAAA;QAAA,eAAA;QAAkB,KAAK,EAAE;YAAE,OAAO,EAAE,UAAU;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;QAAE,KAAK,EAAA;IAAA,GAC1E,QAAQ,CACJ,oKAEP,gBAAA,EAAA,QAAA;QACE,KAAK,EAAE;YACL,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,QAAQ;YACpB,cAAc,EAAE,QAAQ;YACxB,KAAK,EAAE,GAAG;SACX;IAAA,GAEA,OAAO,CACH,CACF,CACR,CAAC;AACJ,CAAC,CAAC;AACF,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC", "debugId": null}}, {"offset": {"line": 1934, "column": 0}, "map": {"version": 3, "file": "base-button.js", "sourceRoot": "", "sources": ["../../../../src/components/base-button/base-button.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAGzD,OAAO,EAAE,0BAA0B,EAAE,MAAM,+BAA+B,CAAC;AAC3E,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;AAQpD,MAAM,UAAU,GAAG,CAAC,KAAsB,EAAE,EAAE;IAC5C,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,GAAG,KAAK,CAAC,OAAO,EACxB,SAAS,EACT,OAAO,GAAG,KAAK,EACf,IAAI,8MAAG,qBAAkB,CAAC,IAAI,CAAC,OAAO,EACtC,OAAO,8MAAG,qBAAkB,CAAC,OAAO,CAAC,OAAO,EAC5C,KAAK,8MAAG,qBAAkB,CAAC,KAAK,CAAC,OAAO,EACxC,YAAY,8MAAG,qBAAkB,CAAC,YAAY,CAAC,OAAO,EACtD,GAAG,eAAe,EACnB,GAAG,KAAK,CAAC;IACV,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,qMAAC,OAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;IAE5C,OAAO,kKACL,gBAAA,EAAC,IAAI,EAAA;QAAA,qBACgB,KAAK,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;QAAA,GAChE,eAAe;QACnB,SAAS,EAAE,qJAAA,AAAU,EAAC,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAA,WAAA,EAAc,IAAI,EAAE,EAAE,CAAA,YAAA,EAAe,OAAO,EAAE,EAAE;YAC9G,mBAAmB,EAAE,YAAY;SAClC,CAAC;QAAA,aACS,OAAO,IAAI,SAAS;QAAA,iBAEhB,QAAQ,IAAI,SAAS;QACpC,QAAQ,EAAE,QAAQ;IAAA,GAEjB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,kKACf,gBAAA,EAAA,6JAAA,CAAA,WAAA,EAAA,wKAQE,gBAAA,EAAA,QAAA;QAAM,KAAK,EAAE;YAAE,OAAO,EAAE,UAAU;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;QAAA,eAAA;IAAA,GACvD,QAAQ,CACJ,oKACP,gBAAA,8NAAC,iBAAc,CAAC,IAAI,EAAA,MAAE,QAAQ,CAAuB,GAErD,iLAAA,EAAA,QAAA;QACE,KAAK,EAAE;YACL,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,QAAQ;YACpB,cAAc,EAAE,QAAQ;YACxB,KAAK,EAAE,GAAG;SACX;IAAA,qKAED,gBAAA,sLAAC,UAAO,EAAA;QAAC,IAAI,0LAAE,6BAAA,AAA0B,EAAC,IAAI,CAAC;IAAA,EAAI,CAC9C,CACN,CACJ,CAAC,CAAC,AACD,CADE,OACM,CACT,CACI,CACR,CAAC;AACJ,CAAC,CAAC;AACF,UAAU,CAAC,WAAW,GAAG,YAAY,CAAC", "debugId": null}}, {"offset": {"line": 1990, "column": 0}, "map": {"version": 3, "file": "button.js", "sourceRoot": "", "sources": ["../../../../src/components/button/button.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;;;;AAI5C,MAAM,MAAM,GAAG,CAAC,KAAkB,EAAE,EAAE,iKAAC,gBAAA,oMAAC,aAAU,EAAA;QAAA,GAAK,KAAK;QAAE,SAAS,6IAAE,UAAA,AAAU,EAAC,YAAY,EAAE,KAAK,CAAC,SAAS,CAAC;IAAA,EAAI,CAAC;AAEvH,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC", "debugId": null}}, {"offset": {"line": 2012, "column": 0}, "map": {"version": 3, "file": "card.props.js", "sourceRoot": "", "sources": ["../../../../src/components/card/card.props.ts"], "names": [], "mappings": ";;;AAEA,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AACjD,MAAM,QAAQ,GAAG;IAAC,SAAS;IAAE,SAAS;IAAE,OAAO;CAAU,CAAC;AAE1D,MAAM,YAAY,GAAG;IACnB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,GAAG;IAAA,CAAE;IACnD,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,QAAQ;QAAE,OAAO,EAAE,SAAS;IAAA,CAAE;CAIhE,CAAC", "debugId": null}}, {"offset": {"line": 2047, "column": 0}, "map": {"version": 3, "file": "card.js", "sourceRoot": "", "sources": ["../../../../src/components/card/card.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;;;;;AAS5C,MAAM,IAAI,GAAG,CAAC,KAAgB,EAAE,EAAE;IAChC,MAAM,EACJ,OAAO,EACP,QAAQ,EACR,SAAS,EACT,IAAI,0LAAG,eAAY,CAAC,IAAI,CAAC,OAAO,EAChC,OAAO,0LAAG,eAAY,CAAC,OAAO,CAAC,OAAO,EACtC,GAAG,SAAS,EACb,GAAG,KAAK,CAAC;IACV,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,qMAAC,OAAI,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC;IAEzC,SAAS,QAAQ;QACf,MAAM,UAAU,iKAAG,KAAK,CAAC,KAAQ,CAAC,IAAI,CAAC,QAAQ,CAAuD,CAAC;QACvG,QAAO,KAAK,CAAC,0KAAA,AAAY,EAAC,UAAU,EAAE;YACpC,QAAQ,oKAAE,gBAAA,EAAA,OAAA;gBAAK,SAAS,EAAC,eAAe;YAAA,GAAE,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAO;SAC3E,CAAC,CAAC;IACL,CAAC;IAED,OAAO,kKACL,gBAAA,EAAC,IAAI,EAAA;QAAA,GACC,SAAS;QACb,SAAS,6IAAE,UAAA,AAAU,EAAC,WAAW,EAAE,UAAU,EAAE,SAAS,EAAE,CAAA,WAAA,EAAc,IAAI,EAAE,EAAE,CAAA,YAAA,EAAe,OAAO,EAAE,CAAC;IAAA,GAExG,OAAO,CAAC,CAAC,CAAC,QAAQ,EAAE,CAAC,CAAC,mKAAC,gBAAA,EAAA,OAAA;QAAK,SAAS,EAAC,eAAe;IAAA,GAAE,QAAQ,CAAO,CAClE,CACR,CAAC;AACJ,CAAC,CAAC;AACF,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC", "debugId": null}}]}