{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/utils.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nexport type Mutable<T> = {\n  -readonly[P in keyof T]: T[P]\n};\n\nexport function mod(amount: number, numerator: number): number {\n  return amount - numerator * Math.floor(amount / numerator);\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAMM,SAAS,0CAAI,MAAc,EAAE,SAAiB;IACnD,OAAO,SAAS,YAAY,KAAK,KAAK,CAAC,SAAS;AAClD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 31, "column": 0}, "map": {"version": 3, "file": "GregorianCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/GregorianCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst EPOCH = 1721426; // 001/01/03 Julian C.E.\nexport function gregorianToJulianDay(era: string, year: number, month: number, day: number): number {\n  year = getExtendedYear(era, year);\n\n  let y1 = year - 1;\n  let monthOffset = -2;\n  if (month <= 2) {\n    monthOffset = 0;\n  } else if (isLeapYear(year)) {\n    monthOffset = -1;\n  }\n\n  return (\n    EPOCH -\n    1 +\n    365 * y1 +\n    Math.floor(y1 / 4) -\n    Math.floor(y1 / 100) +\n    Math.floor(y1 / 400) +\n    Math.floor((367 * month - 362) / 12 + monthOffset + day)\n  );\n}\n\nexport function isLeapYear(year: number): boolean {\n  return year % 4 === 0 && (year % 100 !== 0 || year % 400 === 0);\n}\n\nexport function getExtendedYear(era: string, year: number): number {\n  return era === 'BC' ? 1 - year : year;\n}\n\nexport function fromExtendedYear(year: number): [string, number] {\n  let era = 'AD';\n  if (year <= 0) {\n    era = 'BC';\n    year = 1 - year;\n  }\n\n  return [era, year];\n}\n\nconst daysInMonth = {\n  standard: [31, 28, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31],\n  leapyear: [31, 29, 31, 30, 31, 30, 31, 31, 30, 31, 30, 31]\n};\n\n/**\n * The Gregorian calendar is the most commonly used calendar system in the world. It supports two eras: BC, and AD.\n * Years always contain 12 months, and 365 or 366 days depending on whether it is a leap year.\n */\nexport class GregorianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'gregory';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let jd0 = jd;\n    let depoch = jd0 - EPOCH;\n    let quadricent = Math.floor(depoch / 146097);\n    let dqc = mod(depoch, 146097);\n    let cent = Math.floor(dqc / 36524);\n    let dcent = mod(dqc, 36524);\n    let quad = Math.floor(dcent / 1461);\n    let dquad = mod(dcent, 1461);\n    let yindex = Math.floor(dquad / 365);\n\n    let extendedYear = quadricent * 400 + cent * 100 + quad * 4 + yindex + (cent !== 4 && yindex !== 4 ? 1 : 0);\n    let [era, year] = fromExtendedYear(extendedYear);\n    let yearDay = jd0 - gregorianToJulianDay(era, year, 1, 1);\n    let leapAdj = 2;\n    if (jd0 < gregorianToJulianDay(era, year, 3, 1)) {\n      leapAdj = 0;\n    } else if (isLeapYear(year)) {\n      leapAdj = 1;\n    }\n    let month = Math.floor(((yearDay + leapAdj) * 12 + 373) / 367);\n    let day = jd0 - gregorianToJulianDay(era, year, month, 1) + 1;\n\n    return new CalendarDate(era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return gregorianToJulianDay(date.era, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return daysInMonth[isLeapYear(date.year) ? 'leapyear' : 'standard'][date.month - 1];\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 366 : 365;\n  }\n\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  getYearsInEra(date: AnyCalendarDate): number {\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['BC', 'AD'];\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BC';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    if (date.year <= 0) {\n      date.era = date.era === 'BC' ? 'AD' : 'BC';\n      date.year = 1 - date.year;\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAMhG,MAAM,8BAAQ,SAAS,wBAAwB;AACxC,SAAS,0CAAqB,GAAW,EAAE,IAAY,EAAE,KAAa,EAAE,GAAW;IACxF,OAAO,0CAAgB,KAAK;IAE5B,IAAI,KAAK,OAAO;IAChB,IAAI,cAAc,CAAA;IAClB,IAAI,SAAS,GACX,cAAc;SACT,IAAI,0CAAW,OACpB,cAAc,CAAA;IAGhB,OACE,8BACA,IACA,MAAM,KACN,KAAK,KAAK,CAAC,KAAK,KAChB,KAAK,KAAK,CAAC,KAAK,OAChB,KAAK,KAAK,CAAC,KAAK,OAChB,KAAK,KAAK,CAAE,CAAA,MAAM,QAAQ,GAAE,IAAK,KAAK,cAAc;AAExD;AAEO,SAAS,0CAAW,IAAY;IACrC,OAAO,OAAO,MAAM,KAAM,CAAA,OAAO,QAAQ,KAAK,OAAO,QAAQ,CAAA;AAC/D;AAEO,SAAS,0CAAgB,GAAW,EAAE,IAAY;IACvD,OAAO,QAAQ,OAAO,IAAI,OAAO;AACnC;AAEO,SAAS,0CAAiB,IAAY;IAC3C,IAAI,MAAM;IACV,IAAI,QAAQ,GAAG;QACb,MAAM;QACN,OAAO,IAAI;IACb;IAEA,OAAO;QAAC;QAAK;KAAK;AACpB;AAEA,MAAM,oCAAc;IAClB,UAAU;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;IAC1D,UAAU;QAAC;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;QAAI;KAAG;AAC5D;AAMO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,MAAM;QACV,IAAI,SAAS,MAAM;QACnB,IAAI,aAAa,KAAK,KAAK,CAAC,SAAS;QACrC,IAAI,MAAM,CAAA,oKAAA,MAAE,EAAE,QAAQ;QACtB,IAAI,OAAO,KAAK,KAAK,CAAC,MAAM;QAC5B,IAAI,QAAQ,CAAA,oKAAA,MAAE,EAAE,KAAK;QACrB,IAAI,OAAO,KAAK,KAAK,CAAC,QAAQ;QAC9B,IAAI,QAAQ,CAAA,oKAAA,MAAE,EAAE,OAAO;QACvB,IAAI,SAAS,KAAK,KAAK,CAAC,QAAQ;QAEhC,IAAI,eAAe,aAAa,MAAM,OAAO,MAAM,OAAO,IAAI,SAAU,CAAA,SAAS,KAAK,WAAW,IAAI,IAAI,CAAA;QACzG,IAAI,CAAC,KAAK,KAAK,GAAG,0CAAiB;QACnC,IAAI,UAAU,MAAM,0CAAqB,KAAK,MAAM,GAAG;QACvD,IAAI,UAAU;QACd,IAAI,MAAM,0CAAqB,KAAK,MAAM,GAAG,IAC3C,UAAU;aACL,IAAI,0CAAW,OACpB,UAAU;QAEZ,IAAI,QAAQ,KAAK,KAAK,CAAE,CAAC,CAAA,UAAU,OAAM,IAAK,KAAK,GAAE,IAAK;QAC1D,IAAI,MAAM,MAAM,0CAAqB,KAAK,MAAM,OAAO,KAAK;QAE5D,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,KAAK,MAAM,OAAO;IAC5C;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,0CAAqB,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IACvE;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,iCAAW,CAAC,0CAAW,KAAK,IAAI,IAAI,aAAa,WAAW,CAAC,KAAK,KAAK,GAAG,EAAE;IACrF;IAEA,6DAA6D;IAC7D,gBAAgB,IAAqB,EAAU;QAC7C,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,0CAAW,KAAK,IAAI,IAAI,MAAM;IACvC;IAEA,6DAA6D;IAC7D,cAAc,IAAqB,EAAU;QAC3C,OAAO;IACT;IAEA,UAAoB;QAClB,OAAO;YAAC;YAAM;SAAK;IACrB;IAEA,aAAa,IAAqB,EAAW;QAC3C,OAAO,KAAK,GAAG,KAAK;IACtB;IAEA,YAAY,IAA8B,EAAQ;QAChD,IAAI,KAAK,IAAI,IAAI,GAAG;YAClB,KAAK,GAAG,GAAG,KAAK,GAAG,KAAK,OAAO,OAAO;YACtC,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI;QAC3B;IACF;;aA/DA,UAAA,GAAiC;;AAgEnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 175, "column": 0}, "map": {"version": 3, "file": "weekStartData.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/weekStartData.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Data from https://github.com/unicode-cldr/cldr-core/blob/master/supplemental/weekData.json\n// Locales starting on Sunday have been removed for compression.\nexport const weekStartData = {\n  '001': 1,\n  AD: 1,\n  AE: 6,\n  AF: 6,\n  AI: 1,\n  AL: 1,\n  AM: 1,\n  AN: 1,\n  AR: 1,\n  AT: 1,\n  AU: 1,\n  AX: 1,\n  AZ: 1,\n  BA: 1,\n  BE: 1,\n  BG: 1,\n  BH: 6,\n  BM: 1,\n  BN: 1,\n  BY: 1,\n  CH: 1,\n  CL: 1,\n  CM: 1,\n  CN: 1,\n  CR: 1,\n  CY: 1,\n  CZ: 1,\n  DE: 1,\n  DJ: 6,\n  DK: 1,\n  DZ: 6,\n  EC: 1,\n  EE: 1,\n  EG: 6,\n  ES: 1,\n  FI: 1,\n  FJ: 1,\n  FO: 1,\n  FR: 1,\n  GB: 1,\n  GE: 1,\n  GF: 1,\n  GP: 1,\n  GR: 1,\n  HR: 1,\n  HU: 1,\n  IE: 1,\n  IQ: 6,\n  IR: 6,\n  IS: 1,\n  IT: 1,\n  JO: 6,\n  KG: 1,\n  KW: 6,\n  KZ: 1,\n  LB: 1,\n  LI: 1,\n  LK: 1,\n  LT: 1,\n  LU: 1,\n  LV: 1,\n  LY: 6,\n  MC: 1,\n  MD: 1,\n  ME: 1,\n  MK: 1,\n  MN: 1,\n  MQ: 1,\n  MV: 5,\n  MY: 1,\n  NL: 1,\n  NO: 1,\n  NZ: 1,\n  OM: 6,\n  PL: 1,\n  QA: 6,\n  RE: 1,\n  RO: 1,\n  RS: 1,\n  RU: 1,\n  SD: 6,\n  SE: 1,\n  SI: 1,\n  SK: 1,\n  SM: 1,\n  SY: 6,\n  TJ: 1,\n  TM: 1,\n  TR: 1,\n  UA: 1,\n  UY: 1,\n  UZ: 1,\n  VA: 1,\n  VN: 1,\n  XK: 1\n};\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC,GAED,6FAA6F;AAC7F,gEAAgE;;;;AACzD,MAAM,4CAAgB;IAC3B,OAAO;IACP,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;I<PERSON><PERSON>,IAAI;<PERSON><PERSON><PERSON>,IAAI;IACJ,IAAI;I<PERSON><PERSON>,IAA<PERSON>;I<PERSON><PERSON>,IAAI;IACJ,IAA<PERSON>;IACJ,IAA<PERSON>;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;AACN", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 295, "column": 0}, "map": {"version": 3, "file": "queries.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/queries.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyTime, Calendar} from './types';\nimport {CalendarDate, CalendarDateTime, ZonedDateTime} from './CalendarDate';\nimport {fromAbsolute, toAbsolute, toCalendar, toCalendarDate} from './conversion';\nimport {weekStartData} from './weekStartData';\n\ntype DateValue = CalendarDate | CalendarDateTime | ZonedDateTime;\n\n/** Returns whether the given dates occur on the same day, regardless of the time or calendar system. */\nexport function isSameDay(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  return a.era === b.era && a.year === b.year && a.month === b.month && a.day === b.day;\n}\n\n/** Returns whether the given dates occur in the same month, using the calendar system of the first date. */\nexport function isSameMonth(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  // In the Japanese calendar, months can span multiple eras/years, so only compare the first of the month.\n  a = startOfMonth(a);\n  b = startOfMonth(b);\n  return a.era === b.era && a.year === b.year && a.month === b.month;\n}\n\n/** Returns whether the given dates occur in the same year, using the calendar system of the first date. */\nexport function isSameYear(a: DateValue, b: DateValue): boolean {\n  b = toCalendar(b, a.calendar);\n  a = startOfYear(a);\n  b = startOfYear(b);\n  return a.era === b.era && a.year === b.year;\n}\n\n/** Returns whether the given dates occur on the same day, and are of the same calendar system. */\nexport function isEqualDay(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameDay(a, b);\n}\n\n/** Returns whether the given dates occur in the same month, and are of the same calendar system. */\nexport function isEqualMonth(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameMonth(a, b);\n}\n\n/** Returns whether the given dates occur in the same year, and are of the same calendar system. */\nexport function isEqualYear(a: DateValue, b: DateValue): boolean {\n  return isEqualCalendar(a.calendar, b.calendar) && isSameYear(a, b);\n}\n\n/** Returns whether two calendars are the same. */\nexport function isEqualCalendar(a: Calendar, b: Calendar): boolean {\n  return a.isEqual?.(b) ?? b.isEqual?.(a) ?? a.identifier === b.identifier;\n}\n\n/** Returns whether the date is today in the given time zone. */\nexport function isToday(date: DateValue, timeZone: string): boolean {\n  return isSameDay(date, today(timeZone));\n}\n\nconst DAY_MAP = {\n  sun: 0,\n  mon: 1,\n  tue: 2,\n  wed: 3,\n  thu: 4,\n  fri: 5,\n  sat: 6\n};\n\ntype DayOfWeek = 'sun' | 'mon' | 'tue' | 'wed' | 'thu' | 'fri' | 'sat';\n\n/**\n * Returns the day of week for the given date and locale. Days are numbered from zero to six,\n * where zero is the first day of the week in the given locale. For example, in the United States,\n * the first day of the week is Sunday, but in France it is Monday.\n */\nexport function getDayOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let weekStart = firstDayOfWeek ? DAY_MAP[firstDayOfWeek] : getWeekStart(locale);\n  let dayOfWeek = Math.ceil(julian + 1 - weekStart) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  return dayOfWeek;\n}\n\n/** Returns the current time in the given time zone. */\nexport function now(timeZone: string): ZonedDateTime {\n  return fromAbsolute(Date.now(), timeZone);\n}\n\n/** Returns today's date in the given time zone. */\nexport function today(timeZone: string): CalendarDate {\n  return toCalendarDate(now(timeZone));\n}\n\nexport function compareDate(a: AnyCalendarDate, b: AnyCalendarDate): number {\n  return a.calendar.toJulianDay(a) - b.calendar.toJulianDay(b);\n}\n\nexport function compareTime(a: AnyTime, b: AnyTime): number {\n  return timeToMs(a) - timeToMs(b);\n}\n\nfunction timeToMs(a: AnyTime): number {\n  return a.hour * 60 * 60 * 1000 + a.minute * 60 * 1000 + a.second * 1000 + a.millisecond;\n}\n\n/**\n * Returns the number of hours in the given date and time zone.\n * Usually this is 24, but it could be 23 or 25 if the date is on a daylight saving transition.\n */\nexport function getHoursInDay(a: CalendarDate, timeZone: string): number {\n  let ms = toAbsolute(a, timeZone);\n  let tomorrow = a.add({days: 1});\n  let tomorrowMs = toAbsolute(tomorrow, timeZone);\n  return (tomorrowMs - ms) / 3600000;\n}\n\nlet localTimeZone: string | null = null;\n\n/** Returns the time zone identifier for the current user. */\nexport function getLocalTimeZone(): string {\n  if (localTimeZone == null) {\n    localTimeZone = new Intl.DateTimeFormat().resolvedOptions().timeZone;\n  }\n\n  return localTimeZone!;\n}\n\n/** Sets the time zone identifier for the current user. */\nexport function setLocalTimeZone(timeZone: string): void {\n  localTimeZone = timeZone;\n}\n\n/** Resets the time zone identifier for the current user. */\nexport function resetLocalTimeZone(): void {\n  localTimeZone = null;\n}\n\n/** Returns the first date of the month for the given date. */\nexport function startOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function startOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function startOfMonth(date: CalendarDate): CalendarDate;\nexport function startOfMonth(date: DateValue): DateValue;\nexport function startOfMonth(date: DateValue): DateValue {\n  // Use `subtract` instead of `set` so we don't get constrained in an era.\n  return date.subtract({days: date.day - 1});\n}\n\n/** Returns the last date of the month for the given date. */\nexport function endOfMonth(date: ZonedDateTime): ZonedDateTime;\nexport function endOfMonth(date: CalendarDateTime): CalendarDateTime;\nexport function endOfMonth(date: CalendarDate): CalendarDate;\nexport function endOfMonth(date: DateValue): DateValue;\nexport function endOfMonth(date: DateValue): DateValue {\n  return date.add({days: date.calendar.getDaysInMonth(date) - date.day});\n}\n\n/** Returns the first day of the year for the given date. */\nexport function startOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function startOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function startOfYear(date: CalendarDate): CalendarDate;\nexport function startOfYear(date: DateValue): DateValue;\nexport function startOfYear(date: DateValue): DateValue {\n  return startOfMonth(date.subtract({months: date.month - 1}));\n}\n\n/** Returns the last day of the year for the given date. */\nexport function endOfYear(date: ZonedDateTime): ZonedDateTime;\nexport function endOfYear(date: CalendarDateTime): CalendarDateTime;\nexport function endOfYear(date: CalendarDate): CalendarDate;\nexport function endOfYear(date: DateValue): DateValue;\nexport function endOfYear(date: DateValue): DateValue {\n  return endOfMonth(date.add({months: date.calendar.getMonthsInYear(date) - date.month}));\n}\n\nexport function getMinimumMonthInYear(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumMonthInYear) {\n    return date.calendar.getMinimumMonthInYear(date);\n  }\n\n  return 1;\n}\n\nexport function getMinimumDayInMonth(date: AnyCalendarDate): number {\n  if (date.calendar.getMinimumDayInMonth) {\n    return date.calendar.getMinimumDayInMonth(date);\n  }\n\n  return 1;\n}\n\n/** Returns the first date of the week for the given date and locale. */\nexport function startOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function startOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function startOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function startOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  let dayOfWeek = getDayOfWeek(date, locale, firstDayOfWeek);\n  return date.subtract({days: dayOfWeek});\n}\n\n/** Returns the last date of the week for the given date and locale. */\nexport function endOfWeek(date: ZonedDateTime, locale: string, firstDayOfWeek?: DayOfWeek): ZonedDateTime;\nexport function endOfWeek(date: CalendarDateTime, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDateTime;\nexport function endOfWeek(date: CalendarDate, locale: string, firstDayOfWeek?: DayOfWeek): CalendarDate;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue;\nexport function endOfWeek(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): DateValue {\n  return startOfWeek(date, locale, firstDayOfWeek).add({days: 6});\n}\n\nconst cachedRegions = new Map<string, string>();\n\nfunction getRegion(locale: string): string | undefined {\n  // If the Intl.Locale API is available, use it to get the region for the locale.\n  // @ts-ignore\n  if (Intl.Locale) {\n    // Constructing an Intl.Locale is expensive, so cache the result.\n    let region = cachedRegions.get(locale);\n    if (!region) {\n      // @ts-ignore\n      region = new Intl.Locale(locale).maximize().region;\n      if (region) {\n        cachedRegions.set(locale, region);\n      }\n    }\n    return region;\n  }\n\n  // If not, just try splitting the string.\n  // If the second part of the locale string is 'u',\n  // then this is a unicode extension, so ignore it.\n  // Otherwise, it should be the region.\n  let part = locale.split('-')[1];\n  return part === 'u' ? undefined : part;\n}\n\nfunction getWeekStart(locale: string): number {\n  // TODO: use Intl.Locale for this once browsers support the weekInfo property\n  // https://github.com/tc39/proposal-intl-locale-info\n  let region = getRegion(locale);\n  return region ? weekStartData[region] || 0 : 0;\n}\n\n/** Returns the number of weeks in the given month and locale. */\nexport function getWeeksInMonth(date: DateValue, locale: string, firstDayOfWeek?: DayOfWeek): number {\n  let days = date.calendar.getDaysInMonth(date);\n  return Math.ceil((getDayOfWeek(startOfMonth(date), locale, firstDayOfWeek) + days) / 7);\n}\n\n/** Returns the lesser of the two provider dates. */\nexport function minDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) <= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\n/** Returns the greater of the two provider dates. */\nexport function maxDate<A extends DateValue, B extends DateValue>(a?: A | null, b?: B | null): A | B | null | undefined {\n  if (a && b) {\n    return a.compare(b) >= 0 ? a : b;\n  }\n\n  return a || b;\n}\n\nconst WEEKEND_DATA = {\n  AF: [4, 5],\n  AE: [5, 6],\n  BH: [5, 6],\n  DZ: [5, 6],\n  EG: [5, 6],\n  IL: [5, 6],\n  IQ: [5, 6],\n  IR: [5, 5],\n  JO: [5, 6],\n  KW: [5, 6],\n  LY: [5, 6],\n  OM: [5, 6],\n  QA: [5, 6],\n  SA: [5, 6],\n  SD: [5, 6],\n  SY: [5, 6],\n  YE: [5, 6]\n};\n\n/** Returns whether the given date is on a weekend in the given locale. */\nexport function isWeekend(date: DateValue, locale: string): boolean {\n  let julian = date.calendar.toJulianDay(date);\n\n  // If julian is negative, then julian % 7 will be negative, so we adjust\n  // accordingly.  Julian day 0 is Monday.\n  let dayOfWeek = Math.ceil(julian + 1) % 7;\n  if (dayOfWeek < 0) {\n    dayOfWeek += 7;\n  }\n\n  let region = getRegion(locale);\n  // Use Intl.Locale for this once weekInfo is supported.\n  // https://github.com/tc39/proposal-intl-locale-info\n  let [start, end] = WEEKEND_DATA[region!] || [6, 0];\n  return dayOfWeek === start || dayOfWeek === end;\n}\n\n/** Returns whether the given date is on a weekday in the given locale. */\nexport function isWeekday(date: DateValue, locale: string): boolean {\n  return !isWeekend(date, locale);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAUM,SAAS,0CAAU,CAAY,EAAE,CAAY;IAClD,IAAI,CAAA,yKAAA,aAAS,EAAE,GAAG,EAAE,QAAQ;IAC5B,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,IAAI,EAAE,GAAG,KAAK,EAAE,GAAG;AACvF;AAGO,SAAS,0CAAY,CAAY,EAAE,CAAY;IACpD,IAAI,CAAA,yKAAA,aAAS,EAAE,GAAG,EAAE,QAAQ;IAC5B,yGAAyG;IACzG,IAAI,0CAAa;IACjB,IAAI,0CAAa;IACjB,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK;AACpE;AAGO,SAAS,0CAAW,CAAY,EAAE,CAAY;IACnD,IAAI,CAAA,yKAAA,aAAS,EAAE,GAAG,EAAE,QAAQ;IAC5B,IAAI,0CAAY;IAChB,IAAI,0CAAY;IAChB,OAAO,EAAE,GAAG,KAAK,EAAE,GAAG,IAAI,EAAE,IAAI,KAAK,EAAE,IAAI;AAC7C;AAGO,SAAS,0CAAW,CAAY,EAAE,CAAY;IACnD,OAAO,yCAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ,KAAK,0CAAU,GAAG;AACjE;AAGO,SAAS,0CAAa,CAAY,EAAE,CAAY;IACrD,OAAO,yCAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ,KAAK,0CAAY,GAAG;AACnE;AAGO,SAAS,0CAAY,CAAY,EAAE,CAAY;IACpD,OAAO,yCAAgB,EAAE,QAAQ,EAAE,EAAE,QAAQ,KAAK,0CAAW,GAAG;AAClE;AAGO,SAAS,yCAAgB,CAAW,EAAE,CAAW;QAC/C,YAAkB;QAAlB,aAAA;IAAP,OAAO,CAAA,OAAA,CAAA,cAAA,CAAA,aAAA,EAAE,OAAO,MAAA,QAAT,eAAA,KAAA,IAAA,KAAA,IAAA,WAAA,IAAA,CAAA,GAAY,EAAA,MAAA,QAAZ,gBAAA,KAAA,IAAA,cAAA,CAAkB,aAAA,EAAE,OAAO,MAAA,QAAT,eAAA,KAAA,IAAA,KAAA,IAAA,WAAA,IAAA,CAAA,GAAY,EAAA,MAAA,QAA9B,SAAA,KAAA,IAAA,OAAoC,EAAE,UAAU,KAAK,EAAE,UAAU;AAC1E;AAGO,SAAS,0CAAQ,IAAe,EAAE,QAAgB;IACvD,OAAO,0CAAU,MAAM,0CAAM;AAC/B;AAEA,MAAM,gCAAU;IACd,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACP;AASO,SAAS,0CAAa,IAAe,EAAE,MAAc,EAAE,cAA0B;IACtF,IAAI,SAAS,KAAK,QAAQ,CAAC,WAAW,CAAC;IAEvC,wEAAwE;IACxE,wCAAwC;IACxC,IAAI,YAAY,iBAAiB,6BAAO,CAAC,eAAe,GAAG,mCAAa;IACxE,IAAI,YAAY,KAAK,IAAI,CAAC,SAAS,IAAI,aAAa;IACpD,IAAI,YAAY,GACd,aAAa;IAGf,OAAO;AACT;AAGO,SAAS,yCAAI,QAAgB;IAClC,OAAO,CAAA,yKAAA,eAAW,EAAE,KAAK,GAAG,IAAI;AAClC;AAGO,SAAS,0CAAM,QAAgB;IACpC,OAAO,CAAA,yKAAA,iBAAa,EAAE,yCAAI;AAC5B;AAEO,SAAS,0CAAY,CAAkB,EAAE,CAAkB;IAChE,OAAO,EAAE,QAAQ,CAAC,WAAW,CAAC,KAAK,EAAE,QAAQ,CAAC,WAAW,CAAC;AAC5D;AAEO,SAAS,0CAAY,CAAU,EAAE,CAAU;IAChD,OAAO,+BAAS,KAAK,+BAAS;AAChC;AAEA,SAAS,+BAAS,CAAU;IAC1B,OAAO,EAAE,IAAI,GAAN,UAA0B,EAAE,MAAM,GAAR,QAAuB,EAAE,MAAM,GAAG,OAAO,EAAE,WAAW;AACzF;AAMO,SAAS,wCAAc,CAAe,EAAE,QAAgB;IAC7D,IAAI,KAAK,CAAA,yKAAA,aAAS,EAAE,GAAG;IACvB,IAAI,WAAW,EAAE,GAAG,CAAC;QAAC,MAAM;IAAC;IAC7B,IAAI,aAAa,CAAA,yKAAA,aAAS,EAAE,UAAU;IACtC,OAAQ,CAAA,aAAa,EAAC,IAAK;AAC7B;AAEA,IAAI,sCAA+B;AAG5B,SAAS;IACd,IAAI,uCAAiB,MACnB,sCAAgB,IAAI,KAAK,cAAc,GAAG,eAAe,GAAG,QAAQ;IAGtE,OAAO;AACT;AAGO,SAAS,0CAAiB,QAAgB;IAC/C,sCAAgB;AAClB;AAGO,SAAS;IACd,sCAAgB;AAClB;AAOO,SAAS,0CAAa,IAAe;IAC1C,yEAAyE;IACzE,OAAO,KAAK,QAAQ,CAAC;QAAC,MAAM,KAAK,GAAG,GAAG;IAAC;AAC1C;AAOO,SAAS,0CAAW,IAAe;IACxC,OAAO,KAAK,GAAG,CAAC;QAAC,MAAM,KAAK,QAAQ,CAAC,cAAc,CAAC,QAAQ,KAAK,GAAG;IAAA;AACtE;AAOO,SAAS,0CAAY,IAAe;IACzC,OAAO,0CAAa,KAAK,QAAQ,CAAC;QAAC,QAAQ,KAAK,KAAK,GAAG;IAAC;AAC3D;AAOO,SAAS,0CAAU,IAAe;IACvC,OAAO,0CAAW,KAAK,GAAG,CAAC;QAAC,QAAQ,KAAK,QAAQ,CAAC,eAAe,CAAC,QAAQ,KAAK,KAAK;IAAA;AACtF;AAEO,SAAS,0CAAsB,IAAqB;IACzD,IAAI,KAAK,QAAQ,CAAC,qBAAqB,EACrC,OAAO,KAAK,QAAQ,CAAC,qBAAqB,CAAC;IAG7C,OAAO;AACT;AAEO,SAAS,0CAAqB,IAAqB;IACxD,IAAI,KAAK,QAAQ,CAAC,oBAAoB,EACpC,OAAO,KAAK,QAAQ,CAAC,oBAAoB,CAAC;IAG5C,OAAO;AACT;AAOO,SAAS,0CAAY,IAAe,EAAE,MAAc,EAAE,cAA0B;IACrF,IAAI,YAAY,0CAAa,MAAM,QAAQ;IAC3C,OAAO,KAAK,QAAQ,CAAC;QAAC,MAAM;IAAS;AACvC;AAOO,SAAS,0CAAU,IAAe,EAAE,MAAc,EAAE,cAA0B;IACnF,OAAO,0CAAY,MAAM,QAAQ,gBAAgB,GAAG,CAAC;QAAC,MAAM;IAAC;AAC/D;AAEA,MAAM,sCAAgB,IAAI;AAE1B,SAAS,gCAAU,MAAc;IAC/B,gFAAgF;IAChF,aAAa;IACb,IAAI,KAAK,MAAM,EAAE;QACf,iEAAiE;QACjE,IAAI,SAAS,oCAAc,GAAG,CAAC;QAC/B,IAAI,CAAC,QAAQ;YACX,aAAa;YACb,SAAS,IAAI,KAAK,MAAM,CAAC,QAAQ,QAAQ,GAAG,MAAM;YAClD,IAAI,QACF,oCAAc,GAAG,CAAC,QAAQ;QAE9B;QACA,OAAO;IACT;IAEA,yCAAyC;IACzC,kDAAkD;IAClD,kDAAkD;IAClD,sCAAsC;IACtC,IAAI,OAAO,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE;IAC/B,OAAO,SAAS,MAAM,YAAY;AACpC;AAEA,SAAS,mCAAa,MAAc;IAClC,6EAA6E;IAC7E,oDAAoD;IACpD,IAAI,SAAS,gCAAU;IACvB,OAAO,SAAS,CAAA,4KAAA,gBAAY,CAAC,CAAC,OAAO,IAAI,IAAI;AAC/C;AAGO,SAAS,0CAAgB,IAAe,EAAE,MAAc,EAAE,cAA0B;IACzF,IAAI,OAAO,KAAK,QAAQ,CAAC,cAAc,CAAC;IACxC,OAAO,KAAK,IAAI,CAAE,CAAA,0CAAa,0CAAa,OAAO,QAAQ,kBAAkB,IAAG,IAAK;AACvF;AAGO,SAAS,0CAAkD,CAAY,EAAE,CAAY;IAC1F,IAAI,KAAK,GACP,OAAO,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI;IAGjC,OAAO,KAAK;AACd;AAGO,SAAS,0CAAkD,CAAY,EAAE,CAAY;IAC1F,IAAI,KAAK,GACP,OAAO,EAAE,OAAO,CAAC,MAAM,IAAI,IAAI;IAGjC,OAAO,KAAK;AACd;AAEA,MAAM,qCAAe;IACnB,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;IACV,IAAI;QAAC;QAAG;KAAE;AACZ;AAGO,SAAS,yCAAU,IAAe,EAAE,MAAc;IACvD,IAAI,SAAS,KAAK,QAAQ,CAAC,WAAW,CAAC;IAEvC,wEAAwE;IACxE,wCAAwC;IACxC,IAAI,YAAY,KAAK,IAAI,CAAC,SAAS,KAAK;IACxC,IAAI,YAAY,GACd,aAAa;IAGf,IAAI,SAAS,gCAAU;IACvB,uDAAuD;IACvD,oDAAoD;IACpD,IAAI,CAAC,OAAO,IAAI,GAAG,kCAAY,CAAC,OAAQ,IAAI;QAAC;QAAG;KAAE;IAClD,OAAO,cAAc,SAAS,cAAc;AAC9C;AAGO,SAAS,0CAAU,IAAe,EAAE,MAAc;IACvD,OAAO,CAAC,yCAAU,MAAM;AAC1B", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 602, "column": 0}, "map": {"version": 3, "file": "conversion.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/conversion.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, Calendar, DateFields, Disambiguation, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {constrain} from './manipulation';\nimport {getExtendedYear, GregorianCalendar} from './calendars/GregorianCalendar';\nimport {getLocalTimeZone, isEqualCalendar} from './queries';\nimport {Mutable} from './utils';\n\nexport function epochFromDate(date: AnyDateTime): number {\n  date = toCalendar(date, new GregorianCalendar());\n  let year = getExtendedYear(date.era, date.year);\n  return epochFromParts(year, date.month, date.day, date.hour, date.minute, date.second, date.millisecond);\n}\n\nfunction epochFromParts(year: number, month: number, day: number, hour: number, minute: number, second: number, millisecond: number): number {\n  // Note: Date.UTC() interprets one and two-digit years as being in the\n  // 20th century, so don't use it\n  let date = new Date();\n  date.setUTCHours(hour, minute, second, millisecond);\n  date.setUTCFullYear(year, month - 1, day);\n  return date.getTime();\n}\n\nexport function getTimeZoneOffset(ms: number, timeZone: string): number {\n  // Fast path for UTC.\n  if (timeZone === 'UTC') {\n    return 0;\n  }\n\n  // Fast path: for local timezone after 1970, use native Date.\n  if (ms > 0 && timeZone === getLocalTimeZone()) {\n    return new Date(ms).getTimezoneOffset() * -60 * 1000;\n  }\n\n  let {year, month, day, hour, minute, second} = getTimeZoneParts(ms, timeZone);\n  let utc = epochFromParts(year, month, day, hour, minute, second, 0);\n  return utc - Math.floor(ms / 1000) * 1000;\n}\n\nconst formattersByTimeZone = new Map<string, Intl.DateTimeFormat>();\n\nfunction getTimeZoneParts(ms: number, timeZone: string) {\n  let formatter = formattersByTimeZone.get(timeZone);\n  if (!formatter) {\n    formatter = new Intl.DateTimeFormat('en-US', {\n      timeZone,\n      hour12: false,\n      era: 'short',\n      year: 'numeric',\n      month: 'numeric',\n      day: 'numeric',\n      hour: 'numeric',\n      minute: 'numeric',\n      second: 'numeric'\n    });\n\n    formattersByTimeZone.set(timeZone, formatter);\n  }\n\n  let parts = formatter.formatToParts(new Date(ms));\n  let namedParts: {[name: string]: string} = {};\n  for (let part of parts) {\n    if (part.type !== 'literal') {\n      namedParts[part.type] = part.value;\n    }\n  }\n\n\n  return {\n    // Firefox returns B instead of BC... https://bugzilla.mozilla.org/show_bug.cgi?id=1752253\n    year: namedParts.era === 'BC' || namedParts.era === 'B' ? -namedParts.year + 1 : +namedParts.year,\n    month: +namedParts.month,\n    day: +namedParts.day,\n    hour: namedParts.hour === '24' ? 0 : +namedParts.hour, // bugs.chromium.org/p/chromium/issues/detail?id=1045791\n    minute: +namedParts.minute,\n    second: +namedParts.second\n  };\n}\n\nconst DAYMILLIS = 86400000;\n\nexport function possibleAbsolutes(date: CalendarDateTime, timeZone: string): number[] {\n  let ms = epochFromDate(date);\n  let earlier = ms - getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let later = ms - getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  return getValidWallTimes(date, timeZone, earlier, later);\n}\n\nfunction getValidWallTimes(date: CalendarDateTime, timeZone: string, earlier: number, later: number): number[] {\n  let found = earlier === later ? [earlier] : [earlier, later];\n  return found.filter(absolute => isValidWallTime(date, timeZone, absolute));\n}\n\nfunction isValidWallTime(date: CalendarDateTime, timeZone: string, absolute: number) {\n  let parts = getTimeZoneParts(absolute, timeZone);\n  return date.year === parts.year\n    && date.month === parts.month\n    && date.day === parts.day\n    && date.hour === parts.hour\n    && date.minute === parts.minute\n    && date.second === parts.second;\n}\n\nexport function toAbsolute(date: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): number {\n  let dateTime = toCalendarDateTime(date);\n\n  // Fast path: if the time zone is UTC, use native Date.\n  if (timeZone === 'UTC') {\n    return epochFromDate(dateTime);\n  }\n\n  // Fast path: if the time zone is the local timezone and disambiguation is compatible, use native Date.\n  if (timeZone === getLocalTimeZone() && disambiguation === 'compatible') {\n    dateTime = toCalendar(dateTime, new GregorianCalendar());\n\n    // Don't use Date constructor here because two-digit years are interpreted in the 20th century.\n    let date = new Date();\n    let year = getExtendedYear(dateTime.era, dateTime.year);\n    date.setFullYear(year, dateTime.month - 1, dateTime.day);\n    date.setHours(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n    return date.getTime();\n  }\n\n  let ms = epochFromDate(dateTime);\n  let offsetBefore = getTimeZoneOffset(ms - DAYMILLIS, timeZone);\n  let offsetAfter = getTimeZoneOffset(ms + DAYMILLIS, timeZone);\n  let valid = getValidWallTimes(dateTime, timeZone, ms - offsetBefore, ms - offsetAfter);\n\n  if (valid.length === 1) {\n    return valid[0];\n  }\n\n  if (valid.length > 1) {\n    switch (disambiguation) {\n      // 'compatible' means 'earlier' for \"fall back\" transitions\n      case 'compatible':\n      case 'earlier':\n        return valid[0];\n      case 'later':\n        return valid[valid.length - 1];\n      case 'reject':\n        throw new RangeError('Multiple possible absolute times found');\n    }\n  }\n\n  switch (disambiguation) {\n    case 'earlier':\n      return Math.min(ms - offsetBefore, ms - offsetAfter);\n    // 'compatible' means 'later' for \"spring forward\" transitions\n    case 'compatible':\n    case 'later':\n      return Math.max(ms - offsetBefore, ms - offsetAfter);\n    case 'reject':\n      throw new RangeError('No such absolute time found');\n  }\n}\n\nexport function toDate(dateTime: CalendarDate | CalendarDateTime, timeZone: string, disambiguation: Disambiguation = 'compatible'): Date {\n  return new Date(toAbsolute(dateTime, timeZone, disambiguation));\n}\n\n/**\n * Takes a Unix epoch (milliseconds since 1970) and converts it to the provided time zone.\n */\nexport function fromAbsolute(ms: number, timeZone: string): ZonedDateTime {\n  let offset = getTimeZoneOffset(ms, timeZone);\n  let date = new Date(ms + offset);\n  let year = date.getUTCFullYear();\n  let month = date.getUTCMonth() + 1;\n  let day = date.getUTCDate();\n  let hour = date.getUTCHours();\n  let minute = date.getUTCMinutes();\n  let second = date.getUTCSeconds();\n  let millisecond = date.getUTCMilliseconds();\n\n  return new ZonedDateTime(year < 1 ? 'BC' : 'AD', year < 1 ? -year + 1 : year, month, day, timeZone, offset, hour, minute, second, millisecond);\n}\n\n/**\n * Takes a `Date` object and converts it to the provided time zone.\n */\nexport function fromDate(date: Date, timeZone: string): ZonedDateTime {\n  return fromAbsolute(date.getTime(), timeZone);\n}\n\nexport function fromDateToLocal(date: Date): ZonedDateTime {\n  return fromDate(date, getLocalTimeZone());\n}\n\n/** Converts a value with date components such as a `CalendarDateTime` or `ZonedDateTime` into a `CalendarDate`. */\nexport function toCalendarDate(dateTime: AnyCalendarDate): CalendarDate {\n  return new CalendarDate(dateTime.calendar, dateTime.era, dateTime.year, dateTime.month, dateTime.day);\n}\n\nexport function toDateFields(date: AnyCalendarDate): DateFields {\n  return {\n    era: date.era,\n    year: date.year,\n    month: date.month,\n    day: date.day\n  };\n}\n\nexport function toTimeFields(date: AnyTime): TimeFields {\n  return {\n    hour: date.hour,\n    minute: date.minute,\n    second: date.second,\n    millisecond: date.millisecond\n  };\n}\n\n/**\n * Converts a date value to a `CalendarDateTime`. An optional `Time` value can be passed to set the time\n * of the resulting value, otherwise it will default to midnight.\n */\nexport function toCalendarDateTime(date: CalendarDate | CalendarDateTime | ZonedDateTime, time?: AnyTime): CalendarDateTime {\n  let hour = 0, minute = 0, second = 0, millisecond = 0;\n  if ('timeZone' in date) {\n    ({hour, minute, second, millisecond} = date);\n  } else if ('hour' in date && !time) {\n    return date;\n  }\n\n  if (time) {\n    ({hour, minute, second, millisecond} = time);\n  }\n\n  return new CalendarDateTime(\n    date.calendar,\n    date.era,\n    date.year,\n    date.month,\n    date.day,\n    hour,\n    minute,\n    second,\n    millisecond\n  );\n}\n\n/** Extracts the time components from a value containing a date and time. */\nexport function toTime(dateTime: CalendarDateTime | ZonedDateTime): Time {\n  return new Time(dateTime.hour, dateTime.minute, dateTime.second, dateTime.millisecond);\n}\n\n/** Converts a date from one calendar system to another. */\nexport function toCalendar<T extends AnyCalendarDate>(date: T, calendar: Calendar): T {\n  if (isEqualCalendar(date.calendar, calendar)) {\n    return date;\n  }\n\n  let calendarDate = calendar.fromJulianDay(date.calendar.toJulianDay(date));\n  let copy: Mutable<T> = date.copy();\n  copy.calendar = calendar;\n  copy.era = calendarDate.era;\n  copy.year = calendarDate.year;\n  copy.month = calendarDate.month;\n  copy.day = calendarDate.day;\n  constrain(copy);\n  return copy;\n}\n\n/**\n * Converts a date value to a `ZonedDateTime` in the provided time zone. The `disambiguation` option can be set\n * to control how values that fall on daylight saving time changes are interpreted.\n */\nexport function toZoned(date: CalendarDate | CalendarDateTime | ZonedDateTime, timeZone: string, disambiguation?: Disambiguation): ZonedDateTime {\n  if (date instanceof ZonedDateTime) {\n    if (date.timeZone === timeZone) {\n      return date;\n    }\n\n    return toTimeZone(date, timeZone);\n  }\n\n  let ms = toAbsolute(date, timeZone, disambiguation);\n  return fromAbsolute(ms, timeZone);\n}\n\nexport function zonedToDate(date: ZonedDateTime): Date {\n  let ms = epochFromDate(date) - date.offset;\n  return new Date(ms);\n}\n\n/** Converts a `ZonedDateTime` from one time zone to another. */\nexport function toTimeZone(date: ZonedDateTime, timeZone: string): ZonedDateTime {\n  let ms = epochFromDate(date) - date.offset;\n  return toCalendar(fromAbsolute(ms, timeZone), date.calendar);\n}\n\n/** Converts the given `ZonedDateTime` into the user's local time zone. */\nexport function toLocalTimeZone(date: ZonedDateTime): ZonedDateTime {\n  return toTimeZone(date, getLocalTimeZone());\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAED,uFAAuF;AACvF,gGAAgG;AASzF,SAAS,yCAAc,IAAiB;IAC7C,OAAO,0CAAW,MAAM,IAAI,CAAA,gLAAA,oBAAgB;IAC5C,IAAI,OAAO,CAAA,gLAAA,kBAAc,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI;IAC9C,OAAO,qCAAe,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,KAAK,MAAM,EAAE,KAAK,MAAM,EAAE,KAAK,WAAW;AACzG;AAEA,SAAS,qCAAe,IAAY,EAAE,KAAa,EAAE,GAAW,EAAE,IAAY,EAAE,MAAc,EAAE,MAAc,EAAE,WAAmB;IACjI,sEAAsE;IACtE,gCAAgC;IAChC,IAAI,OAAO,IAAI;IACf,KAAK,WAAW,CAAC,MAAM,QAAQ,QAAQ;IACvC,KAAK,cAAc,CAAC,MAAM,QAAQ,GAAG;IACrC,OAAO,KAAK,OAAO;AACrB;AAEO,SAAS,0CAAkB,EAAU,EAAE,QAAgB;IAC5D,qBAAqB;IACrB,IAAI,aAAa,OACf,OAAO;IAGT,6DAA6D;IAC7D,IAAI,KAAK,KAAK,aAAa,CAAA,sKAAA,mBAAe,KACxC,OAAO,IAAI,KAAK,IAAI,iBAAiB,KAA9B,CAAA;IAGT,IAAI,EAAA,MAAC,IAAI,EAAA,OAAE,KAAK,EAAA,KAAE,GAAG,EAAA,MAAE,IAAI,EAAA,QAAE,MAAM,EAAA,QAAE,MAAM,EAAC,GAAG,uCAAiB,IAAI;IACpE,IAAI,MAAM,qCAAe,MAAM,OAAO,KAAK,MAAM,QAAQ,QAAQ;IACjE,OAAO,MAAM,KAAK,KAAK,CAAC,KAAK,QAAQ;AACvC;AAEA,MAAM,6CAAuB,IAAI;AAEjC,SAAS,uCAAiB,EAAU,EAAE,QAAgB;IACpD,IAAI,YAAY,2CAAqB,GAAG,CAAC;IACzC,IAAI,CAAC,WAAW;QACd,YAAY,IAAI,KAAK,cAAc,CAAC,SAAS;sBAC3C;YACA,QAAQ;YACR,KAAK;YACL,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;YACR,QAAQ;QACV;QAEA,2CAAqB,GAAG,CAAC,UAAU;IACrC;IAEA,IAAI,QAAQ,UAAU,aAAa,CAAC,IAAI,KAAK;IAC7C,IAAI,aAAuC,CAAC;IAC5C,KAAK,IAAI,QAAQ,MACf,IAAI,KAAK,IAAI,KAAK,WAChB,UAAU,CAAC,KAAK,IAAI,CAAC,GAAG,KAAK,KAAK;IAKtC,OAAO;QACL,0FAA0F;QAC1F,MAAM,WAAW,GAAG,KAAK,QAAQ,WAAW,GAAG,KAAK,MAAM,CAAC,WAAW,IAAI,GAAG,IAAI,CAAC,WAAW,IAAI;QACjG,OAAO,CAAC,WAAW,KAAK;QACxB,KAAK,CAAC,WAAW,GAAG;QACpB,MAAM,WAAW,IAAI,KAAK,OAAO,IAAI,CAAC,WAAW,IAAI;QACrD,QAAQ,CAAC,WAAW,MAAM;QAC1B,QAAQ,CAAC,WAAW,MAAM;IAC5B;AACF;AAEA,MAAM,kCAAY;AAEX,SAAS,0CAAkB,IAAsB,EAAE,QAAgB;IACxE,IAAI,KAAK,yCAAc;IACvB,IAAI,UAAU,KAAK,0CAAkB,KAAK,iCAAW;IACrD,IAAI,QAAQ,KAAK,0CAAkB,KAAK,iCAAW;IACnD,OAAO,wCAAkB,MAAM,UAAU,SAAS;AACpD;AAEA,SAAS,wCAAkB,IAAsB,EAAE,QAAgB,EAAE,OAAe,EAAE,KAAa;IACjG,IAAI,QAAQ,YAAY,QAAQ;QAAC;KAAQ,GAAG;QAAC;QAAS;KAAM;IAC5D,OAAO,MAAM,MAAM,CAAC,CAAA,WAAY,sCAAgB,MAAM,UAAU;AAClE;AAEA,SAAS,sCAAgB,IAAsB,EAAE,QAAgB,EAAE,QAAgB;IACjF,IAAI,QAAQ,uCAAiB,UAAU;IACvC,OAAO,KAAK,IAAI,KAAK,MAAM,IAAI,IAC1B,KAAK,KAAK,KAAK,MAAM,KAAK,IAC1B,KAAK,GAAG,KAAK,MAAM,GAAG,IACtB,KAAK,IAAI,KAAK,MAAM,IAAI,IACxB,KAAK,MAAM,KAAK,MAAM,MAAM,IAC5B,KAAK,MAAM,KAAK,MAAM,MAAM;AACnC;AAEO,SAAS,0CAAW,IAAqC,EAAE,QAAgB,EAAE,iBAAiC,YAAY;IAC/H,IAAI,WAAW,0CAAmB;IAElC,uDAAuD;IACvD,IAAI,aAAa,OACf,OAAO,yCAAc;IAGvB,uGAAuG;IACvG,IAAI,aAAa,CAAA,sKAAA,mBAAe,OAAO,mBAAmB,cAAc;QACtE,WAAW,0CAAW,UAAU,IAAI,CAAA,gLAAA,oBAAgB;QAEpD,+FAA+F;QAC/F,IAAI,OAAO,IAAI;QACf,IAAI,OAAO,CAAA,gLAAA,kBAAc,EAAE,SAAS,GAAG,EAAE,SAAS,IAAI;QACtD,KAAK,WAAW,CAAC,MAAM,SAAS,KAAK,GAAG,GAAG,SAAS,GAAG;QACvD,KAAK,QAAQ,CAAC,SAAS,IAAI,EAAE,SAAS,MAAM,EAAE,SAAS,MAAM,EAAE,SAAS,WAAW;QACnF,OAAO,KAAK,OAAO;IACrB;IAEA,IAAI,KAAK,yCAAc;IACvB,IAAI,eAAe,0CAAkB,KAAK,iCAAW;IACrD,IAAI,cAAc,0CAAkB,KAAK,iCAAW;IACpD,IAAI,QAAQ,wCAAkB,UAAU,UAAU,KAAK,cAAc,KAAK;IAE1E,IAAI,MAAM,MAAM,KAAK,GACnB,OAAO,KAAK,CAAC,EAAE;IAGjB,IAAI,MAAM,MAAM,GAAG,GACjB,OAAQ;QACN,2DAA2D;QAC3D,KAAK;QACL,KAAK;YACH,OAAO,KAAK,CAAC,EAAE;QACjB,KAAK;YACH,OAAO,KAAK,CAAC,MAAM,MAAM,GAAG,EAAE;QAChC,KAAK;YACH,MAAM,IAAI,WAAW;IACzB;IAGF,OAAQ;QACN,KAAK;YACH,OAAO,KAAK,GAAG,CAAC,KAAK,cAAc,KAAK;QAC1C,8DAA8D;QAC9D,KAAK;QACL,KAAK;YACH,OAAO,KAAK,GAAG,CAAC,KAAK,cAAc,KAAK;QAC1C,KAAK;YACH,MAAM,IAAI,WAAW;IACzB;AACF;AAEO,SAAS,0CAAO,QAAyC,EAAE,QAAgB,EAAE,iBAAiC,YAAY;IAC/H,OAAO,IAAI,KAAK,0CAAW,UAAU,UAAU;AACjD;AAKO,SAAS,0CAAa,EAAU,EAAE,QAAgB;IACvD,IAAI,SAAS,0CAAkB,IAAI;IACnC,IAAI,OAAO,IAAI,KAAK,KAAK;IACzB,IAAI,OAAO,KAAK,cAAc;IAC9B,IAAI,QAAQ,KAAK,WAAW,KAAK;IACjC,IAAI,MAAM,KAAK,UAAU;IACzB,IAAI,OAAO,KAAK,WAAW;IAC3B,IAAI,SAAS,KAAK,aAAa;IAC/B,IAAI,SAAS,KAAK,aAAa;IAC/B,IAAI,cAAc,KAAK,kBAAkB;IAEzC,OAAO,IAAI,CAAA,2KAAA,gBAAY,EAAE,OAAO,IAAI,OAAO,MAAM,OAAO,IAAI,CAAC,OAAO,IAAI,MAAM,OAAO,KAAK,UAAU,QAAQ,MAAM,QAAQ,QAAQ;AACpI;AAKO,SAAS,0CAAS,IAAU,EAAE,QAAgB;IACnD,OAAO,0CAAa,KAAK,OAAO,IAAI;AACtC;AAEO,SAAS,0CAAgB,IAAU;IACxC,OAAO,0CAAS,MAAM,CAAA,sKAAA,mBAAe;AACvC;AAGO,SAAS,0CAAe,QAAyB;IACtD,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,SAAS,QAAQ,EAAE,SAAS,GAAG,EAAE,SAAS,IAAI,EAAE,SAAS,KAAK,EAAE,SAAS,GAAG;AACtG;AAEO,SAAS,0CAAa,IAAqB;IAChD,OAAO;QACL,KAAK,KAAK,GAAG;QACb,MAAM,KAAK,IAAI;QACf,OAAO,KAAK,KAAK;QACjB,KAAK,KAAK,GAAG;IACf;AACF;AAEO,SAAS,0CAAa,IAAa;IACxC,OAAO;QACL,MAAM,KAAK,IAAI;QACf,QAAQ,KAAK,MAAM;QACnB,QAAQ,KAAK,MAAM;QACnB,aAAa,KAAK,WAAW;IAC/B;AACF;AAMO,SAAS,0CAAmB,IAAqD,EAAE,IAAc;IACtG,IAAI,OAAO,GAAG,SAAS,GAAG,SAAS,GAAG,cAAc;IACpD,IAAI,cAAc,MACf,CAAA,EAAA,MAAC,IAAI,EAAA,QAAE,MAAM,EAAA,QAAE,MAAM,EAAA,aAAE,WAAW,EAAC,GAAG,IAAG;SACrC,IAAI,UAAU,QAAQ,CAAC,MAC5B,OAAO;IAGT,IAAI,MACD,CAAA,EAAA,MAAC,IAAI,EAAA,QAAE,MAAM,EAAA,QAAE,MAAM,EAAA,aAAE,WAAW,EAAC,GAAG,IAAG;IAG5C,OAAO,IAAI,CAAA,2KAAA,mBAAe,EACxB,KAAK,QAAQ,EACb,KAAK,GAAG,EACR,KAAK,IAAI,EACT,KAAK,KAAK,EACV,KAAK,GAAG,EACR,MACA,QACA,QACA;AAEJ;AAGO,SAAS,0CAAO,QAA0C;IAC/D,OAAO,IAAI,CAAA,2KAAA,OAAG,EAAE,SAAS,IAAI,EAAE,SAAS,MAAM,EAAE,SAAS,MAAM,EAAE,SAAS,WAAW;AACvF;AAGO,SAAS,0CAAsC,IAAO,EAAE,QAAkB;IAC/E,IAAI,CAAA,sKAAA,kBAAc,EAAE,KAAK,QAAQ,EAAE,WACjC,OAAO;IAGT,IAAI,eAAe,SAAS,aAAa,CAAC,KAAK,QAAQ,CAAC,WAAW,CAAC;IACpE,IAAI,OAAmB,KAAK,IAAI;IAChC,KAAK,QAAQ,GAAG;IAChB,KAAK,GAAG,GAAG,aAAa,GAAG;IAC3B,KAAK,IAAI,GAAG,aAAa,IAAI;IAC7B,KAAK,KAAK,GAAG,aAAa,KAAK;IAC/B,KAAK,GAAG,GAAG,aAAa,GAAG;IAC3B,CAAA,2KAAA,YAAQ,EAAE;IACV,OAAO;AACT;AAMO,SAAS,0CAAQ,IAAqD,EAAE,QAAgB,EAAE,cAA+B;IAC9H,IAAI,gBAAgB,CAAA,2KAAA,gBAAY,GAAG;QACjC,IAAI,KAAK,QAAQ,KAAK,UACpB,OAAO;QAGT,OAAO,0CAAW,MAAM;IAC1B;IAEA,IAAI,KAAK,0CAAW,MAAM,UAAU;IACpC,OAAO,0CAAa,IAAI;AAC1B;AAEO,SAAS,yCAAY,IAAmB;IAC7C,IAAI,KAAK,yCAAc,QAAQ,KAAK,MAAM;IAC1C,OAAO,IAAI,KAAK;AAClB;AAGO,SAAS,0CAAW,IAAmB,EAAE,QAAgB;IAC9D,IAAI,KAAK,yCAAc,QAAQ,KAAK,MAAM;IAC1C,OAAO,0CAAW,0CAAa,IAAI,WAAW,KAAK,QAAQ;AAC7D;AAGO,SAAS,0CAAgB,IAAmB;IACjD,OAAO,0CAAW,MAAM,CAAA,sKAAA,mBAAe;AACzC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 843, "column": 0}, "map": {"version": 3, "file": "manipulation.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/manipulation.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyCalendarDate, AnyDateTime, AnyTime, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, toAbsolute, toCalendar, toCalendarDateTime} from './conversion';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mutable} from './utils';\n\nconst ONE_HOUR = 3600000;\n\nexport function add(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function add(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime;\nexport function add(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): Mutable<AnyCalendarDate | AnyDateTime> {\n  let mutableDate: Mutable<AnyCalendarDate | AnyDateTime> = date.copy();\n  let days = 'hour' in mutableDate ? addTimeFields(mutableDate, duration) : 0;\n\n  addYears(mutableDate, duration.years || 0);\n  if (mutableDate.calendar.balanceYearMonth) {\n    mutableDate.calendar.balanceYearMonth(mutableDate, date);\n  }\n\n  mutableDate.month += duration.months || 0;\n\n  balanceYearMonth(mutableDate);\n  constrainMonthDay(mutableDate);\n\n  mutableDate.day += (duration.weeks || 0) * 7;\n  mutableDate.day += duration.days || 0;\n  mutableDate.day += days;\n\n  balanceDay(mutableDate);\n\n  if (mutableDate.calendar.balanceDate) {\n    mutableDate.calendar.balanceDate(mutableDate);\n  }\n\n  // Constrain in case adding ended up with a date outside the valid range for the calendar system.\n  // The behavior here is slightly different than when constraining in the `set` function in that\n  // we adjust smaller fields to their minimum/maximum values rather than constraining each field\n  // individually. This matches the general behavior of `add` vs `set` regarding how fields are balanced.\n  if (mutableDate.year < 1) {\n    mutableDate.year = 1;\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxYear = mutableDate.calendar.getYearsInEra(mutableDate);\n  if (mutableDate.year > maxYear) {\n    let isInverseEra = mutableDate.calendar.isInverseEra?.(mutableDate);\n    mutableDate.year = maxYear;\n    mutableDate.month = isInverseEra ? 1 : mutableDate.calendar.getMonthsInYear(mutableDate);\n    mutableDate.day = isInverseEra ? 1 : mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  if (mutableDate.month < 1) {\n    mutableDate.month = 1;\n    mutableDate.day = 1;\n  }\n\n  let maxMonth = mutableDate.calendar.getMonthsInYear(mutableDate);\n  if (mutableDate.month > maxMonth) {\n    mutableDate.month = maxMonth;\n    mutableDate.day = mutableDate.calendar.getDaysInMonth(mutableDate);\n  }\n\n  mutableDate.day = Math.max(1, Math.min(mutableDate.calendar.getDaysInMonth(mutableDate), mutableDate.day));\n  return mutableDate;\n}\n\nfunction addYears(date: Mutable<AnyCalendarDate>, years: number) {\n  if (date.calendar.isInverseEra?.(date)) {\n    years = -years;\n  }\n\n  date.year += years;\n}\n\nfunction balanceYearMonth(date: Mutable<AnyCalendarDate>) {\n  while (date.month < 1) {\n    addYears(date, -1);\n    date.month += date.calendar.getMonthsInYear(date);\n  }\n\n  let monthsInYear = 0;\n  while (date.month > (monthsInYear = date.calendar.getMonthsInYear(date))) {\n    date.month -= monthsInYear;\n    addYears(date, 1);\n  }\n}\n\nfunction balanceDay(date: Mutable<AnyCalendarDate>) {\n  while (date.day < 1) {\n    date.month--;\n    balanceYearMonth(date);\n    date.day += date.calendar.getDaysInMonth(date);\n  }\n\n  while (date.day > date.calendar.getDaysInMonth(date)) {\n    date.day -= date.calendar.getDaysInMonth(date);\n    date.month++;\n    balanceYearMonth(date);\n  }\n}\n\nfunction constrainMonthDay(date: Mutable<AnyCalendarDate>) {\n  date.month = Math.max(1, Math.min(date.calendar.getMonthsInYear(date), date.month));\n  date.day = Math.max(1, Math.min(date.calendar.getDaysInMonth(date), date.day));\n}\n\nexport function constrain(date: Mutable<AnyCalendarDate>): void {\n  if (date.calendar.constrainDate) {\n    date.calendar.constrainDate(date);\n  }\n\n  date.year = Math.max(1, Math.min(date.calendar.getYearsInEra(date), date.year));\n  constrainMonthDay(date);\n}\n\nexport function invertDuration(duration: DateTimeDuration): DateTimeDuration {\n  let inverseDuration = {};\n  for (let key in duration) {\n    if (typeof duration[key] === 'number') {\n      inverseDuration[key] = -duration[key];\n    }\n  }\n\n  return inverseDuration;\n}\n\nexport function subtract(date: CalendarDateTime, duration: DateTimeDuration): CalendarDateTime;\nexport function subtract(date: CalendarDate, duration: DateDuration): CalendarDate;\nexport function subtract(date: CalendarDate | CalendarDateTime, duration: DateTimeDuration): CalendarDate | CalendarDateTime {\n  return add(date, invertDuration(duration));\n}\n\nexport function set(date: CalendarDateTime, fields: DateFields): CalendarDateTime;\nexport function set(date: CalendarDate, fields: DateFields): CalendarDate;\nexport function set(date: CalendarDate | CalendarDateTime, fields: DateFields): Mutable<AnyCalendarDate> {\n  let mutableDate: Mutable<AnyCalendarDate> = date.copy();\n\n  if (fields.era != null) {\n    mutableDate.era = fields.era;\n  }\n\n  if (fields.year != null) {\n    mutableDate.year = fields.year;\n  }\n\n  if (fields.month != null) {\n    mutableDate.month = fields.month;\n  }\n\n  if (fields.day != null) {\n    mutableDate.day = fields.day;\n  }\n\n  constrain(mutableDate);\n  return mutableDate;\n}\n\nexport function setTime(value: CalendarDateTime, fields: TimeFields): CalendarDateTime;\nexport function setTime(value: Time, fields: TimeFields): Time;\nexport function setTime(value: Time | CalendarDateTime, fields: TimeFields): Mutable<Time | CalendarDateTime> {\n  let mutableValue: Mutable<Time | CalendarDateTime> = value.copy();\n\n  if (fields.hour != null) {\n    mutableValue.hour = fields.hour;\n  }\n\n  if (fields.minute != null) {\n    mutableValue.minute = fields.minute;\n  }\n\n  if (fields.second != null) {\n    mutableValue.second = fields.second;\n  }\n\n  if (fields.millisecond != null) {\n    mutableValue.millisecond = fields.millisecond;\n  }\n\n  constrainTime(mutableValue);\n  return mutableValue;\n}\n\nfunction balanceTime(time: Mutable<AnyTime>): number {\n  time.second += Math.floor(time.millisecond / 1000);\n  time.millisecond = nonNegativeMod(time.millisecond, 1000);\n\n  time.minute += Math.floor(time.second / 60);\n  time.second = nonNegativeMod(time.second, 60);\n\n  time.hour += Math.floor(time.minute / 60);\n  time.minute = nonNegativeMod(time.minute, 60);\n\n  let days = Math.floor(time.hour / 24);\n  time.hour = nonNegativeMod(time.hour, 24);\n\n  return days;\n}\n\nexport function constrainTime(time: Mutable<AnyTime>): void {\n  time.millisecond = Math.max(0, Math.min(time.millisecond, 1000));\n  time.second = Math.max(0, Math.min(time.second, 59));\n  time.minute = Math.max(0, Math.min(time.minute, 59));\n  time.hour = Math.max(0, Math.min(time.hour, 23));\n}\n\nfunction nonNegativeMod(a: number, b: number) {\n  let result = a % b;\n  if (result < 0) {\n    result += b;\n  }\n  return result;\n}\n\nfunction addTimeFields(time: Mutable<AnyTime>, duration: TimeDuration): number {\n  time.hour += duration.hours || 0;\n  time.minute += duration.minutes || 0;\n  time.second += duration.seconds || 0;\n  time.millisecond += duration.milliseconds || 0;\n  return balanceTime(time);\n}\n\nexport function addTime(time: Time, duration: TimeDuration): Time {\n  let res = time.copy();\n  addTimeFields(res, duration);\n  return res;\n}\n\nexport function subtractTime(time: Time, duration: TimeDuration): Time {\n  return addTime(time, invertDuration(duration));\n}\n\nexport function cycleDate(value: CalendarDateTime, field: DateField, amount: number, options?: CycleOptions): CalendarDateTime;\nexport function cycleDate(value: CalendarDate, field: DateField, amount: number, options?: CycleOptions): CalendarDate;\nexport function cycleDate(value: CalendarDate | CalendarDateTime, field: DateField, amount: number, options?: CycleOptions): Mutable<CalendarDate | CalendarDateTime> {\n  let mutable: Mutable<CalendarDate | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'era': {\n      let eras = value.calendar.getEras();\n      let eraIndex = eras.indexOf(value.era);\n      if (eraIndex < 0) {\n        throw new Error('Invalid era: ' + value.era);\n      }\n      eraIndex = cycleValue(eraIndex, amount, 0, eras.length - 1, options?.round);\n      mutable.era = eras[eraIndex];\n\n      // Constrain the year and other fields within the era, so the era doesn't change when we balance below.\n      constrain(mutable);\n      break;\n    }\n    case 'year': {\n      if (mutable.calendar.isInverseEra?.(mutable)) {\n        amount = -amount;\n      }\n\n      // The year field should not cycle within the era as that can cause weird behavior affecting other fields.\n      // We need to also allow values < 1 so that decrementing goes to the previous era. If we get -Infinity back\n      // we know we wrapped around after reaching 9999 (the maximum), so set the year back to 1.\n      mutable.year = cycleValue(value.year, amount, -Infinity, 9999, options?.round);\n      if (mutable.year === -Infinity) {\n        mutable.year = 1;\n      }\n\n      if (mutable.calendar.balanceYearMonth) {\n        mutable.calendar.balanceYearMonth(mutable, value);\n      }\n      break;\n    }\n    case 'month':\n      mutable.month = cycleValue(value.month, amount, 1, value.calendar.getMonthsInYear(value), options?.round);\n      break;\n    case 'day':\n      mutable.day = cycleValue(value.day, amount, 1, value.calendar.getDaysInMonth(value), options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  if (value.calendar.balanceDate) {\n    value.calendar.balanceDate(mutable);\n  }\n\n  constrain(mutable);\n  return mutable;\n}\n\nexport function cycleTime(value: CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime;\nexport function cycleTime(value: Time, field: TimeField, amount: number, options?: CycleTimeOptions): Time;\nexport function cycleTime(value: Time | CalendarDateTime, field: TimeField, amount: number, options?: CycleTimeOptions): Mutable<Time | CalendarDateTime> {\n  let mutable: Mutable<Time | CalendarDateTime> = value.copy();\n\n  switch (field) {\n    case 'hour': {\n      let hours = value.hour;\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = hours >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n      mutable.hour = cycleValue(hours, amount, min, max, options?.round);\n      break;\n    }\n    case 'minute':\n      mutable.minute = cycleValue(value.minute, amount, 0, 59, options?.round);\n      break;\n    case 'second':\n      mutable.second = cycleValue(value.second, amount, 0, 59, options?.round);\n      break;\n    case 'millisecond':\n      mutable.millisecond = cycleValue(value.millisecond, amount, 0, 999, options?.round);\n      break;\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n\n  return mutable;\n}\n\nfunction cycleValue(value: number, amount: number, min: number, max: number, round = false) {\n  if (round) {\n    value += Math.sign(amount);\n\n    if (value < min) {\n      value = max;\n    }\n\n    let div = Math.abs(amount);\n    if (amount > 0) {\n      value = Math.ceil(value / div) * div;\n    } else {\n      value = Math.floor(value / div) * div;\n    }\n\n    if (value > max) {\n      value = min;\n    }\n  } else {\n    value += amount;\n    if (value < min) {\n      value = max - (min - value - 1);\n    } else if (value > max) {\n      value = min + (value - max - 1);\n    }\n  }\n\n  return value;\n}\n\nexport function addZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  let ms: number;\n  if ((duration.years != null && duration.years !== 0) || (duration.months != null && duration.months !== 0) || (duration.weeks != null && duration.weeks !== 0) || (duration.days != null && duration.days !== 0)) {\n    let res = add(toCalendarDateTime(dateTime), {\n      years: duration.years,\n      months: duration.months,\n      weeks: duration.weeks,\n      days: duration.days\n    });\n\n    // Changing the date may change the timezone offset, so we need to recompute\n    // using the 'compatible' disambiguation.\n    ms = toAbsolute(res, dateTime.timeZone);\n  } else {\n    // Otherwise, preserve the offset of the original date.\n    ms = epochFromDate(dateTime) - dateTime.offset;\n  }\n\n  // Perform time manipulation in milliseconds rather than on the original time fields to account for DST.\n  // For example, adding one hour during a DST transition may result in the hour field staying the same or\n  // skipping an hour. This results in the offset field changing value instead of the specified field.\n  ms += duration.milliseconds || 0;\n  ms += (duration.seconds || 0) * 1000;\n  ms += (duration.minutes || 0) * 60 * 1000;\n  ms += (duration.hours || 0) * 60 * 60 * 1000;\n\n  let res = fromAbsolute(ms, dateTime.timeZone);\n  return toCalendar(res, dateTime.calendar);\n}\n\nexport function subtractZoned(dateTime: ZonedDateTime, duration: DateTimeDuration): ZonedDateTime {\n  return addZoned(dateTime, invertDuration(duration));\n}\n\nexport function cycleZoned(dateTime: ZonedDateTime, field: DateField | TimeField, amount: number, options?: CycleTimeOptions): ZonedDateTime {\n  // For date fields, we want the time to remain consistent and the UTC offset to potentially change to account for DST changes.\n  // For time fields, we want the time to change by the amount given. This may result in the hour field staying the same, but the UTC\n  // offset changing in the case of a backward DST transition, or skipping an hour in the case of a forward DST transition.\n  switch (field) {\n    case 'hour': {\n      let min = 0;\n      let max = 23;\n      if (options?.hourCycle === 12) {\n        let isPM = dateTime.hour >= 12;\n        min = isPM ? 12 : 0;\n        max = isPM ? 23 : 11;\n      }\n\n      // The minimum and maximum hour may be affected by daylight saving time.\n      // For example, it might jump forward at midnight, and skip 1am.\n      // Or it might end at midnight and repeat the 11pm hour. To handle this, we get\n      // the possible absolute times for the min and max, and find the maximum range\n      // that is within the current day.\n      let plainDateTime = toCalendarDateTime(dateTime);\n      let minDate = toCalendar(setTime(plainDateTime, {hour: min}), new GregorianCalendar());\n      let minAbsolute = [toAbsolute(minDate, dateTime.timeZone, 'earlier'), toAbsolute(minDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === minDate.day)[0];\n\n      let maxDate = toCalendar(setTime(plainDateTime, {hour: max}), new GregorianCalendar());\n      let maxAbsolute = [toAbsolute(maxDate, dateTime.timeZone, 'earlier'), toAbsolute(maxDate, dateTime.timeZone, 'later')]\n        .filter(ms => fromAbsolute(ms, dateTime.timeZone).day === maxDate.day).pop()!;\n\n      // Since hours may repeat, we need to operate on the absolute time in milliseconds.\n      // This is done in hours from the Unix epoch so that cycleValue works correctly,\n      // and then converted back to milliseconds.\n      let ms = epochFromDate(dateTime) - dateTime.offset;\n      let hours = Math.floor(ms / ONE_HOUR);\n      let remainder = ms % ONE_HOUR;\n      ms = cycleValue(\n        hours,\n        amount,\n        Math.floor(minAbsolute / ONE_HOUR),\n        Math.floor(maxAbsolute / ONE_HOUR),\n        options?.round\n      ) * ONE_HOUR + remainder;\n\n      // Now compute the new timezone offset, and convert the absolute time back to local time.\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    case 'minute':\n    case 'second':\n    case 'millisecond':\n      // @ts-ignore\n      return cycleTime(dateTime, field, amount, options);\n    case 'era':\n    case 'year':\n    case 'month':\n    case 'day': {\n      let res = cycleDate(toCalendarDateTime(dateTime), field, amount, options);\n      let ms = toAbsolute(res, dateTime.timeZone);\n      return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n    }\n    default:\n      throw new Error('Unsupported field ' + field);\n  }\n}\n\nexport function setZoned(dateTime: ZonedDateTime, fields: DateFields & TimeFields, disambiguation?: Disambiguation): ZonedDateTime {\n  // Set the date/time fields, and recompute the UTC offset to account for DST changes.\n  // We also need to validate by converting back to a local time in case hours are skipped during forward DST transitions.\n  let plainDateTime = toCalendarDateTime(dateTime);\n  let res = setTime(set(plainDateTime, fields), fields);\n\n  // If the resulting plain date time values are equal, return the original time.\n  // We don't want to change the offset when setting the time to the same value.\n  if (res.compare(plainDateTime) === 0) {\n    return dateTime;\n  }\n\n  let ms = toAbsolute(res, dateTime.timeZone, disambiguation);\n  return toCalendar(fromAbsolute(ms, dateTime.timeZone), dateTime.calendar);\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAQD,MAAM,iCAAW;AAKV,SAAS,0CAAI,IAAqC,EAAE,QAA0B;IACnF,IAAI,cAAsD,KAAK,IAAI;IACnE,IAAI,OAAO,UAAU,cAAc,oCAAc,aAAa,YAAY;IAE1E,+BAAS,aAAa,SAAS,KAAK,IAAI;IACxC,IAAI,YAAY,QAAQ,CAAC,gBAAgB,EACvC,YAAY,QAAQ,CAAC,gBAAgB,CAAC,aAAa;IAGrD,YAAY,KAAK,IAAI,SAAS,MAAM,IAAI;IAExC,uCAAiB;IACjB,wCAAkB;IAElB,YAAY,GAAG,IAAK,CAAA,SAAS,KAAK,IAAI,CAAA,IAAK;IAC3C,YAAY,GAAG,IAAI,SAAS,IAAI,IAAI;IACpC,YAAY,GAAG,IAAI;IAEnB,iCAAW;IAEX,IAAI,YAAY,QAAQ,CAAC,WAAW,EAClC,YAAY,QAAQ,CAAC,WAAW,CAAC;IAGnC,iGAAiG;IACjG,+FAA+F;IAC/F,+FAA+F;IAC/F,uGAAuG;IACvG,IAAI,YAAY,IAAI,GAAG,GAAG;QACxB,YAAY,IAAI,GAAG;QACnB,YAAY,KAAK,GAAG;QACpB,YAAY,GAAG,GAAG;IACpB;IAEA,IAAI,UAAU,YAAY,QAAQ,CAAC,aAAa,CAAC;IACjD,IAAI,YAAY,IAAI,GAAG,SAAS;YACX,oCAAA;QAAnB,IAAI,eAAA,CAAe,qCAAA,CAAA,wBAAA,YAAY,QAAQ,EAAC,YAAY,MAAA,QAAjC,uCAAA,KAAA,IAAA,KAAA,IAAA,mCAAA,IAAA,CAAA,uBAAoC;QACvD,YAAY,IAAI,GAAG;QACnB,YAAY,KAAK,GAAG,eAAe,IAAI,YAAY,QAAQ,CAAC,eAAe,CAAC;QAC5E,YAAY,GAAG,GAAG,eAAe,IAAI,YAAY,QAAQ,CAAC,cAAc,CAAC;IAC3E;IAEA,IAAI,YAAY,KAAK,GAAG,GAAG;QACzB,YAAY,KAAK,GAAG;QACpB,YAAY,GAAG,GAAG;IACpB;IAEA,IAAI,WAAW,YAAY,QAAQ,CAAC,eAAe,CAAC;IACpD,IAAI,YAAY,KAAK,GAAG,UAAU;QAChC,YAAY,KAAK,GAAG;QACpB,YAAY,GAAG,GAAG,YAAY,QAAQ,CAAC,cAAc,CAAC;IACxD;IAEA,YAAY,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,YAAY,QAAQ,CAAC,cAAc,CAAC,cAAc,YAAY,GAAG;IACxG,OAAO;AACT;AAEA,SAAS,+BAAS,IAA8B,EAAE,KAAa;QACzD,6BAAA;IAAJ,IAAA,CAAI,8BAAA,CAAA,iBAAA,KAAK,QAAQ,EAAC,YAAY,MAAA,QAA1B,gCAAA,KAAA,IAAA,KAAA,IAAA,4BAAA,IAAA,CAAA,gBAA6B,OAC/B,QAAQ,CAAC;IAGX,KAAK,IAAI,IAAI;AACf;AAEA,SAAS,uCAAiB,IAA8B;IACtD,MAAO,KAAK,KAAK,GAAG,EAAG;QACrB,+BAAS,MAAM,CAAA;QACf,KAAK,KAAK,IAAI,KAAK,QAAQ,CAAC,eAAe,CAAC;IAC9C;IAEA,IAAI,eAAe;IACnB,MAAO,KAAK,KAAK,GAAI,CAAA,eAAe,KAAK,QAAQ,CAAC,eAAe,CAAC,KAAI,EAAI;QACxE,KAAK,KAAK,IAAI;QACd,+BAAS,MAAM;IACjB;AACF;AAEA,SAAS,iCAAW,IAA8B;IAChD,MAAO,KAAK,GAAG,GAAG,EAAG;QACnB,KAAK,KAAK;QACV,uCAAiB;QACjB,KAAK,GAAG,IAAI,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC3C;IAEA,MAAO,KAAK,GAAG,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC,MAAO;QACpD,KAAK,GAAG,IAAI,KAAK,QAAQ,CAAC,cAAc,CAAC;QACzC,KAAK,KAAK;QACV,uCAAiB;IACnB;AACF;AAEA,SAAS,wCAAkB,IAA8B;IACvD,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,eAAe,CAAC,OAAO,KAAK,KAAK;IACjF,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,cAAc,CAAC,OAAO,KAAK,GAAG;AAC9E;AAEO,SAAS,0CAAU,IAA8B;IACtD,IAAI,KAAK,QAAQ,CAAC,aAAa,EAC7B,KAAK,QAAQ,CAAC,aAAa,CAAC;IAG9B,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,QAAQ,CAAC,aAAa,CAAC,OAAO,KAAK,IAAI;IAC7E,wCAAkB;AACpB;AAEO,SAAS,0CAAe,QAA0B;IACvD,IAAI,kBAAkB,CAAC;IACvB,IAAK,IAAI,OAAO,SACd,IAAI,OAAO,QAAQ,CAAC,IAAI,KAAK,UAC3B,eAAe,CAAC,IAAI,GAAG,CAAC,QAAQ,CAAC,IAAI;IAIzC,OAAO;AACT;AAIO,SAAS,0CAAS,IAAqC,EAAE,QAA0B;IACxF,OAAO,0CAAI,MAAM,0CAAe;AAClC;AAIO,SAAS,0CAAI,IAAqC,EAAE,MAAkB;IAC3E,IAAI,cAAwC,KAAK,IAAI;IAErD,IAAI,OAAO,GAAG,IAAI,MAChB,YAAY,GAAG,GAAG,OAAO,GAAG;IAG9B,IAAI,OAAO,IAAI,IAAI,MACjB,YAAY,IAAI,GAAG,OAAO,IAAI;IAGhC,IAAI,OAAO,KAAK,IAAI,MAClB,YAAY,KAAK,GAAG,OAAO,KAAK;IAGlC,IAAI,OAAO,GAAG,IAAI,MAChB,YAAY,GAAG,GAAG,OAAO,GAAG;IAG9B,0CAAU;IACV,OAAO;AACT;AAIO,SAAS,0CAAQ,KAA8B,EAAE,MAAkB;IACxE,IAAI,eAAiD,MAAM,IAAI;IAE/D,IAAI,OAAO,IAAI,IAAI,MACjB,aAAa,IAAI,GAAG,OAAO,IAAI;IAGjC,IAAI,OAAO,MAAM,IAAI,MACnB,aAAa,MAAM,GAAG,OAAO,MAAM;IAGrC,IAAI,OAAO,MAAM,IAAI,MACnB,aAAa,MAAM,GAAG,OAAO,MAAM;IAGrC,IAAI,OAAO,WAAW,IAAI,MACxB,aAAa,WAAW,GAAG,OAAO,WAAW;IAG/C,0CAAc;IACd,OAAO;AACT;AAEA,SAAS,kCAAY,IAAsB;IACzC,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,WAAW,GAAG;IAC7C,KAAK,WAAW,GAAG,qCAAe,KAAK,WAAW,EAAE;IAEpD,KAAK,MAAM,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;IACxC,KAAK,MAAM,GAAG,qCAAe,KAAK,MAAM,EAAE;IAE1C,KAAK,IAAI,IAAI,KAAK,KAAK,CAAC,KAAK,MAAM,GAAG;IACtC,KAAK,MAAM,GAAG,qCAAe,KAAK,MAAM,EAAE;IAE1C,IAAI,OAAO,KAAK,KAAK,CAAC,KAAK,IAAI,GAAG;IAClC,KAAK,IAAI,GAAG,qCAAe,KAAK,IAAI,EAAE;IAEtC,OAAO;AACT;AAEO,SAAS,0CAAc,IAAsB;IAClD,KAAK,WAAW,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,WAAW,EAAE;IAC1D,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;IAChD,KAAK,MAAM,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,MAAM,EAAE;IAChD,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,KAAK,IAAI,EAAE;AAC9C;AAEA,SAAS,qCAAe,CAAS,EAAE,CAAS;IAC1C,IAAI,SAAS,IAAI;IACjB,IAAI,SAAS,GACX,UAAU;IAEZ,OAAO;AACT;AAEA,SAAS,oCAAc,IAAsB,EAAE,QAAsB;IACnE,KAAK,IAAI,IAAI,SAAS,KAAK,IAAI;IAC/B,KAAK,MAAM,IAAI,SAAS,OAAO,IAAI;IACnC,KAAK,MAAM,IAAI,SAAS,OAAO,IAAI;IACnC,KAAK,WAAW,IAAI,SAAS,YAAY,IAAI;IAC7C,OAAO,kCAAY;AACrB;AAEO,SAAS,0CAAQ,IAAU,EAAE,QAAsB;IACxD,IAAI,MAAM,KAAK,IAAI;IACnB,oCAAc,KAAK;IACnB,OAAO;AACT;AAEO,SAAS,0CAAa,IAAU,EAAE,QAAsB;IAC7D,OAAO,0CAAQ,MAAM,0CAAe;AACtC;AAIO,SAAS,0CAAU,KAAsC,EAAE,KAAgB,EAAE,MAAc,EAAE,OAAsB;IACxH,IAAI,UAAoD,MAAM,IAAI;IAElE,OAAQ;QACN,KAAK;YAAO;gBACV,IAAI,OAAO,MAAM,QAAQ,CAAC,OAAO;gBACjC,IAAI,WAAW,KAAK,OAAO,CAAC,MAAM,GAAG;gBACrC,IAAI,WAAW,GACb,MAAM,IAAI,MAAM,kBAAkB,MAAM,GAAG;gBAE7C,WAAW,iCAAW,UAAU,QAAQ,GAAG,KAAK,MAAM,GAAG,GAAG,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;gBAC1E,QAAQ,GAAG,GAAG,IAAI,CAAC,SAAS;gBAE5B,uGAAuG;gBACvG,0CAAU;gBACV;YACF;QACA,KAAK;gBACC,gCAAA;YAAJ,IAAA,CAAI,iCAAA,CAAA,oBAAA,QAAQ,QAAQ,EAAC,YAAY,MAAA,QAA7B,mCAAA,KAAA,IAAA,KAAA,IAAA,+BAAA,IAAA,CAAA,mBAAgC,UAClC,SAAS,CAAC;YAGZ,0GAA0G;YAC1G,2GAA2G;YAC3G,0FAA0F;YAC1F,QAAQ,IAAI,GAAG,iCAAW,MAAM,IAAI,EAAE,QAAQ,CAAC,UAAU,MAAM,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;YAC7E,IAAI,QAAQ,IAAI,KAAK,CAAC,UACpB,QAAQ,IAAI,GAAG;YAGjB,IAAI,QAAQ,QAAQ,CAAC,gBAAgB,EACnC,QAAQ,QAAQ,CAAC,gBAAgB,CAAC,SAAS;YAE7C;QAEF,KAAK;YACH,QAAQ,KAAK,GAAG,iCAAW,MAAM,KAAK,EAAE,QAAQ,GAAG,MAAM,QAAQ,CAAC,eAAe,CAAC,QAAQ,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;YACxG;QACF,KAAK;YACH,QAAQ,GAAG,GAAG,iCAAW,MAAM,GAAG,EAAE,QAAQ,GAAG,MAAM,QAAQ,CAAC,cAAc,CAAC,QAAQ,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;YACnG;QACF;YACE,MAAM,IAAI,MAAM,uBAAuB;IAC3C;IAEA,IAAI,MAAM,QAAQ,CAAC,WAAW,EAC5B,MAAM,QAAQ,CAAC,WAAW,CAAC;IAG7B,0CAAU;IACV,OAAO;AACT;AAIO,SAAS,0CAAU,KAA8B,EAAE,KAAgB,EAAE,MAAc,EAAE,OAA0B;IACpH,IAAI,UAA4C,MAAM,IAAI;IAE1D,OAAQ;QACN,KAAK;YAAQ;gBACX,IAAI,QAAQ,MAAM,IAAI;gBACtB,IAAI,MAAM;gBACV,IAAI,MAAM;gBACV,IAAI,CAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,SAAS,MAAK,IAAI;oBAC7B,IAAI,OAAO,SAAS;oBACpB,MAAM,OAAO,KAAK;oBAClB,MAAM,OAAO,KAAK;gBACpB;gBACA,QAAQ,IAAI,GAAG,iCAAW,OAAO,QAAQ,KAAK,KAAK,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;gBACjE;YACF;QACA,KAAK;YACH,QAAQ,MAAM,GAAG,iCAAW,MAAM,MAAM,EAAE,QAAQ,GAAG,IAAI,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;YACvE;QACF,KAAK;YACH,QAAQ,MAAM,GAAG,iCAAW,MAAM,MAAM,EAAE,QAAQ,GAAG,IAAI,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;YACvE;QACF,KAAK;YACH,QAAQ,WAAW,GAAG,iCAAW,MAAM,WAAW,EAAE,QAAQ,GAAG,KAAK,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK;YAClF;QACF;YACE,MAAM,IAAI,MAAM,uBAAuB;IAC3C;IAEA,OAAO;AACT;AAEA,SAAS,iCAAW,KAAa,EAAE,MAAc,EAAE,GAAW,EAAE,GAAW,EAAE,QAAQ,KAAK;IACxF,IAAI,OAAO;QACT,SAAS,KAAK,IAAI,CAAC;QAEnB,IAAI,QAAQ,KACV,QAAQ;QAGV,IAAI,MAAM,KAAK,GAAG,CAAC;QACnB,IAAI,SAAS,GACX,QAAQ,KAAK,IAAI,CAAC,QAAQ,OAAO;aAEjC,QAAQ,KAAK,KAAK,CAAC,QAAQ,OAAO;QAGpC,IAAI,QAAQ,KACV,QAAQ;IAEZ,OAAO;QACL,SAAS;QACT,IAAI,QAAQ,KACV,QAAQ,MAAO,CAAA,MAAM,QAAQ,CAAA;aACxB,IAAI,QAAQ,KACjB,QAAQ,MAAO,CAAA,QAAQ,MAAM,CAAA;IAEjC;IAEA,OAAO;AACT;AAEO,SAAS,0CAAS,QAAuB,EAAE,QAA0B;IAC1E,IAAI;IACJ,IAAK,SAAS,KAAK,IAAI,QAAQ,SAAS,KAAK,KAAK,KAAO,SAAS,MAAM,IAAI,QAAQ,SAAS,MAAM,KAAK,KAAO,SAAS,KAAK,IAAI,QAAQ,SAAS,KAAK,KAAK,KAAO,SAAS,IAAI,IAAI,QAAQ,SAAS,IAAI,KAAK,GAAI;QAChN,IAAI,MAAM,0CAAI,CAAA,yKAAA,qBAAiB,EAAE,WAAW;YAC1C,OAAO,SAAS,KAAK;YACrB,QAAQ,SAAS,MAAM;YACvB,OAAO,SAAS,KAAK;YACrB,MAAM,SAAS,IAAI;QACrB;QAEA,4EAA4E;QAC5E,yCAAyC;QACzC,KAAK,CAAA,yKAAA,aAAS,EAAE,KAAK,SAAS,QAAQ;IACxC,OACE,AACA,KAAK,CAAA,iDADkD,wHAClD,gBAAY,EAAE,YAAY,SAAS,MAAM;IAGhD,wGAAwG;IACxG,wGAAwG;IACxG,oGAAoG;IACpG,MAAM,SAAS,YAAY,IAAI;IAC/B,MAAO,CAAA,SAAS,OAAO,IAAI,CAAA,IAAK;IAChC,MAAO,CAAA,SAAS,OAAO,IAAI,CAAA,IAArB;IACN,MAAO,CAAA,SAAS,KAAK,IAAI,CAAA,IAAnB;IAEN,IAAI,MAAM,CAAA,yKAAA,eAAW,EAAE,IAAI,SAAS,QAAQ;IAC5C,OAAO,CAAA,yKAAA,aAAS,EAAE,KAAK,SAAS,QAAQ;AAC1C;AAEO,SAAS,0CAAc,QAAuB,EAAE,QAA0B;IAC/E,OAAO,0CAAS,UAAU,0CAAe;AAC3C;AAEO,SAAS,0CAAW,QAAuB,EAAE,KAA4B,EAAE,MAAc,EAAE,OAA0B;IAC1H,8HAA8H;IAC9H,mIAAmI;IACnI,yHAAyH;IACzH,OAAQ;QACN,KAAK;YAAQ;gBACX,IAAI,MAAM;gBACV,IAAI,MAAM;gBACV,IAAI,CAAA,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,SAAS,MAAK,IAAI;oBAC7B,IAAI,OAAO,SAAS,IAAI,IAAI;oBAC5B,MAAM,OAAO,KAAK;oBAClB,MAAM,OAAO,KAAK;gBACpB;gBAEA,wEAAwE;gBACxE,gEAAgE;gBAChE,+EAA+E;gBAC/E,8EAA8E;gBAC9E,kCAAkC;gBAClC,IAAI,gBAAgB,CAAA,yKAAA,qBAAiB,EAAE;gBACvC,IAAI,UAAU,CAAA,yKAAA,aAAS,EAAE,0CAAQ,eAAe;oBAAC,MAAM;gBAAG,IAAI,IAAI,CAAA,gLAAA,oBAAgB;gBAClF,IAAI,cAAc;oBAAC,CAAA,yKAAA,aAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;oBAAY,CAAA,yKAAA,aAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;iBAAS,CACnH,MAAM,CAAC,CAAA,KAAM,CAAA,yKAAA,eAAW,EAAE,IAAI,SAAS,QAAQ,EAAE,GAAG,KAAK,QAAQ,GAAG,CAAC,CAAC,EAAE;gBAE3E,IAAI,UAAU,CAAA,yKAAA,aAAS,EAAE,0CAAQ,eAAe;oBAAC,MAAM;gBAAG,IAAI,IAAI,CAAA,gLAAA,oBAAgB;gBAClF,IAAI,cAAc;oBAAC,CAAA,yKAAA,aAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;oBAAY,CAAA,yKAAA,aAAS,EAAE,SAAS,SAAS,QAAQ,EAAE;iBAAS,CACnH,MAAM,CAAC,CAAA,KAAM,CAAA,yKAAA,eAAW,EAAE,IAAI,SAAS,QAAQ,EAAE,GAAG,KAAK,QAAQ,GAAG,EAAE,GAAG;gBAE5E,mFAAmF;gBACnF,gFAAgF;gBAChF,2CAA2C;gBAC3C,IAAI,KAAK,CAAA,yKAAA,gBAAY,EAAE,YAAY,SAAS,MAAM;gBAClD,IAAI,QAAQ,KAAK,KAAK,CAAC,KAAK;gBAC5B,IAAI,YAAY,KAAK;gBACrB,KAAK,iCACH,OACA,QACA,KAAK,KAAK,CAAC,cAAc,iCACzB,KAAK,KAAK,CAAC,cAAc,iCACzB,YAAA,QAAA,YAAA,KAAA,IAAA,KAAA,IAAA,QAAS,KAAK,IACZ,iCAAW;gBAEf,yFAAyF;gBACzF,OAAO,CAAA,yKAAA,aAAS,EAAE,CAAA,yKAAA,eAAW,EAAE,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;YAC1E;QACA,KAAK;QACL,KAAK;QACL,KAAK;YACH,aAAa;YACb,OAAO,0CAAU,UAAU,OAAO,QAAQ;QAC5C,KAAK;QACL,KAAK;QACL,KAAK;QACL,KAAK;YAAO;gBACV,IAAI,MAAM,0CAAU,CAAA,yKAAA,qBAAiB,EAAE,WAAW,OAAO,QAAQ;gBACjE,IAAI,KAAK,CAAA,yKAAA,aAAS,EAAE,KAAK,SAAS,QAAQ;gBAC1C,OAAO,CAAA,yKAAA,aAAS,EAAE,CAAA,yKAAA,eAAW,EAAE,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;YAC1E;QACA;YACE,MAAM,IAAI,MAAM,uBAAuB;IAC3C;AACF;AAEO,SAAS,0CAAS,QAAuB,EAAE,MAA+B,EAAE,cAA+B;IAChH,qFAAqF;IACrF,wHAAwH;IACxH,IAAI,gBAAgB,CAAA,yKAAA,qBAAiB,EAAE;IACvC,IAAI,MAAM,0CAAQ,0CAAI,eAAe,SAAS;IAE9C,+EAA+E;IAC/E,8EAA8E;IAC9E,IAAI,IAAI,OAAO,CAAC,mBAAmB,GACjC,OAAO;IAGT,IAAI,KAAK,CAAA,yKAAA,aAAS,EAAE,KAAK,SAAS,QAAQ,EAAE;IAC5C,OAAO,CAAA,yKAAA,aAAS,EAAE,CAAA,yKAAA,eAAW,EAAE,IAAI,SAAS,QAAQ,GAAG,SAAS,QAAQ;AAC1E", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1206, "column": 0}, "map": {"version": 3, "file": "string.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/string.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AnyDateTime, DateTimeDuration, Disambiguation} from './types';\nimport {CalendarDate, CalendarDateTime, Time, ZonedDateTime} from './CalendarDate';\nimport {epochFromDate, fromAbsolute, possibleAbsolutes, toAbsolute, toCalendar, toCalendarDateTime, toTimeZone} from './conversion';\nimport {getLocalTimeZone} from './queries';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {Mu<PERSON>} from './utils';\n\nconst TIME_RE = /^(\\d{2})(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst DATE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})$/;\nconst DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?$/;\nconst ZONED_DATE_TIME_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:([+-]\\d{2})(?::?(\\d{2}))?)?\\[(.*?)\\]$/;\nconst ABSOLUTE_RE = /^([+-]\\d{6}|\\d{4})-(\\d{2})-(\\d{2})(?:T(\\d{2}))?(?::(\\d{2}))?(?::(\\d{2}))?(\\.\\d+)?(?:(?:([+-]\\d{2})(?::?(\\d{2}))?)|Z)$/;\nconst DATE_TIME_DURATION_RE =\n    /^((?<negative>-)|\\+)?P((?<years>\\d*)Y)?((?<months>\\d*)M)?((?<weeks>\\d*)W)?((?<days>\\d*)D)?((?<time>T)((?<hours>\\d*[.,]?\\d{1,9})H)?((?<minutes>\\d*[.,]?\\d{1,9})M)?((?<seconds>\\d*[.,]?\\d{1,9})S)?)?$/;\nconst requiredDurationTimeGroups = ['hours', 'minutes', 'seconds'];\nconst requiredDurationGroups = ['years', 'months', 'weeks', 'days', ...requiredDurationTimeGroups];\n\n/** Parses an ISO 8601 time string. */\nexport function parseTime(value: string): Time {\n  let m = value.match(TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 time string: ' + value);\n  }\n\n  return new Time(\n    parseNumber(m[1], 0, 23),\n    m[2] ? parseNumber(m[2], 0, 59) : 0,\n    m[3] ? parseNumber(m[3], 0, 59) : 0,\n    m[4] ? parseNumber(m[4], 0, Infinity) * 1000 : 0\n  );\n}\n\n/** Parses an ISO 8601 date string, with no time components. */\nexport function parseDate(value: string): CalendarDate {\n  let m = value.match(DATE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date string: ' + value);\n  }\n\n  let date: Mutable<CalendarDate> = new CalendarDate(\n    parseNumber(m[1], 0, 9999),\n    parseNumber(m[2], 1, 12),\n    1\n  );\n\n  date.day = parseNumber(m[3], 1, date.calendar.getDaysInMonth(date));\n  return date as CalendarDate;\n}\n\n/** Parses an ISO 8601 date and time string, with no time zone. */\nexport function parseDateTime(value: string): CalendarDateTime {\n  let m = value.match(DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<CalendarDateTime> = new CalendarDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n  return date as CalendarDateTime;\n}\n\n/**\n * Parses an ISO 8601 date and time string with a time zone extension and optional UTC offset\n * (e.g. \"2021-11-07T00:45[America/Los_Angeles]\" or \"2021-11-07T00:45-07:00[America/Los_Angeles]\").\n * Ambiguous times due to daylight saving time transitions are resolved according to the `disambiguation`\n * parameter.\n */\nexport function parseZonedDateTime(value: string, disambiguation?: Disambiguation): ZonedDateTime {\n  let m = value.match(ZONED_DATE_TIME_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    m[10],\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  let plainDateTime = toCalendarDateTime(date as ZonedDateTime);\n\n  let ms: number;\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n    ms = epochFromDate(date as ZonedDateTime) - date.offset;\n\n    // Validate offset against parsed date.\n    let absolutes = possibleAbsolutes(plainDateTime, date.timeZone);\n    if (!absolutes.includes(ms)) {\n      throw new Error(`Offset ${offsetToString(date.offset)} is invalid for ${dateTimeToString(date)} in ${date.timeZone}`);\n    }\n  } else {\n    // Convert to absolute and back to fix invalid times due to DST.\n    ms = toAbsolute(toCalendarDateTime(plainDateTime), date.timeZone, disambiguation);\n  }\n\n  return fromAbsolute(ms, date.timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the provided time zone.\n */\nexport function parseAbsolute(value: string, timeZone: string): ZonedDateTime {\n  let m = value.match(ABSOLUTE_RE);\n  if (!m) {\n    throw new Error('Invalid ISO 8601 date time string: ' + value);\n  }\n\n  let year = parseNumber(m[1], -9999, 9999);\n  let era = year < 1 ? 'BC' : 'AD';\n\n  let date: Mutable<ZonedDateTime> = new ZonedDateTime(\n    era,\n    year < 1 ? -year + 1 : year,\n    parseNumber(m[2], 1, 12),\n    1,\n    timeZone,\n    0,\n    m[4] ? parseNumber(m[4], 0, 23) : 0,\n    m[5] ? parseNumber(m[5], 0, 59) : 0,\n    m[6] ? parseNumber(m[6], 0, 59) : 0,\n    m[7] ? parseNumber(m[7], 0, Infinity) * 1000 : 0\n  );\n\n  date.day = parseNumber(m[3], 0, date.calendar.getDaysInMonth(date));\n\n  if (m[8]) {\n    date.offset = parseNumber(m[8], -23, 23) * 60 * 60 * 1000 + parseNumber(m[9] ?? '0', 0, 59) * 60 * 1000;\n  }\n\n  return toTimeZone(date as ZonedDateTime, timeZone);\n}\n\n/**\n * Parses an ISO 8601 date and time string with a UTC offset (e.g. \"2021-11-07T07:45:00Z\"\n * or \"2021-11-07T07:45:00-07:00\"). The result is converted to the user's local time zone.\n */\nexport function parseAbsoluteToLocal(value: string): ZonedDateTime {\n  return parseAbsolute(value, getLocalTimeZone());\n}\n\nfunction parseNumber(value: string, min: number, max: number) {\n  let val = Number(value);\n  if (val < min || val > max) {\n    throw new RangeError(`Value out of range: ${min} <= ${val} <= ${max}`);\n  }\n\n  return val;\n}\n\nexport function timeToString(time: Time): string {\n  return `${String(time.hour).padStart(2, '0')}:${String(time.minute).padStart(2, '0')}:${String(time.second).padStart(2, '0')}${time.millisecond ? String(time.millisecond / 1000).slice(1) : ''}`;\n}\n\nexport function dateToString(date: CalendarDate): string {\n  let gregorianDate = toCalendar(date, new GregorianCalendar());\n  let year: string;\n  if (gregorianDate.era === 'BC') {\n    year = gregorianDate.year === 1\n      ? '0000'\n      : '-' + String(Math.abs(1 - gregorianDate.year)).padStart(6, '00');\n  } else {\n    year = String(gregorianDate.year).padStart(4, '0');\n  }\n  return `${year}-${String(gregorianDate.month).padStart(2, '0')}-${String(gregorianDate.day).padStart(2, '0')}`;\n}\n\nexport function dateTimeToString(date: AnyDateTime): string {\n  // @ts-ignore\n  return `${dateToString(date)}T${timeToString(date)}`;\n}\n\nfunction offsetToString(offset: number) {\n  let sign = Math.sign(offset) < 0 ? '-' : '+';\n  offset = Math.abs(offset);\n  let offsetHours = Math.floor(offset / (60 * 60 * 1000));\n  let offsetMinutes = (offset % (60 * 60 * 1000)) / (60 * 1000);\n  return `${sign}${String(offsetHours).padStart(2, '0')}:${String(offsetMinutes).padStart(2, '0')}`;\n}\n\nexport function zonedDateTimeToString(date: ZonedDateTime): string {\n  return `${dateTimeToString(date)}${offsetToString(date.offset)}[${date.timeZone}]`;\n}\n\n/**\n * Parses an ISO 8601 duration string (e.g. \"P3Y6M6W4DT12H30M5S\").\n * @param value An ISO 8601 duration string.\n * @returns A DateTimeDuration object.\n */\nexport function parseDuration(value: string): Required<DateTimeDuration> {\n  const match = value.match(DATE_TIME_DURATION_RE);\n\n  if (!match) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const parseDurationGroup = (\n    group: string | undefined,\n    isNegative: boolean\n  ): number => {\n    if (!group) {\n      return 0;\n    }\n    try {\n      const sign = isNegative ? -1 : 1;\n      return sign * Number(group.replace(',', '.'));\n    } catch {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  };\n\n  const isNegative = !!match.groups?.negative;\n\n  const hasRequiredGroups = requiredDurationGroups.some(group => match.groups?.[group]);\n\n  if (!hasRequiredGroups) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n  }\n\n  const durationStringIncludesTime = match.groups?.time;\n\n  if (durationStringIncludesTime) {\n    const hasRequiredDurationTimeGroups = requiredDurationTimeGroups.some(group => match.groups?.[group]);\n    if (!hasRequiredDurationTimeGroups) {\n      throw new Error(`Invalid ISO 8601 Duration string: ${value}`);\n    }\n  }\n\n  const duration: Mutable<DateTimeDuration> = {\n    years: parseDurationGroup(match.groups?.years, isNegative),\n    months: parseDurationGroup(match.groups?.months, isNegative),\n    weeks: parseDurationGroup(match.groups?.weeks, isNegative),\n    days: parseDurationGroup(match.groups?.days, isNegative),\n    hours: parseDurationGroup(match.groups?.hours, isNegative),\n    minutes: parseDurationGroup(match.groups?.minutes, isNegative),\n    seconds: parseDurationGroup(match.groups?.seconds, isNegative)\n  };\n\n  if (duration.hours !== undefined && ((duration.hours % 1) !== 0) && (duration.minutes || duration.seconds)) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  if (duration.minutes !== undefined && ((duration.minutes % 1) !== 0) && duration.seconds) {\n    throw new Error(`Invalid ISO 8601 Duration string: ${value} - only the smallest unit can be fractional`);\n  }\n\n  return duration as Required<DateTimeDuration>;\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GASD,MAAM,gCAAU;AAChB,MAAM,gCAAU;AAChB,MAAM,qCAAe;AACrB,MAAM,2CAAqB;AAC3B,MAAM,oCAAc;AACpB,MAAM,8CACF;AACJ,MAAM,mDAA6B;IAAC;IAAS;IAAW;CAAU;AAClE,MAAM,+CAAyB;IAAC;IAAS;IAAU;IAAS;OAAW;CAA2B;AAG3F,SAAS,0CAAU,KAAa;IACrC,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,mCAAmC;IAGrD,OAAO,IAAI,CAAA,2KAAA,OAAG,EACZ,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;AAEnD;AAGO,SAAS,yCAAU,KAAa;IACrC,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,mCAAmC;IAGrD,IAAI,OAA8B,IAAI,CAAA,2KAAA,eAAW,EAC/C,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,OACrB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB;IAGF,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC7D,OAAO;AACT;AAGO,SAAS,0CAAc,KAAa;IACzC,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,wCAAwC;IAG1D,IAAI,OAAO,kCAAY,CAAC,CAAC,EAAE,EAAE,CAAA,MAAO;IACpC,IAAI,MAAM,OAAO,IAAI,OAAO;IAE5B,IAAI,OAAkC,IAAI,CAAA,2KAAA,mBAAe,EACvD,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,GACA,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;IAGjD,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;IAC7D,OAAO;AACT;AAQO,SAAS,0CAAmB,KAAa,EAAE,cAA+B;IAC/E,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,wCAAwC;IAG1D,IAAI,OAAO,kCAAY,CAAC,CAAC,EAAE,EAAE,CAAA,MAAO;IACpC,IAAI,MAAM,OAAO,IAAI,OAAO;IAE5B,IAAI,OAA+B,IAAI,CAAA,2KAAA,gBAAY,EACjD,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,GACA,CAAC,CAAC,GAAG,EACL,GACA,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;IAGjD,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;IAE7D,IAAI,gBAAgB,CAAA,yKAAA,qBAAiB,EAAE;IAEvC,IAAI;IACJ,IAAI,CAAC,CAAC,EAAE,EAAE;YACgE;QAAxE,KAAK,MAAM,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,CAAA,IAAK,MAAvB,UAA8C,kCAAY,CAAA,MAAA,CAAC,CAAC,EAAE,MAAA,QAAJ,QAAA,KAAA,IAAA,MAAQ,KAAK,GAAG,MAA5B;QAC5D,KAAK,CAAA,yKAAA,gBAAY,EAAE,QAAyB,KAAK,MAAM;QAEvD,uCAAuC;QACvC,IAAI,YAAY,CAAA,yKAAA,oBAAgB,EAAE,eAAe,KAAK,QAAQ;QAC9D,IAAI,CAAC,UAAU,QAAQ,CAAC,KACtB,MAAM,IAAI,MAAM,CAAC,OAAO,EAAE,qCAAe,KAAK,MAAM,EAAE,gBAAgB,EAAE,0CAAiB,MAAM,IAAI,EAAE,KAAK,QAAQ,EAAE;IAExH,OACE,AACA,KAAK,CAAA,0DAD2D,+GAC3D,aAAS,EAAE,CAAA,yKAAA,qBAAiB,EAAE,gBAAgB,KAAK,QAAQ,EAAE;IAGpE,OAAO,CAAA,yKAAA,eAAW,EAAE,IAAI,KAAK,QAAQ;AACvC;AAMO,SAAS,0CAAc,KAAa,EAAE,QAAgB;IAC3D,IAAI,IAAI,MAAM,KAAK,CAAC;IACpB,IAAI,CAAC,GACH,MAAM,IAAI,MAAM,wCAAwC;IAG1D,IAAI,OAAO,kCAAY,CAAC,CAAC,EAAE,EAAE,CAAA,MAAO;IACpC,IAAI,MAAM,OAAO,IAAI,OAAO;IAE5B,IAAI,OAA+B,IAAI,CAAA,2KAAA,gBAAY,EACjD,KACA,OAAO,IAAI,CAAC,OAAO,IAAI,MACvB,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KACrB,GACA,UACA,GACA,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,MAAM,GAClC,CAAC,CAAC,EAAE,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,YAAY,OAAO;IAGjD,KAAK,GAAG,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,GAAG,KAAK,QAAQ,CAAC,cAAc,CAAC;QAGa;IAD1E,IAAI,CAAC,CAAC,EAAE,EACN,KAAK,MAAM,GAAG,kCAAY,CAAC,CAAC,EAAE,EAAE,CAAA,IAAK,MAAvB,UAA8C,kCAAY,CAAA,MAAA,CAAC,CAAC,EAAE,MAAA,QAAJ,QAAA,KAAA,IAAA,MAAQ,KAAK,GAAG,MAA5B;IAG9D,OAAO,CAAA,yKAAA,aAAS,EAAE,MAAuB;AAC3C;AAMO,SAAS,0CAAqB,KAAa;IAChD,OAAO,0CAAc,OAAO,CAAA,sKAAA,mBAAe;AAC7C;AAEA,SAAS,kCAAY,KAAa,EAAE,GAAW,EAAE,GAAW;IAC1D,IAAI,MAAM,OAAO;IACjB,IAAI,MAAM,OAAO,MAAM,KACrB,MAAM,IAAI,WAAW,CAAC,oBAAoB,EAAE,IAAI,IAAI,EAAE,IAAI,IAAI,EAAE,KAAK;IAGvE,OAAO;AACT;AAEO,SAAS,0CAAa,IAAU;IACrC,OAAO,GAAG,OAAO,KAAK,IAAI,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,KAAK,MAAM,EAAE,QAAQ,CAAC,GAAG,OAAO,KAAK,WAAW,GAAG,OAAO,KAAK,WAAW,GAAG,MAAM,KAAK,CAAC,KAAK,IAAI;AACnM;AAEO,SAAS,0CAAa,IAAkB;IAC7C,IAAI,gBAAgB,CAAA,yKAAA,aAAS,EAAE,MAAM,IAAI,CAAA,gLAAA,oBAAgB;IACzD,IAAI;IACJ,IAAI,cAAc,GAAG,KAAK,MACxB,OAAO,cAAc,IAAI,KAAK,IAC1B,SACA,MAAM,OAAO,KAAK,GAAG,CAAC,IAAI,cAAc,IAAI,GAAG,QAAQ,CAAC,GAAG;SAE/D,OAAO,OAAO,cAAc,IAAI,EAAE,QAAQ,CAAC,GAAG;IAEhD,OAAO,GAAG,KAAK,CAAC,EAAE,OAAO,cAAc,KAAK,EAAE,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,cAAc,GAAG,EAAE,QAAQ,CAAC,GAAG,MAAM;AAChH;AAEO,SAAS,0CAAiB,IAAiB;IAChD,aAAa;IACb,OAAO,GAAG,0CAAa,MAAM,CAAC,EAAE,0CAAa,OAAO;AACtD;AAEA,SAAS,qCAAe,MAAc;IACpC,IAAI,OAAO,KAAK,IAAI,CAAC,UAAU,IAAI,MAAM;IACzC,SAAS,KAAK,GAAG,CAAC;IAClB,IAAI,cAAc,KAAK,KAAK,CAAC,SAAU;IACvC,IAAI,gBAAiB,SAAU,UAAoB;IACnD,OAAO,GAAG,OAAO,OAAO,aAAa,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,OAAO,eAAe,QAAQ,CAAC,GAAG,MAAM;AACnG;AAEO,SAAS,0CAAsB,IAAmB;IACvD,OAAO,GAAG,0CAAiB,QAAQ,qCAAe,KAAK,MAAM,EAAE,CAAC,EAAE,KAAK,QAAQ,CAAC,CAAC,CAAC;AACpF;AAOO,SAAS,0CAAc,KAAa;QAsBpB,eAQc,gBAUP,gBACC,gBACD,gBACD,gBACC,gBACE,gBACA;IA7C9B,MAAM,QAAQ,MAAM,KAAK,CAAC;IAE1B,IAAI,CAAC,OACH,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;IAG9D,MAAM,qBAAqB,CACzB,OACA;QAEA,IAAI,CAAC,OACH,OAAO;QAET,IAAI;YACF,MAAM,OAAO,aAAa,CAAA,IAAK;YAC/B,OAAO,OAAO,OAAO,MAAM,OAAO,CAAC,KAAK;QAC1C,EAAE,OAAM;YACN,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;QAC9D;IACF;IAEA,MAAM,aAAa,CAAC,CAAA,CAAA,CAAC,gBAAA,MAAM,MAAM,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,cAAc,QAAQ;IAE3C,MAAM,oBAAoB,6CAAuB,IAAI,CAAC,CAAA;YAAS;gBAAA,gBAAA,MAAM,MAAM,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,aAAc,CAAC,MAAM;;IAEpF,IAAI,CAAC,mBACH,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;IAG9D,MAAM,6BAAA,CAA6B,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,IAAI;IAErD,IAAI,4BAA4B;QAC9B,MAAM,gCAAgC,iDAA2B,IAAI,CAAC,CAAA;gBAAS;oBAAA,gBAAA,MAAM,MAAM,MAAA,QAAZ,kBAAA,KAAA,IAAA,KAAA,IAAA,aAAc,CAAC,MAAM;;QACpG,IAAI,CAAC,+BACH,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,OAAO;IAEhE;IAEA,MAAM,WAAsC;QAC1C,OAAO,mBAAA,CAAmB,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,KAAK,EAAE;QAC/C,QAAQ,mBAAA,CAAmB,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,MAAM,EAAE;QACjD,OAAO,mBAAA,CAAmB,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,KAAK,EAAE;QAC/C,MAAM,mBAAA,CAAmB,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,IAAI,EAAE;QAC7C,OAAO,mBAAA,CAAmB,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,KAAK,EAAE;QAC/C,SAAS,mBAAA,CAAmB,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,OAAO,EAAE;QACnD,SAAS,mBAAA,CAAmB,iBAAA,MAAM,MAAM,MAAA,QAAZ,mBAAA,KAAA,IAAA,KAAA,IAAA,eAAc,OAAO,EAAE;IACrD;IAEA,IAAI,SAAS,KAAK,KAAK,aAAe,SAAS,KAAK,GAAG,MAAO,KAAO,CAAA,SAAS,OAAO,IAAI,SAAS,OAAM,GACtG,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,2CAA2C,CAAC;IAGzG,IAAI,SAAS,OAAO,KAAK,aAAe,SAAS,OAAO,GAAG,MAAO,KAAM,SAAS,OAAO,EACtF,MAAM,IAAI,MAAM,CAAC,kCAAkC,EAAE,MAAM,2CAA2C,CAAC;IAGzG,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1386, "column": 0}, "map": {"version": 3, "file": "CalendarDate.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/CalendarDate.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {add, addTime, addZoned, constrain, constrainTime, cycleDate, cycleTime, cycleZoned, set, setTime, setZoned, subtract, subtractTime, subtractZoned} from './manipulation';\nimport {AnyCalendarDate, AnyTime, Calendar, CycleOptions, CycleTimeOptions, DateDuration, DateField, DateFields, DateTimeDuration, Disambiguation, TimeDuration, TimeField, TimeFields} from './types';\nimport {compareDate, compareTime} from './queries';\nimport {dateTimeToString, dateToString, timeToString, zonedDateTimeToString} from './string';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {toCalendarDateTime, toDate, toZoned, zonedToDate} from './conversion';\n\nfunction shiftArgs(args: any[]) {\n  let calendar: Calendar = typeof args[0] === 'object'\n    ? args.shift()\n    : new GregorianCalendar();\n\n  let era: string;\n  if (typeof args[0] === 'string') {\n    era = args.shift();\n  } else {\n    let eras = calendar.getEras();\n    era = eras[eras.length - 1];\n  }\n\n  let year = args.shift();\n  let month = args.shift();\n  let day = args.shift();\n\n  return [calendar, era, year, month, day];\n}\n\n/** A CalendarDate represents a date without any time components in a specific calendar system. */\nexport class CalendarDate {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // i.e. a ZonedDateTime should not be be passable to a parameter that expects CalendarDate.\n  // If that behavior is desired, use the AnyCalendarDate interface instead.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n\n  constructor(year: number, month: number, day: number);\n  constructor(era: string, year: number, month: number, day: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDate {\n    if (this.era) {\n      return new CalendarDate(this.calendar, this.era, this.year, this.month, this.day);\n    } else {\n      return new CalendarDate(this.calendar, this.year, this.month, this.day);\n    }\n  }\n\n  /** Returns a new `CalendarDate` with the given duration added to it. */\n  add(duration: DateDuration): CalendarDate {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given duration subtracted from it. */\n  subtract(duration: DateDuration): CalendarDate {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDate` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields): CalendarDate {\n    return set(this, fields);\n  }\n\n  /**\n   * Returns a new `CalendarDate` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField, amount: number, options?: CycleOptions): CalendarDate {\n    return cycleDate(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object, with the time set to midnight in the given time zone. */\n  toDate(timeZone: string): Date {\n    return toDate(this, timeZone);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: AnyCalendarDate): number {\n    return compareDate(this, b);\n  }\n}\n\n/** A Time represents a clock time without any date components. */\nexport class Time {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The hour, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(\n    hour: number = 0,\n    minute: number = 0,\n    second: number = 0,\n    millisecond: number = 0\n  ) {\n    this.hour = hour;\n    this.minute = minute;\n    this.second = second;\n    this.millisecond = millisecond;\n    constrainTime(this);\n  }\n\n  /** Returns a copy of this time. */\n  copy(): Time {\n    return new Time(this.hour, this.minute, this.second, this.millisecond);\n  }\n\n  /** Returns a new `Time` with the given duration added to it. */\n  add(duration: TimeDuration): Time {\n    return addTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given duration subtracted from it. */\n  subtract(duration: TimeDuration): Time {\n    return subtractTime(this, duration);\n  }\n\n  /** Returns a new `Time` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: TimeFields): Time {\n    return setTime(this, fields);\n  }\n\n  /**\n   * Returns a new `Time` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: TimeField, amount: number, options?: CycleTimeOptions): Time {\n    return cycleTime(this, field, amount, options);\n  }\n\n  /** Converts the time to an ISO 8601 formatted string. */\n  toString(): string {\n    return timeToString(this);\n  }\n\n  /** Compares this time with another. A negative result indicates that this time is before the given one, and a positive time indicates that it is after. */\n  compare(b: AnyTime): number {\n    return compareTime(this, b);\n  }\n}\n\n/** A CalendarDateTime represents a date and time without a time zone, in a specific calendar system. */\nexport class CalendarDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n\n  constructor(year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): CalendarDateTime {\n    if (this.era) {\n      return new CalendarDateTime(this.calendar, this.era, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new CalendarDateTime(this.calendar, this.year, this.month, this.day, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration): CalendarDateTime {\n    return add(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration): CalendarDateTime {\n    return subtract(this, duration);\n  }\n\n  /** Returns a new `CalendarDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields): CalendarDateTime {\n    return set(setTime(this, fields), fields);\n  }\n\n  /**\n   * Returns a new `CalendarDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions): CalendarDateTime {\n    switch (field) {\n      case 'era':\n      case 'year':\n      case 'month':\n      case 'day':\n        return cycleDate(this, field, amount, options);\n      default:\n        return cycleTime(this, field, amount, options);\n    }\n  }\n\n  /** Converts the date to a native JavaScript Date object in the given time zone. */\n  toDate(timeZone: string, disambiguation?: Disambiguation): Date {\n    return toDate(this, timeZone, disambiguation);\n  }\n\n  /** Converts the date to an ISO 8601 formatted string. */\n  toString(): string {\n    return dateTimeToString(this);\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime): number {\n    let res = compareDate(this, b);\n    if (res === 0) {\n      return compareTime(this, toCalendarDateTime(b));\n    }\n\n    return res;\n  }\n}\n\n/** A ZonedDateTime represents a date and time in a specific time zone and calendar system. */\nexport class ZonedDateTime {\n  // This prevents TypeScript from allowing other types with the same fields to match.\n  // @ts-ignore\n  #type;\n  /** The calendar system associated with this date, e.g. Gregorian. */\n  public readonly calendar: Calendar;\n  /** The calendar era for this date, e.g. \"BC\" or \"AD\". */\n  public readonly era: string;\n  /** The year of this date within the era. */\n  public readonly year: number;\n  /**\n   * The month number within the year. Note that some calendar systems such as Hebrew\n   * may have a variable number of months per year. Therefore, month numbers may not\n   * always correspond to the same month names in different years.\n   */\n  public readonly month: number;\n  /** The day number within the month. */\n  public readonly day: number;\n  /** The hour in the day, numbered from 0 to 23. */\n  public readonly hour: number;\n  /** The minute in the hour. */\n  public readonly minute: number;\n  /** The second in the minute. */\n  public readonly second: number;\n  /** The millisecond in the second. */\n  public readonly millisecond: number;\n  /** The IANA time zone identifier that this date and time is represented in. */\n  public readonly timeZone: string;\n  /** The UTC offset for this time, in milliseconds. */\n  public readonly offset: number;\n\n  constructor(year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(calendar: Calendar, era: string, year: number, month: number, day: number, timeZone: string, offset: number, hour?: number, minute?: number, second?: number, millisecond?: number);\n  constructor(...args: any[]) {\n    let [calendar, era, year, month, day] = shiftArgs(args);\n    let timeZone = args.shift();\n    let offset = args.shift();\n    this.calendar = calendar;\n    this.era = era;\n    this.year = year;\n    this.month = month;\n    this.day = day;\n    this.timeZone = timeZone;\n    this.offset = offset;\n    this.hour = args.shift() || 0;\n    this.minute = args.shift() || 0;\n    this.second = args.shift() || 0;\n    this.millisecond = args.shift() || 0;\n\n    constrain(this);\n  }\n\n  /** Returns a copy of this date. */\n  copy(): ZonedDateTime {\n    if (this.era) {\n      return new ZonedDateTime(this.calendar, this.era, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    } else {\n      return new ZonedDateTime(this.calendar, this.year, this.month, this.day, this.timeZone, this.offset, this.hour, this.minute, this.second, this.millisecond);\n    }\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration added to it. */\n  add(duration: DateTimeDuration): ZonedDateTime {\n    return addZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given duration subtracted from it. */\n  subtract(duration: DateTimeDuration): ZonedDateTime {\n    return subtractZoned(this, duration);\n  }\n\n  /** Returns a new `ZonedDateTime` with the given fields set to the provided values. Other fields will be constrained accordingly. */\n  set(fields: DateFields & TimeFields, disambiguation?: Disambiguation): ZonedDateTime {\n    return setZoned(this, fields, disambiguation);\n  }\n\n  /**\n   * Returns a new `ZonedDateTime` with the given field adjusted by a specified amount.\n   * When the resulting value reaches the limits of the field, it wraps around.\n   */\n  cycle(field: DateField | TimeField, amount: number, options?: CycleTimeOptions): ZonedDateTime {\n    return cycleZoned(this, field, amount, options);\n  }\n\n  /** Converts the date to a native JavaScript Date object. */\n  toDate(): Date {\n    return zonedToDate(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string, including the UTC offset and time zone identifier. */\n  toString(): string {\n    return zonedDateTimeToString(this);\n  }\n\n   /** Converts the date to an ISO 8601 formatted string in UTC. */\n  toAbsoluteString(): string {\n    return this.toDate().toISOString();\n  }\n\n  /** Compares this date with another. A negative result indicates that this date is before the given one, and a positive date indicates that it is after. */\n  compare(b: CalendarDate | CalendarDateTime | ZonedDateTime): number {\n    // TODO: Is this a bad idea??\n    return this.toDate().getTime() - toZoned(b, this.timeZone).toDate().getTime();\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GASD,SAAS,gCAAU,IAAW;IAC5B,IAAI,WAAqB,OAAO,IAAI,CAAC,EAAE,KAAK,WACxC,KAAK,KAAK,KACV,IAAI,CAAA,gLAAA,oBAAgB;IAExB,IAAI;IACJ,IAAI,OAAO,IAAI,CAAC,EAAE,KAAK,UACrB,MAAM,KAAK,KAAK;SACX;QACL,IAAI,OAAO,SAAS,OAAO;QAC3B,MAAM,IAAI,CAAC,KAAK,MAAM,GAAG,EAAE;IAC7B;IAEA,IAAI,OAAO,KAAK,KAAK;IACrB,IAAI,QAAQ,KAAK,KAAK;IACtB,IAAI,MAAM,KAAK,KAAK;IAEpB,OAAO;QAAC;QAAU;QAAK;QAAM;QAAO;KAAI;AAC1C;IAIE,AACA,oFADoF,OACO;AAC3F,0EAA0E;AAC1E,aAAa;AACb,8BAAA,WAAA,GAAA,IAAA;AALK,MAAM;IAoCX,iCAAiC,GACjC,OAAqB;QACnB,IAAI,IAAI,CAAC,GAAG,EACV,OAAO,IAAI,0CAAa,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG;aAEhF,OAAO,IAAI,0CAAa,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG;IAE1E;IAEA,sEAAsE,GACtE,IAAI,QAAsB,EAAgB;QACxC,OAAO,CAAA,2KAAA,MAAE,EAAE,IAAI,EAAE;IACnB;IAEA,6EAA6E,GAC7E,SAAS,QAAsB,EAAgB;QAC7C,OAAO,CAAA,2KAAA,WAAO,EAAE,IAAI,EAAE;IACxB;IAEA,iIAAiI,GACjI,IAAI,MAAkB,EAAgB;QACpC,OAAO,CAAA,2KAAA,MAAE,EAAE,IAAI,EAAE;IACnB;IAEA;;;GAGC,GACD,MAAM,KAAgB,EAAE,MAAc,EAAE,OAAsB,EAAgB;QAC5E,OAAO,CAAA,2KAAA,YAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;IACxC;IAEA,gHAAgH,GAChH,OAAO,QAAgB,EAAQ;QAC7B,OAAO,CAAA,yKAAA,SAAK,EAAE,IAAI,EAAE;IACtB;IAEA,uDAAuD,GACvD,WAAmB;QACjB,OAAO,CAAA,qKAAA,eAAW,EAAE,IAAI;IAC1B;IAEA,yJAAyJ,GACzJ,QAAQ,CAAkB,EAAU;QAClC,OAAO,CAAA,sKAAA,cAAU,EAAE,IAAI,EAAE;IAC3B;IAxDA,YAAY,GAAG,IAAW,CAAE;QApB5B,CAAA,GAAA,+LAAA,CAAA,IAAA,EAAA,IAAA,EAAA,6BAAA;;mBAAA,KAAA;;QAqBE,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG,gCAAU;QAClD,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QAEX,CAAA,2KAAA,YAAQ,EAAE,IAAI;IAChB;AAgDF;IAKE,AADA,aACa,uEADuE;AAEpF,+BAAA,WAAA,GAAA,IAAA;AAHK,MAAM;IA0BX,iCAAiC,GACjC,OAAa;QACX,OAAO,IAAI,yCAAK,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;IACvE;IAEA,8DAA8D,GAC9D,IAAI,QAAsB,EAAQ;QAChC,OAAO,CAAA,2KAAA,UAAM,EAAE,IAAI,EAAE;IACvB;IAEA,qEAAqE,GACrE,SAAS,QAAsB,EAAQ;QACrC,OAAO,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE;IAC5B;IAEA,yHAAyH,GACzH,IAAI,MAAkB,EAAQ;QAC5B,OAAO,CAAA,2KAAA,UAAM,EAAE,IAAI,EAAE;IACvB;IAEA;;;GAGC,GACD,MAAM,KAAgB,EAAE,MAAc,EAAE,OAA0B,EAAQ;QACxE,OAAO,CAAA,2KAAA,YAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;IACxC;IAEA,uDAAuD,GACvD,WAAmB;QACjB,OAAO,CAAA,qKAAA,eAAW,EAAE,IAAI;IAC1B;IAEA,yJAAyJ,GACzJ,QAAQ,CAAU,EAAU;QAC1B,OAAO,CAAA,sKAAA,cAAU,EAAE,IAAI,EAAE;IAC3B;IAjDA,YACE,OAAe,CAAC,EAChB,SAAiB,CAAC,EAClB,SAAiB,CAAC,EAClB,cAAsB,CAAC,CACvB;QAfF,CAAA,GAAA,+LAAA,CAAA,IAAA,EAAA,IAAA,EAAA,8BAAA;;mBAAA,KAAA;;QAgBE,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,WAAW,GAAG;QACnB,CAAA,2KAAA,gBAAY,EAAE,IAAI;IACpB;AAuCF;IAIE,AACA,aAAa,uEADuE;AAEpF,+BAAA,WAAA,GAAA,IAAA;AAHK,MAAM;IA8CX,iCAAiC,GACjC,OAAyB;QACvB,IAAI,IAAI,CAAC,GAAG,EACV,OAAO,IAAI,0CAAiB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;aAE3I,OAAO,IAAI,0CAAiB,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;IAErI;IAEA,0EAA0E,GAC1E,IAAI,QAA0B,EAAoB;QAChD,OAAO,CAAA,2KAAA,MAAE,EAAE,IAAI,EAAE;IACnB;IAEA,iFAAiF,GACjF,SAAS,QAA0B,EAAoB;QACrD,OAAO,CAAA,2KAAA,WAAO,EAAE,IAAI,EAAE;IACxB;IAEA,qIAAqI,GACrI,IAAI,MAA+B,EAAoB;QACrD,OAAO,CAAA,2KAAA,MAAE,EAAE,CAAA,2KAAA,UAAM,EAAE,IAAI,EAAE,SAAS;IACpC;IAEA;;;GAGC,GACD,MAAM,KAA4B,EAAE,MAAc,EAAE,OAA0B,EAAoB;QAChG,OAAQ;YACN,KAAK;YACL,KAAK;YACL,KAAK;YACL,KAAK;gBACH,OAAO,CAAA,2KAAA,YAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;YACxC;gBACE,OAAO,CAAA,2KAAA,YAAQ,EAAE,IAAI,EAAE,OAAO,QAAQ;QAC1C;IACF;IAEA,iFAAiF,GACjF,OAAO,QAAgB,EAAE,cAA+B,EAAQ;QAC9D,OAAO,CAAA,yKAAA,SAAK,EAAE,IAAI,EAAE,UAAU;IAChC;IAEA,uDAAuD,GACvD,WAAmB;QACjB,OAAO,CAAA,qKAAA,mBAAe,EAAE,IAAI;IAC9B;IAEA,yJAAyJ,GACzJ,QAAQ,CAAkD,EAAU;QAClE,IAAI,MAAM,CAAA,sKAAA,cAAU,EAAE,IAAI,EAAE;QAC5B,IAAI,QAAQ,GACV,OAAO,CAAA,sKAAA,cAAU,EAAE,IAAI,EAAE,CAAA,yKAAA,qBAAiB,EAAE;QAG9C,OAAO;IACT;IAzEA,YAAY,GAAG,IAAW,CAAE;QA5B5B,CAAA,GAAA,+LAAA,CAAA,IAAA,EAAA,IAAA,EAAA,8BAAA;;mBAAA,KAAA;;QA6BE,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG,gCAAU;QAClD,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,MAAM;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM;QAEnC,CAAA,2KAAA,YAAQ,EAAE,IAAI;IAChB;AA6DF;IAIE,AACA,aAAa,uEADuE;AAEpF,+BAAA,WAAA,GAAA,IAAA;AAHK,MAAM;IAsDX,iCAAiC,GACjC,OAAsB;QACpB,IAAI,IAAI,CAAC,GAAG,EACV,OAAO,IAAI,0CAAc,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;aAEpK,OAAO,IAAI,0CAAc,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,KAAK,EAAE,IAAI,CAAC,GAAG,EAAE,IAAI,CAAC,QAAQ,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,IAAI,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,MAAM,EAAE,IAAI,CAAC,WAAW;IAE9J;IAEA,uEAAuE,GACvE,IAAI,QAA0B,EAAiB;QAC7C,OAAO,CAAA,2KAAA,WAAO,EAAE,IAAI,EAAE;IACxB;IAEA,8EAA8E,GAC9E,SAAS,QAA0B,EAAiB;QAClD,OAAO,CAAA,2KAAA,gBAAY,EAAE,IAAI,EAAE;IAC7B;IAEA,kIAAkI,GAClI,IAAI,MAA+B,EAAE,cAA+B,EAAiB;QACnF,OAAO,CAAA,2KAAA,WAAO,EAAE,IAAI,EAAE,QAAQ;IAChC;IAEA;;;GAGC,GACD,MAAM,KAA4B,EAAE,MAAc,EAAE,OAA0B,EAAiB;QAC7F,OAAO,CAAA,2KAAA,aAAS,EAAE,IAAI,EAAE,OAAO,QAAQ;IACzC;IAEA,0DAA0D,GAC1D,SAAe;QACb,OAAO,CAAA,yKAAA,cAAU,EAAE,IAAI;IACzB;IAEC,0GAA0G,GAC3G,WAAmB;QACjB,OAAO,CAAA,qKAAA,wBAAoB,EAAE,IAAI;IACnC;IAEC,8DAA8D,GAC/D,mBAA2B;QACzB,OAAO,IAAI,CAAC,MAAM,GAAG,WAAW;IAClC;IAEA,yJAAyJ,GACzJ,QAAQ,CAAkD,EAAU;QAClE,6BAA6B;QAC7B,OAAO,IAAI,CAAC,MAAM,GAAG,OAAO,KAAK,CAAA,yKAAA,UAAM,EAAE,GAAG,IAAI,CAAC,QAAQ,EAAE,MAAM,GAAG,OAAO;IAC7E;IAtEA,YAAY,GAAG,IAAW,CAAE;QAhC5B,CAAA,GAAA,+LAAA,CAAA,IAAA,EAAA,IAAA,EAAA,8BAAA;;mBAAA,KAAA;;QAiCE,IAAI,CAAC,UAAU,KAAK,MAAM,OAAO,IAAI,GAAG,gCAAU;QAClD,IAAI,WAAW,KAAK,KAAK;QACzB,IAAI,SAAS,KAAK,KAAK;QACvB,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,IAAI,GAAG;QACZ,IAAI,CAAC,KAAK,GAAG;QACb,IAAI,CAAC,GAAG,GAAG;QACX,IAAI,CAAC,QAAQ,GAAG;QAChB,IAAI,CAAC,MAAM,GAAG;QACd,IAAI,CAAC,IAAI,GAAG,KAAK,KAAK,MAAM;QAC5B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,MAAM,GAAG,KAAK,KAAK,MAAM;QAC9B,IAAI,CAAC,WAAW,GAAG,KAAK,KAAK,MAAM;QAEnC,CAAA,2KAAA,YAAQ,EAAE,IAAI;IAChB;AAsDF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1643, "column": 0}, "map": {"version": 3, "file": "BuddhistCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/BuddhistCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\n\nconst BUDDHIST_ERA_START = -543;\n\n/**\n * The Buddhist calendar is the same as the Gregorian calendar, but counts years\n * starting from the birth of Buddha in 543 BC (Gregorian). It supports only one\n * era, identified as 'BE'.\n */\nexport class BuddhistCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'buddhist';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let gregorianDate = super.fromJulianDay(jd);\n    let year = getExtendedYear(gregorianDate.era, gregorianDate.year);\n    return new CalendarDate(\n      this,\n      year - BUDDHIST_ERA_START,\n      gregorianDate.month,\n      gregorianDate.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras(): string[] {\n    return ['BE'];\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  balanceDate(): void {}\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(date.year + BUDDHIST_ERA_START);\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAMhG,MAAM,2CAAqB,CAAA;AAOpB,MAAM,kDAAyB,CAAA,gLAAA,oBAAgB;IAGpD,cAAc,EAAU,EAAgB;QACtC,IAAI,gBAAgB,KAAK,CAAC,cAAc;QACxC,IAAI,OAAO,CAAA,gLAAA,kBAAc,EAAE,cAAc,GAAG,EAAE,cAAc,IAAI;QAChE,OAAO,IAAI,CAAA,2KAAA,eAAW,EACpB,IAAI,EACJ,OAAO,0CACP,cAAc,KAAK,EACnB,cAAc,GAAG;IAErB;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,KAAK,CAAC,YAAY,kCAAY;IACvC;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,KAAK,CAAC,eAAe,kCAAY;IAC1C;IAEA,cAAoB,CAAC;;QA1BhB,KAAA,IAAA,OAAA,IAAA,CACL,UAAA,GAAiC;;AA0BnC;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAA,gLAAA,mBAAe,EAAE,KAAK,IAAI,GAAG;IAC/C,OAAO,IAAI,CAAA,2KAAA,eAAW,EACpB,KACA,MACA,KAAK,KAAK,EACV,KAAK,GAAG;AAEZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1697, "column": 0}, "map": {"version": 3, "file": "EthiopicCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/EthiopicCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {Mutable} from '../utils';\n\nconst ETHIOPIC_EPOCH = 1723856;\nconst COPTIC_EPOCH = 1824665;\n\n// The delta between Amete Alem 1 and Amete Mihret 1\n// AA 5501 = AM 1\nconst AMETE_MIHRET_DELTA = 5500;\n\nfunction ceToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return (\n    epoch                   // difference from Julian epoch to 1,1,1\n    + 365 * year            // number of days from years\n    + Math.floor(year / 4)  // extra day of leap year\n    + 30 * (month - 1)      // number of days from months (1 based)\n    + day - 1               // number of days for present month (1 based)\n  );\n}\n\nfunction julianDayToCE(epoch: number, jd: number) {\n  let year = Math.floor((4 * (jd - epoch)) / 1461);\n  let month = 1 + Math.floor((jd - ceToJulianDay(epoch, year, 1, 1)) / 30);\n  let day = jd + 1 - ceToJulianDay(epoch, year, month, 1);\n  return [year, month, day];\n}\n\nfunction getLeapDay(year: number) {\n  return Math.floor((year % 4) / 3);\n}\n\nfunction getDaysInMonth(year: number, month: number) {\n  // The Ethiopian and Coptic calendars have 13 months, 12 of 30 days each and\n  // an intercalary month at the end of the year of 5 or 6 days, depending whether\n  // the year is a leap year or not. The Leap Year follows the same rules as the\n  // Julian Calendar so that the extra month always has six days in the year before\n  // a Julian Leap Year.\n  if (month % 13 !== 0) {\n    // not intercalary month\n    return 30;\n  } else {\n    // intercalary month 5 days + possible leap day\n    return getLeapDay(year) + 5;\n  }\n}\n\n/**\n * The Ethiopic calendar system is the official calendar used in Ethiopia.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'AA' and 'AM'.\n */\nexport class EthiopicCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'ethiopic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    let era = 'AM';\n    if (year <= 0) {\n      era = 'AA';\n      year += AMETE_MIHRET_DELTA;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'AA') {\n      year -= AMETE_MIHRET_DELTA;\n    }\n\n    return ceToJulianDay(ETHIOPIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(): number {\n    return 13;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return 365 + getLeapDay(date.year);\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-31 gregorian is 9992-20-02 ethiopic.\n    // Round down to 9991 for the last full year.\n    // AA 9999-01-01 ethiopic is 4506-09-30 gregorian.\n    return date.era === 'AA' ? 9999 : 9991;\n  }\n\n  getEras(): string[] {\n    return ['AA', 'AM'];\n  }\n}\n\n/**\n * The Ethiopic (Amete Alem) calendar is the same as the modern Ethiopic calendar,\n * except years were measured from a different epoch. Only one era is supported: 'AA'.\n */\nexport class EthiopicAmeteAlemCalendar extends EthiopicCalendar {\n  identifier: CalendarIdentifier = 'ethioaa'; // also known as 'ethiopic-amete-alem' in ICU\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(ETHIOPIC_EPOCH, jd);\n    year += AMETE_MIHRET_DELTA;\n    return new CalendarDate(this, 'AA', year, month, day);\n  }\n\n  getEras(): string[] {\n    return ['AA'];\n  }\n\n  getYearsInEra(): number {\n    // 9999-13-04 ethioaa is the maximum date, which is equivalent to 4506-09-29 gregorian.\n    return 9999;\n  }\n}\n\n/**\n * The Coptic calendar is similar to the Ethiopic calendar.\n * It includes 12 months of 30 days each, plus 5 or 6 intercalary days depending\n * on whether it is a leap year. Two eras are supported: 'BCE' and 'CE'.\n */\nexport class CopticCalendar extends EthiopicCalendar {\n  identifier: CalendarIdentifier = 'coptic';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let [year, month, day] = julianDayToCE(COPTIC_EPOCH, jd);\n    let era = 'CE';\n    if (year <= 0) {\n      era = 'BCE';\n      year = 1 - year;\n    }\n\n    return new CalendarDate(this, era, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return ceToJulianDay(COPTIC_EPOCH, year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let year = date.year;\n    if (date.era === 'BCE') {\n      year = 1 - year;\n    }\n\n    return getDaysInMonth(year, date.month);\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'BCE';\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    if (date.year <= 0) {\n      date.era = date.era === 'BCE' ? 'CE' : 'BCE';\n      date.year = 1 - date.year;\n    }\n  }\n\n  getEras(): string[] {\n    return ['BCE', 'CE'];\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // 9999-12-30 gregorian is 9716-02-20 coptic.\n    // Round down to 9715 for the last full year.\n    // BCE 9999-01-01 coptic is BC 9716-06-15 gregorian.\n    return date.era === 'BCE' ? 9999 : 9715;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAMhG,MAAM,uCAAiB;AACvB,MAAM,qCAAe;AAErB,oDAAoD;AACpD,iBAAiB;AACjB,MAAM,2CAAqB;AAE3B,SAAS,oCAAc,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,GAAW;IAC5E,OACE,MAAwB,wCAAwC;OAC9D,MAAM,KAAgB,4BAA4B;OAClD,KAAK,KAAK,CAAC,OAAO,GAAI,yBAAyB;OAC/C,KAAM,CAAA,QAAQ,EAAQ,uCAAuC;IAA/C,IACd,MAAM,EAAgB,6CAA6C;;AAEzE;AAEA,SAAS,oCAAc,KAAa,EAAE,EAAU;IAC9C,IAAI,OAAO,KAAK,KAAK,CAAE,IAAK,CAAA,KAAK,KAAI,IAAM;IAC3C,IAAI,QAAQ,IAAI,KAAK,KAAK,CAAE,CAAA,KAAK,oCAAc,OAAO,MAAM,GAAG,EAAC,IAAK;IACrE,IAAI,MAAM,KAAK,IAAI,oCAAc,OAAO,MAAM,OAAO;IACrD,OAAO;QAAC;QAAM;QAAO;KAAI;AAC3B;AAEA,SAAS,iCAAW,IAAY;IAC9B,OAAO,KAAK,KAAK,CAAE,OAAO,IAAK;AACjC;AAEA,SAAS,qCAAe,IAAY,EAAE,KAAa;IACjD,4EAA4E;IAC5E,gFAAgF;IAChF,8EAA8E;IAC9E,iFAAiF;IACjF,sBAAsB;IACtB,IAAI,QAAQ,OAAO,GACjB,AACA,OAAO,iBADiB;SAIxB,OAAO,iCAAW,QAAQ;AAE9B;AAOO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,CAAC,MAAM,OAAO,IAAI,GAAG,oCAAc,sCAAgB;QACvD,IAAI,MAAM;QACV,IAAI,QAAQ,GAAG;YACb,MAAM;YACN,QAAQ;QACV;QAEA,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,KAAK,MAAM,OAAO;IAClD;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,KAAK,GAAG,KAAK,MACf,QAAQ;QAGV,OAAO,oCAAc,sCAAgB,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;IACjE;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,qCAAe,KAAK,IAAI,EAAE,KAAK,KAAK;IAC7C;IAEA,kBAA0B;QACxB,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,MAAM,iCAAW,KAAK,IAAI;IACnC;IAEA,cAAc,IAAqB,EAAU;QAC3C,+CAA+C;QAC/C,6CAA6C;QAC7C,kDAAkD;QAClD,OAAO,KAAK,GAAG,KAAK,OAAO,OAAO;IACpC;IAEA,UAAoB;QAClB,OAAO;YAAC;YAAM;SAAK;IACrB;;aA3CA,UAAA,GAAiC;;AA4CnC;AAMO,MAAM,kDAAkC;IAG7C,cAAc,EAAU,EAAgB;QACtC,IAAI,CAAC,MAAM,OAAO,IAAI,GAAG,oCAAc,sCAAgB;QACvD,QAAQ;QACR,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,MAAM,MAAM,OAAO;IACnD;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;IAEA,gBAAwB;QACtB,uFAAuF;QACvF,OAAO;IACT;;QAhBK,KAAA,IAAA,OAAA,IAAA,CACL,UAAA,GAAiC,UAAW,6CAA6C;;;AAgB3F;AAOO,MAAM,kDAAuB;IAGlC,cAAc,EAAU,EAAgB;QACtC,IAAI,CAAC,MAAM,OAAO,IAAI,GAAG,oCAAc,oCAAc;QACrD,IAAI,MAAM;QACV,IAAI,QAAQ,GAAG;YACb,MAAM;YACN,OAAO,IAAI;QACb;QAEA,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,KAAK,MAAM,OAAO;IAClD;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,KAAK,GAAG,KAAK,OACf,OAAO,IAAI;QAGb,OAAO,oCAAc,oCAAc,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;IAC/D;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,OAAO,KAAK,IAAI;QACpB,IAAI,KAAK,GAAG,KAAK,OACf,OAAO,IAAI;QAGb,OAAO,qCAAe,MAAM,KAAK,KAAK;IACxC;IAEA,aAAa,IAAqB,EAAW;QAC3C,OAAO,KAAK,GAAG,KAAK;IACtB;IAEA,YAAY,IAA8B,EAAQ;QAChD,IAAI,KAAK,IAAI,IAAI,GAAG;YAClB,KAAK,GAAG,GAAG,KAAK,GAAG,KAAK,QAAQ,OAAO;YACvC,KAAK,IAAI,GAAG,IAAI,KAAK,IAAI;QAC3B;IACF;IAEA,UAAoB;QAClB,OAAO;YAAC;YAAO;SAAK;IACtB;IAEA,cAAc,IAAqB,EAAU;QAC3C,6CAA6C;QAC7C,6CAA6C;QAC7C,oDAAoD;QACpD,OAAO,KAAK,GAAG,KAAK,QAAQ,OAAO;IACrC;;QApDK,KAAA,IAAA,OAAA,IAAA,CACL,UAAA,GAAiC;;AAoDnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1864, "column": 0}, "map": {"version": 3, "file": "HebrewCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/HebrewCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod, Mutable} from '../utils';\n\nconst HEBREW_EPOCH = 347997;\n\n// Hebrew date calculations are performed in terms of days, hours, and\n// \"parts\" (or halakim), which are 1/1080 of an hour, or 3 1/3 seconds.\nconst HOUR_PARTS = 1080;\nconst DAY_PARTS  = 24 * HOUR_PARTS;\n\n// An approximate value for the length of a lunar month.\n// It is used to calculate the approximate year and month of a given\n// absolute date.\nconst MONTH_DAYS = 29;\nconst MONTH_FRACT = 12 * HOUR_PARTS + 793;\nconst MONTH_PARTS = MONTH_DAYS * DAY_PARTS + MONTH_FRACT;\n\nfunction isLeapYear(year: number) {\n  return mod(year * 7 + 1, 19) < 7;\n}\n\n// Test for delay of start of new year and to avoid\n// Sunday, Wednesday, and Friday as start of the new year.\nfunction hebrewDelay1(year: number) {\n  let months = Math.floor((235 * year - 234) / 19);\n  let parts = 12084 + 13753 * months;\n  let day = months * 29 + Math.floor(parts / 25920);\n\n  if (mod(3 * (day + 1), 7) < 3) {\n    day += 1;\n  }\n\n  return day;\n}\n\n// Check for delay in start of new year due to length of adjacent years\nfunction hebrewDelay2(year: number) {\n  let last = hebrewDelay1(year - 1);\n  let present = hebrewDelay1(year);\n  let next = hebrewDelay1(year + 1);\n\n  if (next - present === 356) {\n    return 2;\n  }\n\n  if (present - last === 382) {\n    return 1;\n  }\n\n  return 0;\n}\n\nfunction startOfYear(year: number) {\n  return hebrewDelay1(year) + hebrewDelay2(year);\n}\n\nfunction getDaysInYear(year: number) {\n  return startOfYear(year + 1) - startOfYear(year);\n}\n\nfunction getYearType(year: number) {\n  let yearLength = getDaysInYear(year);\n\n  if (yearLength > 380) {\n    yearLength -= 30; // Subtract length of leap month.\n  }\n\n  switch (yearLength) {\n    case 353:\n      return 0; // deficient\n    case 354:\n      return 1; // normal\n    case 355:\n      return 2; // complete\n  }\n}\n\nfunction getDaysInMonth(year: number, month: number): number {\n  // Normalize month numbers from 1 - 13, even on non-leap years\n  if (month >= 6 && !isLeapYear(year)) {\n    month++;\n  }\n\n  // First of all, dispose of fixed-length 29 day months\n  if (month === 4 || month === 7 || month === 9 || month === 11 || month === 13) {\n    return 29;\n  }\n\n  let yearType = getYearType(year);\n\n  // If it's Heshvan, days depend on length of year\n  if (month === 2) {\n    return yearType === 2 ? 30 : 29;\n  }\n\n  // Similarly, Kislev varies with the length of year\n  if (month === 3) {\n    return yearType === 0 ? 29 : 30;\n  }\n\n  // Adar I only exists in leap years\n  if (month === 6) {\n    return isLeapYear(year) ? 30 : 0;\n  }\n\n  return 30;\n}\n\n/**\n * The Hebrew calendar is used in Israel and around the world by the Jewish faith.\n * Years include either 12 or 13 months depending on whether it is a leap year.\n * In leap years, an extra month is inserted at month 6.\n */\nexport class HebrewCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'hebrew';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let d = jd - HEBREW_EPOCH;\n    let m = (d * DAY_PARTS) / MONTH_PARTS;           // Months (approx)\n    let year = Math.floor((19 * m + 234) / 235) + 1; // Years (approx)\n    let ys = startOfYear(year);                      // 1st day of year\n    let dayOfYear = Math.floor(d - ys);\n\n    // Because of the postponement rules, it's possible to guess wrong.  Fix it.\n    while (dayOfYear < 1) {\n      year--;\n      ys = startOfYear(year);\n      dayOfYear = Math.floor(d - ys);\n    }\n\n    // Now figure out which month we're in, and the date within that month\n    let month = 1;\n    let monthStart = 0;\n    while (monthStart < dayOfYear) {\n      monthStart += getDaysInMonth(year, month);\n      month++;\n    }\n\n    month--;\n    monthStart -= getDaysInMonth(year, month);\n\n    let day = dayOfYear - monthStart;\n    return new CalendarDate(this, year, month, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = startOfYear(date.year);\n    for (let month = 1; month < date.month; month++) {\n      jd += getDaysInMonth(date.year, month);\n    }\n\n    return jd + date.day + HEBREW_EPOCH;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return getDaysInMonth(date.year, date.month);\n  }\n\n  getMonthsInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 13 : 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return getDaysInYear(date.year);\n  }\n\n  getYearsInEra(): number {\n    // 6239 gregorian\n    return 9999;\n  }\n\n  getEras(): string[] {\n    return ['AM'];\n  }\n\n  balanceYearMonth(date: Mutable<AnyCalendarDate>, previousDate: AnyCalendarDate): void {\n    // Keep date in the same month when switching between leap years and non leap years\n    if (previousDate.year !== date.year) {\n      if (isLeapYear(previousDate.year) && !isLeapYear(date.year) && previousDate.month > 6) {\n        date.month--;\n      } else if (!isLeapYear(previousDate.year) && isLeapYear(date.year) && previousDate.month > 6) {\n        date.month++;\n      }\n    }\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAMhG,MAAM,qCAAe;AAErB,sEAAsE;AACtE,uEAAuE;AACvE,MAAM,mCAAa;AACnB,MAAM,kCAAa,KAAK;AAExB,wDAAwD;AACxD,oEAAoE;AACpE,iBAAiB;AACjB,MAAM,mCAAa;AACnB,MAAM,oCAAc,KAAK,mCAAa;AACtC,MAAM,oCAAc,mCAAa,kCAAY;AAE7C,SAAS,iCAAW,IAAY;IAC9B,OAAO,CAAA,oKAAA,MAAE,EAAE,OAAO,IAAI,GAAG,MAAM;AACjC;AAEA,mDAAmD;AACnD,0DAA0D;AAC1D,SAAS,mCAAa,IAAY;IAChC,IAAI,SAAS,KAAK,KAAK,CAAE,CAAA,MAAM,OAAO,GAAE,IAAK;IAC7C,IAAI,QAAQ,QAAQ,QAAQ;IAC5B,IAAI,MAAM,SAAS,KAAK,KAAK,KAAK,CAAC,QAAQ;IAE3C,IAAI,CAAA,oKAAA,MAAE,EAAE,IAAK,CAAA,MAAM,CAAA,GAAI,KAAK,GAC1B,OAAO;IAGT,OAAO;AACT;AAEA,uEAAuE;AACvE,SAAS,mCAAa,IAAY;IAChC,IAAI,OAAO,mCAAa,OAAO;IAC/B,IAAI,UAAU,mCAAa;IAC3B,IAAI,OAAO,mCAAa,OAAO;IAE/B,IAAI,OAAO,YAAY,KACrB,OAAO;IAGT,IAAI,UAAU,SAAS,KACrB,OAAO;IAGT,OAAO;AACT;AAEA,SAAS,kCAAY,IAAY;IAC/B,OAAO,mCAAa,QAAQ,mCAAa;AAC3C;AAEA,SAAS,oCAAc,IAAY;IACjC,OAAO,kCAAY,OAAO,KAAK,kCAAY;AAC7C;AAEA,SAAS,kCAAY,IAAY;IAC/B,IAAI,aAAa,oCAAc;IAE/B,IAAI,aAAa,KACf,cAAc,IAAI,iCAAiC;IAGrD,OAAQ;QACN,KAAK;YACH,OAAO,GAAG,YAAY;QACxB,KAAK;YACH,OAAO,GAAG,SAAS;QACrB,KAAK;YACH,OAAO,GAAG,WAAW;IACzB;AACF;AAEA,SAAS,qCAAe,IAAY,EAAE,KAAa;IACjD,8DAA8D;IAC9D,IAAI,SAAS,KAAK,CAAC,iCAAW,OAC5B;IAGF,sDAAsD;IACtD,IAAI,UAAU,KAAK,UAAU,KAAK,UAAU,KAAK,UAAU,MAAM,UAAU,IACzE,OAAO;IAGT,IAAI,WAAW,kCAAY;IAE3B,iDAAiD;IACjD,IAAI,UAAU,GACZ,OAAO,aAAa,IAAI,KAAK;IAG/B,mDAAmD;IACnD,IAAI,UAAU,GACZ,OAAO,aAAa,IAAI,KAAK;IAG/B,mCAAmC;IACnC,IAAI,UAAU,GACZ,OAAO,iCAAW,QAAQ,KAAK;IAGjC,OAAO;AACT;AAOO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,IAAI,KAAK;QACb,IAAI,IAAK,IAAI,kCAAa,mCAAuB,kBAAkB;QACnE,IAAI,OAAO,KAAK,KAAK,CAAE,CAAA,KAAK,IAAI,GAAE,IAAK,OAAO,GAAG,iBAAiB;QAClE,IAAI,KAAK,kCAAY,OAA4B,kBAAkB;QACnE,IAAI,YAAY,KAAK,KAAK,CAAC,IAAI;QAE/B,4EAA4E;QAC5E,MAAO,YAAY,EAAG;YACpB;YACA,KAAK,kCAAY;YACjB,YAAY,KAAK,KAAK,CAAC,IAAI;QAC7B;QAEA,sEAAsE;QACtE,IAAI,QAAQ;QACZ,IAAI,aAAa;QACjB,MAAO,aAAa,UAAW;YAC7B,cAAc,qCAAe,MAAM;YACnC;QACF;QAEA;QACA,cAAc,qCAAe,MAAM;QAEnC,IAAI,MAAM,YAAY;QACtB,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,MAAM,OAAO;IAC7C;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,KAAK,kCAAY,KAAK,IAAI;QAC9B,IAAK,IAAI,QAAQ,GAAG,QAAQ,KAAK,KAAK,EAAE,QACtC,MAAM,qCAAe,KAAK,IAAI,EAAE;QAGlC,OAAO,KAAK,KAAK,GAAG,GAAG;IACzB;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,qCAAe,KAAK,IAAI,EAAE,KAAK,KAAK;IAC7C;IAEA,gBAAgB,IAAqB,EAAU;QAC7C,OAAO,iCAAW,KAAK,IAAI,IAAI,KAAK;IACtC;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,oCAAc,KAAK,IAAI;IAChC;IAEA,gBAAwB;QACtB,iBAAiB;QACjB,OAAO;IACT;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;IAEA,iBAAiB,IAA8B,EAAE,YAA6B,EAAQ;QACpF,mFAAmF;QACnF,IAAI,aAAa,IAAI,KAAK,KAAK,IAAI,EAAE;YACnC,IAAI,iCAAW,aAAa,IAAI,KAAK,CAAC,iCAAW,KAAK,IAAI,KAAK,aAAa,KAAK,GAAG,GAClF,KAAK,KAAK;iBACL,IAAI,CAAC,iCAAW,aAAa,IAAI,KAAK,iCAAW,KAAK,IAAI,KAAK,aAAa,KAAK,GAAG,GACzF,KAAK,KAAK;QAEd;IACF;;aAtEA,UAAA,GAAiC;;AAuEnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2014, "column": 0}, "map": {"version": 3, "file": "IndianCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/IndianCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, GregorianCalendar, gregorianToJulianDay, isLeapYear} from './GregorianCalendar';\n\n// Starts in 78 AD,\nconst INDIAN_ERA_START = 78;\n\n// The Indian year starts 80 days later than the Gregorian year.\nconst INDIAN_YEAR_START = 80;\n\n/**\n * The Indian National Calendar is similar to the Gregorian calendar, but with\n * years numbered since the Saka era in 78 AD (Gregorian). There are 12 months\n * in each year, with either 30 or 31 days. Only one era identifier is supported: 'saka'.\n */\nexport class IndianCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'indian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    // Gregorian date for Julian day\n    let date = super.fromJulianDay(jd);\n\n    // Year in Saka era\n    let indianYear = date.year - INDIAN_ERA_START;\n\n    // Day number in Gregorian year (starting from 0)\n    let yDay = jd - gregorianToJulianDay(date.era, date.year, 1, 1);\n\n    let leapMonth: number;\n    if (yDay < INDIAN_YEAR_START) {\n      //  Day is at the end of the preceding Saka year\n      indianYear--;\n\n      // Days in leapMonth this year, previous Gregorian year\n      leapMonth = isLeapYear(date.year - 1) ? 31 : 30;\n      yDay += leapMonth + (31 * 5) + (30 * 3) + 10;\n    } else {\n      // Days in leapMonth this year\n      leapMonth = isLeapYear(date.year) ? 31 : 30;\n      yDay -= INDIAN_YEAR_START;\n    }\n\n    let indianMonth: number;\n    let indianDay: number;\n    if (yDay < leapMonth) {\n      indianMonth = 1;\n      indianDay = yDay + 1;\n    } else {\n      let mDay = yDay - leapMonth;\n      if (mDay < (31 * 5)) {\n        indianMonth = Math.floor(mDay / 31) + 2;\n        indianDay = (mDay % 31) + 1;\n      } else {\n        mDay -= 31 * 5;\n        indianMonth = Math.floor(mDay / 30) + 7;\n        indianDay = (mDay % 30) + 1;\n      }\n    }\n\n    return new CalendarDate(this, indianYear, indianMonth, indianDay);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let extendedYear = date.year + INDIAN_ERA_START;\n    let [era, year] = fromExtendedYear(extendedYear);\n\n    let leapMonth: number;\n    let jd: number;\n    if (isLeapYear(year)) {\n      leapMonth = 31;\n      jd = gregorianToJulianDay(era, year, 3, 21);\n    } else {\n      leapMonth = 30;\n      jd = gregorianToJulianDay(era, year, 3, 22);\n    }\n\n    if (date.month === 1) {\n      return jd + date.day - 1;\n    }\n\n    jd += leapMonth + Math.min(date.month - 2, 5) * 31;\n\n    if (date.month >= 8) {\n      jd += (date.month - 7) * 30;\n    }\n\n    jd += date.day - 1;\n    return jd;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month === 1 && isLeapYear(date.year + INDIAN_ERA_START)) {\n      return 31;\n    }\n\n    if (date.month >= 2 && date.month <= 6) {\n      return 31;\n    }\n\n    return 30;\n  }\n\n  getYearsInEra(): number {\n    // 9999-12-31 gregorian is 9920-10-10 indian.\n    // Round down to 9919 for the last full year.\n    return 9919;\n  }\n\n  getEras(): string[] {\n    return ['saka'];\n  }\n\n  balanceDate(): void {}\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAMhG,mBAAmB;AACnB,MAAM,yCAAmB;AAEzB,gEAAgE;AAChE,MAAM,0CAAoB;AAOnB,MAAM,kDAAuB,CAAA,gLAAA,oBAAgB;IAGlD,cAAc,EAAU,EAAgB;QACtC,gCAAgC;QAChC,IAAI,OAAO,KAAK,CAAC,cAAc;QAE/B,mBAAmB;QACnB,IAAI,aAAa,KAAK,IAAI,GAAG;QAE7B,iDAAiD;QACjD,IAAI,OAAO,KAAK,CAAA,gLAAA,uBAAmB,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI,EAAE,GAAG;QAE7D,IAAI;QACJ,IAAI,OAAO,yCAAmB;YAC5B,gDAAgD;YAChD;YAEA,uDAAuD;YACvD,YAAY,CAAA,gLAAA,aAAS,EAAE,KAAK,IAAI,GAAG,KAAK,KAAK;YAC7C,QAAQ,YAAa,MAAW,KAAU;QAC5C,OAAO;YACL,8BAA8B;YAC9B,YAAY,CAAA,gLAAA,aAAS,EAAE,KAAK,IAAI,IAAI,KAAK;YACzC,QAAQ;QACV;QAEA,IAAI;QACJ,IAAI;QACJ,IAAI,OAAO,WAAW;YACpB,cAAc;YACd,YAAY,OAAO;QACrB,OAAO;YACL,IAAI,OAAO,OAAO;YAClB,IAAI,OAAQ,KAAS;gBACnB,cAAc,KAAK,KAAK,CAAC,OAAO,MAAM;gBACtC,YAAa,OAAO,KAAM;YAC5B,OAAO;gBACL,QAAQ;gBACR,cAAc,KAAK,KAAK,CAAC,OAAO,MAAM;gBACtC,YAAa,OAAO,KAAM;YAC5B;QACF;QAEA,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,YAAY,aAAa;IACzD;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,eAAe,KAAK,IAAI,GAAG;QAC/B,IAAI,CAAC,KAAK,KAAK,GAAG,CAAA,gLAAA,mBAAe,EAAE;QAEnC,IAAI;QACJ,IAAI;QACJ,IAAI,CAAA,gLAAA,aAAS,EAAE,OAAO;YACpB,YAAY;YACZ,KAAK,CAAA,gLAAA,uBAAmB,EAAE,KAAK,MAAM,GAAG;QAC1C,OAAO;YACL,YAAY;YACZ,KAAK,CAAA,gLAAA,uBAAmB,EAAE,KAAK,MAAM,GAAG;QAC1C;QAEA,IAAI,KAAK,KAAK,KAAK,GACjB,OAAO,KAAK,KAAK,GAAG,GAAG;QAGzB,MAAM,YAAY,KAAK,GAAG,CAAC,KAAK,KAAK,GAAG,GAAG,KAAK;QAEhD,IAAI,KAAK,KAAK,IAAI,GAChB,MAAO,CAAA,KAAK,KAAK,GAAG,CAAA,IAAK;QAG3B,MAAM,KAAK,GAAG,GAAG;QACjB,OAAO;IACT;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,KAAK,KAAK,KAAK,KAAK,CAAA,gLAAA,aAAS,EAAE,KAAK,IAAI,GAAG,yCAC7C,OAAO;QAGT,IAAI,KAAK,KAAK,IAAI,KAAK,KAAK,KAAK,IAAI,GACnC,OAAO;QAGT,OAAO;IACT;IAEA,gBAAwB;QACtB,6CAA6C;QAC7C,6CAA6C;QAC7C,OAAO;IACT;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAO;IACjB;IAEA,cAAoB,CAAC;;QAjGhB,KAAA,IAAA,OAAA,IAAA,CACL,UAAA,GAAiC;;AAiGnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2121, "column": 0}, "map": {"version": 3, "file": "IslamicCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/IslamicCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\n\nconst CIVIL_EPOC = 1948440; // CE 622 July 16 Friday (Julian calendar) / CE 622 July 19 (Gregorian calendar)\nconst ASTRONOMICAL_EPOC = 1948439; // CE 622 July 15 Thursday (Julian calendar)\nconst UMALQURA_YEAR_START = 1300;\nconst UMALQURA_YEAR_END = 1600;\nconst UMALQURA_START_DAYS = 460322;\n\nfunction islamicToJulianDay(epoch: number, year: number, month: number, day: number): number {\n  return day +\n    Math.ceil(29.5 * (month - 1)) +\n    (year - 1) * 354 +\n    Math.floor((3 + 11 * year) / 30) +\n    epoch - 1;\n}\n\nfunction julianDayToIslamic(calendar: Calendar, epoch: number, jd: number) {\n  let year = Math.floor((30 * (jd - epoch) + 10646) / 10631);\n  let month = Math.min(12, Math.ceil((jd - (29 + islamicToJulianDay(epoch, year, 1, 1))) / 29.5) + 1);\n  let day = jd - islamicToJulianDay(epoch, year, month, 1) + 1;\n\n  return new CalendarDate(calendar, year, month, day);\n}\n\nfunction isLeapYear(year: number): boolean {\n  return (14 + 11 * year) % 30 < 11;\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The civil variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Friday, July 16 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicCivilCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'islamic-civil';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, CIVIL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(CIVIL_EPOC, date.year, date.month, date.day);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    let length = 29 + date.month % 2;\n    if (date.month === 12 && isLeapYear(date.year)) {\n      length++;\n    }\n\n    return length;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    return isLeapYear(date.year) ? 355 : 354;\n  }\n\n  getYearsInEra(): number {\n    // 9999 gregorian\n    return 9665;\n  }\n\n  getEras(): string[] {\n    return ['AH'];\n  }\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The tabular variant uses simple arithmetic rules rather than astronomical calculations to approximate\n * the traditional calendar, which is based on sighting of the crescent moon. It uses Thursday, July 15 622 CE (Julian) as the epoch.\n * Each year has 12 months, with either 354 or 355 days depending on whether it is a leap year.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicTabularCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-tbla';\n\n  fromJulianDay(jd: number): CalendarDate {\n    return julianDayToIslamic(this, ASTRONOMICAL_EPOC, jd);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return islamicToJulianDay(ASTRONOMICAL_EPOC, date.year, date.month, date.day);\n  }\n}\n\n// Generated by scripts/generate-umalqura.js\nconst UMALQURA_DATA = 'qgpUDckO1AbqBmwDrQpVBakGkgepC9QF2gpcBS0NlQZKB1QLagutBa4ETwoXBYsGpQbVCtYCWwmdBE0KJg2VDawFtgm6AlsKKwWVCsoG6Qr0AnYJtgJWCcoKpAvSC9kF3AJtCU0FpQpSC6ULtAW2CVcFlwJLBaMGUgdlC2oFqworBZUMSg2lDcoF1gpXCasESwmlClILagt1BXYCtwhbBFUFqQW0BdoJ3QRuAjYJqgpUDbIN1QXaAlsJqwRVCkkLZAtxC7QFtQpVCiUNkg7JDtQG6QprCasEkwpJDaQNsg25CroEWworBZUKKgtVC1wFvQQ9Ah0JlQpKC1oLbQW2AjsJmwRVBqkGVAdqC2wFrQpVBSkLkgupC9QF2gpaBasKlQVJB2QHqgu1BbYCVgpNDiULUgtqC60FrgIvCZcESwalBqwG1gpdBZ0ETQoWDZUNqgW1BdoCWwmtBJUFygbkBuoK9QS2AlYJqgpUC9IL2QXqAm0JrQSVCkoLpQuyBbUJ1gSXCkcFkwZJB1ULagVrCisFiwpGDaMNygXWCtsEawJLCaUKUgtpC3UFdgG3CFsCKwVlBbQF2gntBG0BtgimClINqQ3UBdoKWwmrBFMGKQdiB6kLsgW1ClUFJQuSDckO0gbpCmsFqwRVCikNVA2qDbUJugQ7CpsETQqqCtUK2gJdCV4ELgqaDFUNsga5BroEXQotBZUKUguoC7QLuQXaAloJSgukDdEO6AZqC20FNQWVBkoNqA3UDdoGWwWdAisGFQtKC5ULqgWuCi4JjwwnBZUGqgbWCl0FnQI=';\nlet UMALQURA_MONTHLENGTH: Uint16Array;\nlet UMALQURA_YEAR_START_TABLE: Uint32Array;\n\nfunction umalquraYearStart(year: number): number {\n  return UMALQURA_START_DAYS + UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\nfunction umalquraMonthLength(year: number, month: number): number {\n  let idx = (year - UMALQURA_YEAR_START);\n  let mask = (0x01 << (11 - (month - 1)));\n  if ((UMALQURA_MONTHLENGTH[idx] & mask) === 0) {\n    return 29;\n  } else {\n    return 30;\n  }\n}\n\nfunction umalquraMonthStart(year: number, month: number): number {\n  let day = umalquraYearStart(year);\n  for (let i = 1; i < month; i++) {\n    day += umalquraMonthLength(year, i);\n  }\n  return day;\n}\n\nfunction umalquraYearLength(year: number): number {\n  return UMALQURA_YEAR_START_TABLE[year + 1 - UMALQURA_YEAR_START] - UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START];\n}\n\n/**\n * The Islamic calendar, also known as the \"Hijri\" calendar, is used throughout much of the Arab world.\n * The Umalqura variant is primarily used in Saudi Arabia. It is a lunar calendar, based on astronomical\n * calculations that predict the sighting of a crescent moon. Month and year lengths vary between years\n * depending on these calculations.\n * Learn more about the available Islamic calendars [here](https://cldr.unicode.org/development/development-process/design-proposals/islamic-calendar-types).\n */\nexport class IslamicUmalquraCalendar extends IslamicCivilCalendar {\n  identifier: CalendarIdentifier = 'islamic-umalqura';\n\n  constructor() {\n    super();\n    if (!UMALQURA_MONTHLENGTH) {\n      UMALQURA_MONTHLENGTH = new Uint16Array(Uint8Array.from(atob(UMALQURA_DATA), c => c.charCodeAt(0)).buffer);\n    }\n\n    if (!UMALQURA_YEAR_START_TABLE) {\n      UMALQURA_YEAR_START_TABLE = new Uint32Array(UMALQURA_YEAR_END - UMALQURA_YEAR_START + 1);\n\n      let yearStart = 0;\n      for (let year = UMALQURA_YEAR_START; year <= UMALQURA_YEAR_END; year++) {\n        UMALQURA_YEAR_START_TABLE[year - UMALQURA_YEAR_START] = yearStart;\n        for (let i = 1; i <= 12; i++) {\n          yearStart += umalquraMonthLength(year, i);\n        }\n      }\n    }\n  }\n\n  fromJulianDay(jd: number): CalendarDate {\n    let days = jd - CIVIL_EPOC;\n    let startDays = umalquraYearStart(UMALQURA_YEAR_START);\n    let endDays = umalquraYearStart(UMALQURA_YEAR_END);\n    if (days < startDays || days > endDays) {\n      return super.fromJulianDay(jd);\n    } else {\n      let y = UMALQURA_YEAR_START - 1;\n      let m = 1;\n      let d = 1;\n      while (d > 0) {\n        y++;\n        d = days - umalquraYearStart(y) + 1;\n        let yearLength = umalquraYearLength(y);\n        if (d === yearLength) {\n          m = 12;\n          break;\n        } else if (d < yearLength) {\n          let monthLength = umalquraMonthLength(y, m);\n          m = 1;\n          while (d > monthLength) {\n            d -= monthLength;\n            m++;\n            monthLength = umalquraMonthLength(y, m);\n          }\n          break;\n        }\n      }\n\n      return new CalendarDate(this, y, m, (days - umalquraMonthStart(y, m) + 1));\n    }\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.toJulianDay(date);\n    }\n\n    return CIVIL_EPOC + umalquraMonthStart(date.year, date.month) + (date.day - 1);\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInMonth(date);\n    }\n\n    return umalquraMonthLength(date.year, date.month);\n  }\n\n  getDaysInYear(date: AnyCalendarDate): number {\n    if (date.year < UMALQURA_YEAR_START || date.year > UMALQURA_YEAR_END) {\n      return super.getDaysInYear(date);\n    }\n\n    return umalquraYearLength(date.year);\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAKhG,MAAM,mCAAa,SAAS,gFAAgF;AAC5G,MAAM,0CAAoB,SAAS,4CAA4C;AAC/E,MAAM,4CAAsB;AAC5B,MAAM,0CAAoB;AAC1B,MAAM,4CAAsB;AAE5B,SAAS,yCAAmB,KAAa,EAAE,IAAY,EAAE,KAAa,EAAE,GAAW;IACjF,OAAO,MACL,KAAK,IAAI,CAAC,OAAQ,CAAA,QAAQ,CAAA,KACzB,CAAA,OAAO,CAAA,IAAK,MACb,KAAK,KAAK,CAAE,CAAA,IAAI,KAAK,IAAG,IAAK,MAC7B,QAAQ;AACZ;AAEA,SAAS,yCAAmB,QAAkB,EAAE,KAAa,EAAE,EAAU;IACvE,IAAI,OAAO,KAAK,KAAK,CAAE,CAAA,KAAM,CAAA,KAAK,KAAI,IAAK,KAAI,IAAK;IACpD,IAAI,QAAQ,KAAK,GAAG,CAAC,IAAI,KAAK,IAAI,CAAE,CAAA,KAAM,CAAA,KAAK,yCAAmB,OAAO,MAAM,GAAG,EAAC,CAAC,IAAK,QAAQ;IACjG,IAAI,MAAM,KAAK,yCAAmB,OAAO,MAAM,OAAO,KAAK;IAE3D,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,UAAU,MAAM,OAAO;AACjD;AAEA,SAAS,iCAAW,IAAY;IAC9B,OAAQ,CAAA,KAAK,KAAK,IAAG,IAAK,KAAK;AACjC;AASO,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,OAAO,yCAAmB,IAAI,EAAE,kCAAY;IAC9C;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,yCAAmB,kCAAY,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IACvE;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,SAAS,KAAK,KAAK,KAAK,GAAG;QAC/B,IAAI,KAAK,KAAK,KAAK,MAAM,iCAAW,KAAK,IAAI,GAC3C;QAGF,OAAO;IACT;IAEA,kBAA0B;QACxB,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,iCAAW,KAAK,IAAI,IAAI,MAAM;IACvC;IAEA,gBAAwB;QACtB,iBAAiB;QACjB,OAAO;IACT;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;;aAlCA,UAAA,GAAiC;;AAmCnC;AASO,MAAM,kDAA+B;IAG1C,cAAc,EAAU,EAAgB;QACtC,OAAO,yCAAmB,IAAI,EAAE,yCAAmB;IACrD;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,yCAAmB,yCAAmB,KAAK,IAAI,EAAE,KAAK,KAAK,EAAE,KAAK,GAAG;IAC9E;;QATK,KAAA,IAAA,OAAA,IAAA,CACL,UAAA,GAAiC;;AASnC;AAEA,4CAA4C;AAC5C,MAAM,sCAAgB;AACtB,IAAI;AACJ,IAAI;AAEJ,SAAS,wCAAkB,IAAY;IACrC,OAAO,4CAAsB,+CAAyB,CAAC,OAAO,0CAAoB;AACpF;AAEA,SAAS,0CAAoB,IAAY,EAAE,KAAa;IACtD,IAAI,MAAO,OAAO;IAClB,IAAI,OAAQ,QAAS,KAAM,CAAA,QAAQ,CAAA;IACnC,IAAK,CAAA,0CAAoB,CAAC,IAAI,GAAG,IAAG,MAAO,GACzC,OAAO;SAEP,OAAO;AAEX;AAEA,SAAS,yCAAmB,IAAY,EAAE,KAAa;IACrD,IAAI,MAAM,wCAAkB;IAC5B,IAAK,IAAI,IAAI,GAAG,IAAI,OAAO,IACzB,OAAO,0CAAoB,MAAM;IAEnC,OAAO;AACT;AAEA,SAAS,yCAAmB,IAAY;IACtC,OAAO,+CAAyB,CAAC,OAAO,IAAI,0CAAoB,GAAG,+CAAyB,CAAC,OAAO,0CAAoB;AAC1H;AASO,MAAM,kDAAgC;IAsB3C,cAAc,EAAU,EAAgB;QACtC,IAAI,OAAO,KAAK;QAChB,IAAI,YAAY,wCAAkB;QAClC,IAAI,UAAU,wCAAkB;QAChC,IAAI,OAAO,aAAa,OAAO,SAC7B,OAAO,KAAK,CAAC,cAAc;aACtB;YACL,IAAI,IAAI,4CAAsB;YAC9B,IAAI,IAAI;YACR,IAAI,IAAI;YACR,MAAO,IAAI,EAAG;gBACZ;gBACA,IAAI,OAAO,wCAAkB,KAAK;gBAClC,IAAI,aAAa,yCAAmB;gBACpC,IAAI,MAAM,YAAY;oBACpB,IAAI;oBACJ;gBACF,OAAO,IAAI,IAAI,YAAY;oBACzB,IAAI,cAAc,0CAAoB,GAAG;oBACzC,IAAI;oBACJ,MAAO,IAAI,YAAa;wBACtB,KAAK;wBACL;wBACA,cAAc,0CAAoB,GAAG;oBACvC;oBACA;gBACF;YACF;YAEA,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,GAAG,GAAI,OAAO,yCAAmB,GAAG,KAAK;QACzE;IACF;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,KAAK,IAAI,GAAG,6CAAuB,KAAK,IAAI,GAAG,yCACjD,OAAO,KAAK,CAAC,YAAY;QAG3B,OAAO,mCAAa,yCAAmB,KAAK,IAAI,EAAE,KAAK,KAAK,IAAK,CAAA,KAAK,GAAG,GAAG,CAAA;IAC9E;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,KAAK,IAAI,GAAG,6CAAuB,KAAK,IAAI,GAAG,yCACjD,OAAO,KAAK,CAAC,eAAe;QAG9B,OAAO,0CAAoB,KAAK,IAAI,EAAE,KAAK,KAAK;IAClD;IAEA,cAAc,IAAqB,EAAU;QAC3C,IAAI,KAAK,IAAI,GAAG,6CAAuB,KAAK,IAAI,GAAG,yCACjD,OAAO,KAAK,CAAC,cAAc;QAG7B,OAAO,yCAAmB,KAAK,IAAI;IACrC;IA1EA,aAAc;QACZ,KAAK,IAAA,IAAA,CAHP,UAAA,GAAiC;QAI/B,IAAI,CAAC,4CACH,6CAAuB,IAAI,YAAY,WAAW,IAAI,CAAC,KAAK,sCAAgB,CAAA,IAAK,EAAE,UAAU,CAAC,IAAI,MAAM;QAG1G,IAAI,CAAC,iDAA2B;YAC9B,kDAA4B,IAAI,YAAY,0CAAoB,4CAAsB;YAEtF,IAAI,YAAY;YAChB,IAAK,IAAI,OAAO,2CAAqB,QAAQ,yCAAmB,OAAQ;gBACtE,+CAAyB,CAAC,OAAO,0CAAoB,GAAG;gBACxD,IAAK,IAAI,IAAI,GAAG,KAAK,IAAI,IACvB,aAAa,0CAAoB,MAAM;YAE3C;QACF;IACF;AA0DF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2284, "column": 0}, "map": {"version": 3, "file": "JapaneseCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/JapaneseCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from the TC39 Temporal proposal.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst ERA_START_DATES = [[1868, 9, 8], [1912, 7, 30], [1926, 12, 25], [1989, 1, 8], [2019, 5, 1]];\nconst ERA_END_DATES = [[1912, 7, 29], [1926, 12, 24], [1989, 1, 7], [2019, 4, 30]];\nconst ERA_ADDENDS = [1867, 1911, 1925, 1988, 2018];\nconst ERA_NAMES = ['meiji', 'taisho', 'showa', 'heisei', 'reiwa'];\n\nfunction findEraFromGregorianDate(date: AnyCalendarDate) {\n  const idx = ERA_START_DATES.findIndex(([year, month, day]) => {\n    if (date.year < year) {\n      return true;\n    }\n\n    if (date.year === year && date.month < month) {\n      return true;\n    }\n\n    if (date.year === year && date.month === month && date.day < day) {\n      return true;\n    }\n\n    return false;\n  });\n\n  if (idx === -1) {\n    return ERA_START_DATES.length - 1;\n  }\n\n  if (idx === 0) {\n    return 0;\n  }\n\n  return idx - 1;\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let eraAddend = ERA_ADDENDS[ERA_NAMES.indexOf(date.era)];\n  if (!eraAddend) {\n    throw new Error('Unknown era: ' + date.era);\n  }\n\n  return new CalendarDate(\n    date.year + eraAddend,\n    date.month,\n    date.day\n  );\n}\n\n/**\n * The Japanese calendar is based on the Gregorian calendar, but with eras for the reign of each Japanese emperor.\n * Whenever a new emperor ascends to the throne, a new era begins and the year starts again from 1.\n * Note that eras before 1868 (Gregorian) are not currently supported by this implementation.\n */\nexport class JapaneseCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'japanese';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let era = findEraFromGregorianDate(date);\n\n    return new CalendarDate(\n      this,\n      ERA_NAMES[era],\n      date.year - ERA_ADDENDS[era],\n      date.month,\n      date.day\n    );\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let gregorianDate = toGregorian(date);\n    let era = findEraFromGregorianDate(gregorianDate);\n\n    if (ERA_NAMES[era] !== date.era) {\n      date.era = ERA_NAMES[era];\n      date.year = gregorianDate.year - ERA_ADDENDS[era];\n    }\n\n    // Constrain in case we went before the first supported era.\n    this.constrainDate(date);\n  }\n\n  constrainDate(date: Mutable<AnyCalendarDate>): void {\n    let idx = ERA_NAMES.indexOf(date.era);\n    let end = ERA_END_DATES[idx];\n    if (end != null) {\n      let [endYear, endMonth, endDay] = end;\n\n      // Constrain the year to the maximum possible value in the era.\n      // Then constrain the month and day fields within that.\n      let maxYear = endYear - ERA_ADDENDS[idx];\n      date.year = Math.max(1, Math.min(maxYear, date.year));\n      if (date.year === maxYear) {\n        date.month = Math.min(endMonth, date.month);\n\n        if (date.month === endMonth) {\n          date.day = Math.min(endDay, date.day);\n        }\n      }\n    }\n\n    if (date.year === 1 && idx >= 0) {\n      let [, startMonth, startDay] = ERA_START_DATES[idx];\n      date.month = Math.max(startMonth, date.month);\n\n      if (date.month === startMonth) {\n        date.day = Math.max(startDay, date.day);\n      }\n    }\n  }\n\n  getEras(): string[] {\n    return ERA_NAMES;\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    // Get the number of years in the era, taking into account the date's month and day fields.\n    let era = ERA_NAMES.indexOf(date.era);\n    let cur = ERA_START_DATES[era];\n    let next = ERA_START_DATES[era + 1];\n    if (next == null) {\n      // 9999 gregorian is the maximum year allowed.\n      return 9999 - cur[0] + 1;\n    }\n\n    let years = next[0] - cur[0];\n\n    if (date.month < next[1] || (date.month === next[1] && date.day < next[2])) {\n      years++;\n    }\n\n    return years;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getMinimumMonthInYear(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start ? start[1] : 1;\n  }\n\n  getMinimumDayInMonth(date: AnyCalendarDate): number {\n    let start = getMinimums(date);\n    return start && date.month === start[1] ? start[2] : 1;\n  }\n}\n\nfunction getMinimums(date: AnyCalendarDate) {\n  if (date.year === 1) {\n    let idx = ERA_NAMES.indexOf(date.era);\n    return ERA_START_DATES[idx];\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,uFAAuF;AACvF,gGAAgG;AAOhG,MAAM,wCAAkB;IAAC;QAAC;QAAM;QAAG;KAAE;IAAE;QAAC;QAAM;QAAG;KAAG;IAAE;QAAC;QAAM;QAAI;KAAG;IAAE;QAAC;QAAM;QAAG;KAAE;IAAE;QAAC;QAAM;QAAG;KAAE;CAAC;AACjG,MAAM,sCAAgB;IAAC;QAAC;QAAM;QAAG;KAAG;IAAE;QAAC;QAAM;QAAI;KAAG;IAAE;QAAC;QAAM;QAAG;KAAE;IAAE;QAAC;QAAM;QAAG;KAAG;CAAC;AAClF,MAAM,oCAAc;IAAC;IAAM;IAAM;IAAM;IAAM;CAAK;AAClD,MAAM,kCAAY;IAAC;IAAS;IAAU;IAAS;IAAU;CAAQ;AAEjE,SAAS,+CAAyB,IAAqB;IACrD,MAAM,MAAM,sCAAgB,SAAS,CAAC,CAAC,CAAC,MAAM,OAAO,IAAI;QACvD,IAAI,KAAK,IAAI,GAAG,MACd,OAAO;QAGT,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,GAAG,OACrC,OAAO;QAGT,IAAI,KAAK,IAAI,KAAK,QAAQ,KAAK,KAAK,KAAK,SAAS,KAAK,GAAG,GAAG,KAC3D,OAAO;QAGT,OAAO;IACT;IAEA,IAAI,QAAQ,CAAA,GACV,OAAO,sCAAgB,MAAM,GAAG;IAGlC,IAAI,QAAQ,GACV,OAAO;IAGT,OAAO,MAAM;AACf;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,YAAY,iCAAW,CAAC,gCAAU,OAAO,CAAC,KAAK,GAAG,EAAE;IACxD,IAAI,CAAC,WACH,MAAM,IAAI,MAAM,kBAAkB,KAAK,GAAG;IAG5C,OAAO,IAAI,CAAA,2KAAA,eAAW,EACpB,KAAK,IAAI,GAAG,WACZ,KAAK,KAAK,EACV,KAAK,GAAG;AAEZ;AAOO,MAAM,kDAAyB,CAAA,gLAAA,oBAAgB;IAGpD,cAAc,EAAU,EAAgB;QACtC,IAAI,OAAO,KAAK,CAAC,cAAc;QAC/B,IAAI,MAAM,+CAAyB;QAEnC,OAAO,IAAI,CAAA,2KAAA,eAAW,EACpB,IAAI,EACJ,+BAAS,CAAC,IAAI,EACd,KAAK,IAAI,GAAG,iCAAW,CAAC,IAAI,EAC5B,KAAK,KAAK,EACV,KAAK,GAAG;IAEZ;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,KAAK,CAAC,YAAY,kCAAY;IACvC;IAEA,YAAY,IAA8B,EAAQ;QAChD,IAAI,gBAAgB,kCAAY;QAChC,IAAI,MAAM,+CAAyB;QAEnC,IAAI,+BAAS,CAAC,IAAI,KAAK,KAAK,GAAG,EAAE;YAC/B,KAAK,GAAG,GAAG,+BAAS,CAAC,IAAI;YACzB,KAAK,IAAI,GAAG,cAAc,IAAI,GAAG,iCAAW,CAAC,IAAI;QACnD;QAEA,4DAA4D;QAC5D,IAAI,CAAC,aAAa,CAAC;IACrB;IAEA,cAAc,IAA8B,EAAQ;QAClD,IAAI,MAAM,gCAAU,OAAO,CAAC,KAAK,GAAG;QACpC,IAAI,MAAM,mCAAa,CAAC,IAAI;QAC5B,IAAI,OAAO,MAAM;YACf,IAAI,CAAC,SAAS,UAAU,OAAO,GAAG;YAElC,+DAA+D;YAC/D,uDAAuD;YACvD,IAAI,UAAU,UAAU,iCAAW,CAAC,IAAI;YACxC,KAAK,IAAI,GAAG,KAAK,GAAG,CAAC,GAAG,KAAK,GAAG,CAAC,SAAS,KAAK,IAAI;YACnD,IAAI,KAAK,IAAI,KAAK,SAAS;gBACzB,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,KAAK;gBAE1C,IAAI,KAAK,KAAK,KAAK,UACjB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,QAAQ,KAAK,GAAG;YAExC;QACF;QAEA,IAAI,KAAK,IAAI,KAAK,KAAK,OAAO,GAAG;YAC/B,IAAI,GAAG,YAAY,SAAS,GAAG,qCAAe,CAAC,IAAI;YACnD,KAAK,KAAK,GAAG,KAAK,GAAG,CAAC,YAAY,KAAK,KAAK;YAE5C,IAAI,KAAK,KAAK,KAAK,YACjB,KAAK,GAAG,GAAG,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG;QAE1C;IACF;IAEA,UAAoB;QAClB,OAAO;IACT;IAEA,cAAc,IAAqB,EAAU;QAC3C,2FAA2F;QAC3F,IAAI,MAAM,gCAAU,OAAO,CAAC,KAAK,GAAG;QACpC,IAAI,MAAM,qCAAe,CAAC,IAAI;QAC9B,IAAI,OAAO,qCAAe,CAAC,MAAM,EAAE;QACnC,IAAI,QAAQ,MACV,AACA,OAAO,OAAO,GAAG,CAAC,EAAE,GAAG,uBADuB;QAIhD,IAAI,QAAQ,IAAI,CAAC,EAAE,GAAG,GAAG,CAAC,EAAE;QAE5B,IAAI,KAAK,KAAK,GAAG,IAAI,CAAC,EAAE,IAAK,KAAK,KAAK,KAAK,IAAI,CAAC,EAAE,IAAI,KAAK,GAAG,GAAG,IAAI,CAAC,EAAE,EACvE;QAGF,OAAO;IACT;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,KAAK,CAAC,eAAe,kCAAY;IAC1C;IAEA,sBAAsB,IAAqB,EAAU;QACnD,IAAI,QAAQ,kCAAY;QACxB,OAAO,QAAQ,KAAK,CAAC,EAAE,GAAG;IAC5B;IAEA,qBAAqB,IAAqB,EAAU;QAClD,IAAI,QAAQ,kCAAY;QACxB,OAAO,SAAS,KAAK,KAAK,KAAK,KAAK,CAAC,EAAE,GAAG,KAAK,CAAC,EAAE,GAAG;IACvD;;QAjGK,KAAA,IAAA,OAAA,IAAA,CACL,UAAA,GAAiC;;AAiGnC;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,KAAK,IAAI,KAAK,GAAG;QACnB,IAAI,MAAM,gCAAU,OAAO,CAAC,KAAK,GAAG;QACpC,OAAO,qCAAe,CAAC,IAAI;IAC7B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2463, "column": 0}, "map": {"version": 3, "file": "PersianCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/PersianCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, Calendar, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {mod} from '../utils';\n\nconst PERSIAN_EPOCH = 1948320;\n\n// Number of days from the start of the year to the start of each month.\nconst MONTH_START = [\n  0, // Farvardin\n  31, // Ordibehesht\n  62, // Khordad\n  93, // Tir\n  124, // <PERSON><PERSON><PERSON>\n  155, // <PERSON><PERSON><PERSON>\n  186, // Mehr\n  216, // <PERSON><PERSON>\n  246, // Azar\n  276, // Dey\n  306, // Bahman\n  336  // Esfand\n];\n\n/**\n * The Persian calendar is the main calendar used in Iran and Afghanistan. It has 12 months\n * in each year, the first 6 of which have 31 days, and the next 5 have 30 days. The 12th month\n * has either 29 or 30 days depending on whether it is a leap year. The Persian year starts\n * around the March equinox.\n */\nexport class PersianCalendar implements Calendar {\n  identifier: CalendarIdentifier = 'persian';\n\n  fromJulianDay(jd: number): CalendarDate {\n    let daysSinceEpoch = jd - PERSIAN_EPOCH;\n    let year = 1 + Math.floor((33 * daysSinceEpoch + 3) / 12053);\n    let farvardin1 = 365 * (year - 1) + Math.floor((8 * year + 21) / 33);\n    let dayOfYear = daysSinceEpoch - farvardin1;\n    let month = dayOfYear < 216\n      ? Math.floor(dayOfYear / 31)\n      : Math.floor((dayOfYear - 6) / 30);\n    let day = dayOfYear - MONTH_START[month] + 1;\n    return new CalendarDate(this, year, month + 1, day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    let jd = PERSIAN_EPOCH - 1 + 365 * (date.year - 1) + Math.floor((8 * date.year + 21) / 33);\n    jd += MONTH_START[date.month - 1];\n    jd += date.day;\n    return jd;\n  }\n\n  getMonthsInYear(): number {\n    return 12;\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    if (date.month <= 6) {\n      return 31;\n    }\n\n    if (date.month <= 11) {\n      return 30;\n    }\n\n    let isLeapYear = mod(25 * date.year + 11, 33) < 8;\n    return isLeapYear ? 30 : 29;\n  }\n\n  getEras(): string[] {\n    return ['AP'];\n  }\n\n  getYearsInEra(): number {\n    // 9378-10-10 persian is 9999-12-31 gregorian.\n    // Round down to 9377 to set the maximum full year.\n    return 9377;\n  }\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAMhG,MAAM,sCAAgB;AAEtB,wEAAwE;AACxE,MAAM,oCAAc;IAClB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAK,SAAS;CACf;AAQM,MAAM;IAGX,cAAc,EAAU,EAAgB;QACtC,IAAI,iBAAiB,KAAK;QAC1B,IAAI,OAAO,IAAI,KAAK,KAAK,CAAE,CAAA,KAAK,iBAAiB,CAAA,IAAK;QACtD,IAAI,aAAa,MAAO,CAAA,OAAO,CAAA,IAAK,KAAK,KAAK,CAAE,CAAA,IAAI,OAAO,EAAC,IAAK;QACjE,IAAI,YAAY,iBAAiB;QACjC,IAAI,QAAQ,YAAY,MACpB,KAAK,KAAK,CAAC,YAAY,MACvB,KAAK,KAAK,CAAE,CAAA,YAAY,CAAA,IAAK;QACjC,IAAI,MAAM,YAAY,iCAAW,CAAC,MAAM,GAAG;QAC3C,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,MAAM,QAAQ,GAAG;IACjD;IAEA,YAAY,IAAqB,EAAU;QACzC,IAAI,KAAK,sCAAgB,IAAI,MAAO,CAAA,KAAK,IAAI,GAAG,CAAA,IAAK,KAAK,KAAK,CAAE,CAAA,IAAI,KAAK,IAAI,GAAG,EAAC,IAAK;QACvF,MAAM,iCAAW,CAAC,KAAK,KAAK,GAAG,EAAE;QACjC,MAAM,KAAK,GAAG;QACd,OAAO;IACT;IAEA,kBAA0B;QACxB,OAAO;IACT;IAEA,eAAe,IAAqB,EAAU;QAC5C,IAAI,KAAK,KAAK,IAAI,GAChB,OAAO;QAGT,IAAI,KAAK,KAAK,IAAI,IAChB,OAAO;QAGT,IAAI,aAAa,CAAA,oKAAA,MAAE,EAAE,KAAK,KAAK,IAAI,GAAG,IAAI,MAAM;QAChD,OAAO,aAAa,KAAK;IAC3B;IAEA,UAAoB;QAClB,OAAO;YAAC;SAAK;IACf;IAEA,gBAAwB;QACtB,8CAA8C;QAC9C,mDAAmD;QACnD,OAAO;IACT;;aA9CA,UAAA,GAAiC;;AA+CnC", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2545, "column": 0}, "map": {"version": 3, "file": "TaiwanCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/calendars/TaiwanCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\n// Portions of the code in this file are based on code from ICU.\n// Original licensing can be found in the NOTICE file in the root directory of this source tree.\n\nimport {AnyCalendarDate, CalendarIdentifier} from '../types';\nimport {CalendarDate} from '../CalendarDate';\nimport {fromExtendedYear, getExtendedYear, GregorianCalendar} from './GregorianCalendar';\nimport {Mutable} from '../utils';\n\nconst TAIWAN_ERA_START = 1911;\n\nfunction gregorianYear(date: AnyCalendarDate) {\n  return date.era === 'minguo'\n    ? date.year + TAIWAN_ERA_START\n    : 1 - date.year + TAIWAN_ERA_START;\n}\n\nfunction gregorianToTaiwan(year: number): [string, number] {\n  let y = year - TAIWAN_ERA_START;\n  if (y > 0) {\n    return ['minguo', y];\n  } else {\n    return ['before_minguo', 1 - y];\n  }\n}\n\n/**\n * The Taiwanese calendar is the same as the Gregorian calendar, but years\n * are numbered starting from 1912 (Gregorian). Two eras are supported:\n * 'before_minguo' and 'minguo'.\n */\nexport class TaiwanCalendar extends GregorianCalendar {\n  identifier: CalendarIdentifier = 'roc'; // Republic of China\n\n  fromJulianDay(jd: number): CalendarDate {\n    let date = super.fromJulianDay(jd);\n    let extendedYear = getExtendedYear(date.era, date.year);\n    let [era, year] = gregorianToTaiwan(extendedYear);\n    return new CalendarDate(this, era, year, date.month, date.day);\n  }\n\n  toJulianDay(date: AnyCalendarDate): number {\n    return super.toJulianDay(toGregorian(date));\n  }\n\n  getEras(): string[] {\n    return ['before_minguo', 'minguo'];\n  }\n\n  balanceDate(date: Mutable<AnyCalendarDate>): void {\n    let [era, year] = gregorianToTaiwan(gregorianYear(date));\n    date.era = era;\n    date.year = year;\n  }\n\n  isInverseEra(date: AnyCalendarDate): boolean {\n    return date.era === 'before_minguo';\n  }\n\n  getDaysInMonth(date: AnyCalendarDate): number {\n    return super.getDaysInMonth(toGregorian(date));\n  }\n\n  getYearsInEra(date: AnyCalendarDate): number {\n    return date.era === 'before_minguo' ? 9999 : 9999 - TAIWAN_ERA_START;\n  }\n}\n\nfunction toGregorian(date: AnyCalendarDate) {\n  let [era, year] = fromExtendedYear(gregorianYear(date));\n  return new CalendarDate(\n    era,\n    year,\n    date.month,\n    date.day\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;;;;;;;;CAUC,GAED,gEAAgE;AAChE,gGAAgG;AAOhG,MAAM,yCAAmB;AAEzB,SAAS,oCAAc,IAAqB;IAC1C,OAAO,KAAK,GAAG,KAAK,WAChB,KAAK,IAAI,GAAG,yCACZ,IAAI,KAAK,IAAI,GAAG;AACtB;AAEA,SAAS,wCAAkB,IAAY;IACrC,IAAI,IAAI,OAAO;IACf,IAAI,IAAI,GACN,OAAO;QAAC;QAAU;KAAE;SAEpB,OAAO;QAAC;QAAiB,IAAI;KAAE;AAEnC;AAOO,MAAM,kDAAuB,CAAA,gLAAA,oBAAgB;IAGlD,cAAc,EAAU,EAAgB;QACtC,IAAI,OAAO,KAAK,CAAC,cAAc;QAC/B,IAAI,eAAe,CAAA,gLAAA,kBAAc,EAAE,KAAK,GAAG,EAAE,KAAK,IAAI;QACtD,IAAI,CAAC,KAAK,KAAK,GAAG,wCAAkB;QACpC,OAAO,IAAI,CAAA,2KAAA,eAAW,EAAE,IAAI,EAAE,KAAK,MAAM,KAAK,KAAK,EAAE,KAAK,GAAG;IAC/D;IAEA,YAAY,IAAqB,EAAU;QACzC,OAAO,KAAK,CAAC,YAAY,kCAAY;IACvC;IAEA,UAAoB;QAClB,OAAO;YAAC;YAAiB;SAAS;IACpC;IAEA,YAAY,IAA8B,EAAQ;QAChD,IAAI,CAAC,KAAK,KAAK,GAAG,wCAAkB,oCAAc;QAClD,KAAK,GAAG,GAAG;QACX,KAAK,IAAI,GAAG;IACd;IAEA,aAAa,IAAqB,EAAW;QAC3C,OAAO,KAAK,GAAG,KAAK;IACtB;IAEA,eAAe,IAAqB,EAAU;QAC5C,OAAO,KAAK,CAAC,eAAe,kCAAY;IAC1C;IAEA,cAAc,IAAqB,EAAU;QAC3C,OAAO,KAAK,GAAG,KAAK,kBAAkB,OAAO,OAAO;IACtD;;QAlCK,KAAA,IAAA,OAAA,IAAA,CACL,UAAA,GAAiC,MAAO,oBAAoB;;;AAkC9D;AAEA,SAAS,kCAAY,IAAqB;IACxC,IAAI,CAAC,KAAK,KAAK,GAAG,CAAA,gLAAA,mBAAe,EAAE,oCAAc;IACjD,OAAO,IAAI,CAAA,2KAAA,eAAW,EACpB,KACA,MACA,KAAK,KAAK,EACV,KAAK,GAAG;AAEZ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2626, "column": 0}, "map": {"version": 3, "file": "createCalendar.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/createCalendar.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {BuddhistCalendar} from './calendars/BuddhistCalendar';\nimport {Calendar, CalendarIdentifier} from './types';\nimport {CopticCalendar, EthiopicAmeteAlemCalendar, EthiopicCalendar} from './calendars/EthiopicCalendar';\nimport {GregorianCalendar} from './calendars/GregorianCalendar';\nimport {HebrewCalendar} from './calendars/HebrewCalendar';\nimport {IndianCalendar} from './calendars/IndianCalendar';\nimport {IslamicCivilCalendar, IslamicTabularCalendar, IslamicUmalquraCalendar} from './calendars/IslamicCalendar';\nimport {JapaneseCalendar} from './calendars/JapaneseCalendar';\nimport {PersianCalendar} from './calendars/PersianCalendar';\nimport {TaiwanCalendar} from './calendars/TaiwanCalendar';\n\n/** Creates a `Calendar` instance from a Unicode calendar identifier string. */\nexport function createCalendar(name: CalendarIdentifier): Calendar {\n  switch (name) {\n    case 'buddhist':\n      return new BuddhistCalendar();\n    case 'ethiopic':\n      return new EthiopicCalendar();\n    case 'ethioaa':\n      return new EthiopicAmeteAlemCalendar();\n    case 'coptic':\n      return new CopticCalendar();\n    case 'hebrew':\n      return new HebrewCalendar();\n    case 'indian':\n      return new IndianCalendar();\n    case 'islamic-civil':\n      return new IslamicCivilCalendar();\n    case 'islamic-tbla':\n      return new IslamicTabularCalendar();\n    case 'islamic-umalqura':\n      return new IslamicUmalquraCalendar();\n    case 'japanese':\n      return new JapaneseCalendar();\n    case 'persian':\n      return new PersianCalendar();\n    case 'roc':\n      return new TaiwanCalendar();\n    case 'gregory':\n    default:\n      return new GregorianCalendar();\n  }\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAcM,SAAS,0CAAe,IAAwB;IACrD,OAAQ;QACN,KAAK;YACH,OAAO,IAAI,CAAA,+KAAA,mBAAe;QAC5B,KAAK;YACH,OAAO,IAAI,CAAA,+KAAA,mBAAe;QAC5B,KAAK;YACH,OAAO,IAAI,CAAA,+KAAA,4BAAwB;QACrC,KAAK;YACH,OAAO,IAAI,CAAA,+KAAA,iBAAa;QAC1B,KAAK;YACH,OAAO,IAAI,CAAA,6KAAA,iBAAa;QAC1B,KAAK;YACH,OAAO,IAAI,CAAA,6KAAA,iBAAa;QAC1B,KAAK;YACH,OAAO,IAAI,CAAA,8KAAA,uBAAmB;QAChC,KAAK;YACH,OAAO,IAAI,CAAA,8KAAA,yBAAqB;QAClC,KAAK;YACH,OAAO,IAAI,CAAA,8KAAA,0BAAsB;QACnC,KAAK;YACH,OAAO,IAAI,CAAA,+KAAA,mBAAe;QAC5B,KAAK;YACH,OAAO,IAAI,CAAA,8KAAA,kBAAc;QAC3B,KAAK;YACH,OAAO,IAAI,CAAA,6KAAA,iBAAa;QAC1B,KAAK;QACL;YACE,OAAO,IAAI,CAAA,gLAAA,oBAAgB;IAC/B;AACF", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 2696, "column": 0}, "map": {"version": 3, "file": "DateFormatter.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40internationalized/date/dist/packages/%40internationalized/date/src/DateFormatter.ts"], "sourcesContent": ["/*\n * Copyright 2020 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nlet formatterCache = new Map<string, Intl.DateTimeFormat>();\n\ninterface DateRangeFormatPart extends Intl.DateTimeFormatPart {\n  source: 'startRange' | 'endRange' | 'shared'\n}\n\n/** A wrapper around Intl.DateTimeFormat that fixes various browser bugs, and polyfills new features. */\nexport class DateFormatter implements Intl.DateTimeFormat {\n  private formatter: Intl.DateTimeFormat;\n  private options: Intl.DateTimeFormatOptions;\n  private resolvedHourCycle: Intl.DateTimeFormatOptions['hourCycle'];\n\n  constructor(locale: string, options: Intl.DateTimeFormatOptions = {}) {\n    this.formatter = getCachedDateFormatter(locale, options);\n    this.options = options;\n  }\n\n  /** Formats a date as a string according to the locale and format options passed to the constructor. */\n  format(value: Date): string {\n    return this.formatter.format(value);\n  }\n\n  /** Formats a date to an array of parts such as separators, numbers, punctuation, and more. */\n  formatToParts(value: Date): Intl.DateTimeFormatPart[] {\n    return this.formatter.formatToParts(value);\n  }\n\n  /** Formats a date range as a string. */\n  formatRange(start: Date, end: Date): string {\n    // @ts-ignore\n    if (typeof this.formatter.formatRange === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRange(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    // Very basic fallback for old browsers.\n    return `${this.formatter.format(start)} – ${this.formatter.format(end)}`;\n  }\n\n  /** Formats a date range as an array of parts. */\n  formatRangeToParts(start: Date, end: Date): DateRangeFormatPart[] {\n    // @ts-ignore\n    if (typeof this.formatter.formatRangeToParts === 'function') {\n      // @ts-ignore\n      return this.formatter.formatRangeToParts(start, end);\n    }\n\n    if (end < start) {\n      throw new RangeError('End date must be >= start date');\n    }\n\n    let startParts = this.formatter.formatToParts(start);\n    let endParts = this.formatter.formatToParts(end);\n    return [\n      ...startParts.map(p => ({...p, source: 'startRange'} as DateRangeFormatPart)),\n      {type: 'literal', value: ' – ', source: 'shared'},\n      ...endParts.map(p => ({...p, source: 'endRange'} as DateRangeFormatPart))\n    ];\n  }\n\n  /** Returns the resolved formatting options based on the values passed to the constructor. */\n  resolvedOptions(): Intl.ResolvedDateTimeFormatOptions {\n    let resolvedOptions = this.formatter.resolvedOptions();\n    if (hasBuggyResolvedHourCycle()) {\n      if (!this.resolvedHourCycle) {\n        this.resolvedHourCycle = getResolvedHourCycle(resolvedOptions.locale, this.options);\n      }\n      resolvedOptions.hourCycle = this.resolvedHourCycle;\n      resolvedOptions.hour12 = this.resolvedHourCycle === 'h11' || this.resolvedHourCycle === 'h12';\n    }\n\n    // Safari uses a different name for the Ethiopic (Amete Alem) calendar.\n    // https://bugs.webkit.org/show_bug.cgi?id=241564\n    if (resolvedOptions.calendar === 'ethiopic-amete-alem') {\n      resolvedOptions.calendar = 'ethioaa';\n    }\n\n    return resolvedOptions;\n  }\n}\n\n// There are multiple bugs involving the hour12 and hourCycle options in various browser engines.\n//   - Chrome [1] (and the ECMA 402 spec [2]) resolve hour12: false in English and other locales to h24 (24:00 - 23:59)\n//     rather than h23 (00:00 - 23:59). Same can happen with hour12: true in French, which Chrome resolves to h11 (00:00 - 11:59)\n//     rather than h12 (12:00 - 11:59).\n//   - WebKit returns an incorrect hourCycle resolved option in the French locale due to incorrect parsing of 'h' literal\n//     in the resolved pattern. It also formats incorrectly when specifying the hourCycle option for the same reason. [3]\n// [1] https://bugs.chromium.org/p/chromium/issues/detail?id=1045791\n// [2] https://github.com/tc39/ecma402/issues/402\n// [3] https://bugs.webkit.org/show_bug.cgi?id=229313\n\n// https://github.com/unicode-org/cldr/blob/018b55eff7ceb389c7e3fc44e2f657eae3b10b38/common/supplemental/supplementalData.xml#L4774-L4802\nconst hour12Preferences = {\n  true: {\n    // Only Japanese uses the h11 style for 12 hour time. All others use h12.\n    ja: 'h11'\n  },\n  false: {\n    // All locales use h23 for 24 hour time. None use h24.\n  }\n};\n\nfunction getCachedDateFormatter(locale: string, options: Intl.DateTimeFormatOptions = {}): Intl.DateTimeFormat {\n  // Work around buggy hour12 behavior in Chrome / ECMA 402 spec by using hourCycle instead.\n  // Only apply the workaround if the issue is detected, because the hourCycle option is buggy in Safari.\n  if (typeof options.hour12 === 'boolean' && hasBuggyHour12Behavior()) {\n    options = {...options};\n    let pref = hour12Preferences[String(options.hour12)][locale.split('-')[0]];\n    let defaultHourCycle = options.hour12 ? 'h12' : 'h23';\n    options.hourCycle = pref ?? defaultHourCycle;\n    delete options.hour12;\n  }\n\n  let cacheKey = locale + (options ? Object.entries(options).sort((a, b) => a[0] < b[0] ? -1 : 1).join() : '');\n  if (formatterCache.has(cacheKey)) {\n    return formatterCache.get(cacheKey)!;\n  }\n\n  let numberFormatter = new Intl.DateTimeFormat(locale, options);\n  formatterCache.set(cacheKey, numberFormatter);\n  return numberFormatter;\n}\n\nlet _hasBuggyHour12Behavior: boolean | null = null;\nfunction hasBuggyHour12Behavior() {\n  if (_hasBuggyHour12Behavior == null) {\n    _hasBuggyHour12Behavior = new Intl.DateTimeFormat('en-US', {\n      hour: 'numeric',\n      hour12: false\n    }).format(new Date(2020, 2, 3, 0)) === '24';\n  }\n\n  return _hasBuggyHour12Behavior;\n}\n\nlet _hasBuggyResolvedHourCycle: boolean | null = null;\nfunction hasBuggyResolvedHourCycle() {\n  if (_hasBuggyResolvedHourCycle == null) {\n    _hasBuggyResolvedHourCycle = new Intl.DateTimeFormat('fr', {\n      hour: 'numeric',\n      hour12: false\n    }).resolvedOptions().hourCycle === 'h12';\n  }\n\n  return _hasBuggyResolvedHourCycle;\n}\n\nfunction getResolvedHourCycle(locale: string, options: Intl.DateTimeFormatOptions) {\n  if (!options.timeStyle && !options.hour) {\n    return undefined;\n  }\n\n  // Work around buggy results in resolved hourCycle and hour12 options in WebKit.\n  // Format the minimum possible hour and maximum possible hour in a day and parse the results.\n  locale = locale.replace(/(-u-)?-nu-[a-zA-Z0-9]+/, '');\n  locale += (locale.includes('-u-') ? '' : '-u') + '-nu-latn';\n  let formatter = getCachedDateFormatter(locale, {\n    ...options,\n    timeZone: undefined // use local timezone\n  });\n\n  let min = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 0)).find(p => p.type === 'hour')!.value, 10);\n  let max = parseInt(formatter.formatToParts(new Date(2020, 2, 3, 23)).find(p => p.type === 'hour')!.value, 10);\n\n  if (min === 0 && max === 23) {\n    return 'h23';\n  }\n\n  if (min === 24 && max === 23) {\n    return 'h24';\n  }\n\n  if (min === 0 && max === 11) {\n    return 'h11';\n  }\n\n  if (min === 12 && max === 11) {\n    return 'h12';\n  }\n\n  throw new Error('Unexpected hour cycle result');\n}\n"], "names": [], "mappings": "AAAA;;;;;;;;;;CAUC;;;AAED,IAAI,uCAAiB,IAAI;AAOlB,MAAM;IAUX,qGAAqG,GACrG,OAAO,KAAW,EAAU;QAC1B,OAAO,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC;IAC/B;IAEA,4FAA4F,GAC5F,cAAc,KAAW,EAA6B;QACpD,OAAO,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;IACtC;IAEA,sCAAsC,GACtC,YAAY,KAAW,EAAE,GAAS,EAAU;QAC1C,aAAa;QACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,WAAW,KAAK,YAExC,AADA,OACO,IAAI,CAAC,CADC,QACQ,CAAC,WAAW,CAAC,OAAO;QAG3C,IAAI,MAAM,OACR,MAAM,IAAI,WAAW;QAGvB,wCAAwC;QACxC,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,OAAO,UAAG,EAAE,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,MAAM;IAC1E;IAEA,+CAA+C,GAC/C,mBAAmB,KAAW,EAAE,GAAS,EAAyB;QAChE,aAAa;QACb,IAAI,OAAO,IAAI,CAAC,SAAS,CAAC,kBAAkB,KAAK,YAC/C,AACA,OAAO,IAAI,CAAC,CADC,QACQ,CAAC,kBAAkB,CAAC,OAAO;QAGlD,IAAI,MAAM,OACR,MAAM,IAAI,WAAW;QAGvB,IAAI,aAAa,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;QAC9C,IAAI,WAAW,IAAI,CAAC,SAAS,CAAC,aAAa,CAAC;QAC5C,OAAO;eACF,WAAW,GAAG,CAAC,CAAA,IAAM,CAAA;oBAAC,GAAG,CAAC;oBAAE,QAAQ;gBAAY,CAAA;YACnD;gBAAC,MAAM;gBAAW,OAAO;gBAAO,QAAQ;YAAQ;eAC7C,SAAS,GAAG,CAAC,CAAA,IAAM,CAAA;oBAAC,GAAG,CAAC;oBAAE,QAAQ;gBAAU,CAAA;SAChD;IACH;IAEA,2FAA2F,GAC3F,kBAAsD;QACpD,IAAI,kBAAkB,IAAI,CAAC,SAAS,CAAC,eAAe;QACpD,IAAI,mDAA6B;YAC/B,IAAI,CAAC,IAAI,CAAC,iBAAiB,EACzB,IAAI,CAAC,iBAAiB,GAAG,2CAAqB,gBAAgB,MAAM,EAAE,IAAI,CAAC,OAAO;YAEpF,gBAAgB,SAAS,GAAG,IAAI,CAAC,iBAAiB;YAClD,gBAAgB,MAAM,GAAG,IAAI,CAAC,iBAAiB,KAAK,SAAS,IAAI,CAAC,iBAAiB,KAAK;QAC1F;QAEA,uEAAuE;QACvE,iDAAiD;QACjD,IAAI,gBAAgB,QAAQ,KAAK,uBAC/B,gBAAgB,QAAQ,GAAG;QAG7B,OAAO;IACT;IAtEA,YAAY,MAAc,EAAE,UAAsC,CAAC,CAAC,CAAE;QACpE,IAAI,CAAC,SAAS,GAAG,6CAAuB,QAAQ;QAChD,IAAI,CAAC,OAAO,GAAG;IACjB;AAoEF;AAEA,iGAAiG;AACjG,uHAAuH;AACvH,iIAAiI;AACjI,uCAAuC;AACvC,yHAAyH;AACzH,yHAAyH;AACzH,oEAAoE;AACpE,iDAAiD;AACjD,qDAAqD;AAErD,yIAAyI;AACzI,MAAM,0CAAoB;IACxB,MAAM;QACJ,yEAAyE;QACzE,IAAI;IACN;IACA,OAAO,CAEP;AACF;AAEA,SAAS,6CAAuB,MAAc,EAAE,UAAsC,CAAC,CAAC;IACtF,0FAA0F;IAC1F,uGAAuG;IACvG,IAAI,OAAO,QAAQ,MAAM,KAAK,aAAa,gDAA0B;QACnE,UAAU;YAAC,GAAG,OAAO;QAAA;QACrB,IAAI,OAAO,uCAAiB,CAAC,OAAO,QAAQ,MAAM,EAAE,CAAC,OAAO,KAAK,CAAC,IAAI,CAAC,EAAE,CAAC;QAC1E,IAAI,mBAAmB,QAAQ,MAAM,GAAG,QAAQ;QAChD,QAAQ,SAAS,GAAG,SAAA,QAAA,SAAA,KAAA,IAAA,OAAQ;QAC5B,OAAO,QAAQ,MAAM;IACvB;IAEA,IAAI,WAAW,SAAU,CAAA,UAAU,OAAO,OAAO,CAAC,SAAS,IAAI,CAAC,CAAC,GAAG,IAAM,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,EAAE,GAAG,CAAA,IAAK,GAAG,IAAI,KAAK,EAAC;IAC1G,IAAI,qCAAe,GAAG,CAAC,WACrB,OAAO,qCAAe,GAAG,CAAC;IAG5B,IAAI,kBAAkB,IAAI,KAAK,cAAc,CAAC,QAAQ;IACtD,qCAAe,GAAG,CAAC,UAAU;IAC7B,OAAO;AACT;AAEA,IAAI,gDAA0C;AAC9C,SAAS;IACP,IAAI,iDAA2B,MAC7B,gDAA0B,IAAI,KAAK,cAAc,CAAC,SAAS;QACzD,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK,MAAM,GAAG,GAAG,QAAQ;IAGzC,OAAO;AACT;AAEA,IAAI,mDAA6C;AACjD,SAAS;IACP,IAAI,oDAA8B,MAChC,mDAA6B,IAAI,KAAK,cAAc,CAAC,MAAM;QACzD,MAAM;QACN,QAAQ;IACV,GAAG,eAAe,GAAG,SAAS,KAAK;IAGrC,OAAO;AACT;AAEA,SAAS,2CAAqB,MAAc,EAAE,OAAmC;IAC/E,IAAI,CAAC,QAAQ,SAAS,IAAI,CAAC,QAAQ,IAAI,EACrC,OAAO;IAGT,gFAAgF;IAChF,6FAA6F;IAC7F,SAAS,OAAO,OAAO,CAAC,0BAA0B;IAClD,UAAW,CAAA,OAAO,QAAQ,CAAC,SAAS,KAAK,IAAG,IAAK;IACjD,IAAI,YAAY,6CAAuB,QAAQ;QAC7C,GAAG,OAAO;QACV,UAAU,UAAU,qBAAqB;IAC3C;IAEA,IAAI,MAAM,SAAS,UAAU,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,GAAG,IAAI,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAS,KAAK,EAAE;IACzG,IAAI,MAAM,SAAS,UAAU,aAAa,CAAC,IAAI,KAAK,MAAM,GAAG,GAAG,KAAK,IAAI,CAAC,CAAA,IAAK,EAAE,IAAI,KAAK,QAAS,KAAK,EAAE;IAE1G,IAAI,QAAQ,KAAK,QAAQ,IACvB,OAAO;IAGT,IAAI,QAAQ,MAAM,QAAQ,IACxB,OAAO;IAGT,IAAI,QAAQ,KAAK,QAAQ,IACvB,OAAO;IAGT,IAAI,QAAQ,MAAM,QAAQ,IACxB,OAAO;IAGT,MAAM,IAAI,MAAM;AAClB", "ignoreList": [0], "debugId": null}}]}