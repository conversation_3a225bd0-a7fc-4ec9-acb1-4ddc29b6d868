{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Heading, Text, Button, Card, Separator } from '@whop/react/components';\n\nexport default function Page() {\n\tconst [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');\n\n\treturn (\n\t\t<div className=\"min-h-screen bg-gray-12\">\n\t\t\t{/* Top Navigation Bar */}\n\t\t\t<nav className=\"border-b border-gray-11 bg-gray-12\">\n\t\t\t\t<div className=\"max-w-6xl mx-auto px-6 py-4\">\n\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Heading size=\"6\" className=\"text-white\">\n\t\t\t\t\t\t\t\tWhop Leaderboards\n\t\t\t\t\t\t\t</Heading>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-9\">\n\t\t\t\t\t\t\t\tCompete and climb the rankings\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center gap-4\">\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tvariant={activeTab === 'leaderboards' ? 'solid' : 'ghost'}\n\t\t\t\t\t\t\t\tcolor={activeTab === 'leaderboards' ? 'green' : 'gray'}\n\t\t\t\t\t\t\t\tonClick={() => setActiveTab('leaderboards')}\n\t\t\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\tLeaderboards\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tvariant={activeTab === 'competitions' ? 'solid' : 'ghost'}\n\t\t\t\t\t\t\t\tcolor={activeTab === 'competitions' ? 'green' : 'gray'}\n\t\t\t\t\t\t\t\tonClick={() => setActiveTab('competitions')}\n\t\t\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\tCompetitions\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</nav>\n\n\t\t\t{/* Content */}\n\t\t\t<div className=\"max-w-6xl mx-auto px-6 py-6\">\n\t\t\t\t{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction LeaderboardsPage() {\n\t// Sample leaderboard data\n\tconst leaderboardData = [\n\t\t{ rank: 1, name: \"Alex Thompson\", score: 2847, change: \"+12\", avatar: \"AT\", streak: 7, country: \"🇺🇸\" },\n\t\t{ rank: 2, name: \"Sarah Chen\", score: 2756, change: \"+8\", avatar: \"SC\", streak: 5, country: \"🇨🇦\" },\n\t\t{ rank: 3, name: \"Marcus Johnson\", score: 2698, change: \"-3\", avatar: \"MJ\", streak: 3, country: \"🇬🇧\" },\n\t\t{ rank: 4, name: \"Emma Rodriguez\", score: 2634, change: \"+15\", avatar: \"ER\", streak: 12, country: \"🇪🇸\" },\n\t\t{ rank: 5, name: \"David Kim\", score: 2589, change: \"+5\", avatar: \"DK\", streak: 2, country: \"🇰🇷\" },\n\t\t{ rank: 6, name: \"Lisa Wang\", score: 2543, change: \"-2\", avatar: \"LW\", streak: 8, country: \"🇨🇳\" },\n\t\t{ rank: 7, name: \"James Wilson\", score: 2498, change: \"+7\", avatar: \"JW\", streak: 4, country: \"🇦🇺\" },\n\t\t{ rank: 8, name: \"Maya Patel\", score: 2456, change: \"+3\", avatar: \"MP\", streak: 6, country: \"🇮🇳\" },\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-4\">\n\t\t\t{/* Header */}\n\t\t\t<Card className=\"p-4 bg-gradient-to-r from-green-2 to-green-3 border-green-6\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<Heading size=\"5\" className=\"text-green-12 mb-1\">\n\t\t\t\t\t\tGlobal Leaderboard\n\t\t\t\t\t</Heading>\n\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\tCompete with the best and climb to the top\n\t\t\t\t\t</Text>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Leaderboard */}\n\t\t\t<Card className=\"overflow-hidden shadow-sm\">\n\t\t\t\t<div className=\"p-3 bg-gradient-to-r from-green-1 to-green-2 border-b border-green-4\">\n\t\t\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Heading size=\"4\" className=\"text-green-12\">\n\t\t\t\t\t\t\t\tTop Performers\n\t\t\t\t\t\t\t</Heading>\n\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11 mt-0.5\">\n\t\t\t\t\t\t\t\tUpdated in real-time • Last update: 2 minutes ago\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Button variant=\"soft\" color=\"green\" size=\"1\">\n\t\t\t\t\t\t\tView All\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<div className=\"divide-y divide-gray-4\">\n\t\t\t\t\t{leaderboardData.map((player, index) => (\n\t\t\t\t\t\t<div key={player.rank} className=\"p-3 hover:bg-green-1 transition-all duration-200\">\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t\t\t{/* Rank Badge */}\n\t\t\t\t\t\t\t\t\t<div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-bold ${\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 1 ? 'bg-gradient-to-br from-yellow-9 to-yellow-10 text-yellow-1' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 2 ? 'bg-gradient-to-br from-gray-9 to-gray-10 text-gray-1' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 3 ? 'bg-gradient-to-br from-orange-9 to-orange-10 text-orange-1' :\n\t\t\t\t\t\t\t\t\t\t'bg-gradient-to-br from-green-3 to-green-4 text-green-11'\n\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t{player.rank}\n\t\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t\t{/* Player Info */}\n\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t<div className=\"w-6 h-6 rounded-full bg-gray-3 flex items-center justify-center text-xs font-semibold text-gray-11\">\n\t\t\t\t\t\t\t\t\t\t\t{player.avatar}\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-1.5\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"medium\" className=\"text-gray-12\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{player.name}\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t\t<span className=\"text-xs\">{player.country}</span>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-1.5 mt-0.5\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{player.streak} day streak\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"w-0.5 h-0.5 bg-gray-6 rounded-full\"></div>\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\t\t\tRank #{player.rank}\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t\t\t<div className=\"text-right\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-gray-12 block\">\n\t\t\t\t\t\t\t\t\t\t\t{player.score.toLocaleString()}\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\tpoints\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div className={`px-2 py-0.5 rounded-full text-xs font-semibold ${\n\t\t\t\t\t\t\t\t\t\tplayer.change.startsWith('+') ? 'bg-green-3 text-green-11' : 'bg-red-3 text-red-11'\n\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t{player.change}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\n\t\t\t\t{/* View More */}\n\t\t\t\t<div className=\"p-3 bg-gray-1 border-t border-gray-4\">\n\t\t\t\t\t<Button variant=\"soft\" color=\"gray\" className=\"w-full\" size=\"1\">\n\t\t\t\t\t\tView Full Leaderboard\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Stats Cards */}\n\t\t\t<div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t1,247\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tTotal Players\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\t+23 this week\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t89.2%\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tActivity Rate\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\t+2.1% from last month\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t2,847\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tTop Score\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\tAlex Thompson\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t156\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tAvg Score\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\tDaily average\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction CompetitionsPage() {\n\tconst competitions = [\n\t\t{\n\t\t\tid: 1,\n\t\t\ttitle: \"Weekly Challenge\",\n\t\t\tdescription: \"Complete daily tasks to earn points and climb the weekly leaderboard\",\n\t\t\ttimeLeft: \"3d 14h\",\n\t\t\tparticipants: 342,\n\t\t\tprize: \"$500\",\n\t\t\tstatus: \"active\",\n\t\t\tprogress: 67\n\t\t},\n\t\t{\n\t\t\tid: 2,\n\t\t\ttitle: \"Monthly Tournament\",\n\t\t\tdescription: \"Ultimate test of skill for the top performers\",\n\t\t\ttimeLeft: \"5d\",\n\t\t\tparticipants: 89,\n\t\t\tprize: \"$2,000\",\n\t\t\tstatus: \"upcoming\",\n\t\t\tprogress: 0\n\t\t},\n\t\t{\n\t\t\tid: 3,\n\t\t\ttitle: \"Speed Run Challenge\",\n\t\t\tdescription: \"Race against time in this fast-paced competition\",\n\t\t\ttimeLeft: \"2h\",\n\t\t\tparticipants: 156,\n\t\t\tprize: \"$250\",\n\t\t\tstatus: \"active\",\n\t\t\tprogress: 89\n\t\t},\n\t\t{\n\t\t\tid: 4,\n\t\t\ttitle: \"Elite Championship\",\n\t\t\tdescription: \"Invitation-only tournament for elite players\",\n\t\t\ttimeLeft: \"Closed\",\n\t\t\tparticipants: 50,\n\t\t\tprize: \"$5,000\",\n\t\t\tstatus: \"closed\",\n\t\t\tprogress: 100\n\t\t}\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-4\">\n\t\t\t{/* Header */}\n\t\t\t<Card className=\"p-4 bg-gradient-to-r from-green-2 to-green-3 border-green-6\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<Heading size=\"5\" className=\"text-green-12 mb-1\">\n\t\t\t\t\t\tCompetitions\n\t\t\t\t\t</Heading>\n\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\tJoin exciting competitions and win amazing prizes\n\t\t\t\t\t</Text>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Featured Competition - Sleek Design */}\n\t\t\t<Card className=\"bg-gray-12 border-gray-11 overflow-hidden relative\">\n\t\t\t\t<div className=\"absolute inset-0 bg-gradient-to-br from-green-9/20 to-green-10/10\"></div>\n\t\t\t\t<div className=\"relative p-4\">\n\t\t\t\t\t{/* Header */}\n\t\t\t\t\t<div className=\"flex items-center justify-between mb-3\">\n\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t<div className=\"w-8 h-8 rounded-lg bg-green-9 flex items-center justify-center shadow-lg shadow-green-9/50\">\n\t\t\t\t\t\t\t\t<span className=\"text-white text-sm font-bold\">✓</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">\n\t\t\t\t\t\t\t\t\tWeekly Challenge\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-3\">\n\t\t\t\t\t\t\t\t\tWhoever makes the most money\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"text-right\">\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"bold\" className=\"text-green-9 block\">\n\t\t\t\t\t\t\t\t$500\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\t\tPrize\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Prize Breakdown */}\n\t\t\t\t\t<div className=\"space-y-2 mb-4\">\n\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t<div className=\"w-2 h-2 rounded-full bg-yellow-9 shadow-sm shadow-yellow-9/50\"></div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">1st place</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-white\">$300</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t<div className=\"w-2 h-2 rounded-full bg-gray-9\"></div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">2nd place</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-white\">$150</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t<div className=\"w-2 h-2 rounded-full bg-orange-9\"></div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">3rd place</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-white\">$50</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Timer */}\n\t\t\t\t\t<div className=\"mb-4\">\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11 mb-1\">Time remaining</Text>\n\t\t\t\t\t\t<div className=\"flex items-center gap-1\">\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">0</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">3</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-4\">:</Text>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">1</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">4</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-4\">:</Text>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">5</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">0</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Participants */}\n\t\t\t\t\t<div className=\"mb-4\">\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11 mb-2\">Participants (342)</Text>\n\t\t\t\t\t\t<div className=\"space-y-1\">\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t<div className=\"w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-white font-medium\">AS</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">Alex Smith</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-green-9\">$2,847</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t<div className=\"w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-white font-medium\">SC</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">Sarah Chen</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-green-9\">$2,756</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t<div className=\"w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-white font-medium\">MJ</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">Marcus Johnson</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-green-9\">$2,698</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Join Button */}\n\t\t\t\t\t<Button\n\t\t\t\t\t\tclassName=\"w-full bg-green-9 hover:bg-green-10 text-white border-0 shadow-lg shadow-green-9/30\"\n\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t>\n\t\t\t\t\t\tJoin Competition\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Other Competitions */}\n\t\t\t<div>\n\t\t\t\t<Heading size=\"4\" className=\"text-gray-12 mb-3\">\n\t\t\t\t\tOther Competitions\n\t\t\t\t</Heading>\n\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n\t\t\t\t\t{competitions.slice(1).map((comp) => (\n\t\t\t\t\t\t<Card key={comp.id} className=\"p-3 hover:shadow-sm transition-shadow bg-gray-2 border-gray-4\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2 mb-2\">\n\t\t\t\t\t\t\t\t<div className={`w-6 h-6 rounded-lg flex items-center justify-center text-xs ${\n\t\t\t\t\t\t\t\t\tcomp.status === 'active' ? 'bg-green-9 text-white shadow-sm shadow-green-9/50' :\n\t\t\t\t\t\t\t\t\tcomp.status === 'upcoming' ? 'bg-blue-9 text-white' :\n\t\t\t\t\t\t\t\t\t'bg-gray-9 text-white'\n\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t{comp.icon}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div className=\"flex-1\">\n\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"medium\" className=\"text-gray-12\">\n\t\t\t\t\t\t\t\t\t\t{comp.title}\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t{comp.description}\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"bold\" className=\"text-green-11\">\n\t\t\t\t\t\t\t\t\t{comp.prize}\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between mb-2\">\n\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-11\">{comp.timeLeft}</Text>\n\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-11\">{comp.participants} players</Text>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tcolor=\"green\"\n\t\t\t\t\t\t\t\tvariant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}\n\t\t\t\t\t\t\t\tclassName=\"w-full\"\n\t\t\t\t\t\t\t\tsize=\"1\"\n\t\t\t\t\t\t\t\tdisabled={comp.status === 'closed'}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{comp.status === 'active' ? 'Join' :\n\t\t\t\t\t\t\t\t comp.status === 'upcoming' ? 'Register' :\n\t\t\t\t\t\t\t\t 'Closed'}\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Competition Stats */}\n\t\t\t<div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t12\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tActive Competitions\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t$15K\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tTotal Prize Pool\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t847\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tTotal Participants\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t24h\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tAvg Duration\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;;;AAHA;;;AAKe,SAAS;;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,qBACC,6LAAC;QAAI,WAAU;;0BAEd,6LAAC;gBAAI,WAAU;0BACd,cAAA,6LAAC;oBAAI,WAAU;8BACd,cAAA,6LAAC;wBAAI,WAAU;;0CACd,6LAAC;;kDACA,6LAAC,mLAAA,CAAA,UAAO;wCAAC,MAAK;wCAAI,WAAU;kDAAa;;;;;;kDAGzC,6LAAC,6KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAc;;;;;;;;;;;;0CAIxC,6LAAC;gCAAI,WAAU;;kDACd,6LAAC,iLAAA,CAAA,SAAM;wCACN,SAAS,cAAc,iBAAiB,UAAU;wCAClD,OAAO,cAAc,iBAAiB,UAAU;wCAChD,SAAS,IAAM,aAAa;wCAC5B,MAAK;kDACL;;;;;;kDAGD,6LAAC,iLAAA,CAAA,SAAM;wCACN,SAAS,cAAc,iBAAiB,UAAU;wCAClD,OAAO,cAAc,iBAAiB,UAAU;wCAChD,SAAS,IAAM,aAAa;wCAC5B,MAAK;kDACL;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASL,6LAAC;gBAAI,WAAU;0BACb,cAAc,+BAAiB,6LAAC;;;;yCAAsB,6LAAC;;;;;;;;;;;;;;;;AAI5D;GA7CwB;KAAA;AA+CxB,SAAS;IACR,0BAA0B;IAC1B,MAAM,kBAAkB;QACvB;YAAE,MAAM;YAAG,MAAM;YAAiB,OAAO;YAAM,QAAQ;YAAO,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACvG;YAAE,MAAM;YAAG,MAAM;YAAc,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACnG;YAAE,MAAM;YAAG,MAAM;YAAkB,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACvG;YAAE,MAAM;YAAG,MAAM;YAAkB,OAAO;YAAM,QAAQ;YAAO,QAAQ;YAAM,QAAQ;YAAI,SAAS;QAAO;QACzG;YAAE,MAAM;YAAG,MAAM;YAAa,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QAClG;YAAE,MAAM;YAAG,MAAM;YAAa,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QAClG;YAAE,MAAM;YAAG,MAAM;YAAgB,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACrG;YAAE,MAAM;YAAG,MAAM;YAAc,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;KACnG;IAED,qBACC,6LAAC;QAAI,WAAU;;0BAEd,6LAAC,6KAAA,CAAA,OAAI;gBAAC,WAAU;0BACf,cAAA,6LAAC;oBAAI,WAAU;;sCACd,6LAAC,mLAAA,CAAA,UAAO;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAGjD,6LAAC,6KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,6KAAA,CAAA,OAAI;gBAAC,WAAU;;kCACf,6LAAC;wBAAI,WAAU;kCACd,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC;;sDACA,6LAAC,mLAAA,CAAA,UAAO;4CAAC,MAAK;4CAAI,WAAU;sDAAgB;;;;;;sDAG5C,6LAAC,6KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAuB;;;;;;;;;;;;8CAIjD,6LAAC,iLAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAO,OAAM;oCAAQ,MAAK;8CAAI;;;;;;;;;;;;;;;;;kCAMhD,6LAAC;wBAAI,WAAU;kCACb,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,6LAAC;gCAAsB,WAAU;0CAChC,cAAA,6LAAC;oCAAI,WAAU;;sDACd,6LAAC;4CAAI,WAAU;;8DAEd,6LAAC;oDAAI,WAAW,CAAC,wEAAwE,EACxF,OAAO,IAAI,KAAK,IAAI,+DACpB,OAAO,IAAI,KAAK,IAAI,yDACpB,OAAO,IAAI,KAAK,IAAI,+DACpB,2DACC;8DACA,OAAO,IAAI;;;;;;8DAIb,6LAAC;oDAAI,WAAU;;sEACd,6LAAC;4DAAI,WAAU;sEACb,OAAO,MAAM;;;;;;sEAEf,6LAAC;;8EACA,6LAAC;oEAAI,WAAU;;sFACd,6LAAC,6KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,QAAO;4EAAS,WAAU;sFACvC,OAAO,IAAI;;;;;;sFAEb,6LAAC;4EAAK,WAAU;sFAAW,OAAO,OAAO;;;;;;;;;;;;8EAE1C,6LAAC;oEAAI,WAAU;;sFACd,6LAAC,6KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,WAAU;;gFACvB,OAAO,MAAM;gFAAC;;;;;;;sFAEhB,6LAAC;4EAAI,WAAU;;;;;;sFACf,6LAAC,6KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,WAAU;;gFAAe;gFAChC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOvB,6LAAC;4CAAI,WAAU;;8DACd,6LAAC;oDAAI,WAAU;;sEACd,6LAAC,6KAAA,CAAA,OAAI;4DAAC,MAAK;4DAAI,QAAO;4DAAO,WAAU;sEACrC,OAAO,KAAK,CAAC,cAAc;;;;;;sEAE7B,6LAAC,6KAAA,CAAA,OAAI;4DAAC,MAAK;4DAAI,WAAU;sEAAe;;;;;;;;;;;;8DAIzC,6LAAC;oDAAI,WAAW,CAAC,+CAA+C,EAC/D,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,6BAA6B,wBAC5D;8DACA,OAAO,MAAM;;;;;;;;;;;;;;;;;;+BAlDR,OAAO,IAAI;;;;;;;;;;kCA2DvB,6LAAC;wBAAI,WAAU;kCACd,cAAA,6LAAC,iLAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAO,OAAM;4BAAO,WAAU;4BAAS,MAAK;sCAAI;;;;;;;;;;;;;;;;;0BAOlE,6LAAC;gBAAI,WAAU;;kCACd,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;MA7KS;AA+KT,SAAS;IACR,MAAM,eAAe;QACpB;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,UAAU;QACX;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,UAAU;QACX;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,UAAU;QACX;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,UAAU;QACX;KACA;IAED,qBACC,6LAAC;QAAI,WAAU;;0BAEd,6LAAC,6KAAA,CAAA,OAAI;gBAAC,WAAU;0BACf,cAAA,6LAAC;oBAAI,WAAU;;sCACd,6LAAC,mLAAA,CAAA,UAAO;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAGjD,6LAAC,6KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAO3C,6LAAC,6KAAA,CAAA,OAAI;gBAAC,WAAU;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;0CAEd,6LAAC;gCAAI,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAU;0DACd,cAAA,6LAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEhD,6LAAC;;kEACA,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAO,WAAU;kEAAa;;;;;;kEAGpD,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAe;;;;;;;;;;;;;;;;;;kDAK1C,6LAAC;wCAAI,WAAU;;0DACd,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAO,WAAU;0DAAqB;;;;;;0DAG5D,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAO3C,6LAAC;gCAAI,WAAU;;kDACd,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAc;;;;;;;;;;;;0DAExC,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DAAa;;;;;;;;;;;;kDAEvD,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAc;;;;;;;;;;;;0DAExC,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DAAa;;;;;;;;;;;;kDAEvD,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAI,WAAU;;;;;;kEACf,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAc;;;;;;;;;;;;0DAExC,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DAAa;;;;;;;;;;;;;;;;;;0CAKxD,6LAAC;gCAAI,WAAU;;kDACd,6LAAC,6KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAqB;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAU;0DACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,6LAAC;gDAAI,WAAU;0DACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAc;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,6LAAC;gDAAI,WAAU;0DACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAc;;;;;;0DACvC,6LAAC;gDAAI,WAAU;0DACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,6LAAC;gDAAI,WAAU;0DACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;;;;;;;;;;;;;0CAMvD,6LAAC;gCAAI,WAAU;;kDACd,6LAAC,6KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAqB;;;;;;kDAC9C,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAI,WAAU;;0EACd,6LAAC;gEAAI,WAAU;0EACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oEAAC,MAAK;oEAAI,WAAU;8EAAyB;;;;;;;;;;;0EAEnD,6LAAC,6KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAExC,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAI,WAAU;;0EACd,6LAAC;gEAAI,WAAU;0EACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oEAAC,MAAK;oEAAI,WAAU;8EAAyB;;;;;;;;;;;0EAEnD,6LAAC,6KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAExC,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,6LAAC;gDAAI,WAAU;;kEACd,6LAAC;wDAAI,WAAU;;0EACd,6LAAC;gEAAI,WAAU;0EACd,cAAA,6LAAC,6KAAA,CAAA,OAAI;oEAAC,MAAK;oEAAI,WAAU;8EAAyB;;;;;;;;;;;0EAEnD,6LAAC,6KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAExC,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEAAe;;;;;;;;;;;;;;;;;;;;;;;;0CAM3D,6LAAC,iLAAA,CAAA,SAAM;gCACN,WAAU;gCACV,MAAK;0CACL;;;;;;;;;;;;;;;;;;0BAOH,6LAAC;;kCACA,6LAAC,mLAAA,CAAA,UAAO;wBAAC,MAAK;wBAAI,WAAU;kCAAoB;;;;;;kCAGhD,6LAAC;wBAAI,WAAU;kCACb,aAAa,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,qBAC3B,6LAAC,6KAAA,CAAA,OAAI;gCAAe,WAAU;;kDAC7B,6LAAC;wCAAI,WAAU;;0DACd,6LAAC;gDAAI,WAAW,CAAC,4DAA4D,EAC5E,KAAK,MAAM,KAAK,WAAW,sDAC3B,KAAK,MAAM,KAAK,aAAa,yBAC7B,wBACC;0DACA,KAAK,IAAI;;;;;;0DAEX,6LAAC;gDAAI,WAAU;;kEACd,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEACvC,KAAK,KAAK;;;;;;kEAEZ,6LAAC,6KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEACvB,KAAK,WAAW;;;;;;;;;;;;0DAGnB,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAO,WAAU;0DACrC,KAAK,KAAK;;;;;;;;;;;;kDAIb,6LAAC;wCAAI,WAAU;;0DACd,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAgB,KAAK,QAAQ;;;;;;0DACtD,6LAAC,6KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;;oDAAgB,KAAK,YAAY;oDAAC;;;;;;;;;;;;;kDAG5D,6LAAC,iLAAA,CAAA,SAAM;wCACN,OAAM;wCACN,SAAS,KAAK,MAAM,KAAK,WAAW,UAAU,KAAK,MAAM,KAAK,aAAa,SAAS;wCACpF,WAAU;wCACV,MAAK;wCACL,UAAU,KAAK,MAAM,KAAK;kDAEzB,KAAK,MAAM,KAAK,WAAW,SAC3B,KAAK,MAAM,KAAK,aAAa,aAC7B;;;;;;;+BApCQ,KAAK,EAAE;;;;;;;;;;;;;;;;0BA4CrB,6LAAC;gBAAI,WAAU;;kCACd,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,6LAAC,6KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,6LAAC;4BAAI,WAAU;;8CACd,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,6LAAC,6KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;MAzRS", "debugId": null}}]}