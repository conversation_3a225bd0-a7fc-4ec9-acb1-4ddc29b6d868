{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 60, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/app/favicon--route-entry.js"], "sourcesContent": ["import { NextResponse } from 'next/server'\n\nconst contentType = \"image/x-icon\"\nconst cacheControl = \"public, max-age=0, must-revalidate\"\nconst buffer = Buffer.from(\"AAABAAEAAAAAAAEAIAAfJQAAFgAAAIlQTkcNChoKAAAADUlIRFIAAAEAAAABAAgGAAAAXHKoZgAAJOZJREFUeNrtnXmU3VWV7z/7/G7dmlKVOVUECAECYZ4EWlEijyEERMS2m34OyGQYXOt191u2Q8sDGX3aNuuJ9mplEJC2G4jdIioYEmwRbAZtiRgEkTAYQkhlrCE13Xt/Z78/zr2pm7nmuvf325+1CqoqNdzav7O/57vPKFQBuqgVgBhQ1YG3QoFMQ534QuyiKFOn3k+JVWcKTAVtRpkKTAKaEJm87X2oQ3USUFv8WIrvNxTfd4g0Aw7QgReiAjQC2WqIWwrJAd2IaNnnBNXw+YFn2V/2cSdQQKS7+LkuYCuqHUAXIh3AVtBOkC0ibiPObdFCvkczGZ/p7dVCJoOIICIggq/JInFM9tG3Kz5gUokvqvvsFrKAL71AVRSIoqhevZ+lMAPYBzgYdA7KAUBrMTknAfVAXTGha4uJbBhDxQN9RcHoLb51B0FgHSJvA6uBV4C1AhvEuY2+kO9BQpNTEXAOX8hTv3yjCcCu0AvmQhRR6O4OH6vS39dHtr6+2cFs9f5Y4DjgkJD0zAEmFxO7IkXMSBUx0AG8WXx7FViByPMKbe1Hn9w5deWvwEUgQo2PQ/ItXWcCkC/ae/WeWJWMc9OAY1T13cCpwNHAdEJvbhjVQg/QDrwA8jTC04h7XtF2vMe54BBqJlgEJkQAcmfNBO8hisKLiKJa9f4IVBcC5wKHAzOtDRkJYiPwMvCwCI/jopc1jnMAxDE4R3b5hmQLQKm3RwT1HrxvQOQ9wIXAecBsaydGCngTeAK4H9VfItKDhvHJmiga19Jg3AQgPm8/UCXO55AoU6NxfApwBbAImGZtwkghm4GlwL3Ak0B/xoWUdEvbkiEAvYtaUREy3rPm923sf+Ss4xS5EtUPAy3WBgyDTcD9ItyZyUQvFvKxz0QRqI65GxgzAehd1EpGlbiY/F6kBdWLgcuA+fbMDWMnVgHfBv7Fq67POkfee7JjWBaM2fx4Jo4R9ZDNihdZgOo9wC2W/IaxW+YB/xf4t0jkTKJIAPrieMx+4ag7gL7iQJ/zHoGZKlyO8mlgf3u+hjFo1gK3CdytsDEqrjR0o+wERlUA8sV6v6Z7K4X6+mNUuQ74EJCx52kYQ6Yf+DEi1/d5//vGKMIzumsHRk0A8gtnAoLU1DhfKJyD6i3AsfYMDWPErBSRz4tzyzQO9UDNsvWVIQB6wVy629qobWpC8LUetxi4hrA23zCM0eEd4KsOvqWQ6+/qorGlBfnhmxMnAL2LWonb26lpasJlMpM1jv8W+Axhh51hGKNLB3CrRNE3tVBo7+vqomnKlBHNEAxbAHoXtVLX10c+m0VFZonqTcDFhN13hmGMDf3AvU7kOq+6vj2XY2Zd3bBFYFjTgHrBXOL29lLyt4jq14BPWfIbxphTCyz2ql9zIi1Tslm62tvDjtphMKzR+e62Nmqamko9/z8An8D23BvGeOGAT3hVnMhn65qa1ne3DW/p8JBLgPzCWeGdKJqicfwVYLElv2FMCB64S6Loc+p9B0D2saEJwZASt7SbTyCrcfy/gEss+Q1jwnDAxerjv0WktjxHR10A+ha1ooCLosjD1YTRfqv5DWNiqUX5jHq/WKPIiQh+CCIw+N5blZpCjtj7hcDnCUdyGYYx8TQJXOPi+JzO3t4hfeNexwB6F7VSE8d4EYAjgfsJx3QZhlFZvCAin1TV38XeU5fJ7HV6cK8OoCaOS2cpzwCut+Q3jIrlWFW9zonMjKKIHtW9fsMeBaB3USuxc0hNjRD28X/QYmwYFc2HPFyer62VrMi2OzWGJQAqQuQ9vlA4A/gbbNDPMCqdDKqfrunvX5DzHkSGJwB9i1rIhNp/FvAF7MBOw6gW9kf18xmRlrz34TzOoQhAb9E25GsiB1wELLCYGkZVcZaoXtK2IiwM2t36gF0KQBTHOK/UFOKjgKuAGounYVQVGeDSluNbjotzud1+0U4CkDtr230ctSiLCeeUGYZRfcwHrpQoqkGkPLd3LwAMDBwsAD5qMTSMKkb1wxrHpxQv4hmEAITzyBsI6/ynWwQNo6ppAa7A+4bSVXy7FYCyq7veR7ixxzCM6mdR8Qq+nQYDtwlAaeTfRVGWYP3tui7DSAbTgAslimqB7Q4P2SYASrii2/v4cOA0i5lhJIrz1PsjtOxW7u0EIBPHYexPOROYa/EyjEQxG9WFTpVCd/fOAgCgXqcQLvIwDCN5nOud2660d1Cs/8PU3wnAERYnw0gkh6N6jPee7rNbBgTAec+WMP9/Cjb1ZxhJZSbw7lxvL9niJ0IJIMKUKGoGTrEYGUaiOTXb2NjsywVAVJGwYMDu8jOMZHO0g9mlTcJu2/pg1ROAKRYfw0g009X7Y1FFF7XiVLU0AHg80GDxMYxE0wAcpwB1dTgngnjfABxisTGMVHBIBPVxTw9ORVCRGdjiH8NICwd7kRYvsm0h0EwTAMNIDXOA6ajiigcHz8Yu+jCMtDAZ2EdVcdk4hnBySGRxMYxU4FCdh0AmFhGCJTAMIx0IwhypyYhT5xywr8XEMFKEMoeCOkeYAmxNfUAMI120Rs7VOZybCkyyeBhGqmjUOJ7iNFz62WjxMIxUMSlWnelQnYotATaMtNEgIlMdzjUDdRYPw0gVdQrNjlD/262/hpEu6gWmOlQnYw7AMNJGLaqTHNDEHq4JNwwjkTigyWF7AAwjnYhMLo0BGIaRPraVAIZhpA3VJoctAjKMtFJnAmAY6WWSAzIWB8NIJbUOaLY4GEYqaXLYGgDDSCvisI1AhpFWah22D8Aw0kqD2X/DSC/iALE4GEYqcbYOwDDSS7MDshYHw0glzgFqcTCMVKI2CGgYKcYEwDBSLgA2C2AY6URsFsAw0kujzQIYRnrJ2hiAYaQYEwDDMAEwDMMEwDCMVJGw48AUtPRW/BgACZOd5Z8TByLhLU1oWYz2GB8ZiFEa41NqSyVKcdDymEnVt6HqFgAte0giUFsPTVOgsQnqm6C2DqmphboGiCKIY+juRPu6oasdtmyA7s7wM6T0kBPU4HWHRpypgUmTw1tjM9TWI9laqJ80EJ++brS3G7Z2QPtG6NoSPr9NFJIkCOWdRZH6BmieBg3N4f3ahhCj2gZwArkc2tMJvd3QsQk6NkJvT9XGp/oEQBXUQ5SB5mnItBY4+Cg46Eho2R+ZPL0oAs1Q1wg1O8xyxgWkZ2t4eBvXon96Bf7wG3TVStjcBvl+cFF1t2vvg441TobJ05ED5sOBh8N+85CpM0MDnzQF6hshW1tcDlKKr0f6eqBjM2xeh77zJ3hlBfrK87BuNfRurX5nUGpDNbWhDc3cF+YdBXMODe9PmQlNk0Mn0jAptLUyJN8PPV2weT1seBt942V45Xn0zT/AlvVBMF11VNeSWzirOjYD+TgkZvNUZP4JcPiJMP8EZM4hoSHX1IWgl9s33c2fts26ufCwcn3Qthr97VPob56AP/wmPOBqaujeh//XNcAB85EjToKj3o0ceAQ0Tw2NPVPD7sukHeKDhB5PCaLYsQl98Vl4/hfoymdg4zvhZ1RJQwcNMYpqYHoLUmo/R5wErXOCe6zJhme+uzJgpzbkgtAWCtDfi65ZFeLz65/BGy9Brj/EsIJdZWULQOlBZGth34ORE96PnHwmHDA/2NjyrxlxJIobI7va0ZXPoP/57/Dis9DdVdnWTn14bS1zkCNPhlM+gBx0JEydGXou9aMUn2IM+nrhrVfRn/8H+txyWL+mTDQquA3V1sHBRyMnn4kcvwBmHxg6DmWH8ZARtiH1sLkNXfEkPPEQ+tKvIZ+rWKGsXAFQD9k6mHcMcvpfIMecArP2C7XqaCX9bhu6g+4O9IX/Qpc/ACufhf6e7a1yJTRscaHsee85yIIPwZz5kMkUG7Ufw/hI6PXeeAl94gfokz8K4ymV1si9h4Ym5Nj3wnvPRY5+D0xrCa9/TNtQ0Rm0b0SfWYo+vgRWrQwutsKEsvIEoPRQ9p+HnHMR8t4PhN5MZMDmjktkikLQsQl9+hH0oTth7RsT7wZKDXdaC3LaBcj/+HPY/5Bg78czPhBKsv5eeOG/0B/fHUqDOD/xQqkeMjWhVFz0ceTE04NjHEth3F0bQsI4wdLvoY9/P4wzVZBQVpYAeA9TpiPv/zBy9sdgv4MH1HrCIlRM9tdeRB+6HX32sVDbTYQIeB9G7t91Gpx7EXLkn0EmO76NepdC4EJv99i/oY98NwyOTUQjL4njPnORcy9CFpwP01vHtrcfbBuKC6G0XPJN+P2vig5u4t1AZQhAaTDpyJORj3w62P2a7Pj3aHtr5N1doZE/fCdsGmclV4V9DkA+eBly2gXQNK0YnwrRb3EQ59H//jn64G3w2ovj34aydXDC+3EfuRoOPW6gJq+MAIUBwY3voA/dgS67H/q6J9wtTbwAqIeGZuSMv0AuuCLU+T6unMTfLloCPkafW4Y+cBu88fLYq7gqRBFy/ALkwr+G+cdPvCvaW4xWv4re+2X0Nz8vCtQYx8jHMGM2csEVyOl/HqY5K6nzKMc56O1GH1+C/se3wmzKBJYE0bUHN14/Yb/dx8GuffzvkPMvg8nTK0ixd9/AZf9DkEOORVf/ETasHTsR8B4am5DzLkUu+lyo9auBKTOQo/4sjH6/8XKYah2TGBWnMucfj1x+LfL+C8I0aKWKI4TXVpNF5h0Tph9XrQyLrSaoHJg4AVCFw05ArrgBOeWcMIhVyQ9uR6a3Ioceh77zBrStZtR7OVWYORv5xGeDODZNraL4KDQ2I4e9K4yXvP57iAuj28hVIcog716Iu/LGMK9fTefbFjsS9j8E3vpjWEA0AesFJkAAgiWUE0/HXX0zcuhx1ZX45X/H1FnBCaxZFVbJjVYD9x72OxhZfD1y6gerTxxLCZqtQ+YfD/19YUxgtJyAKtTUImd/DLnkizBr/8p3jrvTgdlz4aCjglPa+M64O4HxFYDiYJ+ccg7yqetg9kFV++C2/T1TpiPzjgnlwPo1I3+Aqsihx+IWXx8WrFT72vtsbRD5/j54/cXi+I6MKD5ka5EPXop89G/DKseq7EDKnveMfaBlf3hlBXRuHtdnPn4CUKp9FpyPXHYtzNq3upO//O+aPB056Ej0zZdHNiagihx+InLVzXD4SdXdsLcTgXrk0GND43799yOLdf0k5IJPIX/x6bDXIQkxUkVa58DMfcMy9NLq0+QIQFiKKedchHzy8zB5RjKSv5xprdCyH/z+ubDDcKgPUD1y+EnIlTeFjSmVOoo97Odfj8ydD2+tgnfeHEZ8FOoakI9chXzk6rDRKykCWURmHxgGMV/69bitNRmf+QdV5OSFyP/8m7ALLWnJD+Bj5OhTkHM/Wdx0M5Tv9TD3cOTy6+DgIxOW/KU24KH1APjI1TBlxtCSt5T8f34VcsHiMN+fsOQP2eiQ0z6MnHQ64zWgOU4TkIKufT3UyUl8cMWHx/o1wcLFhaE17jmHIJdfC4cem8zkh9Cb9XSFDVZ9PUOLT30j8pGrkQ9fEXbtJbUNqaIv/3fYXjxOMwLjIwAisGol+u3/g77868raVDNayb/mNfSuG9Bnlw3++7wPex6uuBE59n3Jbdgi0NcTllI/dEcQgMHY2/Ke/8NXJjv5RdAVT6J3fAne/MO4jQGMXyY6B6+/hN7xpWSJgHNhe+yd16O/enzwD04VOfQ43NW3hN1qlvy7T/4LFoct4UlP/rtuhDWvjevKwPHNQufgtZXo7dehL/2Kqj9+a1vy34A+/4vBf19ptP/TX4ajT7Hk31Xyp8X2A/r8L9A7b4C3Xh33ZcHj3w27KOysu/3a6nYCzsGaVSH5Vzw5hJ7fh+S/8iaYd3Tl7nuYyORPm+2/8/oJSf6JEYBS8rz+EnrHddUpAuU9/2+fGnryX3Fjckf7Ryv5U2P7bxh32z/xAlBKotderL5ywLmw221Ytv+kkPzzjrbk31XyNzSl0PavmtDdgBPb9VZbOVCy/XfdgK74hdn+UUt+D5OnIR//jNn+VAlAKanKy4FKPWXWOXhrVZntH+Tr3Jb8Sbf9bgTJPx35+GfDIiqz/SkTgFJylcqBlc9UZvKvLk71Ddf2H5x02989vOSfMhP5xOeQsy4cOPA1oVSK7a88AYCBcuCfvzi0UfUxb9zDtf1qtn9vyT95BvKJv0PO+qviEeZm+9MrADBgs++6sTJEoHyqbyi233vk8HeZ7d9T8jdPDydBnfGXA5dxJDn5K8j2V64AbBOBVwfm1yfydQzX9h9htn+PyT9lJnJRyfZnqKqTfIaIrqg821/ZAlAuAndcPzFOQEpOxGz/7pO/vOfvNtu/ixiFtf3XV5ztr3wBKInAmvJywI3z7zXbv+uGvSvbP4gYbWf7L0y47XcVbfurQwBKyfjWqwM98Xj8PrP9e2jYYUuvfv+f0IduH4Htj0i87b+rcm1/9QhAeVKOdTkwEtt/3PvCMV6Jtv3Fi1Ee+Dr68F3hklCz/TvEqGxL7+rKtf3VJQAlEdg2FTcG5cBwbb965LhTkcU3wEFHJNv293ShD96GPnrf4I+rMttvAjCqSVreQ4/mzx2O7QfkuAXI4i/BnEMSbvs7Q8//yBCT32y/CcCoi8BolgMjsv2nIlfcAHMOTXbP392FPlDs+fP9w7D9Fybb9kO4Ku6O66rG9levAJRE4O1RKAdGavs/9SXYb17CbX/n8Gz/lJnhRqMzLgRJ8PJeEfTZZcWTfF6vuuSvTgEoNc6RlAPltn/FCGx/Ek83LjbsYPtvG5rt9z5c0nnZtciZKbD9zyxF774J2t6qyuSvXgHYKYmHUA7sJB5m+3dK/uHYfu/DXYaXfBF5/4fC3o4k2/5nlqLfuSlcCVelyV/dAlASgaHMDpjt30vyD3O033uY0Ypc/AXkfeeVgpbQGBVt/903w/rq7fmTIQClRjuYcmBHxzCUX2G2f8/JP3M2csk14SLTKk+IvbGd7U/AydbJeFp7KweGO2Zgtn+Qyf9FZMEHkz3gR3Jsf/IEoCQCuyoHzPbvJflHYPunl9t+wWy/CcDEN+bynr780g6z/btJ/hHY/kvTYvsfS5Tt364J5BbOSp5sex/u2zv/cnhmKfr8E0N4cDqQ/PsnfIXf1g50yTeH3vOXbP/7zivGNelTfTdX9VRf+gSgRLYW8rnB16Xqi8l/fUj+pPb8zqGb2+C+f0CffDjEaCi2/9IvIqeeXznHto1JZkjo+b9zY7D9SbvPskgmuU8QyPUx+PsGNCVr+0Py6z1fhicfDn/nUHr+i/8eOfW80BsmcsAvjGVsZ/sTXOIkWwCGk/yJtv3F5L/3FnjyR0NP/nLbn9jRfk287U+RAAzmefuB5N8vwcnvypL/qR+H8mZIo/1/X0z+hI72i0Acoz//AfrA/4P1axKf/CYAaev57/ly6PlVGZQ7SovtF4FCAX38QfR7/wgdm1KR/KkXADluQZjnT0PNb7Z/z8m//H70X2+Fzs2pSX5I2jqAweI9HHhE8pPfOXSL2f49Jr+Piz3/rdC5JbGj/SYAOz74rnb07dfTY/u9Z0i2/9JrBmx/UttAoYAueyDY/q7NyZ7W3A3pLAFEYMOacISTj5H3LEpm8o/U9ie95i+3/Snr+Uuk868uJgnr30Lvvhl9dlly1H80bX9Skz/ltr+cdM8CiIO2t8KCD0DevbDq/54B219M/uGM9id1ea8IxAV0+YNltj+9yW8CAMHmrlsdlnyqr95yQKRo+4e5wi8Vtj+PLn8g9bZ/u+af+giURGBbOfBY9ZUDzqFb1qP33AJP/chs/66S38fo40vM9u+AOYBtjaRUDtwMSPWUA+LQDWvR735loOYfku3/gtl+EwADqL5yQARtWx1OqXluWTGBzfZvl/yFfEj+f/1Hs/27avIWgl2IQDWUA86h69eE5P/V8lKLH1zyp8X2xyXb/49m+3eDOYBdNp4KLwfEoW1vBafy3LLBf1+abH+uH33s39AHv2G23wRgGJTKgbsrrBwQGVnyp8H25/rQR76LLvkmdHda8u+pmVsI9iICbTuWAzKhryfY/hvhueVDS/4Z+5TN8yfY9udz6KP3hZ6/u8uSfy+YA9hroyqOCXznRujZirz/AshMwGWXbgS2v3UOcsnfF11Mgm1/vh995D70wW9Cz9ZUru03ARgrEWhbg95zM+T6kLP+CjI14ycCIui6YSb/PnORxdchJ55BYo/uNts/bCxKg46Ug84t6Pe+hi5/EOJ4fHqYkdj+1jnIp64tJj/JTf58rtjzm+0fKuYAhtrYuraEpaQiwQlEY1gOjMT273MAcvl1yElnJPt5lGz/ErP9JgDj0ugcdG5Cv/e1cHXYWJUDI7H9s+cinzLbb+wdi9iwRWAMy4GR2v7LzfYbg8McwEgaYVf76JcDI7b915rtN0wAxq0xdm4KS01HoxwYqe3fVvMn3fbfhy75htn+UcCiN+JG6aBz88jLgRHb/vIBv6Tb/tvM9o8S5gBGq3GOpBwYFdt/erLjm88NDPiZ7TcBqMhGOpxyoGT7774RnjXbv8u4mu0fMyySo9pYh1gOlNv+Z4exvPfy65CTzix+0my/YQJQGY22WA7o8gd2LwLDtv1xcYXfdUXbn9BLOs32jwtWAoxV4y2VA4UcsvCjkK0bKAfKt/QOpedXD/vNQy67JiWLfIpTfd0d1vObAFRbIy6VA7dCPo+cdwnUZEc21XfwUcji65EjTy6KSQpsf89WS34TgCpuzD1doRcTQc67BN28Pgz4DSP53ZU3weHvSuZe/u2S32y/CUCSGnV3R1iyurUDXf3HoSf/vGNwV90I809IdvKb7R//sOcWzkpoi6owVMFFxWO7h5D8Bx+Fu+pmOOxdQ/veakv+fA79yb1ltt96/vHAJHY8G/lwkv/Km+CwE5Kf/Gb7JwQrASoRs/2GCUCKk//go4rJnwLbb6P9E4pFvBKT/8qbij2/2X5jbDEHUCmowiHH4K64sVjzm+03TABSkvweZu6LXHoNHH5iWO6b1OTv60V/9B30B98y218BWPQrIzPCLrfXXoRcXzLtsAj096I/vAP9/j+Z7a8QzAFUSnL0bA2LhVSRD3xy+70DSfj7ervRH96JPnQ79Pda8psAGDslSXdnGBEH5LyLoaa2+kWgZPt/eAf6g29Df0IdTpViJUClJUvPVnTJN9BH7oN8rrqTpdz2P3S7JX8FYg6gEpOmu7P6y4EdbX+f2X4TAGOQyePKygEtbiWuonLAbH/VYCVAJSdRz1Z0yTerqxwot/0/MNtf6ZgDqPRkqqZyoORcfnQX+tAdNtpfJQKQA7IWikoVATcgAnEBOfciaGiqPBFwEWxuQx+8DX18CeT6Lfkrn1wG6DYBqHQRKJ4s9MDXYd1q5GP/G6bvUxkrBkXC25pV6P1fR//rJ+N3dboxUrozJPZY2QSKQK4f/dn30XV/Qv7qr5EjToYomjg3IC7c07fiF+iSf4JXXwjNyZK/WlAbA6g2EfAxvPBLdO0bcO4nkTP+EqbOLD5OHb/XAdC2Gv3pv6CPfx86NoFzhJOKjappUrmFszYB0ywUVYZquHnosHchZ38UOX4BTJ4e/NxYbSN2xUmjrnb0ueXoo/eF/QveLH+VsllyC2dtBqZaLKpSBYIQNDTBESchp56PHPVumLFPSFbVEboCCR26SDiroO0t9IVfok//FP7wm7Chx9lMchWzRXILZ20EplssqlkHiolek4WDjkSOeS8c8x7kwCOCOGTrineI6J5FoTSgJxKcRH9vGHx842V44Sl0xVOw+o9QyA98nVHNtElu4azXgQMtFgnB+9ArT5oM+8xF5h0NBxyGtM6BaS3QPC2IQqZm+++LC9DTCe0bYcsGdN2fYNWL6Gu/g/Vvw9aO4snG1uMniNczQELPnUopZXU6XSvQV54HF6GNk6Fpckj+2nqkrhGyteFrc/1of09Yr9/dGU7p2doRxEQglALW4ycQtWnApCLCthoeYGs7dG3ZdqWYlv+blv4j25cB1tsnngzQY2FIiSBYD25sT78jLAU2DCN9dDtsDMAw0oo6oMviYBippNMB/RYHw0glBQdstTgYRirpdkCfxcEw0isANgZgGGlEpMtKAMNIL1sd0GFxMIwUotpRKgFsLYBhpAsPbCsBbCrQMNJFHyIdDtgC9Fo8DCNV9ANbnUInNhVoGGmjD7Sz5ABsR6BhpIselC1ORDZgU4GGkTa6xbmNTrxvJ1wOYhhGetiKi7Y4FekD1lk8DCNVtKmPe1zsvQdWWzwMIz2oyJp8FHnnokiLAmBnAxpGShBYXd/To654RvwqbDWgYaSFGHgln8ngisdEvoPtCTCMtNABrBURSuc+b8LGAQwjLbyJyAbE4cIp8bQBr1lcDCMNyJsaZTaqc7iehgZc2AvwqgXGMNKAviq5/h7nHK6ur680/P9bbEmwYSSdHmAFIqgqrn5pWAMk8AJhLMAwjOTSjsjzANlH3w6DgCqCF1kLrLT4GEaieUGhTYvXxDkAL0K2p6cTeMriYxgJRuTp9sNO6Cxd/OoARJV8fT3As8AGi5JhJJJNwNNT//A8vpAfEIC6x9oQ50Dkd8DLFifDSCQv4dzzuIj65RsHBKCEE9kMPGpxMowEIvKw9ufat8v50juZnh582BewDFhr0TKMRPGmoI+TiahRv7MA4BwigkTRS8BPLF6GkSCEJ3DuZee2M/0DAiBPtAOg3vcjsgTYbFEzjESwGeR+9ZoDkKXrdhYAgJrSP4g8g8hSi5thJACRpYj8crsc35UAABBFoNoD3EHYJGQYRvWyCbgX1R6iaKd/3FkAvA9jAc49jchDFj/DqGrux7knEQHv9y4A2aJF0EIhj+rtwCsWQ8OoSlaJcCc+7i/P7T0KQInIe9YddthvgXuAgsXSMKqKPCLfzkTRixkRanbzRbK7745Pn0acyaAiLaJ6D3COxdQwqoafIfIxUV0fOcEt3fVw3u4dwH9uphBFiGqbinwVeMtiahhVwVrgK7H36zPO7Tb59ygAEDYJFZyjv7b2SUT+GSsFDKPS6UfkNnXuZzXO7fWL9/gV9UvXEalS19+vwHeAhy2+hlHR/FhE7kZV895vt+hnyAIA4awAF8egugHVGwknBxmGUXmsROQGVd3oVanbxbz/jshgf3Lf2S1kvafg3AdQvQtotXgbRsXwjohc3uv9TxujiGjp4K77dEP5DYUogkzmpwq3AF0Wc8OoCDqBr6rIsoYoGnyvPhQBqHusLfzgfN6L93cCtwL9FnvDmFD6gVs9fAvvYwXc0sFf9j0kB1CzdB2ogki/eP914LvYnYKGMVF4RL4rUfQNBzll580+e0OG81u3nj+HmlwOVGcJfA3VTzBEMTEMY8TJ/z0n8lkfx+tzW7cy6ZneIf+QYSVtY2cn+bo6BNaryOcQ+R7mBAxjvJP/c151fV93N40tLcP6QTLcV9C7qJW6QoF8FKEwS1RvAi4Gau35GMaY0Y/Id53ItV51fXscMzOT2et8/6gLQEkE4ro6avr6cIXCFHXur4HPAM32nAxj1OkCbpUo+roWCh193d00NTcPO/lHLAAAetoUupubyfb14SHr4Grg88A+9rwMY9RYp3CLOHcnqv25zk4aW1qQH745oh8qo/Xq+hfOQgBxLlLVhah+FTjanpthjJgXROQaH0U/lTj2ANnHRuewrlEbua9dth5xDoW4Jp//KfAx4N+xtQKGMVwKwH+IyCe7+vsfiVS9iIxa8sMoOoASfYvCCmEX1gvMQPUyVP8GmG3P0zAGzVvAP0vYhLchKt3lN4J6f1wEoERh4Sy8CLGIOO/PEPgCsAB2eziJYRih11+OyFfztbVPZnp71QP1y8fmys7MWP0VeeeI4hicU6f6uDr3O+AiVK8C5tlzNoydeAW4R0XuLai2ZXM5cs7RIGPWT4+dAyjRu6gVJZwxmI8iVxPHRwksBj4KTLdnbhi0FU/gvn3df7f9tvXEFmqcA9URTfFVhACU6Du7JQiBKkRRLd4vAC5BdREwzdqAkUI2Fy/guUOce1rjOB9lswBEP1kzLi9AxvOv7V3UyrYjCsIgYQOq70P1o8BpwFxrE0YKWAv8BJEliDyDao+U2fyaMe71J0wASuQWtYJzEMfhRYhkvfeHi8iZqH4IOAIrD4xksQF4GXgUkWXi3EvqfZgijyLwfpfn9idSAMrJF8cI8B7J59GamimInIDqKcApwLHAFKDB2pBRRfQQruVaCTyFyLPA75z3m324eQsY396+IgUABgYKM0VHgHNs6etjSl1ds0ALqicAxwOHEMqEucBkIKqE12+kGiXshO0AVgOvAa8CvxXnXvCwNtfb25mtq8MVbX6msRHieMTLeBMjALsSBOc9iCCqxVcqiPcN6twMYCZhYdF8VOcA+xLOKJwENBLcQh1hZ2IddlaBMTw8YSVrT9n/twLdwDqE1SCrgVXAO8Am51xbHMe9UmyzWmx8OaDxscq7a7ciBWBHcmfNRFVxzqEioIqqUpPP019XJ5GqkzCgOFVhBuhUkGZgEqqTgSaCY5iESBPQiGojYR1EczEOjQxsZQ4fi2QJCh9QLX1d1nKjMpsK0I2I7tDGPaqdxYTWsoRWwg67fkS2An2odhWTvKP4b1uBLYh0KmyJRDaIc+1xXOgjE3nt6VMyGUQERHAi22ypTLC9Hwz/HzpMesFycOK3AAAAAElFTkSuQmCC\", 'base64')\n\nif (false || false) {\n    const fileSizeInMB = buffer.byteLength / 1024 / 1024\n    if (fileSizeInMB > 8) {\n        throw new Error('File size for Open Graph image \"[project]/app/favicon.ico\" exceeds 8MB. ' +\n        `(Current: ${fileSizeInMB.toFixed(2)}MB)\\n` +\n        'Read more: https://nextjs.org/docs/app/api-reference/file-conventions/metadata/opengraph-image#image-files-jpg-png-gif'\n        )\n    }\n}\n\nexport function GET() {\n    return new NextResponse(buffer, {\n        headers: {\n            'Content-Type': contentType,\n            'Cache-Control': cacheControl,\n        },\n    })\n}\n\nexport const dynamic = 'force-static'\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,cAAc;AACpB,MAAM,eAAe;AACrB,MAAM,SAAS,OAAO,IAAI,CAAC,g6YAAg6Y;AAE37Y,uCAAoB;;AAQpB;AAEO,SAAS;IACZ,OAAO,IAAI,8HAAA,CAAA,eAAY,CAAC,QAAQ;QAC5B,SAAS;YACL,gBAAgB;YAChB,iBAAiB;QACrB;IACJ;AACJ;AAEO,MAAM,UAAU", "debugId": null}}]}