{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "file": "utils.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/utils.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps as SharedDOMProps} from '@react-types/shared';\nimport {mergeProps, mergeRefs, useLayoutEffect, useObjectRef} from '@react-aria/utils';\nimport React, {Context, createContext, CSSProperties, ForwardedRef, JSX, ReactNode, RefCallback, RefObject, UIEvent, useCallback, useContext, useMemo, useRef, useState} from 'react';\nimport ReactDOM from 'react-dom';\nimport {useIsSSR} from 'react-aria';\n\n// Override forwardRef types so generics work.\ndeclare function forwardRef<T, P = {}>(\n  render: (props: P, ref: React.Ref<T>) => React.ReactElement | null\n): (props: P & React.RefAttributes<T>) => React.ReactElement | null;\n\nexport type forwardRefType = typeof forwardRef;\n\nexport const DEFAULT_SLOT = Symbol('default');\n\ninterface SlottedValue<T> {\n  slots?: Record<string | symbol, T>\n}\n\nexport type SlottedContextValue<T> = SlottedValue<T> | T | null | undefined;\nexport type ContextValue<T, E extends Element> = SlottedContextValue<WithRef<T, E>>;\n\ntype ProviderValue<T> = [Context<T>, T];\ntype ProviderValues<A, B, C, D, E, F, G, H, I, J, K> =\n  | [ProviderValue<A>]\n  | [ProviderValue<A>, ProviderValue<B>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>, ProviderValue<E>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>, ProviderValue<E>, ProviderValue<F>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>, ProviderValue<E>, ProviderValue<F>, ProviderValue<G>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>, ProviderValue<E>, ProviderValue<F>, ProviderValue<G>, ProviderValue<H>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>, ProviderValue<E>, ProviderValue<F>, ProviderValue<G>, ProviderValue<H>, ProviderValue<I>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>, ProviderValue<E>, ProviderValue<F>, ProviderValue<G>, ProviderValue<H>, ProviderValue<I>, ProviderValue<J>]\n  | [ProviderValue<A>, ProviderValue<B>, ProviderValue<C>, ProviderValue<D>, ProviderValue<E>, ProviderValue<F>, ProviderValue<G>, ProviderValue<H>, ProviderValue<I>, ProviderValue<J>, ProviderValue<K>];\n\ninterface ProviderProps<A, B, C, D, E, F, G, H, I, J, K> {\n  values: ProviderValues<A, B, C, D, E, F, G, H, I, J, K>,\n  children: ReactNode\n}\n\nexport function Provider<A, B, C, D, E, F, G, H, I, J, K>({values, children}: ProviderProps<A, B, C, D, E, F, G, H, I, J, K>): JSX.Element {\n  for (let [Context, value] of values) {\n    // @ts-ignore\n    children = <Context.Provider value={value}>{children}</Context.Provider>;\n  }\n\n  return children as JSX.Element;\n}\n\nexport interface StyleProps {\n  /** The CSS [className](https://developer.mozilla.org/en-US/docs/Web/API/Element/className) for the element. */\n  className?: string,\n  /** The inline [style](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/style) for the element. */\n  style?: CSSProperties\n}\n\nexport interface DOMProps extends StyleProps, SharedDOMProps {\n  /** The children of the component. */\n  children?: ReactNode\n}\n\nexport interface ScrollableProps<T extends Element> {\n  /** Handler that is called when a user scrolls. See [MDN](https://developer.mozilla.org/en-US/docs/Web/API/Element/scroll_event). */\n  onScroll?: (e: UIEvent<T>) => void\n}\n\nexport interface StyleRenderProps<T> {\n  /** The CSS [className](https://developer.mozilla.org/en-US/docs/Web/API/Element/className) for the element. A function may be provided to compute the class based on component state. */\n  className?: string | ((values: T & {defaultClassName: string | undefined}) => string),\n  /** The inline [style](https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/style) for the element. A function may be provided to compute the style based on component state. */\n  style?: CSSProperties | ((values: T & {defaultStyle: CSSProperties}) => CSSProperties)\n}\n\nexport interface RenderProps<T> extends StyleRenderProps<T> {\n  /** The children of the component. A function may be provided to alter the children based on component state. */\n  children?: ReactNode | ((values: T & {defaultChildren: ReactNode | undefined}) => ReactNode)\n}\n\ninterface RenderPropsHookOptions<T> extends RenderProps<T>, SharedDOMProps, AriaLabelingProps {\n  values: T,\n  defaultChildren?: ReactNode,\n  defaultClassName?: string,\n  defaultStyle?: CSSProperties\n}\n\nexport function useRenderProps<T>(props: RenderPropsHookOptions<T>) {\n  let {\n    className,\n    style,\n    children,\n    defaultClassName = undefined,\n    defaultChildren = undefined,\n    defaultStyle,\n    values\n  } = props;\n\n  return useMemo(() => {\n    let computedClassName: string | undefined;\n    let computedStyle: React.CSSProperties | undefined;\n    let computedChildren: React.ReactNode | undefined;\n\n    if (typeof className === 'function') {\n      computedClassName = className({...values, defaultClassName});\n    } else {\n      computedClassName = className;\n    }\n\n    if (typeof style === 'function') {\n      computedStyle = style({...values, defaultStyle: defaultStyle || {}});\n    } else {\n      computedStyle = style;\n    }\n\n    if (typeof children === 'function') {\n      computedChildren = children({...values, defaultChildren});\n    } else if (children == null) {\n      computedChildren = defaultChildren;\n    } else {\n      computedChildren = children;\n    }\n\n    return {\n      className: computedClassName ?? defaultClassName,\n      style: (computedStyle || defaultStyle) ? {...defaultStyle, ...computedStyle} : undefined,\n      children: computedChildren ?? defaultChildren,\n      'data-rac': ''\n    };\n  }, [className, style, children, defaultClassName, defaultChildren, defaultStyle, values]);\n}\n\n/**\n * A helper function that accepts a user-provided render prop value (either a static value or a function),\n * and combines it with another value to create a final result.\n */\nexport function composeRenderProps<T, U, V extends T>(\n  // https://stackoverflow.com/questions/60898079/typescript-type-t-or-function-t-usage\n  value: T extends any ? (T | ((renderProps: U) => V)) : never,\n  wrap: (prevValue: T, renderProps: U) => V\n): (renderProps: U) => V {\n  return (renderProps) => wrap(typeof value === 'function' ? value(renderProps) : value, renderProps);\n}\n\nexport type WithRef<T, E> = T & {ref?: ForwardedRef<E>};\nexport interface SlotProps {\n  /**\n   * A slot name for the component. Slots allow the component to receive props from a parent component.\n   * An explicit `null` value indicates that the local props completely override all props received from a parent.\n   */\n  slot?: string | null\n}\n\nexport function useSlottedContext<T>(context: Context<SlottedContextValue<T>>, slot?: string | null): T | null | undefined {\n  let ctx = useContext(context);\n  if (slot === null) {\n    // An explicit `null` slot means don't use context.\n    return null;\n  }\n  if (ctx && typeof ctx === 'object' && 'slots' in ctx && ctx.slots) {\n    let availableSlots = new Intl.ListFormat().format(Object.keys(ctx.slots).map(p => `\"${p}\"`));\n\n    if (!slot && !ctx.slots[DEFAULT_SLOT]) {\n      throw new Error(`A slot prop is required. Valid slot names are ${availableSlots}.`);\n    }\n    let slotKey = slot || DEFAULT_SLOT;\n    if (!ctx.slots[slotKey]) {\n      // @ts-ignore\n      throw new Error(`Invalid slot \"${slot}\". Valid slot names are ${availableSlots}.`);\n    }\n    return ctx.slots[slotKey];\n  }\n  // @ts-ignore\n  return ctx;\n}\n\nexport function useContextProps<T, U extends SlotProps, E extends Element>(props: T & SlotProps, ref: ForwardedRef<E>, context: Context<ContextValue<U, E>>): [T, RefObject<E>] {\n  let ctx = useSlottedContext(context, props.slot) || {};\n  // @ts-ignore - TS says \"Type 'unique symbol' cannot be used as an index type.\" but not sure why.\n  let {ref: contextRef, ...contextProps} = ctx as any;\n  let mergedRef = useObjectRef(useMemo(() => mergeRefs(ref, contextRef), [ref, contextRef]));\n  let mergedProps = mergeProps(contextProps, props) as unknown as T;\n\n  // mergeProps does not merge `style`. Adding this there might be a breaking change.\n  if (\n    'style' in contextProps &&\n    contextProps.style &&\n    'style' in props &&\n    props.style\n  ) {\n    if (typeof contextProps.style === 'function' || typeof props.style === 'function') {\n      // @ts-ignore\n      mergedProps.style = (renderProps) => {\n        let contextStyle = typeof contextProps.style === 'function' ? contextProps.style(renderProps) : contextProps.style;\n        let defaultStyle = {...renderProps.defaultStyle, ...contextStyle};\n        let style = typeof props.style === 'function' \n          ? props.style({...renderProps, defaultStyle})\n          : props.style;\n        return {...defaultStyle, ...style};\n      };\n    } else {\n      // @ts-ignore\n      mergedProps.style = {...contextProps.style, ...props.style};\n    }\n  }\n\n  return [mergedProps, mergedRef];\n}\n\nexport function useSlot(): [RefCallback<Element>, boolean] {\n  // Assume we do have the slot in the initial render.\n  let [hasSlot, setHasSlot] = useState(true);\n  let hasRun = useRef(false);\n\n  // A callback ref which will run when the slotted element mounts.\n  // This should happen before the useLayoutEffect below.\n  let ref = useCallback(el => {\n    hasRun.current = true;\n    setHasSlot(!!el);\n  }, []);\n\n  // If the callback hasn't been called, then reset to false.\n  useLayoutEffect(() => {\n    if (!hasRun.current) {\n      setHasSlot(false);\n    }\n  }, []);\n\n  return [ref, hasSlot];\n}\n\nexport function useEnterAnimation(ref: RefObject<HTMLElement>, isReady: boolean = true) {\n  let [isEntering, setEntering] = useState(true);\n  useAnimation(ref, isEntering && isReady, useCallback(() => setEntering(false), []));\n  return isEntering && isReady;\n}\n\nexport function useExitAnimation(ref: RefObject<HTMLElement>, isOpen: boolean) {\n  // State to trigger a re-render after animation is complete, which causes the element to be removed from the DOM.\n  // Ref to track the state we're in, so we don't immediately reset isExiting to true after the animation.\n  let [isExiting, setExiting] = useState(false);\n  let [exitState, setExitState] = useState('idle');\n\n  // If isOpen becomes false, set isExiting to true.\n  if (!isOpen && ref.current && exitState === 'idle') {\n    isExiting = true;\n    setExiting(true);\n    setExitState('exiting');\n  }\n\n  // If we exited, and the element has been removed, reset exit state to idle.\n  if (!ref.current && exitState === 'exited') {\n    setExitState('idle');\n  }\n\n  useAnimation(\n    ref,\n    isExiting,\n    useCallback(() => {\n      setExitState('exited');\n      setExiting(false);\n    }, [])\n  );\n\n  return isExiting;\n}\n\nfunction useAnimation(ref: RefObject<HTMLElement>, isActive: boolean, onEnd: () => void) {\n  let prevAnimation = useRef<string | null>(null);\n  if (isActive && ref.current) {\n    // This is ok because we only read it in the layout effect below, immediately after the commit phase.\n    // We could move this to another effect that runs every render, but this would be unnecessarily slow.\n    // We only need the computed style right before the animation becomes active.\n    // eslint-disable-next-line rulesdir/pure-render\n    prevAnimation.current = window.getComputedStyle(ref.current).animation;\n  }\n\n  useLayoutEffect(() => {\n    if (isActive && ref.current) {\n      // Make sure there's actually an animation, and it wasn't there before we triggered the update.\n      let computedStyle = window.getComputedStyle(ref.current);\n      if (computedStyle.animationName && computedStyle.animationName !== 'none' && computedStyle.animation !== prevAnimation.current) {\n        let onAnimationEnd = (e: AnimationEvent) => {\n          if (e.target === ref.current) {\n            element.removeEventListener('animationend', onAnimationEnd);\n            ReactDOM.flushSync(() => {onEnd();});\n          }\n        };\n\n        let element = ref.current;\n        element.addEventListener('animationend', onAnimationEnd);\n        return () => {\n          element.removeEventListener('animationend', onAnimationEnd);\n        };\n      } else {\n        onEnd();\n      }\n    }\n  }, [ref, isActive, onEnd]);\n}\n\n// React doesn't understand the <template> element, which doesn't have children like a normal element.\n// It will throw an error during hydration when it expects the firstChild to contain content rendered\n// on the server, when in reality, the browser will have placed this inside the `content` document fragment.\n// This monkey patches the firstChild property for our special hidden template elements to work around this error.\n// See https://github.com/facebook/react/issues/19932\nif (typeof HTMLTemplateElement !== 'undefined') {\n  const getFirstChild = Object.getOwnPropertyDescriptor(Node.prototype, 'firstChild')!.get!;\n  Object.defineProperty(HTMLTemplateElement.prototype, 'firstChild', {\n    configurable: true,\n    enumerable: true,\n    get: function () {\n      if (this.dataset.reactAriaHidden) {\n        return this.content.firstChild;\n      } else {\n        return getFirstChild.call(this);\n      }\n    }\n  });\n}\n\nexport const HiddenContext = createContext<boolean>(false);\n\n// Portal to nowhere\nconst hiddenFragment = typeof DocumentFragment !== 'undefined' ? new DocumentFragment() : null;\n\nexport function Hidden(props: {children: ReactNode}) {\n  let isHidden = useContext(HiddenContext);\n  let isSSR = useIsSSR();\n  if (isHidden) {\n    // Don't hide again if we are already hidden.\n    return <>{props.children}</>;\n  }\n\n  let children = (\n    <HiddenContext.Provider value>\n      {props.children}\n    </HiddenContext.Provider>\n  );\n\n  // In SSR, portals are not supported by React. Instead, render into a <template>\n  // element, which the browser will never display to the user. In addition, the\n  // content is not part of the DOM tree, so it won't affect ids or other accessibility attributes.\n  return isSSR\n    ? <template data-react-aria-hidden>{children}</template>\n    : ReactDOM.createPortal(children, hiddenFragment!);\n}\n\n// Creates a component that forwards its ref and returns null if it is in a <Hidden> subtree.\n// Note: this function is handled specially in the documentation generator. If you change it, you'll need to update DocsTransformer as well.\nexport function createHideableComponent<T, P = {}>(fn: (props: P, ref: React.Ref<T>) => React.ReactElement | null): (props: P & React.RefAttributes<T>) => React.ReactElement | null {\n  let Wrapper = (props: P, ref: React.Ref<T>) => {\n    let isHidden = useContext(HiddenContext);\n    if (isHidden) {\n      return null;\n    }\n\n    return fn(props, ref);\n  };\n  // @ts-ignore - for react dev tools\n  Wrapper.displayName = fn.displayName || fn.name;\n  return (React.forwardRef as forwardRefType)(Wrapper);\n}\n\n/**\n * Filters out `data-*` attributes to keep them from being passed down and duplicated.\n * @param props\n */\nexport function removeDataAttributes<T>(props: T): T {\n  const prefix = /^(data-.*)$/;\n  let filteredProps = {} as T;\n\n  for (const prop in props) {\n    if (!prefix.test(prop)) {\n      filteredProps[prop] = props[prop];\n    }\n  }\n\n  return filteredProps;\n}\n\n// Override base type to change the default.\nexport interface RACValidation {\n  /**\n   * Whether to use native HTML form validation to prevent form submission\n   * when the value is missing or invalid, or mark the field as required\n   * or invalid via ARIA.\n   * @default 'native'\n   */\n  validationBehavior?: 'native' | 'aria'\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAeM,MAAM,4CAAe,OAAO;AA4B5B,SAAS,0CAA0C,EAAA,QAAC,MAAM,EAAA,UAAE,QAAQ,EAAiD;IAC1H,KAAK,IAAI,CAAC,SAAS,MAAM,IAAI,OAC3B,AACA,WAAA,EADa,SACb,GAAW,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,QAAQ,QAAQ,EAAA;QAAC,OAAO;OAAQ;IAG9C,OAAO;AACT;AAsCO,SAAS,wCAAkB,KAAgC;IAChE,IAAI,EAAA,WACF,SAAS,EAAA,OACT,KAAK,EAAA,UACL,QAAQ,EAAA,kBACR,gBAAgB,EAAA,iBAChB,eAAe,EAAA,cACf,YAAY,EAAA,QACZ,MAAM,EACP,GAAG;IAEJ,OAAO,CAAA,yMAAA,UAAM,EAAE;QACb,IAAI;QACJ,IAAI;QACJ,IAAI;QAEJ,IAAI,OAAO,cAAc,YACvB,oBAAoB,UAAU;YAAC,GAAG,MAAM;8BAAE;QAAgB;aAE1D,oBAAoB;QAGtB,IAAI,OAAO,UAAU,YACnB,gBAAgB,MAAM;YAAC,GAAG,MAAM;YAAE,cAAc,gBAAgB,CAAC;QAAC;aAElE,gBAAgB;QAGlB,IAAI,OAAO,aAAa,YACtB,mBAAmB,SAAS;YAAC,GAAG,MAAM;6BAAE;QAAe;aAClD,IAAI,YAAY,MACrB,mBAAmB;aAEnB,mBAAmB;QAGrB,OAAO;YACL,WAAW,sBAAA,QAAA,sBAAA,KAAA,IAAA,oBAAqB;YAChC,OAAQ,iBAAiB,eAAgB;gBAAC,GAAG,YAAY;gBAAE,GAAG,aAAa;YAAA,IAAI;YAC/E,UAAU,qBAAA,QAAA,qBAAA,KAAA,IAAA,mBAAoB;YAC9B,YAAY;QACd;IACF,GAAG;QAAC;QAAW;QAAO;QAAU;QAAkB;QAAiB;QAAc;KAAO;AAC1F;AAMO,SAAS,0CACd,AACA,KAA4D,EAC5D,IAAyC,0EAF4C;IAIrF,OAAO,CAAC,cAAgB,KAAK,OAAO,UAAU,aAAa,MAAM,eAAe,OAAO;AACzF;AAWO,SAAS,0CAAqB,OAAwC,EAAE,IAAoB;IACjG,IAAI,MAAM,CAAA,yMAAA,aAAS,EAAE;IACrB,IAAI,SAAS,MACX,AACA,OAAO,4CAD4C;IAGrD,IAAI,OAAO,OAAO,QAAQ,YAAY,WAAW,OAAO,IAAI,KAAK,EAAE;QACjE,IAAI,iBAAiB,IAAI,KAAK,UAAU,GAAG,MAAM,CAAC,OAAO,IAAI,CAAC,IAAI,KAAK,EAAE,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAAC;QAE1F,IAAI,CAAC,QAAQ,CAAC,IAAI,KAAK,CAAC,0CAAa,EACnC,MAAM,IAAI,MAAM,CAAC,8CAA8C,EAAE,eAAe,CAAC,CAAC;QAEpF,IAAI,UAAU,QAAQ;QACtB,IAAI,CAAC,IAAI,KAAK,CAAC,QAAQ,EACrB,AACA,MAAM,IAAI,GADG,GACG,CAAC,cAAc,EAAE,KAAK,wBAAwB,EAAE,eAAe,CAAC,CAAC;QAEnF,OAAO,IAAI,KAAK,CAAC,QAAQ;IAC3B;IACA,aAAa;IACb,OAAO;AACT;AAEO,SAAS,0CAA2D,KAAoB,EAAE,GAAoB,EAAE,OAAoC;IACzJ,IAAI,MAAM,0CAAkB,SAAS,MAAM,IAAI,KAAK,CAAC;IACrD,iGAAiG;IACjG,IAAI,EAAC,KAAK,UAAU,EAAE,GAAG,cAAa,GAAG;IACzC,IAAI,YAAY,CAAA,qKAAA,eAAW,EAAE,CAAA,yMAAA,UAAM,EAAE,IAAM,CAAA,kKAAA,YAAQ,EAAE,KAAK,aAAa;QAAC;QAAK;KAAW;IACxF,IAAI,cAAc,CAAA,mKAAA,aAAS,EAAE,cAAc;IAE3C,mFAAmF;IACnF,IACE,WAAW,gBACX,aAAa,KAAK,IAClB,WAAW,SACX,MAAM,KAAK,EAAA;QAEX,IAAI,OAAO,aAAa,KAAK,KAAK,cAAc,OAAO,MAAM,KAAK,KAAK,YACrE,AACA,YAAY,CADC,IACI,GAAG,CAAC;YACnB,IAAI,eAAe,OAAO,aAAa,KAAK,KAAK,aAAa,aAAa,KAAK,CAAC,eAAe,aAAa,KAAK;YAClH,IAAI,eAAe;gBAAC,GAAG,YAAY,YAAY;gBAAE,GAAG,YAAY;YAAA;YAChE,IAAI,QAAQ,OAAO,MAAM,KAAK,KAAK,aAC/B,MAAM,KAAK,CAAC;gBAAC,GAAG,WAAW;8BAAE;YAAY,KACzC,MAAM,KAAK;YACf,OAAO;gBAAC,GAAG,YAAY;gBAAE,GAAG,KAAK;YAAA;QACnC;aAGA,YAAY,KAAK,GAAG;YAAC,GAAG,aAAa,KAAK;YAAE,GAAG,MAAM,KAAK;QAAA;;IAI9D,OAAO;QAAC;QAAa;KAAU;AACjC;AAEO,SAAS;IACd,oDAAoD;IACpD,IAAI,CAAC,SAAS,WAAW,GAAG,CAAA,yMAAA,WAAO,EAAE;IACrC,IAAI,SAAS,CAAA,yMAAA,SAAK,EAAE;IAEpB,iEAAiE;IACjE,uDAAuD;IACvD,IAAI,MAAM,CAAA,yMAAA,cAAU,EAAE,CAAA;QACpB,OAAO,OAAO,GAAG;QACjB,WAAW,CAAC,CAAC;IACf,GAAG,EAAE;IAEL,2DAA2D;IAC3D,CAAA,wKAAA,kBAAc,EAAE;QACd,IAAI,CAAC,OAAO,OAAO,EACjB,WAAW;IAEf,GAAG,EAAE;IAEL,OAAO;QAAC;QAAK;KAAQ;AACvB;AAEO,SAAS,0CAAkB,GAA2B,EAAE,UAAmB,IAAI;IACpF,IAAI,CAAC,YAAY,YAAY,GAAG,CAAA,yMAAA,WAAO,EAAE;IACzC,mCAAa,KAAK,cAAc,SAAS,CAAA,yMAAA,cAAU,EAAE,IAAM,YAAY,QAAQ,EAAE;IACjF,OAAO,cAAc;AACvB;AAEO,SAAS,0CAAiB,GAA2B,EAAE,MAAe;IAC3E,iHAAiH;IACjH,wGAAwG;IACxG,IAAI,CAAC,WAAW,WAAW,GAAG,CAAA,yMAAA,WAAO,EAAE;IACvC,IAAI,CAAC,WAAW,aAAa,GAAG,CAAA,yMAAA,WAAO,EAAE;IAEzC,kDAAkD;IAClD,IAAI,CAAC,UAAU,IAAI,OAAO,IAAI,cAAc,QAAQ;QAClD,YAAY;QACZ,WAAW;QACX,aAAa;IACf;IAEA,4EAA4E;IAC5E,IAAI,CAAC,IAAI,OAAO,IAAI,cAAc,UAChC,aAAa;IAGf,mCACE,KACA,WACA,CAAA,yMAAA,cAAU,EAAE;QACV,aAAa;QACb,WAAW;IACb,GAAG,EAAE;IAGP,OAAO;AACT;AAEA,SAAS,mCAAa,GAA2B,EAAE,QAAiB,EAAE,KAAiB;IACrF,IAAI,gBAAgB,CAAA,yMAAA,SAAK,EAAiB;IAC1C,IAAI,YAAY,IAAI,OAAO,EAEzB,AADA,qGAAqG,AACA;IACrG,6EAA6E;IAC7E,gDAAgD;IAChD,cAAc,OAAO,GAAG,OAAO,gBAAgB,CAAC,IAAI,OAAO,EAAE,SAAS;IAGxE,CAAA,wKAAA,kBAAc,EAAE;QACd,IAAI,YAAY,IAAI,OAAO,EAAE;YAC3B,+FAA+F;YAC/F,IAAI,gBAAgB,OAAO,gBAAgB,CAAC,IAAI,OAAO;YACvD,IAAI,cAAc,aAAa,IAAI,cAAc,aAAa,KAAK,UAAU,cAAc,SAAS,KAAK,cAAc,OAAO,EAAE;gBAC9H,IAAI,iBAAiB,CAAC;oBACpB,IAAI,EAAE,MAAM,KAAK,IAAI,OAAO,EAAE;wBAC5B,QAAQ,mBAAmB,CAAC,gBAAgB;wBAC5C,CAAA,gNAAA,UAAO,EAAE,SAAS,CAAC;4BAAO;wBAAQ;oBACpC;gBACF;gBAEA,IAAI,UAAU,IAAI,OAAO;gBACzB,QAAQ,gBAAgB,CAAC,gBAAgB;gBACzC,OAAO;oBACL,QAAQ,mBAAmB,CAAC,gBAAgB;gBAC9C;YACF,OACE;QAEJ;IACF,GAAG;QAAC;QAAK;QAAU;KAAM;AAC3B;AAEA,sGAAsG;AACtG,qGAAqG;AACrG,4GAA4G;AAC5G,kHAAkH;AAClH,qDAAqD;AACrD,IAAI,OAAO,wBAAwB,aAAa;IAC9C,MAAM,gBAAgB,OAAO,wBAAwB,CAAC,KAAK,SAAS,EAAE,cAAe,GAAG;IACxF,OAAO,cAAc,CAAC,oBAAoB,SAAS,EAAE,cAAc;QACjE,cAAc;QACd,YAAY;QACZ,KAAK;YACH,IAAI,IAAI,CAAC,OAAO,CAAC,eAAe,EAC9B,OAAO,IAAI,CAAC,OAAO,CAAC,UAAU;iBAE9B,OAAO,cAAc,IAAI,CAAC,IAAI;QAElC;IACF;AACF;AAEO,MAAM,4CAAA,WAAA,GAAgB,CAAA,yMAAA,gBAAY,EAAW;AAEpD,oBAAoB;AACpB,MAAM,uCAAiB,OAAO,qBAAqB,cAAc,IAAI,qBAAqB;AAEnF,SAAS,0CAAO,KAA4B;IACjD,IAAI,WAAW,CAAA,yMAAA,aAAS,EAAE;IAC1B,IAAI,QAAQ,CAAA,kKAAA,WAAO;IACnB,IAAI,UACF,AACA,OAAA,WAAA,GAAO,CAAA,GAAA,oBADsC,iLACtC,CAAA,UAAA,EAAA,aAAA,CAAA,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,QAAA,EAAA,MAAG,MAAM,QAAQ;IAG1B,IAAI,WAAA,WAAA,GACF,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,0CAAc,QAAQ,EAAA;QAAC,OAAA;OACrB,MAAM,QAAQ;IAInB,gFAAgF;IAChF,8EAA8E;IAC9E,iGAAiG;IACjG,OAAO,QAAA,WAAA,GACH,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,YAAA;QAAS,0BAAA;OAAwB,YAAA,WAAA,GAClC,CAAA,gNAAA,UAAO,EAAE,YAAY,CAAC,UAAU;AACtC;AAIO,SAAS,0CAAmC,EAA8D;IAC/G,IAAI,UAAU,CAAC,OAAU;QACvB,IAAI,WAAW,CAAA,yMAAA,aAAS,EAAE;QAC1B,IAAI,UACF,OAAO;QAGT,OAAO,GAAG,OAAO;IACnB;IACA,mCAAmC;IACnC,QAAQ,WAAW,GAAG,GAAG,WAAW,IAAI,GAAG,IAAI;IAC/C,OAAQ,CAAA,yMAAA,UAAI,EAAE,UAAU,CAAoB;AAC9C;AAMO,SAAS,0CAAwB,KAAQ;IAC9C,MAAM,SAAS;IACf,IAAI,gBAAgB,CAAC;IAErB,IAAK,MAAM,QAAQ,MACjB,IAAI,CAAC,OAAO,IAAI,CAAC,OACf,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC,KAAK;IAIrC,OAAO;AACT", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 275, "column": 0}, "map": {"version": 3, "file": "Text.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/Text.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ContextValue, useContextProps} from './utils';\nimport React, {createContext, ForwardedRef, forwardRef, HTMLAttributes} from 'react';\n\nexport interface TextProps extends HTMLAttributes<HTMLElement> {\n  elementType?: string\n}\n\nexport const TextContext = createContext<ContextValue<TextProps, HTMLElement>>({});\n\nfunction Text(props: TextProps, ref: ForwardedRef<HTMLElement>) {\n  [props, ref] = useContextProps(props, ref, TextContext);\n  let {elementType: ElementType = 'span', ...domProps} = props;\n  // @ts-ignore\n  return <ElementType className=\"react-aria-Text\" {...domProps} ref={ref} />;\n}\n\nconst _Text = forwardRef(Text);\nexport {_Text as Text};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC,GASM,MAAM,4CAAA,WAAA,GAAc,CAAA,yMAAA,gBAAY,EAAwC,CAAC;AAEhF,SAAS,2BAAK,KAAgB,EAAE,GAA8B;IAC5D,CAAC,OAAO,IAAI,GAAG,CAAA,kMAAA,kBAAc,EAAE,OAAO,KAAK;IAC3C,IAAI,EAAC,aAAa,cAAc,MAAM,EAAE,GAAG,UAAS,GAAG;IACvD,aAAa;IACb,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,aAAA;QAAY,WAAU;QAAmB,GAAG,QAAQ;QAAE,KAAK;;AACrE;AAEA,MAAM,4CAAA,WAAA,GAAQ,CAAA,yMAAA,aAAS,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 313, "column": 0}, "map": {"version": 3, "file": "FieldError.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/FieldError.tsx"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport React, {createContext, ForwardedRef, forwardRef, useContext} from 'react';\nimport {RenderProps, useRenderProps} from './utils';\nimport {Text} from './Text';\nimport {ValidationResult} from '@react-types/shared';\n\nexport const FieldErrorContext = createContext<ValidationResult | null>(null);\n\nexport interface FieldErrorRenderProps extends ValidationResult {}\nexport interface FieldErrorProps extends RenderProps<FieldErrorRenderProps> {}\n\nfunction FieldError(props: FieldErrorProps, ref: ForwardedRef<HTMLElement>) {\n  let validation = useContext(FieldErrorContext);\n  if (!validation?.isInvalid) {\n    return null;\n  }\n\n  return <FieldErrorInner {...props} ref={ref} />;\n}\n\n/**\n * A FieldError displays validation errors for a form field.\n */\nconst _FieldError = forwardRef(FieldError);\nexport {_FieldError as FieldError};\n\nconst FieldErrorInner = forwardRef((props: FieldErrorProps, ref: ForwardedRef<HTMLElement>) => {\n  let validation = useContext(FieldErrorContext)!;\n  let renderProps = useRenderProps({\n    ...props,\n    defaultClassName: 'react-aria-FieldError',\n    defaultChildren: validation.validationErrors.length === 0 ? undefined : validation.validationErrors.join(' '),\n    values: validation\n  });\n\n  if (renderProps.children == null) {\n    return null;\n  }\n\n  return <Text slot=\"errorMessage\" {...renderProps} ref={ref} />;\n});\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAOM,MAAM,4CAAA,WAAA,GAAoB,CAAA,yMAAA,gBAAY,EAA2B;AAKxE,SAAS,iCAAW,KAAsB,EAAE,GAA8B;IACxE,IAAI,aAAa,CAAA,yMAAA,aAAS,EAAE;IAC5B,IAAI,CAAA,CAAC,eAAA,QAAA,eAAA,KAAA,IAAA,KAAA,IAAA,WAAY,SAAS,GACxB,OAAO;IAGT,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,uCAAA;QAAiB,GAAG,KAAK;QAAE,KAAK;;AAC1C;AAEA;;CAEC,GACD,MAAM,4CAAA,WAAA,GAAc,CAAA,yMAAA,aAAS,EAAE;AAG/B,MAAM,wCAAA,WAAA,GAAkB,CAAA,yMAAA,aAAS,EAAE,CAAC,OAAwB;IAC1D,IAAI,aAAa,CAAA,yMAAA,aAAS,EAAE;IAC5B,IAAI,cAAc,CAAA,kMAAA,iBAAa,EAAE;QAC/B,GAAG,KAAK;QACR,kBAAkB;QAClB,iBAAiB,WAAW,gBAAgB,CAAC,MAAM,KAAK,IAAI,YAAY,WAAW,gBAAgB,CAAC,IAAI,CAAC;QACzG,QAAQ;IACV;IAEA,IAAI,YAAY,QAAQ,IAAI,MAC1B,OAAO;IAGT,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,iMAAA,OAAG,GAAA;QAAE,MAAK;QAAgB,GAAG,WAAW;QAAE,KAAK;;AACzD", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 368, "column": 0}, "map": {"version": 3, "file": "Form.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/Form.tsx"], "sourcesContent": ["/*\n * Copyright 2023 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the 'License');\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an 'AS IS' BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ContextValue, DOMProps, useContextProps} from './utils';\nimport {FormValidationContext} from 'react-stately';\nimport React, {createContext, ForwardedRef, forwardRef} from 'react';\nimport {FormProps as SharedFormProps} from '@react-types/form';\n\nexport interface FormProps extends SharedFormProps, DOMProps {\n  /**\n   * Whether to use native HTML form validation to prevent form submission\n   * when a field value is missing or invalid, or mark fields as required\n   * or invalid via ARIA.\n   * @default 'native'\n   */\n  validationBehavior?: 'aria' | 'native'\n}\n\nexport const FormContext = createContext<ContextValue<FormProps, HTMLFormElement>>(null);\n\nfunction Form(props: FormProps, ref: ForwardedRef<HTMLFormElement>) {\n  [props, ref] = useContextProps(props, ref, FormContext);\n  let {validationErrors, validationBehavior = 'native', children, className, ...domProps} = props;\n  return (\n    <form noValidate={validationBehavior !== 'native'} {...domProps} ref={ref} className={className || 'react-aria-Form'}>\n      <FormContext.Provider value={{...props, validationBehavior}}>\n        <FormValidationContext.Provider value={validationErrors ?? {}}>\n          {children}\n        </FormValidationContext.Provider>\n      </FormContext.Provider>\n    </form>\n  );\n}\n\n/**\n * A form is a group of inputs that allows users to submit data to a server,\n * with support for providing field validation errors.\n */\nconst _Form = forwardRef(Form);\nexport {_Form as Form};\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAiBM,MAAM,4CAAA,WAAA,GAAc,CAAA,yMAAA,gBAAY,EAA4C;AAEnF,SAAS,2BAAK,KAAgB,EAAE,GAAkC;IAChE,CAAC,OAAO,IAAI,GAAG,CAAA,kMAAA,kBAAc,EAAE,OAAO,KAAK;IAC3C,IAAI,EAAA,kBAAC,gBAAgB,EAAA,oBAAE,qBAAqB,QAAA,EAAA,UAAU,QAAQ,EAAA,WAAE,SAAS,EAAE,GAAG,UAAS,GAAG;IAC1F,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,QAAA;QAAK,YAAY,uBAAuB;QAAW,GAAG,QAAQ;QAAE,KAAK;QAAK,WAAW,aAAa;qBACjG,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,0CAAY,QAAQ,EAAA;QAAC,OAAO;YAAC,GAAG,KAAK;gCAAE;QAAkB;qBACxD,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,iLAAA,wBAAoB,EAAE,QAAQ,EAAA;QAAC,OAAO,qBAAA,QAAA,qBAAA,KAAA,IAAA,mBAAoB,CAAC;OACzD;AAKX;AAEA;;;CAGC,GACD,MAAM,4CAAA,WAAA,GAAQ,CAAA,yMAAA,aAAS,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 418, "column": 0}, "map": {"version": 3, "file": "Group.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/Group.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {AriaLabelingProps, DOMProps} from '@react-types/shared';\nimport {ContextValue, forwardRefType, RenderProps, SlotProps, useContextProps, useRenderProps} from './utils';\nimport {HoverProps, mergeProps, useFocusRing, useHover} from 'react-aria';\nimport React, {createContext, ForwardedRef, forwardRef, HTMLAttributes} from 'react';\n\nexport interface GroupRenderProps {\n  /**\n   * Whether the group is currently hovered with a mouse.\n   * @selector [data-hovered]\n   */\n  isHovered: boolean,\n  /**\n   * Whether an element within the group is focused, either via a mouse or keyboard.\n   * @selector [data-focus-within]\n   */\n  isFocusWithin: boolean,\n  /**\n   * Whether an element within the group is keyboard focused.\n   * @selector [data-focus-visible]\n   */\n  isFocusVisible: boolean,\n  /**\n   * Whether the group is disabled.\n   * @selector [data-disabled]\n   */\n  isDisabled: boolean,\n  /**\n   * Whether the group is invalid.\n   * @selector [data-invalid]\n   */\n  isInvalid: boolean\n}\n\nexport interface GroupProps extends AriaLabelingProps, Omit<HTMLAttributes<HTMLElement>, 'children' | 'className' | 'style' | 'role' | 'slot'>, DOMProps, HoverProps, RenderProps<GroupRenderProps>, SlotProps {\n  /** Whether the group is disabled. */\n  isDisabled?: boolean,\n  /** Whether the group is invalid. */\n  isInvalid?: boolean,\n  /**\n   * An accessibility role for the group. By default, this is set to `'group'`.\n   * Use `'region'` when the contents of the group is important enough to be\n   * included in the page table of contents. Use `'presentation'` if the group\n   * is visual only and does not represent a semantic grouping of controls.\n   * @default 'group'\n   */\n  role?: 'group' | 'region' | 'presentation'\n}\n\nexport const GroupContext = createContext<ContextValue<GroupProps, HTMLDivElement>>({});\n\nfunction Group(props: GroupProps, ref: ForwardedRef<HTMLDivElement>) {\n  [props, ref] = useContextProps(props, ref, GroupContext);\n  let {isDisabled, isInvalid, onHoverStart, onHoverChange, onHoverEnd, ...otherProps} = props;\n\n  let {hoverProps, isHovered} = useHover({onHoverStart, onHoverChange, onHoverEnd, isDisabled});\n  let {isFocused, isFocusVisible, focusProps} = useFocusRing({\n    within: true\n  });\n\n  isDisabled ??= !!props['aria-disabled'] && props['aria-disabled'] !== 'false';\n  isInvalid ??= !!props['aria-invalid'] && props['aria-invalid'] !== 'false';\n  let renderProps = useRenderProps({\n    ...props,\n    values: {isHovered, isFocusWithin: isFocused, isFocusVisible, isDisabled, isInvalid},\n    defaultClassName: 'react-aria-Group'\n  });\n\n  return (\n    <div\n      {...mergeProps(otherProps, focusProps, hoverProps)}\n      {...renderProps}\n      ref={ref}\n      role={props.role ?? 'group'}\n      slot={props.slot ?? undefined}\n      data-focus-within={isFocused || undefined}\n      data-hovered={isHovered || undefined}\n      data-focus-visible={isFocusVisible || undefined}\n      data-disabled={isDisabled || undefined}\n      data-invalid={isInvalid || undefined}>\n      {renderProps.children}\n    </div>\n  );\n}\n\n/**\n * A group represents a set of related UI controls, and supports interactive states for styling.\n */\nconst _Group = /*#__PURE__*/ (forwardRef as forwardRefType)(Group);\nexport {_Group as Group};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAkDM,MAAM,4CAAA,WAAA,GAAe,CAAA,yMAAA,gBAAY,EAA4C,CAAC;AAErF,SAAS,4BAAM,KAAiB,EAAE,GAAiC;IACjE,CAAC,OAAO,IAAI,GAAG,CAAA,kMAAA,kBAAc,EAAE,OAAO,KAAK;IAC3C,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAA,cAAE,YAAY,EAAA,eAAE,aAAa,EAAA,YAAE,UAAU,EAAE,GAAG,YAAW,GAAG;IAEtF,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;sBAAC;uBAAc;oBAAe;oBAAY;IAAU;IAC3F,IAAI,EAAA,WAAC,SAAS,EAAA,gBAAE,cAAc,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,qKAAA,eAAW,EAAE;QACzD,QAAQ;IACV;IAEA,eAAA,QAAA,eAAA,KAAA,IAAA,aAAA,aAAe,CAAC,CAAC,KAAK,CAAC,gBAAgB,IAAI,KAAK,CAAC,gBAAgB,KAAK;IACtE,cAAA,QAAA,cAAA,KAAA,IAAA,YAAA,YAAc,CAAC,CAAC,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,eAAe,KAAK;IACnE,IAAI,cAAc,CAAA,kMAAA,iBAAa,EAAE;QAC/B,GAAG,KAAK;QACR,QAAQ;uBAAC;YAAW,eAAe;4BAAW;wBAAgB;uBAAY;QAAS;QACnF,kBAAkB;IACpB;QAOU,aACA;IANV,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,OAAA;QACE,GAAG,CAAA,mKAAA,aAAS,EAAE,YAAY,YAAY,WAAW;QACjD,GAAG,WAAW;QACf,KAAK;QACL,MAAM,CAAA,cAAA,MAAM,IAAI,MAAA,QAAV,gBAAA,KAAA,IAAA,cAAc;QACpB,MAAM,CAAA,cAAA,MAAM,IAAI,MAAA,QAAV,gBAAA,KAAA,IAAA,cAAc;QACpB,qBAAmB,aAAa;QAChC,gBAAc,aAAa;QAC3B,sBAAoB,kBAAkB;QACtC,iBAAe,cAAc;QAC7B,gBAAc,aAAa;OAC1B,YAAY,QAAQ;AAG3B;AAEA;;CAEC,GACD,MAAM,4CAAS,WAAW,GAAI,CAAA,yMAAA,aAAS,EAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 491, "column": 0}, "map": {"version": 3, "file": "Input.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/Input.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ContextValue, createHideableComponent, StyleRenderProps, useContextProps, useRenderProps} from './utils';\nimport {HoverEvents, mergeProps, useFocusRing, useHover} from 'react-aria';\nimport React, {createContext, ForwardedRef, InputHTMLAttributes} from 'react';\n\nexport interface InputRenderProps {\n  /**\n   * Whether the input is currently hovered with a mouse.\n   * @selector [data-hovered]\n   */\n  isHovered: boolean,\n  /**\n   * Whether the input is focused, either via a mouse or keyboard.\n   * @selector [data-focused]\n   */\n  isFocused: boolean,\n  /**\n   * Whether the input is keyboard focused.\n   * @selector [data-focus-visible]\n   */\n  isFocusVisible: boolean,\n  /**\n   * Whether the input is disabled.\n   * @selector [data-disabled]\n   */\n  isDisabled: boolean,\n  /**\n   * Whether the input is invalid.\n   * @selector [data-invalid]\n   */\n  isInvalid: boolean\n}\n\nexport interface InputProps extends Omit<InputHTMLAttributes<HTMLInputElement>, 'className' | 'style'>, HoverEvents, StyleRenderProps<InputRenderProps> {}\n\nexport const InputContext = createContext<ContextValue<InputProps, HTMLInputElement>>({});\n\nlet filterHoverProps = (props: InputProps) => {\n  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n  let {onHoverStart, onHoverChange, onHoverEnd, ...otherProps} = props;\n  return otherProps;\n};\n\nfunction Input(props: InputProps, ref: ForwardedRef<HTMLInputElement>) {\n  [props, ref] = useContextProps(props, ref, InputContext);\n\n  let {hoverProps, isHovered} = useHover(props);\n  let {isFocused, isFocusVisible, focusProps} = useFocusRing({\n    isTextInput: true,\n    autoFocus: props.autoFocus\n  });\n\n  let isInvalid = !!props['aria-invalid'] && props['aria-invalid'] !== 'false';\n  let renderProps = useRenderProps({\n    ...props,\n    values: {\n      isHovered,\n      isFocused,\n      isFocusVisible,\n      isDisabled: props.disabled || false,\n      isInvalid\n    },\n    defaultClassName: 'react-aria-Input'\n  });\n\n  return (\n    <input\n      {...mergeProps(filterHoverProps(props), focusProps, hoverProps)}\n      {...renderProps}\n      ref={ref}\n      data-focused={isFocused || undefined}\n      data-disabled={props.disabled || undefined}\n      data-hovered={isHovered || undefined}\n      data-focus-visible={isFocusVisible || undefined}\n      data-invalid={isInvalid || undefined} />\n  );\n}\n\n/**\n * An input allows a user to input text.\n */\nconst _Input = /*#__PURE__*/ createHideableComponent(Input);\nexport {_Input as Input};\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAoCM,MAAM,4CAAA,WAAA,GAAe,CAAA,yMAAA,gBAAY,EAA8C,CAAC;AAEvF,IAAI,yCAAmB,CAAC;IACtB,6DAA6D;IAC7D,IAAI,EAAA,cAAC,YAAY,EAAA,eAAE,aAAa,EAAA,YAAE,UAAU,EAAE,GAAG,YAAW,GAAG;IAC/D,OAAO;AACT;AAEA,SAAS,4BAAM,KAAiB,EAAE,GAAmC;IACnE,CAAC,OAAO,IAAI,GAAG,CAAA,kMAAA,kBAAc,EAAE,OAAO,KAAK;IAE3C,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;IACvC,IAAI,EAAA,WAAC,SAAS,EAAA,gBAAE,cAAc,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,qKAAA,eAAW,EAAE;QACzD,aAAa;QACb,WAAW,MAAM,SAAS;IAC5B;IAEA,IAAI,YAAY,CAAC,CAAC,KAAK,CAAC,eAAe,IAAI,KAAK,CAAC,eAAe,KAAK;IACrE,IAAI,cAAc,CAAA,kMAAA,iBAAa,EAAE;QAC/B,GAAG,KAAK;QACR,QAAQ;uBACN;uBACA;4BACA;YACA,YAAY,MAAM,QAAQ,IAAI;uBAC9B;QACF;QACA,kBAAkB;IACpB;IAEA,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,SAAA;QACE,GAAG,CAAA,mKAAA,aAAS,EAAE,uCAAiB,QAAQ,YAAY,WAAW;QAC9D,GAAG,WAAW;QACf,KAAK;QACL,gBAAc,aAAa;QAC3B,iBAAe,MAAM,QAAQ,IAAI;QACjC,gBAAc,aAAa;QAC3B,sBAAoB,kBAAkB;QACtC,gBAAc,aAAa;;AAEjC;AAEA;;CAEC,GACD,MAAM,4CAAS,WAAW,GAAG,CAAA,kMAAA,0BAAsB,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "file": "Label.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/Label.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\n\nimport {ContextValue, createHideableComponent, useContextProps} from './utils';\nimport React, {createContext, ForwardedRef, LabelHTMLAttributes} from 'react';\n\nexport interface LabelProps extends LabelHTMLAttributes<HTMLLabelElement> {\n  elementType?: string\n}\n\nexport const LabelContext = createContext<ContextValue<LabelProps, HTMLLabelElement>>({});\n\nfunction Label(props: LabelProps, ref: ForwardedRef<HTMLLabelElement>) {\n  [props, ref] = useContextProps(props, ref, LabelContext);\n  let {elementType: ElementType = 'label', ...labelProps} = props;\n  // @ts-ignore\n  return <ElementType className=\"react-aria-Label\" {...labelProps} ref={ref} />;\n}\n\nconst _Label = /*#__PURE__*/ createHideableComponent(Label);\nexport {_Label as Label};\n"], "names": [], "mappings": ";;;;;;;;AAAA;;;;;;;;;;CAUC,GASM,MAAM,4CAAA,WAAA,GAAe,CAAA,yMAAA,gBAAY,EAA8C,CAAC;AAEvF,SAAS,4BAAM,KAAiB,EAAE,GAAmC;IACnE,CAAC,OAAO,IAAI,GAAG,CAAA,kMAAA,kBAAc,EAAE,OAAO,KAAK;IAC3C,IAAI,EAAC,aAAa,cAAc,OAAO,EAAE,GAAG,YAAW,GAAG;IAC1D,aAAa;IACb,OAAA,WAAA,GAAO,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,aAAA;QAAY,WAAU;QAAoB,GAAG,UAAU;QAAE,KAAK;;AACxE;AAEA,MAAM,4CAAS,WAAW,GAAG,CAAA,kMAAA,0BAAsB,EAAE", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 598, "column": 0}, "map": {"version": 3, "file": "DateField.module.js.map", "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/react-aria-components/dist/packages/react-aria-components/src/DateField.tsx"], "sourcesContent": ["/*\n * Copyright 2022 Adobe. All rights reserved.\n * This file is licensed to you under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License. You may obtain a copy\n * of the License at http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software distributed under\n * the License is distributed on an \"AS IS\" BASIS, WITHOUT WARRANTIES OR REPRESENTATIONS\n * OF ANY KIND, either express or implied. See the License for the specific language\n * governing permissions and limitations under the License.\n */\nimport {AriaDateFieldProps, AriaTimeFieldProps, DateValue, HoverEvents, mergeProps, TimeValue, useDateField, useDateSegment, useFocusRing, useHover, useLocale, useTimeField} from 'react-aria';\nimport {ContextValue, forwardRefType, Provider, RACValidation, removeDataAttributes, RenderProps, SlotProps, StyleRenderProps, useContextProps, useRenderProps, useSlot, useSlottedContext} from './utils';\nimport {createCalendar} from '@internationalized/date';\nimport {DateFieldState, DateSegmentType, DateSegment as IDateSegment, TimeFieldState, useDateFieldState, useTimeFieldState} from 'react-stately';\nimport {FieldErrorContext} from './FieldError';\nimport {filterDOMProps, useObjectRef} from '@react-aria/utils';\nimport {FormContext} from './Form';\nimport {Group, GroupContext} from './Group';\nimport {Input, InputContext} from './Input';\nimport {LabelContext} from './Label';\nimport React, {cloneElement, createContext, ForwardedRef, forwardRef, JSX, ReactElement, useContext, useRef} from 'react';\nimport {TextContext} from './Text';\n\nexport interface DateFieldRenderProps {\n  /**\n   * State of the date field.\n   */\n  state: DateFieldState,\n  /**\n   * Whether the date field is invalid.\n   * @selector [data-invalid]\n   */\n  isInvalid: boolean,\n  /**\n   * Whether the date field is disabled.\n   * @selector [data-disabled]\n   */\n  isDisabled: boolean\n}\nexport interface DateFieldProps<T extends DateValue> extends Omit<AriaDateFieldProps<T>, 'label' | 'description' | 'errorMessage' | 'validationState' | 'validationBehavior'>, RACValidation, RenderProps<DateFieldRenderProps>, SlotProps {}\nexport interface TimeFieldProps<T extends TimeValue> extends Omit<AriaTimeFieldProps<T>, 'label' | 'description' | 'errorMessage' | 'validationState' | 'validationBehavior'>, RACValidation, RenderProps<DateFieldRenderProps>, SlotProps {}\n\nexport const DateFieldContext = createContext<ContextValue<DateFieldProps<any>, HTMLDivElement>>(null);\nexport const TimeFieldContext = createContext<ContextValue<TimeFieldProps<any>, HTMLDivElement>>(null);\nexport const DateFieldStateContext = createContext<DateFieldState | null>(null);\nexport const TimeFieldStateContext = createContext<TimeFieldState | null>(null);\n\nfunction DateField<T extends DateValue>(props: DateFieldProps<T>, ref: ForwardedRef<HTMLDivElement>) {\n  [props, ref] = useContextProps(props, ref, DateFieldContext);\n  let {validationBehavior: formValidationBehavior} = useSlottedContext(FormContext) || {};\n  let validationBehavior = props.validationBehavior ?? formValidationBehavior ?? 'native';\n  let {locale} = useLocale();\n  let state = useDateFieldState({\n    ...props,\n    locale,\n    createCalendar,\n    validationBehavior\n  });\n\n  let fieldRef = useRef<HTMLDivElement>(null);\n  let [labelRef, label] = useSlot();\n  let inputRef = useRef<HTMLInputElement>(null);\n  let {labelProps, fieldProps, inputProps, descriptionProps, errorMessageProps, ...validation} = useDateField({\n    ...removeDataAttributes(props),\n    label,\n    inputRef,\n    validationBehavior\n  }, state, fieldRef);\n\n  let renderProps = useRenderProps({\n    ...removeDataAttributes(props),\n    values: {\n      state,\n      isInvalid: state.isInvalid,\n      isDisabled: state.isDisabled\n    },\n    defaultClassName: 'react-aria-DateField'\n  });\n\n  let DOMProps = filterDOMProps(props);\n  delete DOMProps.id;\n\n  return (\n    <Provider\n      values={[\n        [DateFieldStateContext, state],\n        [GroupContext, {...fieldProps, ref: fieldRef, isInvalid: state.isInvalid}],\n        [InputContext, {...inputProps, ref: inputRef}],\n        [LabelContext, {...labelProps, ref: labelRef, elementType: 'span'}],\n        [TextContext, {\n          slots: {\n            description: descriptionProps,\n            errorMessage: errorMessageProps\n          }\n        }],\n        [FieldErrorContext, validation]\n      ]}>\n      <div\n        {...DOMProps}\n        {...renderProps}\n        ref={ref}\n        slot={props.slot || undefined}\n        data-invalid={state.isInvalid || undefined} />\n    </Provider>\n  );\n}\n\n/**\n * A date field allows users to enter and edit date and time values using a keyboard.\n * Each part of a date value is displayed in an individually editable segment.\n */\nconst _DateField = /*#__PURE__*/ (forwardRef as forwardRefType)(DateField);\nexport {_DateField as DateField};\n\nfunction TimeField<T extends TimeValue>(props: TimeFieldProps<T>, ref: ForwardedRef<HTMLDivElement>) {\n  [props, ref] = useContextProps(props, ref, TimeFieldContext);\n  let {validationBehavior: formValidationBehavior} = useSlottedContext(FormContext) || {};\n  let validationBehavior = props.validationBehavior ?? formValidationBehavior ?? 'native';\n  let {locale} = useLocale();\n  let state = useTimeFieldState({\n    ...props,\n    locale,\n    validationBehavior\n  });\n\n  let fieldRef = useRef<HTMLDivElement>(null);\n  let [labelRef, label] = useSlot();\n  let inputRef = useRef<HTMLInputElement>(null);\n  let {labelProps, fieldProps, inputProps, descriptionProps, errorMessageProps, ...validation} = useTimeField({\n    ...removeDataAttributes(props),\n    label,\n    inputRef,\n    validationBehavior\n  }, state, fieldRef);\n\n  let renderProps = useRenderProps({\n    ...props,\n    values: {\n      state,\n      isInvalid: state.isInvalid,\n      isDisabled: state.isDisabled\n    },\n    defaultClassName: 'react-aria-TimeField'\n  });\n\n  let DOMProps = filterDOMProps(props);\n  delete DOMProps.id;\n\n  return (\n    <Provider\n      values={[\n        [TimeFieldStateContext, state],\n        [GroupContext, {...fieldProps, ref: fieldRef, isInvalid: state.isInvalid}],\n        [InputContext, {...inputProps, ref: inputRef}],\n        [LabelContext, {...labelProps, ref: labelRef, elementType: 'span'}],\n        [TextContext, {\n          slots: {\n            description: descriptionProps,\n            errorMessage: errorMessageProps\n          }\n        }],\n        [FieldErrorContext, validation]\n      ]}>\n      <div\n        {...DOMProps}\n        {...renderProps}\n        ref={ref}\n        slot={props.slot || undefined}\n        data-invalid={state.isInvalid || undefined} />\n    </Provider>\n  );\n}\n\n/**\n * A time field allows users to enter and edit time values using a keyboard.\n * Each part of a time value is displayed in an individually editable segment.\n */\nconst _TimeField = /*#__PURE__*/ (forwardRef as forwardRefType)(TimeField);\nexport {_TimeField as TimeField};\n\nexport interface DateInputRenderProps {\n  /**\n   * Whether the date input is currently hovered with a mouse.\n   * @selector [data-hovered]\n   */\n  isHovered: boolean,\n  /**\n   * Whether an element within the date input is focused, either via a mouse or keyboard.\n   * @selector [data-focus-within]\n   */\n  isFocusWithin: boolean,\n  /**\n   * Whether an element within the date input is keyboard focused.\n   * @selector [data-focus-visible]\n   */\n  isFocusVisible: boolean,\n  /**\n   * Whether the date input is disabled.\n   * @selector [data-disabled]\n   */\n  isDisabled: boolean,\n\n  /**\n   * Whether the date input is invalid.\n   * @selector [data-invalid]\n   */\n  isInvalid: boolean\n}\n\nexport interface DateInputProps extends SlotProps, StyleRenderProps<DateInputRenderProps> {\n  children: (segment: IDateSegment) => ReactElement\n}\n\nfunction DateInput(props: DateInputProps, ref: ForwardedRef<HTMLDivElement>): JSX.Element {\n  // If state is provided by DateField/TimeField, just render.\n  // Otherwise (e.g. in DatePicker), we need to call hooks and create state ourselves.\n  let dateFieldState = useContext(DateFieldStateContext);\n  let timeFieldState = useContext(TimeFieldStateContext);\n  return dateFieldState || timeFieldState\n    ? <DateInputInner {...props} ref={ref} />\n    : <DateInputStandalone {...props} ref={ref} />;\n}\n\nconst DateInputStandalone = forwardRef((props: DateInputProps, ref: ForwardedRef<HTMLDivElement>) => {\n  let [dateFieldProps, fieldRef] = useContextProps({slot: props.slot} as DateFieldProps<any>, ref, DateFieldContext);\n  let {locale} = useLocale();\n  let state = useDateFieldState({\n    ...dateFieldProps,\n    locale,\n    createCalendar\n  });\n\n  let inputRef = useRef<HTMLInputElement>(null);\n  let {fieldProps, inputProps} = useDateField({...dateFieldProps, inputRef}, state, fieldRef);\n\n  return (\n    <Provider\n      values={[\n        [DateFieldStateContext, state],\n        [InputContext, {...inputProps, ref: inputRef}],\n        [GroupContext, {...fieldProps, ref: fieldRef, isInvalid: state.isInvalid}]\n      ]}>\n      <DateInputInner {...props} />\n    </Provider>\n  );\n});\n\nconst DateInputInner = forwardRef((props: DateInputProps, ref: ForwardedRef<HTMLDivElement>) => {\n  let {className, children} = props;\n  let dateFieldState = useContext(DateFieldStateContext);\n  let timeFieldState = useContext(TimeFieldStateContext);\n  let state = dateFieldState ?? timeFieldState!;\n\n  return (\n    <>\n      <Group\n        {...props}\n        ref={ref}\n        slot={props.slot || undefined}\n        className={className ?? 'react-aria-DateInput'}\n        isInvalid={state.isInvalid}>\n        {state.segments.map((segment, i) => cloneElement(children(segment), {key: i}))}\n      </Group>\n      <Input />\n    </>\n  );\n});\n\n/**\n * A date input groups the editable date segments within a date field.\n */\nconst _DateInput = /*#__PURE__*/ (forwardRef as forwardRefType)(DateInput);\nexport {_DateInput as DateInput};\n\nexport interface DateSegmentRenderProps extends Omit<IDateSegment, 'isEditable'> {\n  /**\n   * Whether the segment is currently hovered with a mouse.\n   * @selector [data-hovered]\n   */\n  isHovered: boolean,\n  /**\n   * Whether the segment is focused, either via a mouse or keyboard.\n   * @selector [data-focused]\n   */\n  isFocused: boolean,\n  /**\n   * Whether the segment is keyboard focused.\n   * @selector [data-focus-visible]\n   */\n  isFocusVisible: boolean,\n  /**\n   * Whether the value is a placeholder.\n   * @selector [data-placeholder]\n   */\n  isPlaceholder: boolean,\n  /**\n   * Whether the segment is read only.\n   * @selector [data-readonly]\n   */\n  isReadOnly: boolean,\n  /**\n   * Whether the date field is disabled.\n   * @selector [data-disabled]\n   */\n  isDisabled: boolean,\n  /**\n   * Whether the date field is in an invalid state.\n   * @selector [data-invalid]\n   */\n  isInvalid: boolean,\n  /**\n   * The type of segment. Values include `literal`, `year`, `month`, `day`, etc.\n   * @selector [data-type=\"...\"]\n   */\n  type: DateSegmentType\n}\n\nexport interface DateSegmentProps extends RenderProps<DateSegmentRenderProps>, HoverEvents {\n  segment: IDateSegment\n}\n\nfunction DateSegment({segment, ...otherProps}: DateSegmentProps, ref: ForwardedRef<HTMLDivElement>) {\n  let dateFieldState = useContext(DateFieldStateContext);\n  let timeFieldState = useContext(TimeFieldStateContext);\n  let state = dateFieldState ?? timeFieldState!;\n  let domRef = useObjectRef(ref);\n  let {segmentProps} = useDateSegment(segment, state, domRef);\n  let {focusProps, isFocused, isFocusVisible} = useFocusRing();\n  let {hoverProps, isHovered} = useHover({...otherProps, isDisabled: state.isDisabled || segment.type === 'literal'});\n  let renderProps = useRenderProps({\n    ...otherProps,\n    values: {\n      ...segment,\n      isReadOnly: !segment.isEditable,\n      isInvalid: state.isInvalid,\n      isDisabled: state.isDisabled,\n      isHovered,\n      isFocused,\n      isFocusVisible\n    },\n    defaultChildren: segment.text,\n    defaultClassName: 'react-aria-DateSegment'\n  });\n\n\n  return (\n    <div\n      {...mergeProps(filterDOMProps(otherProps as any), segmentProps, focusProps, hoverProps)}\n      {...renderProps}\n      ref={domRef}\n      data-placeholder={segment.isPlaceholder || undefined}\n      data-invalid={state.isInvalid || undefined}\n      data-readonly={!segment.isEditable || undefined}\n      data-disabled={state.isDisabled || undefined}\n      data-type={segment.type}\n      data-hovered={isHovered || undefined}\n      data-focused={isFocused || undefined}\n      data-focus-visible={isFocusVisible || undefined} />\n  );\n}\n\n/**\n * A date segment displays an individual unit of a date and time, and allows users to edit\n * the value by typing or using the arrow keys to increment and decrement.\n */\nconst _DateSegment = /*#__PURE__*/ (forwardRef as forwardRefType)(DateSegment);\nexport {_DateSegment as DateSegment};\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;;;;;;;;;;CAUC,GAiCM,MAAM,4CAAA,WAAA,GAAmB,CAAA,yMAAA,gBAAY,EAAqD;AAC1F,MAAM,4CAAA,WAAA,GAAmB,CAAA,yMAAA,gBAAY,EAAqD;AAC1F,MAAM,4CAAA,WAAA,GAAwB,CAAA,yMAAA,gBAAY,EAAyB;AACnE,MAAM,4CAAA,WAAA,GAAwB,CAAA,yMAAA,gBAAY,EAAyB;AAE1E,SAAS,gCAA+B,KAAwB,EAAE,GAAiC;IACjG,CAAC,OAAO,IAAI,GAAG,CAAA,kMAAA,kBAAc,EAAE,OAAO,KAAK;IAC3C,IAAI,EAAC,oBAAoB,sBAAsB,EAAC,GAAG,CAAA,kMAAA,oBAAgB,EAAE,CAAA,iMAAA,cAAU,MAAM,CAAC;QAC7D,2BAAA;IAAzB,IAAI,qBAAqB,CAAA,OAAA,CAAA,4BAAA,MAAM,kBAAkB,MAAA,QAAxB,8BAAA,KAAA,IAAA,4BAA4B,sBAAA,MAAA,QAA5B,SAAA,KAAA,IAAA,OAAsD;IAC/E,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,+JAAA,YAAQ;IACvB,IAAI,QAAQ,CAAA,kLAAA,oBAAgB,EAAE;QAC5B,GAAG,KAAK;gBACR;+LACA,iBAAA;4BACA;IACF;IAEA,IAAI,WAAW,CAAA,yMAAA,SAAK,EAAkB;IACtC,IAAI,CAAC,UAAU,MAAM,GAAG,CAAA,kMAAA,UAAM;IAC9B,IAAI,WAAW,CAAA,yMAAA,SAAK,EAAoB;IACxC,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAA,YAAE,UAAU,EAAA,kBAAE,gBAAgB,EAAA,mBAAE,iBAAiB,EAAE,GAAG,YAAW,GAAG,CAAA,0KAAA,eAAW,EAAE;QAC1G,GAAG,CAAA,kMAAA,uBAAmB,EAAE,MAAM;eAC9B;kBACA;4BACA;IACF,GAAG,OAAO;IAEV,IAAI,cAAc,CAAA,kMAAA,iBAAa,EAAE;QAC/B,GAAG,CAAA,kMAAA,uBAAmB,EAAE,MAAM;QAC9B,QAAQ;mBACN;YACA,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;QAC9B;QACA,kBAAkB;IACpB;IAEA,IAAI,WAAW,CAAA,uKAAA,iBAAa,EAAE;IAC9B,OAAO,SAAS,EAAE;IAElB,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,kMAAA,WAAO,GAAA;QACN,QAAQ;YACN;gBAAC;gBAAuB;aAAM;YAC9B;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;oBAAU,WAAW,MAAM,SAAS;gBAAA;aAAE;YAC1E;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;gBAAQ;aAAE;YAC9C;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;oBAAU,aAAa;gBAAM;aAAE;YACnE;gBAAC,CAAA,iMAAA,cAAU;gBAAG;oBACZ,OAAO;wBACL,aAAa;wBACb,cAAc;oBAChB;gBACF;aAAE;YACF;gBAAC,CAAA,uMAAA,oBAAgB;gBAAG;aAAW;SAChC;qBACD,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,OAAA;QACE,GAAG,QAAQ;QACX,GAAG,WAAW;QACf,KAAK;QACL,MAAM,MAAM,IAAI,IAAI;QACpB,gBAAc,MAAM,SAAS,IAAI;;AAGzC;AAEA;;;CAGC,GACD,MAAM,4CAAa,WAAW,GAAI,CAAA,yMAAA,aAAS,EAAqB;AAGhE,SAAS,gCAA+B,KAAwB,EAAE,GAAiC;IACjG,CAAC,OAAO,IAAI,GAAG,CAAA,kMAAA,kBAAc,EAAE,OAAO,KAAK;IAC3C,IAAI,EAAC,oBAAoB,sBAAsB,EAAC,GAAG,CAAA,kMAAA,oBAAgB,EAAE,CAAA,iMAAA,cAAU,MAAM,CAAC;QAC7D,2BAAA;IAAzB,IAAI,qBAAqB,CAAA,OAAA,CAAA,4BAAA,MAAM,kBAAkB,MAAA,QAAxB,8BAAA,KAAA,IAAA,4BAA4B,sBAAA,MAAA,QAA5B,SAAA,KAAA,IAAA,OAAsD;IAC/E,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,+JAAA,YAAQ;IACvB,IAAI,QAAQ,CAAA,kLAAA,oBAAgB,EAAE;QAC5B,GAAG,KAAK;gBACR;4BACA;IACF;IAEA,IAAI,WAAW,CAAA,yMAAA,SAAK,EAAkB;IACtC,IAAI,CAAC,UAAU,MAAM,GAAG,CAAA,kMAAA,UAAM;IAC9B,IAAI,WAAW,CAAA,yMAAA,SAAK,EAAoB;IACxC,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAA,YAAE,UAAU,EAAA,kBAAE,gBAAgB,EAAA,mBAAE,iBAAiB,EAAE,GAAG,YAAW,GAAG,CAAA,0KAAA,eAAW,EAAE;QAC1G,GAAG,CAAA,kMAAA,uBAAmB,EAAE,MAAM;eAC9B;kBACA;4BACA;IACF,GAAG,OAAO;IAEV,IAAI,cAAc,CAAA,kMAAA,iBAAa,EAAE;QAC/B,GAAG,KAAK;QACR,QAAQ;mBACN;YACA,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;QAC9B;QACA,kBAAkB;IACpB;IAEA,IAAI,WAAW,CAAA,uKAAA,iBAAa,EAAE;IAC9B,OAAO,SAAS,EAAE;IAElB,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,kMAAA,WAAO,GAAA;QACN,QAAQ;YACN;gBAAC;gBAAuB;aAAM;YAC9B;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;oBAAU,WAAW,MAAM,SAAS;gBAAA;aAAE;YAC1E;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;gBAAQ;aAAE;YAC9C;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;oBAAU,aAAa;gBAAM;aAAE;YACnE;gBAAC,CAAA,iMAAA,cAAU;gBAAG;oBACZ,OAAO;wBACL,aAAa;wBACb,cAAc;oBAChB;gBACF;aAAE;YACF;gBAAC,CAAA,uMAAA,oBAAgB;gBAAG;aAAW;SAChC;qBACD,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,OAAA;QACE,GAAG,QAAQ;QACX,GAAG,WAAW;QACf,KAAK;QACL,MAAM,MAAM,IAAI,IAAI;QACpB,gBAAc,MAAM,SAAS,IAAI;;AAGzC;AAEA;;;CAGC,GACD,MAAM,4CAAa,WAAW,GAAI,CAAA,yMAAA,aAAS,EAAqB;AAoChE,SAAS,gCAAU,KAAqB,EAAE,GAAiC;IACzE,4DAA4D;IAC5D,oFAAoF;IACpF,IAAI,iBAAiB,CAAA,yMAAA,aAAS,EAAE;IAChC,IAAI,iBAAiB,CAAA,yMAAA,aAAS,EAAE;IAChC,OAAO,kBAAkB,iBAAA,WAAA,GACrB,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,sCAAA;QAAgB,GAAG,KAAK;QAAE,KAAK;uBAChC,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,2CAAA;QAAqB,GAAG,KAAK;QAAE,KAAK;;AAC3C;AAEA,MAAM,4CAAA,WAAA,GAAsB,CAAA,yMAAA,aAAS,EAAE,CAAC,OAAuB;IAC7D,IAAI,CAAC,gBAAgB,SAAS,GAAG,CAAA,kMAAA,kBAAc,EAAE;QAAC,MAAM,MAAM,IAAI;IAAA,GAA0B,KAAK;IACjG,IAAI,EAAA,QAAC,MAAM,EAAC,GAAG,CAAA,+JAAA,YAAQ;IACvB,IAAI,QAAQ,CAAA,kLAAA,oBAAgB,EAAE;QAC5B,GAAG,cAAc;gBACjB;+LACA,iBAAA;IACF;IAEA,IAAI,WAAW,CAAA,yMAAA,SAAK,EAAoB;IACxC,IAAI,EAAA,YAAC,UAAU,EAAA,YAAE,UAAU,EAAC,GAAG,CAAA,0KAAA,eAAW,EAAE;QAAC,GAAG,cAAc;kBAAE;IAAQ,GAAG,OAAO;IAElF,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,kMAAA,WAAO,GAAA;QACN,QAAQ;YACN;gBAAC;gBAAuB;aAAM;YAC9B;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;gBAAQ;aAAE;YAC9C;gBAAC,CAAA,kMAAA,eAAW;gBAAG;oBAAC,GAAG,UAAU;oBAAE,KAAK;oBAAU,WAAW,MAAM,SAAS;gBAAA;aAAE;SAC3E;qBACD,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,sCAAmB;AAG1B;AAEA,MAAM,uCAAA,WAAA,GAAiB,CAAA,yMAAA,aAAS,EAAE,CAAC,OAAuB;IACxD,IAAI,EAAA,WAAC,SAAS,EAAA,UAAE,QAAQ,EAAC,GAAG;IAC5B,IAAI,iBAAiB,CAAA,yMAAA,aAAS,EAAE;IAChC,IAAI,iBAAiB,CAAA,yMAAA,aAAS,EAAE;IAChC,IAAI,QAAQ,mBAAA,QAAA,mBAAA,KAAA,IAAA,iBAAkB;IAE9B,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAA,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,QAAA,EAAA,MAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,kMAAA,QAAI,GAAA;QACF,GAAG,KAAK;QACT,KAAK;QACL,MAAM,MAAM,IAAI,IAAI;QACpB,WAAW,cAAA,QAAA,cAAA,KAAA,IAAA,YAAa;QACxB,WAAW,MAAM,SAAS;OACzB,MAAM,QAAQ,CAAC,GAAG,CAAC,CAAC,SAAS,IAAA,WAAA,GAAM,CAAA,yMAAA,eAAW,EAAE,SAAS,UAAU;YAAC,KAAK;QAAC,MAAA,WAAA,GAE7E,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,CAAA,kMAAA,QAAI,GAAA;AAGX;AAEA;;CAEC,GACD,MAAM,4CAAa,WAAW,GAAI,CAAA,yMAAA,aAAS,EAAqB;AAkDhE,SAAS,kCAAY,EAAA,SAAC,OAAO,EAAE,GAAG,YAA6B,EAAE,GAAiC;IAChG,IAAI,iBAAiB,CAAA,yMAAA,aAAS,EAAE;IAChC,IAAI,iBAAiB,CAAA,yMAAA,aAAS,EAAE;IAChC,IAAI,QAAQ,mBAAA,QAAA,mBAAA,KAAA,IAAA,iBAAkB;IAC9B,IAAI,SAAS,CAAA,qKAAA,eAAW,EAAE;IAC1B,IAAI,EAAA,cAAC,YAAY,EAAC,GAAG,CAAA,4KAAA,iBAAa,EAAE,SAAS,OAAO;IACpD,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAA,gBAAE,cAAc,EAAC,GAAG,CAAA,qKAAA,eAAW;IACzD,IAAI,EAAA,YAAC,UAAU,EAAA,WAAE,SAAS,EAAC,GAAG,CAAA,wKAAA,WAAO,EAAE;QAAC,GAAG,UAAU;QAAE,YAAY,MAAM,UAAU,IAAI,QAAQ,IAAI,KAAK;IAAS;IACjH,IAAI,cAAc,CAAA,kMAAA,iBAAa,EAAE;QAC/B,GAAG,UAAU;QACb,QAAQ;YACN,GAAG,OAAO;YACV,YAAY,CAAC,QAAQ,UAAU;YAC/B,WAAW,MAAM,SAAS;YAC1B,YAAY,MAAM,UAAU;uBAC5B;uBACA;4BACA;QACF;QACA,iBAAiB,QAAQ,IAAI;QAC7B,kBAAkB;IACpB;IAGA,OAAA,WAAA,GACE,CAAA,GAAA,qMAAA,CAAA,UAAA,EAAA,aAAA,CAAC,OAAA;QACE,GAAG,CAAA,mKAAA,aAAS,EAAE,CAAA,uKAAA,iBAAa,EAAE,aAAoB,cAAc,YAAY,WAAW;QACtF,GAAG,WAAW;QACf,KAAK;QACL,oBAAkB,QAAQ,aAAa,IAAI;QAC3C,gBAAc,MAAM,SAAS,IAAI;QACjC,iBAAe,CAAC,QAAQ,UAAU,IAAI;QACtC,iBAAe,MAAM,UAAU,IAAI;QACnC,aAAW,QAAQ,IAAI;QACvB,gBAAc,aAAa;QAC3B,gBAAc,aAAa;QAC3B,sBAAoB,kBAAkB;;AAE5C;AAEA;;;CAGC,GACD,MAAM,4CAAe,WAAW,GAAI,CAAA,yMAAA,aAAS,EAAqB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 947, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/frosted-ui/node_modules/vaul/dist/index.mjs"], "sourcesContent": ["'use client';\nfunction __insertCSS(code) {\n  if (!code || typeof document == 'undefined') return\n  let head = document.head || document.getElementsByTagName('head')[0]\n  let style = document.createElement('style')\n  style.type = 'text/css'\n  head.appendChild(style)\n  ;style.styleSheet ? (style.styleSheet.cssText = code) : style.appendChild(document.createTextNode(code))\n}\n\nimport * as DialogPrimitive from '@radix-ui/react-dialog';\nimport * as React from 'react';\nimport React__default, { useLayoutEffect, useEffect, useMemo } from 'react';\n\nconst DrawerContext = React__default.createContext({\n    drawerRef: {\n        current: null\n    },\n    overlayRef: {\n        current: null\n    },\n    onPress: ()=>{},\n    onRelease: ()=>{},\n    onDrag: ()=>{},\n    onNestedDrag: ()=>{},\n    onNestedOpenChange: ()=>{},\n    onNestedRelease: ()=>{},\n    openProp: undefined,\n    dismissible: false,\n    isOpen: false,\n    isDragging: false,\n    keyboardIsOpen: {\n        current: false\n    },\n    snapPointsOffset: null,\n    snapPoints: null,\n    handleOnly: false,\n    modal: false,\n    shouldFade: false,\n    activeSnapPoint: null,\n    onOpenChange: ()=>{},\n    setActiveSnapPoint: ()=>{},\n    closeDrawer: ()=>{},\n    direction: 'bottom',\n    shouldScaleBackground: false,\n    setBackgroundColorOnScale: true,\n    noBodyStyles: false,\n    container: null,\n    autoFocus: false\n});\nconst useDrawerContext = ()=>{\n    const context = React__default.useContext(DrawerContext);\n    if (!context) {\n        throw new Error('useDrawerContext must be used within a Drawer.Root');\n    }\n    return context;\n};\n\n__insertCSS(\"[data-vaul-drawer]{touch-action:none;will-change:transform;transition:transform .5s cubic-bezier(.32, .72, 0, 1);animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=open]{animation-name:slideFromBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=bottom][data-state=closed]{animation-name:slideToBottom}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=open]{animation-name:slideFromTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=top][data-state=closed]{animation-name:slideToTop}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=open]{animation-name:slideFromLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=left][data-state=closed]{animation-name:slideToLeft}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=open]{animation-name:slideFromRight}[data-vaul-drawer][data-vaul-snap-points=false][data-vaul-drawer-direction=right][data-state=closed]{animation-name:slideToRight}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,100%,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,-100%,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(-100%,0,0)}[data-vaul-drawer][data-vaul-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(100%,0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=top]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=bottom]{transform:translate3d(0,var(--snap-point-height,0),0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=left]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-drawer][data-vaul-delayed-snap-points=true][data-vaul-drawer-direction=right]{transform:translate3d(var(--snap-point-height,0),0,0)}[data-vaul-overlay][data-vaul-snap-points=false]{animation-duration:.5s;animation-timing-function:cubic-bezier(0.32,0.72,0,1)}[data-vaul-overlay][data-vaul-snap-points=false][data-state=open]{animation-name:fadeIn}[data-vaul-overlay][data-state=closed]{animation-name:fadeOut}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:0;transition:opacity .5s cubic-bezier(.32, .72, 0, 1)}[data-vaul-overlay][data-vaul-snap-points=true]{opacity:1}[data-vaul-drawer]:not([data-vaul-custom-container=true])::after{content:'';position:absolute;background:inherit;background-color:inherit}[data-vaul-drawer][data-vaul-drawer-direction=top]::after{top:initial;bottom:100%;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=bottom]::after{top:100%;bottom:initial;left:0;right:0;height:200%}[data-vaul-drawer][data-vaul-drawer-direction=left]::after{left:initial;right:100%;top:0;bottom:0;width:200%}[data-vaul-drawer][data-vaul-drawer-direction=right]::after{left:100%;right:initial;top:0;bottom:0;width:200%}[data-vaul-overlay][data-vaul-snap-points=true]:not([data-vaul-snap-points-overlay=true]):not(\\n[data-state=closed]\\n){opacity:0}[data-vaul-overlay][data-vaul-snap-points-overlay=true]{opacity:1}[data-vaul-handle]{display:block;position:relative;opacity:.7;background:#e2e2e4;margin-left:auto;margin-right:auto;height:5px;width:32px;border-radius:1rem;touch-action:pan-y}[data-vaul-handle]:active,[data-vaul-handle]:hover{opacity:1}[data-vaul-handle-hitarea]{position:absolute;left:50%;top:50%;transform:translate(-50%,-50%);width:max(100%,2.75rem);height:max(100%,2.75rem);touch-action:inherit}@media (hover:hover) and (pointer:fine){[data-vaul-drawer]{user-select:none}}@media (pointer:fine){[data-vaul-handle-hitarea]:{width:100%;height:100%}}@keyframes fadeIn{from{opacity:0}to{opacity:1}}@keyframes fadeOut{to{opacity:0}}@keyframes slideFromBottom{from{transform:translate3d(0,100%,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToBottom{to{transform:translate3d(0,100%,0)}}@keyframes slideFromTop{from{transform:translate3d(0,-100%,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToTop{to{transform:translate3d(0,-100%,0)}}@keyframes slideFromLeft{from{transform:translate3d(-100%,0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToLeft{to{transform:translate3d(-100%,0,0)}}@keyframes slideFromRight{from{transform:translate3d(100%,0,0)}to{transform:translate3d(0,0,0)}}@keyframes slideToRight{to{transform:translate3d(100%,0,0)}}\");\n\n// This code comes from https://github.com/adobe/react-spectrum/blob/main/packages/%40react-aria/overlays/src/usePreventScroll.ts\nconst KEYBOARD_BUFFER = 24;\nconst useIsomorphicLayoutEffect = typeof window !== 'undefined' ? useLayoutEffect : useEffect;\nfunction chain$1(...callbacks) {\n    return (...args)=>{\n        for (let callback of callbacks){\n            if (typeof callback === 'function') {\n                callback(...args);\n            }\n        }\n    };\n}\nfunction isMac() {\n    return testPlatform(/^Mac/);\n}\nfunction isIPhone() {\n    return testPlatform(/^iPhone/);\n}\nfunction isSafari() {\n    return /^((?!chrome|android).)*safari/i.test(navigator.userAgent);\n}\nfunction isIPad() {\n    return testPlatform(/^iPad/) || // iPadOS 13 lies and says it's a Mac, but we can distinguish by detecting touch support.\n    isMac() && navigator.maxTouchPoints > 1;\n}\nfunction isIOS() {\n    return isIPhone() || isIPad();\n}\nfunction testPlatform(re) {\n    return typeof window !== 'undefined' && window.navigator != null ? re.test(window.navigator.platform) : undefined;\n}\n// @ts-ignore\nconst visualViewport = typeof document !== 'undefined' && window.visualViewport;\nfunction isScrollable(node) {\n    let style = window.getComputedStyle(node);\n    return /(auto|scroll)/.test(style.overflow + style.overflowX + style.overflowY);\n}\nfunction getScrollParent(node) {\n    if (isScrollable(node)) {\n        node = node.parentElement;\n    }\n    while(node && !isScrollable(node)){\n        node = node.parentElement;\n    }\n    return node || document.scrollingElement || document.documentElement;\n}\n// HTML input types that do not cause the software keyboard to appear.\nconst nonTextInputTypes = new Set([\n    'checkbox',\n    'radio',\n    'range',\n    'color',\n    'file',\n    'image',\n    'button',\n    'submit',\n    'reset'\n]);\n// The number of active usePreventScroll calls. Used to determine whether to revert back to the original page style/scroll position\nlet preventScrollCount = 0;\nlet restore;\n/**\n * Prevents scrolling on the document body on mount, and\n * restores it on unmount. Also ensures that content does not\n * shift due to the scrollbars disappearing.\n */ function usePreventScroll(options = {}) {\n    let { isDisabled } = options;\n    useIsomorphicLayoutEffect(()=>{\n        if (isDisabled) {\n            return;\n        }\n        preventScrollCount++;\n        if (preventScrollCount === 1) {\n            if (isIOS()) {\n                restore = preventScrollMobileSafari();\n            }\n        }\n        return ()=>{\n            preventScrollCount--;\n            if (preventScrollCount === 0) {\n                restore == null ? void 0 : restore();\n            }\n        };\n    }, [\n        isDisabled\n    ]);\n}\n// Mobile Safari is a whole different beast. Even with overflow: hidden,\n// it still scrolls the page in many situations:\n//\n// 1. When the bottom toolbar and address bar are collapsed, page scrolling is always allowed.\n// 2. When the keyboard is visible, the viewport does not resize. Instead, the keyboard covers part of\n//    it, so it becomes scrollable.\n// 3. When tapping on an input, the page always scrolls so that the input is centered in the visual viewport.\n//    This may cause even fixed position elements to scroll off the screen.\n// 4. When using the next/previous buttons in the keyboard to navigate between inputs, the whole page always\n//    scrolls, even if the input is inside a nested scrollable element that could be scrolled instead.\n//\n// In order to work around these cases, and prevent scrolling without jankiness, we do a few things:\n//\n// 1. Prevent default on `touchmove` events that are not in a scrollable element. This prevents touch scrolling\n//    on the window.\n// 2. Prevent default on `touchmove` events inside a scrollable element when the scroll position is at the\n//    top or bottom. This avoids the whole page scrolling instead, but does prevent overscrolling.\n// 3. Prevent default on `touchend` events on input elements and handle focusing the element ourselves.\n// 4. When focusing an input, apply a transform to trick Safari into thinking the input is at the top\n//    of the page, which prevents it from scrolling the page. After the input is focused, scroll the element\n//    into view ourselves, without scrolling the whole page.\n// 5. Offset the body by the scroll position using a negative margin and scroll to the top. This should appear the\n//    same visually, but makes the actual scroll position always zero. This is required to make all of the\n//    above work or Safari will still try to scroll the page when focusing an input.\n// 6. As a last resort, handle window scroll events, and scroll back to the top. This can happen when attempting\n//    to navigate to an input with the next/previous buttons that's outside a modal.\nfunction preventScrollMobileSafari() {\n    let scrollable;\n    let lastY = 0;\n    let onTouchStart = (e)=>{\n        // Store the nearest scrollable parent element from the element that the user touched.\n        scrollable = getScrollParent(e.target);\n        if (scrollable === document.documentElement && scrollable === document.body) {\n            return;\n        }\n        lastY = e.changedTouches[0].pageY;\n    };\n    let onTouchMove = (e)=>{\n        // Prevent scrolling the window.\n        if (!scrollable || scrollable === document.documentElement || scrollable === document.body) {\n            e.preventDefault();\n            return;\n        }\n        // Prevent scrolling up when at the top and scrolling down when at the bottom\n        // of a nested scrollable area, otherwise mobile Safari will start scrolling\n        // the window instead. Unfortunately, this disables bounce scrolling when at\n        // the top but it's the best we can do.\n        let y = e.changedTouches[0].pageY;\n        let scrollTop = scrollable.scrollTop;\n        let bottom = scrollable.scrollHeight - scrollable.clientHeight;\n        if (bottom === 0) {\n            return;\n        }\n        if (scrollTop <= 0 && y > lastY || scrollTop >= bottom && y < lastY) {\n            e.preventDefault();\n        }\n        lastY = y;\n    };\n    let onTouchEnd = (e)=>{\n        let target = e.target;\n        // Apply this change if we're not already focused on the target element\n        if (isInput(target) && target !== document.activeElement) {\n            e.preventDefault();\n            // Apply a transform to trick Safari into thinking the input is at the top of the page\n            // so it doesn't try to scroll it into view. When tapping on an input, this needs to\n            // be done before the \"focus\" event, so we have to focus the element ourselves.\n            target.style.transform = 'translateY(-2000px)';\n            target.focus();\n            requestAnimationFrame(()=>{\n                target.style.transform = '';\n            });\n        }\n    };\n    let onFocus = (e)=>{\n        let target = e.target;\n        if (isInput(target)) {\n            // Transform also needs to be applied in the focus event in cases where focus moves\n            // other than tapping on an input directly, e.g. the next/previous buttons in the\n            // software keyboard. In these cases, it seems applying the transform in the focus event\n            // is good enough, whereas when tapping an input, it must be done before the focus event. 🤷‍♂️\n            target.style.transform = 'translateY(-2000px)';\n            requestAnimationFrame(()=>{\n                target.style.transform = '';\n                // This will have prevented the browser from scrolling the focused element into view,\n                // so we need to do this ourselves in a way that doesn't cause the whole page to scroll.\n                if (visualViewport) {\n                    if (visualViewport.height < window.innerHeight) {\n                        // If the keyboard is already visible, do this after one additional frame\n                        // to wait for the transform to be removed.\n                        requestAnimationFrame(()=>{\n                            scrollIntoView(target);\n                        });\n                    } else {\n                        // Otherwise, wait for the visual viewport to resize before scrolling so we can\n                        // measure the correct position to scroll to.\n                        visualViewport.addEventListener('resize', ()=>scrollIntoView(target), {\n                            once: true\n                        });\n                    }\n                }\n            });\n        }\n    };\n    let onWindowScroll = ()=>{\n        // Last resort. If the window scrolled, scroll it back to the top.\n        // It should always be at the top because the body will have a negative margin (see below).\n        window.scrollTo(0, 0);\n    };\n    // Record the original scroll position so we can restore it.\n    // Then apply a negative margin to the body to offset it by the scroll position. This will\n    // enable us to scroll the window to the top, which is required for the rest of this to work.\n    let scrollX = window.pageXOffset;\n    let scrollY = window.pageYOffset;\n    let restoreStyles = chain$1(setStyle(document.documentElement, 'paddingRight', `${window.innerWidth - document.documentElement.clientWidth}px`));\n    // Scroll to the top. The negative margin on the body will make this appear the same.\n    window.scrollTo(0, 0);\n    let removeEvents = chain$1(addEvent(document, 'touchstart', onTouchStart, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'touchmove', onTouchMove, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'touchend', onTouchEnd, {\n        passive: false,\n        capture: true\n    }), addEvent(document, 'focus', onFocus, true), addEvent(window, 'scroll', onWindowScroll));\n    return ()=>{\n        // Restore styles and scroll the page back to where it was.\n        restoreStyles();\n        removeEvents();\n        window.scrollTo(scrollX, scrollY);\n    };\n}\n// Sets a CSS property on an element, and returns a function to revert it to the previous value.\nfunction setStyle(element, style, value) {\n    let cur = element.style[style];\n    element.style[style] = value;\n    return ()=>{\n        element.style[style] = cur;\n    };\n}\n// Adds an event listener to an element, and returns a function to remove it.\nfunction addEvent(target, event, handler, options) {\n    // @ts-ignore\n    target.addEventListener(event, handler, options);\n    return ()=>{\n        // @ts-ignore\n        target.removeEventListener(event, handler, options);\n    };\n}\nfunction scrollIntoView(target) {\n    let root = document.scrollingElement || document.documentElement;\n    while(target && target !== root){\n        // Find the parent scrollable element and adjust the scroll position if the target is not already in view.\n        let scrollable = getScrollParent(target);\n        if (scrollable !== document.documentElement && scrollable !== document.body && scrollable !== target) {\n            let scrollableTop = scrollable.getBoundingClientRect().top;\n            let targetTop = target.getBoundingClientRect().top;\n            let targetBottom = target.getBoundingClientRect().bottom;\n            // Buffer is needed for some edge cases\n            const keyboardHeight = scrollable.getBoundingClientRect().bottom + KEYBOARD_BUFFER;\n            if (targetBottom > keyboardHeight) {\n                scrollable.scrollTop += targetTop - scrollableTop;\n            }\n        }\n        // @ts-ignore\n        target = scrollable.parentElement;\n    }\n}\nfunction isInput(target) {\n    return target instanceof HTMLInputElement && !nonTextInputTypes.has(target.type) || target instanceof HTMLTextAreaElement || target instanceof HTMLElement && target.isContentEditable;\n}\n\n// This code comes from https://github.com/radix-ui/primitives/tree/main/packages/react/compose-refs\n/**\n * Set a given ref to a given value\n * This utility takes care of different types of refs: callback refs and RefObject(s)\n */ function setRef(ref, value) {\n    if (typeof ref === 'function') {\n        ref(value);\n    } else if (ref !== null && ref !== undefined) {\n        ref.current = value;\n    }\n}\n/**\n * A utility to compose multiple refs together\n * Accepts callback refs and RefObject(s)\n */ function composeRefs(...refs) {\n    return (node)=>refs.forEach((ref)=>setRef(ref, node));\n}\n/**\n * A custom hook that composes multiple refs\n * Accepts callback refs and RefObject(s)\n */ function useComposedRefs(...refs) {\n    // eslint-disable-next-line react-hooks/exhaustive-deps\n    return React.useCallback(composeRefs(...refs), refs);\n}\n\nconst cache = new WeakMap();\nfunction set(el, styles, ignoreCache = false) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = {};\n    Object.entries(styles).forEach(([key, value])=>{\n        if (key.startsWith('--')) {\n            el.style.setProperty(key, value);\n            return;\n        }\n        originalStyles[key] = el.style[key];\n        el.style[key] = value;\n    });\n    if (ignoreCache) return;\n    cache.set(el, originalStyles);\n}\nfunction reset(el, prop) {\n    if (!el || !(el instanceof HTMLElement)) return;\n    let originalStyles = cache.get(el);\n    if (!originalStyles) {\n        return;\n    }\n    {\n        el.style[prop] = originalStyles[prop];\n    }\n}\nconst isVertical = (direction)=>{\n    switch(direction){\n        case 'top':\n        case 'bottom':\n            return true;\n        case 'left':\n        case 'right':\n            return false;\n        default:\n            return direction;\n    }\n};\nfunction getTranslate(element, direction) {\n    if (!element) {\n        return null;\n    }\n    const style = window.getComputedStyle(element);\n    const transform = // @ts-ignore\n    style.transform || style.webkitTransform || style.mozTransform;\n    let mat = transform.match(/^matrix3d\\((.+)\\)$/);\n    if (mat) {\n        // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix3d\n        return parseFloat(mat[1].split(', ')[isVertical(direction) ? 13 : 12]);\n    }\n    // https://developer.mozilla.org/en-US/docs/Web/CSS/transform-function/matrix\n    mat = transform.match(/^matrix\\((.+)\\)$/);\n    return mat ? parseFloat(mat[1].split(', ')[isVertical(direction) ? 5 : 4]) : null;\n}\nfunction dampenValue(v) {\n    return 8 * (Math.log(v + 1) - 2);\n}\nfunction assignStyle(element, style) {\n    if (!element) return ()=>{};\n    const prevStyle = element.style.cssText;\n    Object.assign(element.style, style);\n    return ()=>{\n        element.style.cssText = prevStyle;\n    };\n}\n/**\n * Receives functions as arguments and returns a new function that calls all.\n */ function chain(...fns) {\n    return (...args)=>{\n        for (const fn of fns){\n            if (typeof fn === 'function') {\n                // @ts-ignore\n                fn(...args);\n            }\n        }\n    };\n}\n\nconst TRANSITIONS = {\n    DURATION: 0.5,\n    EASE: [\n        0.32,\n        0.72,\n        0,\n        1\n    ]\n};\nconst VELOCITY_THRESHOLD = 0.4;\nconst CLOSE_THRESHOLD = 0.25;\nconst SCROLL_LOCK_TIMEOUT = 100;\nconst BORDER_RADIUS = 8;\nconst NESTED_DISPLACEMENT = 16;\nconst WINDOW_TOP_OFFSET = 26;\nconst DRAG_CLASS = 'vaul-dragging';\n\n// This code comes from https://github.com/radix-ui/primitives/blob/main/packages/react/use-controllable-state/src/useControllableState.tsx\nfunction useCallbackRef(callback) {\n    const callbackRef = React__default.useRef(callback);\n    React__default.useEffect(()=>{\n        callbackRef.current = callback;\n    });\n    // https://github.com/facebook/react/issues/19240\n    return React__default.useMemo(()=>(...args)=>callbackRef.current == null ? void 0 : callbackRef.current.call(callbackRef, ...args), []);\n}\nfunction useUncontrolledState({ defaultProp, onChange }) {\n    const uncontrolledState = React__default.useState(defaultProp);\n    const [value] = uncontrolledState;\n    const prevValueRef = React__default.useRef(value);\n    const handleChange = useCallbackRef(onChange);\n    React__default.useEffect(()=>{\n        if (prevValueRef.current !== value) {\n            handleChange(value);\n            prevValueRef.current = value;\n        }\n    }, [\n        value,\n        prevValueRef,\n        handleChange\n    ]);\n    return uncontrolledState;\n}\nfunction useControllableState({ prop, defaultProp, onChange = ()=>{} }) {\n    const [uncontrolledProp, setUncontrolledProp] = useUncontrolledState({\n        defaultProp,\n        onChange\n    });\n    const isControlled = prop !== undefined;\n    const value = isControlled ? prop : uncontrolledProp;\n    const handleChange = useCallbackRef(onChange);\n    const setValue = React__default.useCallback((nextValue)=>{\n        if (isControlled) {\n            const setter = nextValue;\n            const value = typeof nextValue === 'function' ? setter(prop) : nextValue;\n            if (value !== prop) handleChange(value);\n        } else {\n            setUncontrolledProp(nextValue);\n        }\n    }, [\n        isControlled,\n        prop,\n        setUncontrolledProp,\n        handleChange\n    ]);\n    return [\n        value,\n        setValue\n    ];\n}\n\nfunction useSnapPoints({ activeSnapPointProp, setActiveSnapPointProp, snapPoints, drawerRef, overlayRef, fadeFromIndex, onSnapPointChange, direction = 'bottom', container, snapToSequentialPoint }) {\n    const [activeSnapPoint, setActiveSnapPoint] = useControllableState({\n        prop: activeSnapPointProp,\n        defaultProp: snapPoints == null ? void 0 : snapPoints[0],\n        onChange: setActiveSnapPointProp\n    });\n    const [windowDimensions, setWindowDimensions] = React__default.useState(typeof window !== 'undefined' ? {\n        innerWidth: window.innerWidth,\n        innerHeight: window.innerHeight\n    } : undefined);\n    React__default.useEffect(()=>{\n        function onResize() {\n            setWindowDimensions({\n                innerWidth: window.innerWidth,\n                innerHeight: window.innerHeight\n            });\n        }\n        window.addEventListener('resize', onResize);\n        return ()=>window.removeEventListener('resize', onResize);\n    }, []);\n    const isLastSnapPoint = React__default.useMemo(()=>activeSnapPoint === (snapPoints == null ? void 0 : snapPoints[snapPoints.length - 1]) || null, [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const activeSnapPointIndex = React__default.useMemo(()=>snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPoint), [\n        snapPoints,\n        activeSnapPoint\n    ]);\n    const shouldFade = snapPoints && snapPoints.length > 0 && (fadeFromIndex || fadeFromIndex === 0) && !Number.isNaN(fadeFromIndex) && snapPoints[fadeFromIndex] === activeSnapPoint || !snapPoints;\n    const snapPointsOffset = React__default.useMemo(()=>{\n        const containerSize = container ? {\n            width: container.getBoundingClientRect().width,\n            height: container.getBoundingClientRect().height\n        } : typeof window !== 'undefined' ? {\n            width: window.innerWidth,\n            height: window.innerHeight\n        } : {\n            width: 0,\n            height: 0\n        };\n        var _snapPoints_map;\n        return (_snapPoints_map = snapPoints == null ? void 0 : snapPoints.map((snapPoint)=>{\n            const isPx = typeof snapPoint === 'string';\n            let snapPointAsNumber = 0;\n            if (isPx) {\n                snapPointAsNumber = parseInt(snapPoint, 10);\n            }\n            if (isVertical(direction)) {\n                const height = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.height : 0;\n                if (windowDimensions) {\n                    return direction === 'bottom' ? containerSize.height - height : -containerSize.height + height;\n                }\n                return height;\n            }\n            const width = isPx ? snapPointAsNumber : windowDimensions ? snapPoint * containerSize.width : 0;\n            if (windowDimensions) {\n                return direction === 'right' ? containerSize.width - width : -containerSize.width + width;\n            }\n            return width;\n        })) != null ? _snapPoints_map : [];\n    }, [\n        snapPoints,\n        windowDimensions,\n        container\n    ]);\n    const activeSnapPointOffset = React__default.useMemo(()=>activeSnapPointIndex !== null ? snapPointsOffset == null ? void 0 : snapPointsOffset[activeSnapPointIndex] : null, [\n        snapPointsOffset,\n        activeSnapPointIndex\n    ]);\n    const snapToPoint = React__default.useCallback((dimension)=>{\n        var _snapPointsOffset_findIndex;\n        const newSnapPointIndex = (_snapPointsOffset_findIndex = snapPointsOffset == null ? void 0 : snapPointsOffset.findIndex((snapPointDim)=>snapPointDim === dimension)) != null ? _snapPointsOffset_findIndex : null;\n        onSnapPointChange(newSnapPointIndex);\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            transform: isVertical(direction) ? `translate3d(0, ${dimension}px, 0)` : `translate3d(${dimension}px, 0, 0)`\n        });\n        if (snapPointsOffset && newSnapPointIndex !== snapPointsOffset.length - 1 && newSnapPointIndex !== fadeFromIndex && newSnapPointIndex < fadeFromIndex) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                opacity: '0'\n            });\n        } else {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                opacity: '1'\n            });\n        }\n        setActiveSnapPoint(snapPoints == null ? void 0 : snapPoints[Math.max(newSnapPointIndex, 0)]);\n    }, [\n        drawerRef.current,\n        snapPoints,\n        snapPointsOffset,\n        fadeFromIndex,\n        overlayRef,\n        setActiveSnapPoint\n    ]);\n    React__default.useEffect(()=>{\n        if (activeSnapPoint || activeSnapPointProp) {\n            var _snapPoints_findIndex;\n            const newIndex = (_snapPoints_findIndex = snapPoints == null ? void 0 : snapPoints.findIndex((snapPoint)=>snapPoint === activeSnapPointProp || snapPoint === activeSnapPoint)) != null ? _snapPoints_findIndex : -1;\n            if (snapPointsOffset && newIndex !== -1 && typeof snapPointsOffset[newIndex] === 'number') {\n                snapToPoint(snapPointsOffset[newIndex]);\n            }\n        }\n    }, [\n        activeSnapPoint,\n        activeSnapPointProp,\n        snapPoints,\n        snapPointsOffset,\n        snapToPoint\n    ]);\n    function onRelease({ draggedDistance, closeDrawer, velocity, dismissible }) {\n        if (fadeFromIndex === undefined) return;\n        const currentPosition = direction === 'bottom' || direction === 'right' ? (activeSnapPointOffset != null ? activeSnapPointOffset : 0) - draggedDistance : (activeSnapPointOffset != null ? activeSnapPointOffset : 0) + draggedDistance;\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isFirst = activeSnapPointIndex === 0;\n        const hasDraggedUp = draggedDistance > 0;\n        if (isOverlaySnapPoint) {\n            set(overlayRef.current, {\n                transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            });\n        }\n        if (!snapToSequentialPoint && velocity > 2 && !hasDraggedUp) {\n            if (dismissible) closeDrawer();\n            else snapToPoint(snapPointsOffset[0]); // snap to initial point\n            return;\n        }\n        if (!snapToSequentialPoint && velocity > 2 && hasDraggedUp && snapPointsOffset && snapPoints) {\n            snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n            return;\n        }\n        // Find the closest snap point to the current position\n        const closestSnapPoint = snapPointsOffset == null ? void 0 : snapPointsOffset.reduce((prev, curr)=>{\n            if (typeof prev !== 'number' || typeof curr !== 'number') return prev;\n            return Math.abs(curr - currentPosition) < Math.abs(prev - currentPosition) ? curr : prev;\n        });\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        if (velocity > VELOCITY_THRESHOLD && Math.abs(draggedDistance) < dim * 0.4) {\n            const dragDirection = hasDraggedUp ? 1 : -1; // 1 = up, -1 = down\n            // Don't do anything if we swipe upwards while being on the last snap point\n            if (dragDirection > 0 && isLastSnapPoint) {\n                snapToPoint(snapPointsOffset[snapPoints.length - 1]);\n                return;\n            }\n            if (isFirst && dragDirection < 0 && dismissible) {\n                closeDrawer();\n            }\n            if (activeSnapPointIndex === null) return;\n            snapToPoint(snapPointsOffset[activeSnapPointIndex + dragDirection]);\n            return;\n        }\n        snapToPoint(closestSnapPoint);\n    }\n    function onDrag({ draggedDistance }) {\n        if (activeSnapPointOffset === null) return;\n        const newValue = direction === 'bottom' || direction === 'right' ? activeSnapPointOffset - draggedDistance : activeSnapPointOffset + draggedDistance;\n        // Don't do anything if we exceed the last(biggest) snap point\n        if ((direction === 'bottom' || direction === 'right') && newValue < snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        if ((direction === 'top' || direction === 'left') && newValue > snapPointsOffset[snapPointsOffset.length - 1]) {\n            return;\n        }\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `translate3d(0, ${newValue}px, 0)` : `translate3d(${newValue}px, 0, 0)`\n        });\n    }\n    function getPercentageDragged(absDraggedDistance, isDraggingDown) {\n        if (!snapPoints || typeof activeSnapPointIndex !== 'number' || !snapPointsOffset || fadeFromIndex === undefined) return null;\n        // If this is true we are dragging to a snap point that is supposed to have an overlay\n        const isOverlaySnapPoint = activeSnapPointIndex === fadeFromIndex - 1;\n        const isOverlaySnapPointOrHigher = activeSnapPointIndex >= fadeFromIndex;\n        if (isOverlaySnapPointOrHigher && isDraggingDown) {\n            return 0;\n        }\n        // Don't animate, but still use this one if we are dragging away from the overlaySnapPoint\n        if (isOverlaySnapPoint && !isDraggingDown) return 1;\n        if (!shouldFade && !isOverlaySnapPoint) return null;\n        // Either fadeFrom index or the one before\n        const targetSnapPointIndex = isOverlaySnapPoint ? activeSnapPointIndex + 1 : activeSnapPointIndex - 1;\n        // Get the distance from overlaySnapPoint to the one before or vice-versa to calculate the opacity percentage accordingly\n        const snapPointDistance = isOverlaySnapPoint ? snapPointsOffset[targetSnapPointIndex] - snapPointsOffset[targetSnapPointIndex - 1] : snapPointsOffset[targetSnapPointIndex + 1] - snapPointsOffset[targetSnapPointIndex];\n        const percentageDragged = absDraggedDistance / Math.abs(snapPointDistance);\n        if (isOverlaySnapPoint) {\n            return 1 - percentageDragged;\n        } else {\n            return percentageDragged;\n        }\n    }\n    return {\n        isLastSnapPoint,\n        activeSnapPoint,\n        shouldFade,\n        getPercentageDragged,\n        setActiveSnapPoint,\n        activeSnapPointIndex,\n        onRelease,\n        onDrag,\n        snapPointsOffset\n    };\n}\n\nconst noop = ()=>()=>{};\nfunction useScaleBackground() {\n    const { direction, isOpen, shouldScaleBackground, setBackgroundColorOnScale, noBodyStyles } = useDrawerContext();\n    const timeoutIdRef = React__default.useRef(null);\n    const initialBackgroundColor = useMemo(()=>document.body.style.backgroundColor, []);\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    React__default.useEffect(()=>{\n        if (isOpen && shouldScaleBackground) {\n            if (timeoutIdRef.current) clearTimeout(timeoutIdRef.current);\n            const wrapper = document.querySelector('[data-vaul-drawer-wrapper]') || document.querySelector('[vaul-drawer-wrapper]');\n            if (!wrapper) return;\n            chain(setBackgroundColorOnScale && !noBodyStyles ? assignStyle(document.body, {\n                background: 'black'\n            }) : noop, assignStyle(wrapper, {\n                transformOrigin: isVertical(direction) ? 'top' : 'left',\n                transitionProperty: 'transform, border-radius',\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            }));\n            const wrapperStylesCleanup = assignStyle(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: 'hidden',\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`\n                }\n            });\n            return ()=>{\n                wrapperStylesCleanup();\n                timeoutIdRef.current = window.setTimeout(()=>{\n                    if (initialBackgroundColor) {\n                        document.body.style.background = initialBackgroundColor;\n                    } else {\n                        document.body.style.removeProperty('background');\n                    }\n                }, TRANSITIONS.DURATION * 1000);\n            };\n        }\n    }, [\n        isOpen,\n        shouldScaleBackground,\n        initialBackgroundColor\n    ]);\n}\n\nlet previousBodyPosition = null;\n/**\n * This hook is necessary to prevent buggy behavior on iOS devices (need to test on Android).\n * I won't get into too much detail about what bugs it solves, but so far I've found that setting the body to `position: fixed` is the most reliable way to prevent those bugs.\n * Issues that this hook solves:\n * https://github.com/emilkowalski/vaul/issues/435\n * https://github.com/emilkowalski/vaul/issues/433\n * And more that I discovered, but were just not reported.\n */ function usePositionFixed({ isOpen, modal, nested, hasBeenOpened, preventScrollRestoration, noBodyStyles }) {\n    const [activeUrl, setActiveUrl] = React__default.useState(()=>typeof window !== 'undefined' ? window.location.href : '');\n    const scrollPos = React__default.useRef(0);\n    const setPositionFixed = React__default.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        // If previousBodyPosition is already set, don't set it again.\n        if (previousBodyPosition === null && isOpen && !noBodyStyles) {\n            previousBodyPosition = {\n                position: document.body.style.position,\n                top: document.body.style.top,\n                left: document.body.style.left,\n                height: document.body.style.height,\n                right: 'unset'\n            };\n            // Update the dom inside an animation frame\n            const { scrollX, innerHeight } = window;\n            document.body.style.setProperty('position', 'fixed', 'important');\n            Object.assign(document.body.style, {\n                top: `${-scrollPos.current}px`,\n                left: `${-scrollX}px`,\n                right: '0px',\n                height: 'auto'\n            });\n            window.setTimeout(()=>window.requestAnimationFrame(()=>{\n                    // Attempt to check if the bottom bar appeared due to the position change\n                    const bottomBarHeight = innerHeight - window.innerHeight;\n                    if (bottomBarHeight && scrollPos.current >= innerHeight) {\n                        // Move the content further up so that the bottom bar doesn't hide it\n                        document.body.style.top = `${-(scrollPos.current + bottomBarHeight)}px`;\n                    }\n                }), 300);\n        }\n    }, [\n        isOpen\n    ]);\n    const restorePositionSetting = React__default.useCallback(()=>{\n        // All browsers on iOS will return true here.\n        if (!isSafari()) return;\n        if (previousBodyPosition !== null && !noBodyStyles) {\n            // Convert the position from \"px\" to Int\n            const y = -parseInt(document.body.style.top, 10);\n            const x = -parseInt(document.body.style.left, 10);\n            // Restore styles\n            Object.assign(document.body.style, previousBodyPosition);\n            window.requestAnimationFrame(()=>{\n                if (preventScrollRestoration && activeUrl !== window.location.href) {\n                    setActiveUrl(window.location.href);\n                    return;\n                }\n                window.scrollTo(x, y);\n            });\n            previousBodyPosition = null;\n        }\n    }, [\n        activeUrl\n    ]);\n    React__default.useEffect(()=>{\n        function onScroll() {\n            scrollPos.current = window.scrollY;\n        }\n        onScroll();\n        window.addEventListener('scroll', onScroll);\n        return ()=>{\n            window.removeEventListener('scroll', onScroll);\n        };\n    }, []);\n    React__default.useEffect(()=>{\n        if (nested || !hasBeenOpened) return;\n        // This is needed to force Safari toolbar to show **before** the drawer starts animating to prevent a gnarly shift from happening\n        if (isOpen) {\n            // avoid for standalone mode (PWA)\n            const isStandalone = window.matchMedia('(display-mode: standalone)').matches;\n            !isStandalone && setPositionFixed();\n            if (!modal) {\n                window.setTimeout(()=>{\n                    restorePositionSetting();\n                }, 500);\n            }\n        } else {\n            restorePositionSetting();\n        }\n    }, [\n        isOpen,\n        hasBeenOpened,\n        activeUrl,\n        modal,\n        nested,\n        setPositionFixed,\n        restorePositionSetting\n    ]);\n    return {\n        restorePositionSetting\n    };\n}\n\nfunction Root({ open: openProp, onOpenChange, children, onDrag: onDragProp, onRelease: onReleaseProp, snapPoints, shouldScaleBackground = false, setBackgroundColorOnScale = true, closeThreshold = CLOSE_THRESHOLD, scrollLockTimeout = SCROLL_LOCK_TIMEOUT, dismissible = true, handleOnly = false, fadeFromIndex = snapPoints && snapPoints.length - 1, activeSnapPoint: activeSnapPointProp, setActiveSnapPoint: setActiveSnapPointProp, fixed, modal = true, onClose, nested, noBodyStyles, direction = 'bottom', defaultOpen = false, disablePreventScroll = true, snapToSequentialPoint = false, preventScrollRestoration = false, repositionInputs = true, onAnimationEnd, container, autoFocus = false }) {\n    var _drawerRef_current, _drawerRef_current1;\n    const [isOpen = false, setIsOpen] = useControllableState({\n        defaultProp: defaultOpen,\n        prop: openProp,\n        onChange: (o)=>{\n            onOpenChange == null ? void 0 : onOpenChange(o);\n            if (!o && !nested) {\n                restorePositionSetting();\n            }\n            setTimeout(()=>{\n                onAnimationEnd == null ? void 0 : onAnimationEnd(o);\n            }, TRANSITIONS.DURATION * 1000);\n            if (o && !modal) {\n                if (typeof window !== 'undefined') {\n                    window.requestAnimationFrame(()=>{\n                        document.body.style.pointerEvents = 'auto';\n                    });\n                }\n            }\n            if (!o) {\n                // This will be removed when the exit animation ends (`500ms`)\n                document.body.style.pointerEvents = 'auto';\n            }\n        }\n    });\n    const [hasBeenOpened, setHasBeenOpened] = React__default.useState(false);\n    const [isDragging, setIsDragging] = React__default.useState(false);\n    const [justReleased, setJustReleased] = React__default.useState(false);\n    const overlayRef = React__default.useRef(null);\n    const openTime = React__default.useRef(null);\n    const dragStartTime = React__default.useRef(null);\n    const dragEndTime = React__default.useRef(null);\n    const lastTimeDragPrevented = React__default.useRef(null);\n    const isAllowedToDrag = React__default.useRef(false);\n    const nestedOpenChangeTimer = React__default.useRef(null);\n    const pointerStart = React__default.useRef(0);\n    const keyboardIsOpen = React__default.useRef(false);\n    const previousDiffFromInitial = React__default.useRef(0);\n    const drawerRef = React__default.useRef(null);\n    const drawerHeightRef = React__default.useRef(((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0);\n    const drawerWidthRef = React__default.useRef(((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0);\n    const initialDrawerHeight = React__default.useRef(0);\n    const onSnapPointChange = React__default.useCallback((activeSnapPointIndex)=>{\n        // Change openTime ref when we reach the last snap point to prevent dragging for 500ms incase it's scrollable.\n        if (snapPoints && activeSnapPointIndex === snapPointsOffset.length - 1) openTime.current = new Date();\n    }, []);\n    const { activeSnapPoint, activeSnapPointIndex, setActiveSnapPoint, onRelease: onReleaseSnapPoints, snapPointsOffset, onDrag: onDragSnapPoints, shouldFade, getPercentageDragged: getSnapPointsPercentageDragged } = useSnapPoints({\n        snapPoints,\n        activeSnapPointProp,\n        setActiveSnapPointProp,\n        drawerRef,\n        fadeFromIndex,\n        overlayRef,\n        onSnapPointChange,\n        direction,\n        container,\n        snapToSequentialPoint\n    });\n    usePreventScroll({\n        isDisabled: !isOpen || isDragging || !modal || justReleased || !hasBeenOpened || !repositionInputs || !disablePreventScroll\n    });\n    const { restorePositionSetting } = usePositionFixed({\n        isOpen,\n        modal,\n        nested,\n        hasBeenOpened,\n        preventScrollRestoration,\n        noBodyStyles\n    });\n    function getScale() {\n        return (window.innerWidth - WINDOW_TOP_OFFSET) / window.innerWidth;\n    }\n    function onPress(event) {\n        var _drawerRef_current, _drawerRef_current1;\n        if (!dismissible && !snapPoints) return;\n        if (drawerRef.current && !drawerRef.current.contains(event.target)) return;\n        drawerHeightRef.current = ((_drawerRef_current = drawerRef.current) == null ? void 0 : _drawerRef_current.getBoundingClientRect().height) || 0;\n        drawerWidthRef.current = ((_drawerRef_current1 = drawerRef.current) == null ? void 0 : _drawerRef_current1.getBoundingClientRect().width) || 0;\n        setIsDragging(true);\n        dragStartTime.current = new Date();\n        // iOS doesn't trigger mouseUp after scrolling so we need to listen to touched in order to disallow dragging\n        if (isIOS()) {\n            window.addEventListener('touchend', ()=>isAllowedToDrag.current = false, {\n                once: true\n            });\n        }\n        // Ensure we maintain correct pointer capture even when going outside of the drawer\n        event.target.setPointerCapture(event.pointerId);\n        pointerStart.current = isVertical(direction) ? event.pageY : event.pageX;\n    }\n    function shouldDrag(el, isDraggingInDirection) {\n        var _window_getSelection, _lastTimeDragPrevented_current;\n        let element = el;\n        const highlightedText = (_window_getSelection = window.getSelection()) == null ? void 0 : _window_getSelection.toString();\n        const swipeAmount = drawerRef.current ? getTranslate(drawerRef.current, direction) : null;\n        const date = new Date();\n        if (element.hasAttribute('data-vaul-no-drag') || element.closest('[data-vaul-no-drag]')) {\n            return false;\n        }\n        if (direction === 'right' || direction === 'left') {\n            return true;\n        }\n        // Allow scrolling when animating\n        if (openTime.current && date.getTime() - openTime.current.getTime() < 500) {\n            return false;\n        }\n        if (swipeAmount !== null) {\n            if (direction === 'bottom' ? swipeAmount > 0 : swipeAmount < 0) {\n                return true;\n            }\n        }\n        // Don't drag if there's highlighted text\n        if (highlightedText && highlightedText.length > 0) {\n            return false;\n        }\n        // Disallow dragging if drawer was scrolled within `scrollLockTimeout`\n        if (date.getTime() - ((_lastTimeDragPrevented_current = lastTimeDragPrevented.current) == null ? void 0 : _lastTimeDragPrevented_current.getTime()) < scrollLockTimeout && swipeAmount === 0) {\n            lastTimeDragPrevented.current = date;\n            return false;\n        }\n        if (isDraggingInDirection) {\n            lastTimeDragPrevented.current = date;\n            // We are dragging down so we should allow scrolling\n            return false;\n        }\n        // Keep climbing up the DOM tree as long as there's a parent\n        while(element){\n            // Check if the element is scrollable\n            if (element.scrollHeight > element.clientHeight) {\n                if (element.scrollTop !== 0) {\n                    lastTimeDragPrevented.current = new Date();\n                    // The element is scrollable and not scrolled to the top, so don't drag\n                    return false;\n                }\n                if (element.getAttribute('role') === 'dialog') {\n                    return true;\n                }\n            }\n            // Move up to the parent element\n            element = element.parentNode;\n        }\n        // No scrollable parents not scrolled to the top found, so drag\n        return true;\n    }\n    function onDrag(event) {\n        if (!drawerRef.current) {\n            return;\n        }\n        // We need to know how much of the drawer has been dragged in percentages so that we can transform background accordingly\n        if (isDragging) {\n            const directionMultiplier = direction === 'bottom' || direction === 'right' ? 1 : -1;\n            const draggedDistance = (pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX)) * directionMultiplier;\n            const isDraggingInDirection = draggedDistance > 0;\n            // Pre condition for disallowing dragging in the close direction.\n            const noCloseSnapPointsPreCondition = snapPoints && !dismissible && !isDraggingInDirection;\n            // Disallow dragging down to close when first snap point is the active one and dismissible prop is set to false.\n            if (noCloseSnapPointsPreCondition && activeSnapPointIndex === 0) return;\n            // We need to capture last time when drag with scroll was triggered and have a timeout between\n            const absDraggedDistance = Math.abs(draggedDistance);\n            const wrapper = document.querySelector('[data-vaul-drawer-wrapper]');\n            const drawerDimension = direction === 'bottom' || direction === 'top' ? drawerHeightRef.current : drawerWidthRef.current;\n            // Calculate the percentage dragged, where 1 is the closed position\n            let percentageDragged = absDraggedDistance / drawerDimension;\n            const snapPointPercentageDragged = getSnapPointsPercentageDragged(absDraggedDistance, isDraggingInDirection);\n            if (snapPointPercentageDragged !== null) {\n                percentageDragged = snapPointPercentageDragged;\n            }\n            // Disallow close dragging beyond the smallest snap point.\n            if (noCloseSnapPointsPreCondition && percentageDragged >= 1) {\n                return;\n            }\n            if (!isAllowedToDrag.current && !shouldDrag(event.target, isDraggingInDirection)) return;\n            drawerRef.current.classList.add(DRAG_CLASS);\n            // If shouldDrag gave true once after pressing down on the drawer, we set isAllowedToDrag to true and it will remain true until we let go, there's no reason to disable dragging mid way, ever, and that's the solution to it\n            isAllowedToDrag.current = true;\n            set(drawerRef.current, {\n                transition: 'none'\n            });\n            set(overlayRef.current, {\n                transition: 'none'\n            });\n            if (snapPoints) {\n                onDragSnapPoints({\n                    draggedDistance\n                });\n            }\n            // Run this only if snapPoints are not defined or if we are at the last snap point (highest one)\n            if (isDraggingInDirection && !snapPoints) {\n                const dampenedDraggedDistance = dampenValue(draggedDistance);\n                const translateValue = Math.min(dampenedDraggedDistance * -1, 0) * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n                return;\n            }\n            const opacityValue = 1 - percentageDragged;\n            if (shouldFade || fadeFromIndex && activeSnapPointIndex === fadeFromIndex - 1) {\n                onDragProp == null ? void 0 : onDragProp(event, percentageDragged);\n                set(overlayRef.current, {\n                    opacity: `${opacityValue}`,\n                    transition: 'none'\n                }, true);\n            }\n            if (wrapper && overlayRef.current && shouldScaleBackground) {\n                // Calculate percentageDragged as a fraction (0 to 1)\n                const scaleValue = Math.min(getScale() + percentageDragged * (1 - getScale()), 1);\n                const borderRadiusValue = 8 - percentageDragged * 8;\n                const translateValue = Math.max(0, 14 - percentageDragged * 14);\n                set(wrapper, {\n                    borderRadius: `${borderRadiusValue}px`,\n                    transform: isVertical(direction) ? `scale(${scaleValue}) translate3d(0, ${translateValue}px, 0)` : `scale(${scaleValue}) translate3d(${translateValue}px, 0, 0)`,\n                    transition: 'none'\n                }, true);\n            }\n            if (!snapPoints) {\n                const translateValue = absDraggedDistance * directionMultiplier;\n                set(drawerRef.current, {\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }\n        }\n    }\n    React__default.useEffect(()=>{\n        var _window_visualViewport;\n        function onVisualViewportChange() {\n            if (!drawerRef.current || !repositionInputs) return;\n            const focusedElement = document.activeElement;\n            if (isInput(focusedElement) || keyboardIsOpen.current) {\n                var _window_visualViewport;\n                const visualViewportHeight = ((_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.height) || 0;\n                const totalHeight = window.innerHeight;\n                // This is the height of the keyboard\n                let diffFromInitial = totalHeight - visualViewportHeight;\n                const drawerHeight = drawerRef.current.getBoundingClientRect().height || 0;\n                // Adjust drawer height only if it's tall enough\n                const isTallEnough = drawerHeight > totalHeight * 0.8;\n                if (!initialDrawerHeight.current) {\n                    initialDrawerHeight.current = drawerHeight;\n                }\n                const offsetFromTop = drawerRef.current.getBoundingClientRect().top;\n                // visualViewport height may change due to somq e subtle changes to the keyboard. Checking if the height changed by 60 or more will make sure that they keyboard really changed its open state.\n                if (Math.abs(previousDiffFromInitial.current - diffFromInitial) > 60) {\n                    keyboardIsOpen.current = !keyboardIsOpen.current;\n                }\n                if (snapPoints && snapPoints.length > 0 && snapPointsOffset && activeSnapPointIndex) {\n                    const activeSnapPointHeight = snapPointsOffset[activeSnapPointIndex] || 0;\n                    diffFromInitial += activeSnapPointHeight;\n                }\n                previousDiffFromInitial.current = diffFromInitial;\n                // We don't have to change the height if the input is in view, when we are here we are in the opened keyboard state so we can correctly check if the input is in view\n                if (drawerHeight > visualViewportHeight || keyboardIsOpen.current) {\n                    const height = drawerRef.current.getBoundingClientRect().height;\n                    let newDrawerHeight = height;\n                    if (height > visualViewportHeight) {\n                        newDrawerHeight = visualViewportHeight - (isTallEnough ? offsetFromTop : WINDOW_TOP_OFFSET);\n                    }\n                    // When fixed, don't move the drawer upwards if there's space, but rather only change it's height so it's fully scrollable when the keyboard is open\n                    if (fixed) {\n                        drawerRef.current.style.height = `${height - Math.max(diffFromInitial, 0)}px`;\n                    } else {\n                        drawerRef.current.style.height = `${Math.max(newDrawerHeight, visualViewportHeight - offsetFromTop)}px`;\n                    }\n                } else {\n                    drawerRef.current.style.height = `${initialDrawerHeight.current}px`;\n                }\n                if (snapPoints && snapPoints.length > 0 && !keyboardIsOpen.current) {\n                    drawerRef.current.style.bottom = `0px`;\n                } else {\n                    // Negative bottom value would never make sense\n                    drawerRef.current.style.bottom = `${Math.max(diffFromInitial, 0)}px`;\n                }\n            }\n        }\n        (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.addEventListener('resize', onVisualViewportChange);\n        return ()=>{\n            var _window_visualViewport;\n            return (_window_visualViewport = window.visualViewport) == null ? void 0 : _window_visualViewport.removeEventListener('resize', onVisualViewportChange);\n        };\n    }, [\n        activeSnapPointIndex,\n        snapPoints,\n        snapPointsOffset\n    ]);\n    function closeDrawer(fromWithin) {\n        cancelDrag();\n        onClose == null ? void 0 : onClose();\n        if (!fromWithin) {\n            setIsOpen(false);\n        }\n        setTimeout(()=>{\n            if (snapPoints) {\n                setActiveSnapPoint(snapPoints[0]);\n            }\n        }, TRANSITIONS.DURATION * 1000); // seconds to ms\n    }\n    function resetDrawer() {\n        if (!drawerRef.current) return;\n        const wrapper = document.querySelector('[data-vaul-drawer-wrapper]');\n        const currentSwipeAmount = getTranslate(drawerRef.current, direction);\n        set(drawerRef.current, {\n            transform: 'translate3d(0, 0, 0)',\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n        });\n        set(overlayRef.current, {\n            transition: `opacity ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            opacity: '1'\n        });\n        // Don't reset background if swiped upwards\n        if (shouldScaleBackground && currentSwipeAmount && currentSwipeAmount > 0 && isOpen) {\n            set(wrapper, {\n                borderRadius: `${BORDER_RADIUS}px`,\n                overflow: 'hidden',\n                ...isVertical(direction) ? {\n                    transform: `scale(${getScale()}) translate3d(0, calc(env(safe-area-inset-top) + 14px), 0)`,\n                    transformOrigin: 'top'\n                } : {\n                    transform: `scale(${getScale()}) translate3d(calc(env(safe-area-inset-top) + 14px), 0, 0)`,\n                    transformOrigin: 'left'\n                },\n                transitionProperty: 'transform, border-radius',\n                transitionDuration: `${TRANSITIONS.DURATION}s`,\n                transitionTimingFunction: `cubic-bezier(${TRANSITIONS.EASE.join(',')})`\n            }, true);\n        }\n    }\n    function cancelDrag() {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n    }\n    function onRelease(event) {\n        if (!isDragging || !drawerRef.current) return;\n        drawerRef.current.classList.remove(DRAG_CLASS);\n        isAllowedToDrag.current = false;\n        setIsDragging(false);\n        dragEndTime.current = new Date();\n        const swipeAmount = getTranslate(drawerRef.current, direction);\n        if (!shouldDrag(event.target, false) || !swipeAmount || Number.isNaN(swipeAmount)) return;\n        if (dragStartTime.current === null) return;\n        const timeTaken = dragEndTime.current.getTime() - dragStartTime.current.getTime();\n        const distMoved = pointerStart.current - (isVertical(direction) ? event.pageY : event.pageX);\n        const velocity = Math.abs(distMoved) / timeTaken;\n        if (velocity > 0.05) {\n            // `justReleased` is needed to prevent the drawer from focusing on an input when the drag ends, as it's not the intent most of the time.\n            setJustReleased(true);\n            setTimeout(()=>{\n                setJustReleased(false);\n            }, 200);\n        }\n        if (snapPoints) {\n            const directionMultiplier = direction === 'bottom' || direction === 'right' ? 1 : -1;\n            onReleaseSnapPoints({\n                draggedDistance: distMoved * directionMultiplier,\n                closeDrawer,\n                velocity,\n                dismissible\n            });\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        // Moved upwards, don't do anything\n        if (direction === 'bottom' || direction === 'right' ? distMoved > 0 : distMoved < 0) {\n            resetDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n            return;\n        }\n        if (velocity > VELOCITY_THRESHOLD) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        var _drawerRef_current_getBoundingClientRect_height;\n        const visibleDrawerHeight = Math.min((_drawerRef_current_getBoundingClientRect_height = drawerRef.current.getBoundingClientRect().height) != null ? _drawerRef_current_getBoundingClientRect_height : 0, window.innerHeight);\n        var _drawerRef_current_getBoundingClientRect_width;\n        const visibleDrawerWidth = Math.min((_drawerRef_current_getBoundingClientRect_width = drawerRef.current.getBoundingClientRect().width) != null ? _drawerRef_current_getBoundingClientRect_width : 0, window.innerWidth);\n        const isHorizontalSwipe = direction === 'left' || direction === 'right';\n        if (Math.abs(swipeAmount) >= (isHorizontalSwipe ? visibleDrawerWidth : visibleDrawerHeight) * closeThreshold) {\n            closeDrawer();\n            onReleaseProp == null ? void 0 : onReleaseProp(event, false);\n            return;\n        }\n        onReleaseProp == null ? void 0 : onReleaseProp(event, true);\n        resetDrawer();\n    }\n    React__default.useEffect(()=>{\n        // Trigger enter animation without using CSS animation\n        if (isOpen) {\n            set(document.documentElement, {\n                scrollBehavior: 'auto'\n            });\n            openTime.current = new Date();\n        }\n        return ()=>{\n            reset(document.documentElement, 'scrollBehavior');\n        };\n    }, [\n        isOpen\n    ]);\n    function onNestedOpenChange(o) {\n        const scale = o ? (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth : 1;\n        const y = o ? -NESTED_DISPLACEMENT : 0;\n        if (nestedOpenChangeTimer.current) {\n            window.clearTimeout(nestedOpenChangeTimer.current);\n        }\n        set(drawerRef.current, {\n            transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n            transform: `scale(${scale}) translate3d(0, ${y}px, 0)`\n        });\n        if (!o && drawerRef.current) {\n            nestedOpenChangeTimer.current = setTimeout(()=>{\n                const translateValue = getTranslate(drawerRef.current, direction);\n                set(drawerRef.current, {\n                    transition: 'none',\n                    transform: isVertical(direction) ? `translate3d(0, ${translateValue}px, 0)` : `translate3d(${translateValue}px, 0, 0)`\n                });\n            }, 500);\n        }\n    }\n    function onNestedDrag(_event, percentageDragged) {\n        if (percentageDragged < 0) return;\n        const initialScale = (window.innerWidth - NESTED_DISPLACEMENT) / window.innerWidth;\n        const newScale = initialScale + percentageDragged * (1 - initialScale);\n        const newTranslate = -NESTED_DISPLACEMENT + percentageDragged * NESTED_DISPLACEMENT;\n        set(drawerRef.current, {\n            transform: isVertical(direction) ? `scale(${newScale}) translate3d(0, ${newTranslate}px, 0)` : `scale(${newScale}) translate3d(${newTranslate}px, 0, 0)`,\n            transition: 'none'\n        });\n    }\n    function onNestedRelease(_event, o) {\n        const dim = isVertical(direction) ? window.innerHeight : window.innerWidth;\n        const scale = o ? (dim - NESTED_DISPLACEMENT) / dim : 1;\n        const translate = o ? -NESTED_DISPLACEMENT : 0;\n        if (o) {\n            set(drawerRef.current, {\n                transition: `transform ${TRANSITIONS.DURATION}s cubic-bezier(${TRANSITIONS.EASE.join(',')})`,\n                transform: isVertical(direction) ? `scale(${scale}) translate3d(0, ${translate}px, 0)` : `scale(${scale}) translate3d(${translate}px, 0, 0)`\n            });\n        }\n    }\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Root, {\n        defaultOpen: defaultOpen,\n        onOpenChange: (open)=>{\n            if (!dismissible && !open) return;\n            if (open) {\n                setHasBeenOpened(true);\n            } else {\n                closeDrawer(true);\n            }\n            setIsOpen(open);\n        },\n        open: isOpen\n    }, /*#__PURE__*/ React__default.createElement(DrawerContext.Provider, {\n        value: {\n            activeSnapPoint,\n            snapPoints,\n            setActiveSnapPoint,\n            drawerRef,\n            overlayRef,\n            onOpenChange,\n            onPress,\n            onRelease,\n            onDrag,\n            dismissible,\n            handleOnly,\n            isOpen,\n            isDragging,\n            shouldFade,\n            closeDrawer,\n            onNestedDrag,\n            onNestedOpenChange,\n            onNestedRelease,\n            keyboardIsOpen,\n            modal,\n            snapPointsOffset,\n            direction,\n            shouldScaleBackground,\n            setBackgroundColorOnScale,\n            noBodyStyles,\n            container,\n            autoFocus\n        }\n    }, children));\n}\nconst Overlay = /*#__PURE__*/ React__default.forwardRef(function({ ...rest }, ref) {\n    const { overlayRef, snapPoints, onRelease, shouldFade, isOpen, modal } = useDrawerContext();\n    const composedRef = useComposedRefs(ref, overlayRef);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    // Overlay is the component that is locking scroll, removing it will unlock the scroll without having to dig into Radix's Dialog library\n    if (!modal) {\n        // Need to do this manually unfortunately\n        if (typeof window !== 'undefined') {\n            window.requestAnimationFrame(()=>{\n                document.body.style.pointerEvents = 'auto';\n            });\n        }\n        return null;\n    }\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Overlay, {\n        onMouseUp: onRelease,\n        ref: composedRef,\n        \"data-vaul-overlay\": \"\",\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? 'true' : 'false',\n        \"data-vaul-snap-points-overlay\": isOpen && shouldFade ? 'true' : 'false',\n        ...rest\n    });\n});\nOverlay.displayName = 'Drawer.Overlay';\nconst Content = /*#__PURE__*/ React__default.forwardRef(function({ onPointerDownOutside, style, onOpenAutoFocus, ...rest }, ref) {\n    const { drawerRef, onPress, onRelease, onDrag, keyboardIsOpen, snapPointsOffset, modal, isOpen, direction, snapPoints, container, handleOnly, autoFocus } = useDrawerContext();\n    // Needed to use transition instead of animations\n    const [delayedSnapPoints, setDelayedSnapPoints] = React__default.useState(false);\n    const composedRef = useComposedRefs(ref, drawerRef);\n    const pointerStartRef = React__default.useRef(null);\n    const lastKnownPointerEventRef = React__default.useRef(null);\n    const wasBeyondThePointRef = React__default.useRef(false);\n    const hasSnapPoints = snapPoints && snapPoints.length > 0;\n    useScaleBackground();\n    const isDeltaInDirection = (delta, direction, threshold = 0)=>{\n        if (wasBeyondThePointRef.current) return true;\n        const deltaY = Math.abs(delta.y);\n        const deltaX = Math.abs(delta.x);\n        const isDeltaX = deltaX > deltaY;\n        const dFactor = [\n            'bottom',\n            'right'\n        ].includes(direction) ? 1 : -1;\n        if (direction === 'left' || direction === 'right') {\n            const isReverseDirection = delta.x * dFactor < 0;\n            if (!isReverseDirection && deltaX >= 0 && deltaX <= threshold) {\n                return isDeltaX;\n            }\n        } else {\n            const isReverseDirection = delta.y * dFactor < 0;\n            if (!isReverseDirection && deltaY >= 0 && deltaY <= threshold) {\n                return !isDeltaX;\n            }\n        }\n        wasBeyondThePointRef.current = true;\n        return true;\n    };\n    React__default.useEffect(()=>{\n        if (hasSnapPoints) {\n            window.requestAnimationFrame(()=>{\n                setDelayedSnapPoints(true);\n            });\n        }\n    }, []);\n    function handleOnPointerUp(event) {\n        pointerStartRef.current = null;\n        wasBeyondThePointRef.current = false;\n        onRelease(event);\n    }\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Content, {\n        \"data-vaul-drawer-direction\": direction,\n        \"data-vaul-drawer\": \"\",\n        \"data-vaul-delayed-snap-points\": delayedSnapPoints ? 'true' : 'false',\n        \"data-vaul-snap-points\": isOpen && hasSnapPoints ? 'true' : 'false',\n        \"data-vaul-custom-container\": container ? 'true' : 'false',\n        ...rest,\n        ref: composedRef,\n        style: snapPointsOffset && snapPointsOffset.length > 0 ? {\n            '--snap-point-height': `${snapPointsOffset[0]}px`,\n            ...style\n        } : style,\n        onPointerDown: (event)=>{\n            if (handleOnly) return;\n            rest.onPointerDown == null ? void 0 : rest.onPointerDown.call(rest, event);\n            pointerStartRef.current = {\n                x: event.pageX,\n                y: event.pageY\n            };\n            onPress(event);\n        },\n        onOpenAutoFocus: (e)=>{\n            onOpenAutoFocus == null ? void 0 : onOpenAutoFocus(e);\n            if (!autoFocus) {\n                e.preventDefault();\n            }\n        },\n        onPointerDownOutside: (e)=>{\n            onPointerDownOutside == null ? void 0 : onPointerDownOutside(e);\n            if (!modal || e.defaultPrevented) {\n                e.preventDefault();\n                return;\n            }\n            if (keyboardIsOpen.current) {\n                keyboardIsOpen.current = false;\n            }\n        },\n        onFocusOutside: (e)=>{\n            if (!modal) {\n                e.preventDefault();\n                return;\n            }\n        },\n        onPointerMove: (event)=>{\n            lastKnownPointerEventRef.current = event;\n            if (handleOnly) return;\n            rest.onPointerMove == null ? void 0 : rest.onPointerMove.call(rest, event);\n            if (!pointerStartRef.current) return;\n            const yPosition = event.pageY - pointerStartRef.current.y;\n            const xPosition = event.pageX - pointerStartRef.current.x;\n            const swipeStartThreshold = event.pointerType === 'touch' ? 10 : 2;\n            const delta = {\n                x: xPosition,\n                y: yPosition\n            };\n            const isAllowedToSwipe = isDeltaInDirection(delta, direction, swipeStartThreshold);\n            if (isAllowedToSwipe) onDrag(event);\n            else if (Math.abs(xPosition) > swipeStartThreshold || Math.abs(yPosition) > swipeStartThreshold) {\n                pointerStartRef.current = null;\n            }\n        },\n        onPointerUp: (event)=>{\n            rest.onPointerUp == null ? void 0 : rest.onPointerUp.call(rest, event);\n            pointerStartRef.current = null;\n            wasBeyondThePointRef.current = false;\n            onRelease(event);\n        },\n        onPointerOut: (event)=>{\n            rest.onPointerOut == null ? void 0 : rest.onPointerOut.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        },\n        onContextMenu: (event)=>{\n            rest.onContextMenu == null ? void 0 : rest.onContextMenu.call(rest, event);\n            handleOnPointerUp(lastKnownPointerEventRef.current);\n        }\n    });\n});\nContent.displayName = 'Drawer.Content';\nconst LONG_HANDLE_PRESS_TIMEOUT = 250;\nconst DOUBLE_TAP_TIMEOUT = 120;\nconst Handle = /*#__PURE__*/ React__default.forwardRef(function({ preventCycle = false, children, ...rest }, ref) {\n    const { closeDrawer, isDragging, snapPoints, activeSnapPoint, setActiveSnapPoint, dismissible, handleOnly, isOpen, onPress, onDrag } = useDrawerContext();\n    const closeTimeoutIdRef = React__default.useRef(null);\n    const shouldCancelInteractionRef = React__default.useRef(false);\n    function handleStartCycle() {\n        // Stop if this is the second click of a double click\n        if (shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        window.setTimeout(()=>{\n            handleCycleSnapPoints();\n        }, DOUBLE_TAP_TIMEOUT);\n    }\n    function handleCycleSnapPoints() {\n        // Prevent accidental taps while resizing drawer\n        if (isDragging || preventCycle || shouldCancelInteractionRef.current) {\n            handleCancelInteraction();\n            return;\n        }\n        // Make sure to clear the timeout id if the user releases the handle before the cancel timeout\n        handleCancelInteraction();\n        if ((!snapPoints || snapPoints.length === 0) && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const isLastSnapPoint = activeSnapPoint === snapPoints[snapPoints.length - 1];\n        if (isLastSnapPoint && dismissible) {\n            closeDrawer();\n            return;\n        }\n        const currentSnapIndex = snapPoints.findIndex((point)=>point === activeSnapPoint);\n        if (currentSnapIndex === -1) return; // activeSnapPoint not found in snapPoints\n        const nextSnapPoint = snapPoints[currentSnapIndex + 1];\n        setActiveSnapPoint(nextSnapPoint);\n    }\n    function handleStartInteraction() {\n        closeTimeoutIdRef.current = window.setTimeout(()=>{\n            // Cancel click interaction on a long press\n            shouldCancelInteractionRef.current = true;\n        }, LONG_HANDLE_PRESS_TIMEOUT);\n    }\n    function handleCancelInteraction() {\n        window.clearTimeout(closeTimeoutIdRef.current);\n        shouldCancelInteractionRef.current = false;\n    }\n    return /*#__PURE__*/ React__default.createElement(\"div\", {\n        onClick: handleStartCycle,\n        onPointerCancel: handleCancelInteraction,\n        onPointerDown: (e)=>{\n            if (handleOnly) onPress(e);\n            handleStartInteraction();\n        },\n        onPointerMove: (e)=>{\n            if (handleOnly) onDrag(e);\n        },\n        // onPointerUp is already handled by the content component\n        ref: ref,\n        \"data-vaul-drawer-visible\": isOpen ? 'true' : 'false',\n        \"data-vaul-handle\": \"\",\n        \"aria-hidden\": \"true\",\n        ...rest\n    }, /*#__PURE__*/ React__default.createElement(\"span\", {\n        \"data-vaul-handle-hitarea\": \"\",\n        \"aria-hidden\": \"true\"\n    }, children));\n});\nHandle.displayName = 'Drawer.Handle';\nfunction NestedRoot({ onDrag, onOpenChange, ...rest }) {\n    const { onNestedDrag, onNestedOpenChange, onNestedRelease } = useDrawerContext();\n    if (!onNestedDrag) {\n        throw new Error('Drawer.NestedRoot must be placed in another drawer');\n    }\n    return /*#__PURE__*/ React__default.createElement(Root, {\n        nested: true,\n        onClose: ()=>{\n            onNestedOpenChange(false);\n        },\n        onDrag: (e, p)=>{\n            onNestedDrag(e, p);\n            onDrag == null ? void 0 : onDrag(e, p);\n        },\n        onOpenChange: (o)=>{\n            if (o) {\n                onNestedOpenChange(o);\n            }\n        },\n        onRelease: onNestedRelease,\n        ...rest\n    });\n}\nfunction Portal(props) {\n    const context = useDrawerContext();\n    const { container = context.container, ...portalProps } = props;\n    return /*#__PURE__*/ React__default.createElement(DialogPrimitive.Portal, {\n        container: container,\n        ...portalProps\n    });\n}\nconst Drawer = {\n    Root,\n    NestedRoot,\n    Content,\n    Overlay,\n    Trigger: DialogPrimitive.Trigger,\n    Portal,\n    Handle,\n    Close: DialogPrimitive.Close,\n    Title: DialogPrimitive.Title,\n    Description: DialogPrimitive.Description\n};\n\nexport { Content, Drawer, Handle, NestedRoot, Overlay, Portal, Root };\n"], "names": [], "mappings": ";;;;;;;;;AAUA;AACA;AAXA;AACA,SAAS,YAAY,IAAI;IACvB,IAAI,CAAC,QAAQ,OAAO,YAAY,aAAa;IAC7C,IAAI,OAAO,SAAS,IAAI,IAAI,SAAS,oBAAoB,CAAC,OAAO,CAAC,EAAE;IACpE,IAAI,QAAQ,SAAS,aAAa,CAAC;IACnC,MAAM,IAAI,GAAG;IACb,KAAK,WAAW,CAAC;IAChB,MAAM,UAAU,GAAI,MAAM,UAAU,CAAC,OAAO,GAAG,OAAQ,MAAM,WAAW,CAAC,SAAS,cAAc,CAAC;AACpG;;;;AAMA,MAAM,gBAAgB,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC;IAC/C,WAAW;QACP,SAAS;IACb;IACA,YAAY;QACR,SAAS;IACb;IACA,SAAS,KAAK;IACd,WAAW,KAAK;IAChB,QAAQ,KAAK;IACb,cAAc,KAAK;IACnB,oBAAoB,KAAK;IACzB,iBAAiB,KAAK;IACtB,UAAU;IACV,aAAa;IACb,QAAQ;IACR,YAAY;IACZ,gBAAgB;QACZ,SAAS;IACb;IACA,kBAAkB;IAClB,YAAY;IACZ,YAAY;IACZ,OAAO;IACP,YAAY;IACZ,iBAAiB;IACjB,cAAc,KAAK;IACnB,oBAAoB,KAAK;IACzB,aAAa,KAAK;IAClB,WAAW;IACX,uBAAuB;IACvB,2BAA2B;IAC3B,cAAc;IACd,WAAW;IACX,WAAW;AACf;AACA,MAAM,mBAAmB;IACrB,MAAM,UAAU,qMAAA,CAAA,UAAc,CAAC,UAAU,CAAC;IAC1C,IAAI,CAAC,SAAS;QACV,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX;AAEA,YAAY;AAEZ,iIAAiI;AACjI,MAAM,kBAAkB;AACxB,MAAM,4BAA4B,OAAO,WAAW,cAAc,qMAAA,CAAA,kBAAe,GAAG,qMAAA,CAAA,YAAS;AAC7F,SAAS,QAAQ,GAAG,SAAS;IACzB,OAAO,CAAC,GAAG;QACP,KAAK,IAAI,YAAY,UAAU;YAC3B,IAAI,OAAO,aAAa,YAAY;gBAChC,YAAY;YAChB;QACJ;IACJ;AACJ;AACA,SAAS;IACL,OAAO,aAAa;AACxB;AACA,SAAS;IACL,OAAO,aAAa;AACxB;AACA,SAAS;IACL,OAAO,iCAAiC,IAAI,CAAC,UAAU,SAAS;AACpE;AACA,SAAS;IACL,OAAO,aAAa,YAAY,yFAAyF;IACzH,WAAW,UAAU,cAAc,GAAG;AAC1C;AACA,SAAS;IACL,OAAO,cAAc;AACzB;AACA,SAAS,aAAa,EAAE;IACpB,OAAO,OAAO,WAAW,eAAe,OAAO,SAAS,IAAI,OAAO,GAAG,IAAI,CAAC,OAAO,SAAS,CAAC,QAAQ,IAAI;AAC5G;AACA,aAAa;AACb,MAAM,iBAAiB,OAAO,aAAa,eAAe,OAAO,cAAc;AAC/E,SAAS,aAAa,IAAI;IACtB,IAAI,QAAQ,OAAO,gBAAgB,CAAC;IACpC,OAAO,gBAAgB,IAAI,CAAC,MAAM,QAAQ,GAAG,MAAM,SAAS,GAAG,MAAM,SAAS;AAClF;AACA,SAAS,gBAAgB,IAAI;IACzB,IAAI,aAAa,OAAO;QACpB,OAAO,KAAK,aAAa;IAC7B;IACA,MAAM,QAAQ,CAAC,aAAa,MAAM;QAC9B,OAAO,KAAK,aAAa;IAC7B;IACA,OAAO,QAAQ,SAAS,gBAAgB,IAAI,SAAS,eAAe;AACxE;AACA,sEAAsE;AACtE,MAAM,oBAAoB,IAAI,IAAI;IAC9B;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;CACH;AACD,mIAAmI;AACnI,IAAI,qBAAqB;AACzB,IAAI;AACJ;;;;CAIC,GAAG,SAAS,iBAAiB,UAAU,CAAC,CAAC;IACtC,IAAI,EAAE,UAAU,EAAE,GAAG;IACrB,0BAA0B;QACtB,IAAI,YAAY;YACZ;QACJ;QACA;QACA,IAAI,uBAAuB,GAAG;YAC1B,IAAI,SAAS;gBACT,UAAU;YACd;QACJ;QACA,OAAO;YACH;YACA,IAAI,uBAAuB,GAAG;gBAC1B,WAAW,OAAO,KAAK,IAAI;YAC/B;QACJ;IACJ,GAAG;QACC;KACH;AACL;AACA,wEAAwE;AACxE,gDAAgD;AAChD,EAAE;AACF,8FAA8F;AAC9F,sGAAsG;AACtG,mCAAmC;AACnC,6GAA6G;AAC7G,2EAA2E;AAC3E,4GAA4G;AAC5G,sGAAsG;AACtG,EAAE;AACF,oGAAoG;AACpG,EAAE;AACF,+GAA+G;AAC/G,oBAAoB;AACpB,0GAA0G;AAC1G,kGAAkG;AAClG,uGAAuG;AACvG,qGAAqG;AACrG,4GAA4G;AAC5G,4DAA4D;AAC5D,kHAAkH;AAClH,0GAA0G;AAC1G,oFAAoF;AACpF,gHAAgH;AAChH,oFAAoF;AACpF,SAAS;IACL,IAAI;IACJ,IAAI,QAAQ;IACZ,IAAI,eAAe,CAAC;QAChB,sFAAsF;QACtF,aAAa,gBAAgB,EAAE,MAAM;QACrC,IAAI,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,EAAE;YACzE;QACJ;QACA,QAAQ,EAAE,cAAc,CAAC,EAAE,CAAC,KAAK;IACrC;IACA,IAAI,cAAc,CAAC;QACf,gCAAgC;QAChC,IAAI,CAAC,cAAc,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,EAAE;YACxF,EAAE,cAAc;YAChB;QACJ;QACA,6EAA6E;QAC7E,4EAA4E;QAC5E,4EAA4E;QAC5E,uCAAuC;QACvC,IAAI,IAAI,EAAE,cAAc,CAAC,EAAE,CAAC,KAAK;QACjC,IAAI,YAAY,WAAW,SAAS;QACpC,IAAI,SAAS,WAAW,YAAY,GAAG,WAAW,YAAY;QAC9D,IAAI,WAAW,GAAG;YACd;QACJ;QACA,IAAI,aAAa,KAAK,IAAI,SAAS,aAAa,UAAU,IAAI,OAAO;YACjE,EAAE,cAAc;QACpB;QACA,QAAQ;IACZ;IACA,IAAI,aAAa,CAAC;QACd,IAAI,SAAS,EAAE,MAAM;QACrB,uEAAuE;QACvE,IAAI,QAAQ,WAAW,WAAW,SAAS,aAAa,EAAE;YACtD,EAAE,cAAc;YAChB,sFAAsF;YACtF,oFAAoF;YACpF,+EAA+E;YAC/E,OAAO,KAAK,CAAC,SAAS,GAAG;YACzB,OAAO,KAAK;YACZ,sBAAsB;gBAClB,OAAO,KAAK,CAAC,SAAS,GAAG;YAC7B;QACJ;IACJ;IACA,IAAI,UAAU,CAAC;QACX,IAAI,SAAS,EAAE,MAAM;QACrB,IAAI,QAAQ,SAAS;YACjB,mFAAmF;YACnF,iFAAiF;YACjF,wFAAwF;YACxF,+FAA+F;YAC/F,OAAO,KAAK,CAAC,SAAS,GAAG;YACzB,sBAAsB;gBAClB,OAAO,KAAK,CAAC,SAAS,GAAG;gBACzB,qFAAqF;gBACrF,wFAAwF;gBACxF,IAAI,gBAAgB;oBAChB,IAAI,eAAe,MAAM,GAAG,OAAO,WAAW,EAAE;wBAC5C,yEAAyE;wBACzE,2CAA2C;wBAC3C,sBAAsB;4BAClB,eAAe;wBACnB;oBACJ,OAAO;wBACH,+EAA+E;wBAC/E,6CAA6C;wBAC7C,eAAe,gBAAgB,CAAC,UAAU,IAAI,eAAe,SAAS;4BAClE,MAAM;wBACV;oBACJ;gBACJ;YACJ;QACJ;IACJ;IACA,IAAI,iBAAiB;QACjB,kEAAkE;QAClE,2FAA2F;QAC3F,OAAO,QAAQ,CAAC,GAAG;IACvB;IACA,4DAA4D;IAC5D,0FAA0F;IAC1F,6FAA6F;IAC7F,IAAI,UAAU,OAAO,WAAW;IAChC,IAAI,UAAU,OAAO,WAAW;IAChC,IAAI,gBAAgB,QAAQ,SAAS,SAAS,eAAe,EAAE,gBAAgB,GAAG,OAAO,UAAU,GAAG,SAAS,eAAe,CAAC,WAAW,CAAC,EAAE,CAAC;IAC9I,qFAAqF;IACrF,OAAO,QAAQ,CAAC,GAAG;IACnB,IAAI,eAAe,QAAQ,SAAS,UAAU,cAAc,cAAc;QACtE,SAAS;QACT,SAAS;IACb,IAAI,SAAS,UAAU,aAAa,aAAa;QAC7C,SAAS;QACT,SAAS;IACb,IAAI,SAAS,UAAU,YAAY,YAAY;QAC3C,SAAS;QACT,SAAS;IACb,IAAI,SAAS,UAAU,SAAS,SAAS,OAAO,SAAS,QAAQ,UAAU;IAC3E,OAAO;QACH,2DAA2D;QAC3D;QACA;QACA,OAAO,QAAQ,CAAC,SAAS;IAC7B;AACJ;AACA,gGAAgG;AAChG,SAAS,SAAS,OAAO,EAAE,KAAK,EAAE,KAAK;IACnC,IAAI,MAAM,QAAQ,KAAK,CAAC,MAAM;IAC9B,QAAQ,KAAK,CAAC,MAAM,GAAG;IACvB,OAAO;QACH,QAAQ,KAAK,CAAC,MAAM,GAAG;IAC3B;AACJ;AACA,6EAA6E;AAC7E,SAAS,SAAS,MAAM,EAAE,KAAK,EAAE,OAAO,EAAE,OAAO;IAC7C,aAAa;IACb,OAAO,gBAAgB,CAAC,OAAO,SAAS;IACxC,OAAO;QACH,aAAa;QACb,OAAO,mBAAmB,CAAC,OAAO,SAAS;IAC/C;AACJ;AACA,SAAS,eAAe,MAAM;IAC1B,IAAI,OAAO,SAAS,gBAAgB,IAAI,SAAS,eAAe;IAChE,MAAM,UAAU,WAAW,KAAK;QAC5B,0GAA0G;QAC1G,IAAI,aAAa,gBAAgB;QACjC,IAAI,eAAe,SAAS,eAAe,IAAI,eAAe,SAAS,IAAI,IAAI,eAAe,QAAQ;YAClG,IAAI,gBAAgB,WAAW,qBAAqB,GAAG,GAAG;YAC1D,IAAI,YAAY,OAAO,qBAAqB,GAAG,GAAG;YAClD,IAAI,eAAe,OAAO,qBAAqB,GAAG,MAAM;YACxD,uCAAuC;YACvC,MAAM,iBAAiB,WAAW,qBAAqB,GAAG,MAAM,GAAG;YACnE,IAAI,eAAe,gBAAgB;gBAC/B,WAAW,SAAS,IAAI,YAAY;YACxC;QACJ;QACA,aAAa;QACb,SAAS,WAAW,aAAa;IACrC;AACJ;AACA,SAAS,QAAQ,MAAM;IACnB,OAAO,kBAAkB,oBAAoB,CAAC,kBAAkB,GAAG,CAAC,OAAO,IAAI,KAAK,kBAAkB,uBAAuB,kBAAkB,eAAe,OAAO,iBAAiB;AAC1L;AAEA,oGAAoG;AACpG;;;CAGC,GAAG,SAAS,OAAO,GAAG,EAAE,KAAK;IAC1B,IAAI,OAAO,QAAQ,YAAY;QAC3B,IAAI;IACR,OAAO,IAAI,QAAQ,QAAQ,QAAQ,WAAW;QAC1C,IAAI,OAAO,GAAG;IAClB;AACJ;AACA;;;CAGC,GAAG,SAAS,YAAY,GAAG,IAAI;IAC5B,OAAO,CAAC,OAAO,KAAK,OAAO,CAAC,CAAC,MAAM,OAAO,KAAK;AACnD;AACA;;;CAGC,GAAG,SAAS,gBAAgB,GAAG,IAAI;IAChC,uDAAuD;IACvD,OAAO,CAAA,GAAA,qMAAA,CAAA,cAAiB,AAAD,EAAE,eAAe,OAAO;AACnD;AAEA,MAAM,QAAQ,IAAI;AAClB,SAAS,IAAI,EAAE,EAAE,MAAM,EAAE,cAAc,KAAK;IACxC,IAAI,CAAC,MAAM,CAAC,CAAC,cAAc,WAAW,GAAG;IACzC,IAAI,iBAAiB,CAAC;IACtB,OAAO,OAAO,CAAC,QAAQ,OAAO,CAAC,CAAC,CAAC,KAAK,MAAM;QACxC,IAAI,IAAI,UAAU,CAAC,OAAO;YACtB,GAAG,KAAK,CAAC,WAAW,CAAC,KAAK;YAC1B;QACJ;QACA,cAAc,CAAC,IAAI,GAAG,GAAG,KAAK,CAAC,IAAI;QACnC,GAAG,KAAK,CAAC,IAAI,GAAG;IACpB;IACA,IAAI,aAAa;IACjB,MAAM,GAAG,CAAC,IAAI;AAClB;AACA,SAAS,MAAM,EAAE,EAAE,IAAI;IACnB,IAAI,CAAC,MAAM,CAAC,CAAC,cAAc,WAAW,GAAG;IACzC,IAAI,iBAAiB,MAAM,GAAG,CAAC;IAC/B,IAAI,CAAC,gBAAgB;QACjB;IACJ;IACA;QACI,GAAG,KAAK,CAAC,KAAK,GAAG,cAAc,CAAC,KAAK;IACzC;AACJ;AACA,MAAM,aAAa,CAAC;IAChB,OAAO;QACH,KAAK;QACL,KAAK;YACD,OAAO;QACX,KAAK;QACL,KAAK;YACD,OAAO;QACX;YACI,OAAO;IACf;AACJ;AACA,SAAS,aAAa,OAAO,EAAE,SAAS;IACpC,IAAI,CAAC,SAAS;QACV,OAAO;IACX;IACA,MAAM,QAAQ,OAAO,gBAAgB,CAAC;IACtC,MAAM,YACN,MAAM,SAAS,IAAI,MAAM,eAAe,IAAI,MAAM,YAAY;IAC9D,IAAI,MAAM,UAAU,KAAK,CAAC;IAC1B,IAAI,KAAK;QACL,+EAA+E;QAC/E,OAAO,WAAW,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,aAAa,KAAK,GAAG;IACzE;IACA,6EAA6E;IAC7E,MAAM,UAAU,KAAK,CAAC;IACtB,OAAO,MAAM,WAAW,GAAG,CAAC,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC,WAAW,aAAa,IAAI,EAAE,IAAI;AACjF;AACA,SAAS,YAAY,CAAC;IAClB,OAAO,IAAI,CAAC,KAAK,GAAG,CAAC,IAAI,KAAK,CAAC;AACnC;AACA,SAAS,YAAY,OAAO,EAAE,KAAK;IAC/B,IAAI,CAAC,SAAS,OAAO,KAAK;IAC1B,MAAM,YAAY,QAAQ,KAAK,CAAC,OAAO;IACvC,OAAO,MAAM,CAAC,QAAQ,KAAK,EAAE;IAC7B,OAAO;QACH,QAAQ,KAAK,CAAC,OAAO,GAAG;IAC5B;AACJ;AACA;;CAEC,GAAG,SAAS,MAAM,GAAG,GAAG;IACrB,OAAO,CAAC,GAAG;QACP,KAAK,MAAM,MAAM,IAAI;YACjB,IAAI,OAAO,OAAO,YAAY;gBAC1B,aAAa;gBACb,MAAM;YACV;QACJ;IACJ;AACJ;AAEA,MAAM,cAAc;IAChB,UAAU;IACV,MAAM;QACF;QACA;QACA;QACA;KACH;AACL;AACA,MAAM,qBAAqB;AAC3B,MAAM,kBAAkB;AACxB,MAAM,sBAAsB;AAC5B,MAAM,gBAAgB;AACtB,MAAM,sBAAsB;AAC5B,MAAM,oBAAoB;AAC1B,MAAM,aAAa;AAEnB,2IAA2I;AAC3I,SAAS,eAAe,QAAQ;IAC5B,MAAM,cAAc,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC1C,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,YAAY,OAAO,GAAG;IAC1B;IACA,iDAAiD;IACjD,OAAO,qMAAA,CAAA,UAAc,CAAC,OAAO,CAAC,IAAI,CAAC,GAAG,OAAO,YAAY,OAAO,IAAI,OAAO,KAAK,IAAI,YAAY,OAAO,CAAC,IAAI,CAAC,gBAAgB,OAAO,EAAE;AAC1I;AACA,SAAS,qBAAqB,EAAE,WAAW,EAAE,QAAQ,EAAE;IACnD,MAAM,oBAAoB,qMAAA,CAAA,UAAc,CAAC,QAAQ,CAAC;IAClD,MAAM,CAAC,MAAM,GAAG;IAChB,MAAM,eAAe,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC3C,MAAM,eAAe,eAAe;IACpC,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,IAAI,aAAa,OAAO,KAAK,OAAO;YAChC,aAAa;YACb,aAAa,OAAO,GAAG;QAC3B;IACJ,GAAG;QACC;QACA;QACA;KACH;IACD,OAAO;AACX;AACA,SAAS,qBAAqB,EAAE,IAAI,EAAE,WAAW,EAAE,WAAW,KAAK,CAAC,EAAE;IAClE,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qBAAqB;QACjE;QACA;IACJ;IACA,MAAM,eAAe,SAAS;IAC9B,MAAM,QAAQ,eAAe,OAAO;IACpC,MAAM,eAAe,eAAe;IACpC,MAAM,WAAW,qMAAA,CAAA,UAAc,CAAC,WAAW,CAAC,CAAC;QACzC,IAAI,cAAc;YACd,MAAM,SAAS;YACf,MAAM,QAAQ,OAAO,cAAc,aAAa,OAAO,QAAQ;YAC/D,IAAI,UAAU,MAAM,aAAa;QACrC,OAAO;YACH,oBAAoB;QACxB;IACJ,GAAG;QACC;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;QACA;KACH;AACL;AAEA,SAAS,cAAc,EAAE,mBAAmB,EAAE,sBAAsB,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,aAAa,EAAE,iBAAiB,EAAE,YAAY,QAAQ,EAAE,SAAS,EAAE,qBAAqB,EAAE;IAC/L,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,qBAAqB;QAC/D,MAAM;QACN,aAAa,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,EAAE;QACxD,UAAU;IACd;IACA,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,qMAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,OAAO,WAAW,cAAc;QACpG,YAAY,OAAO,UAAU;QAC7B,aAAa,OAAO,WAAW;IACnC,IAAI;IACJ,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,SAAS;YACL,oBAAoB;gBAChB,YAAY,OAAO,UAAU;gBAC7B,aAAa,OAAO,WAAW;YACnC;QACJ;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO,IAAI,OAAO,mBAAmB,CAAC,UAAU;IACpD,GAAG,EAAE;IACL,MAAM,kBAAkB,qMAAA,CAAA,UAAc,CAAC,OAAO,CAAC,IAAI,oBAAoB,CAAC,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE,KAAK,MAAM;QAC9I;QACA;KACH;IACD,MAAM,uBAAuB,qMAAA,CAAA,UAAc,CAAC,OAAO,CAAC,IAAI,cAAc,OAAO,KAAK,IAAI,WAAW,SAAS,CAAC,CAAC,YAAY,cAAc,kBAAkB;QACpJ;QACA;KACH;IACD,MAAM,aAAa,cAAc,WAAW,MAAM,GAAG,KAAK,CAAC,iBAAiB,kBAAkB,CAAC,KAAK,CAAC,OAAO,KAAK,CAAC,kBAAkB,UAAU,CAAC,cAAc,KAAK,mBAAmB,CAAC;IACtL,MAAM,mBAAmB,qMAAA,CAAA,UAAc,CAAC,OAAO,CAAC;QAC5C,MAAM,gBAAgB,YAAY;YAC9B,OAAO,UAAU,qBAAqB,GAAG,KAAK;YAC9C,QAAQ,UAAU,qBAAqB,GAAG,MAAM;QACpD,IAAI,OAAO,WAAW,cAAc;YAChC,OAAO,OAAO,UAAU;YACxB,QAAQ,OAAO,WAAW;QAC9B,IAAI;YACA,OAAO;YACP,QAAQ;QACZ;QACA,IAAI;QACJ,OAAO,CAAC,kBAAkB,cAAc,OAAO,KAAK,IAAI,WAAW,GAAG,CAAC,CAAC;YACpE,MAAM,OAAO,OAAO,cAAc;YAClC,IAAI,oBAAoB;YACxB,IAAI,MAAM;gBACN,oBAAoB,SAAS,WAAW;YAC5C;YACA,IAAI,WAAW,YAAY;gBACvB,MAAM,SAAS,OAAO,oBAAoB,mBAAmB,YAAY,cAAc,MAAM,GAAG;gBAChG,IAAI,kBAAkB;oBAClB,OAAO,cAAc,WAAW,cAAc,MAAM,GAAG,SAAS,CAAC,cAAc,MAAM,GAAG;gBAC5F;gBACA,OAAO;YACX;YACA,MAAM,QAAQ,OAAO,oBAAoB,mBAAmB,YAAY,cAAc,KAAK,GAAG;YAC9F,IAAI,kBAAkB;gBAClB,OAAO,cAAc,UAAU,cAAc,KAAK,GAAG,QAAQ,CAAC,cAAc,KAAK,GAAG;YACxF;YACA,OAAO;QACX,EAAE,KAAK,OAAO,kBAAkB,EAAE;IACtC,GAAG;QACC;QACA;QACA;KACH;IACD,MAAM,wBAAwB,qMAAA,CAAA,UAAc,CAAC,OAAO,CAAC,IAAI,yBAAyB,OAAO,oBAAoB,OAAO,KAAK,IAAI,gBAAgB,CAAC,qBAAqB,GAAG,MAAM;QACxK;QACA;KACH;IACD,MAAM,cAAc,qMAAA,CAAA,UAAc,CAAC,WAAW,CAAC,CAAC;QAC5C,IAAI;QACJ,MAAM,oBAAoB,CAAC,8BAA8B,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,SAAS,CAAC,CAAC,eAAe,iBAAiB,UAAU,KAAK,OAAO,8BAA8B;QAC7M,kBAAkB;QAClB,IAAI,UAAU,OAAO,EAAE;YACnB,YAAY,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5F,WAAW,WAAW,aAAa,CAAC,eAAe,EAAE,UAAU,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,UAAU,SAAS,CAAC;QAChH;QACA,IAAI,oBAAoB,sBAAsB,iBAAiB,MAAM,GAAG,KAAK,sBAAsB,iBAAiB,oBAAoB,eAAe;YACnJ,IAAI,WAAW,OAAO,EAAE;gBACpB,YAAY,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1F,SAAS;YACb;QACJ,OAAO;YACH,IAAI,WAAW,OAAO,EAAE;gBACpB,YAAY,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC1F,SAAS;YACb;QACJ;QACA,mBAAmB,cAAc,OAAO,KAAK,IAAI,UAAU,CAAC,KAAK,GAAG,CAAC,mBAAmB,GAAG;IAC/F,GAAG;QACC,UAAU,OAAO;QACjB;QACA;QACA;QACA;QACA;KACH;IACD,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,IAAI,mBAAmB,qBAAqB;YACxC,IAAI;YACJ,MAAM,WAAW,CAAC,wBAAwB,cAAc,OAAO,KAAK,IAAI,WAAW,SAAS,CAAC,CAAC,YAAY,cAAc,uBAAuB,cAAc,gBAAgB,KAAK,OAAO,wBAAwB,CAAC;YAClN,IAAI,oBAAoB,aAAa,CAAC,KAAK,OAAO,gBAAgB,CAAC,SAAS,KAAK,UAAU;gBACvF,YAAY,gBAAgB,CAAC,SAAS;YAC1C;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;KACH;IACD,SAAS,UAAU,EAAE,eAAe,EAAE,WAAW,EAAE,QAAQ,EAAE,WAAW,EAAE;QACtE,IAAI,kBAAkB,WAAW;QACjC,MAAM,kBAAkB,cAAc,YAAY,cAAc,UAAU,CAAC,yBAAyB,OAAO,wBAAwB,CAAC,IAAI,kBAAkB,CAAC,yBAAyB,OAAO,wBAAwB,CAAC,IAAI;QACxN,MAAM,qBAAqB,yBAAyB,gBAAgB;QACpE,MAAM,UAAU,yBAAyB;QACzC,MAAM,eAAe,kBAAkB;QACvC,IAAI,oBAAoB;YACpB,IAAI,WAAW,OAAO,EAAE;gBACpB,YAAY,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC9F;QACJ;QACA,IAAI,CAAC,yBAAyB,WAAW,KAAK,CAAC,cAAc;YACzD,IAAI,aAAa;iBACZ,YAAY,gBAAgB,CAAC,EAAE,GAAG,wBAAwB;YAC/D;QACJ;QACA,IAAI,CAAC,yBAAyB,WAAW,KAAK,gBAAgB,oBAAoB,YAAY;YAC1F,YAAY,gBAAgB,CAAC,WAAW,MAAM,GAAG,EAAE;YACnD;QACJ;QACA,sDAAsD;QACtD,MAAM,mBAAmB,oBAAoB,OAAO,KAAK,IAAI,iBAAiB,MAAM,CAAC,CAAC,MAAM;YACxF,IAAI,OAAO,SAAS,YAAY,OAAO,SAAS,UAAU,OAAO;YACjE,OAAO,KAAK,GAAG,CAAC,OAAO,mBAAmB,KAAK,GAAG,CAAC,OAAO,mBAAmB,OAAO;QACxF;QACA,MAAM,MAAM,WAAW,aAAa,OAAO,WAAW,GAAG,OAAO,UAAU;QAC1E,IAAI,WAAW,sBAAsB,KAAK,GAAG,CAAC,mBAAmB,MAAM,KAAK;YACxE,MAAM,gBAAgB,eAAe,IAAI,CAAC,GAAG,oBAAoB;YACjE,2EAA2E;YAC3E,IAAI,gBAAgB,KAAK,iBAAiB;gBACtC,YAAY,gBAAgB,CAAC,WAAW,MAAM,GAAG,EAAE;gBACnD;YACJ;YACA,IAAI,WAAW,gBAAgB,KAAK,aAAa;gBAC7C;YACJ;YACA,IAAI,yBAAyB,MAAM;YACnC,YAAY,gBAAgB,CAAC,uBAAuB,cAAc;YAClE;QACJ;QACA,YAAY;IAChB;IACA,SAAS,OAAO,EAAE,eAAe,EAAE;QAC/B,IAAI,0BAA0B,MAAM;QACpC,MAAM,WAAW,cAAc,YAAY,cAAc,UAAU,wBAAwB,kBAAkB,wBAAwB;QACrI,8DAA8D;QAC9D,IAAI,CAAC,cAAc,YAAY,cAAc,OAAO,KAAK,WAAW,gBAAgB,CAAC,iBAAiB,MAAM,GAAG,EAAE,EAAE;YAC/G;QACJ;QACA,IAAI,CAAC,cAAc,SAAS,cAAc,MAAM,KAAK,WAAW,gBAAgB,CAAC,iBAAiB,MAAM,GAAG,EAAE,EAAE;YAC3G;QACJ;QACA,IAAI,UAAU,OAAO,EAAE;YACnB,WAAW,WAAW,aAAa,CAAC,eAAe,EAAE,SAAS,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,SAAS,SAAS,CAAC;QAC9G;IACJ;IACA,SAAS,qBAAqB,kBAAkB,EAAE,cAAc;QAC5D,IAAI,CAAC,cAAc,OAAO,yBAAyB,YAAY,CAAC,oBAAoB,kBAAkB,WAAW,OAAO;QACxH,sFAAsF;QACtF,MAAM,qBAAqB,yBAAyB,gBAAgB;QACpE,MAAM,6BAA6B,wBAAwB;QAC3D,IAAI,8BAA8B,gBAAgB;YAC9C,OAAO;QACX;QACA,0FAA0F;QAC1F,IAAI,sBAAsB,CAAC,gBAAgB,OAAO;QAClD,IAAI,CAAC,cAAc,CAAC,oBAAoB,OAAO;QAC/C,0CAA0C;QAC1C,MAAM,uBAAuB,qBAAqB,uBAAuB,IAAI,uBAAuB;QACpG,yHAAyH;QACzH,MAAM,oBAAoB,qBAAqB,gBAAgB,CAAC,qBAAqB,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,GAAG,gBAAgB,CAAC,uBAAuB,EAAE,GAAG,gBAAgB,CAAC,qBAAqB;QACxN,MAAM,oBAAoB,qBAAqB,KAAK,GAAG,CAAC;QACxD,IAAI,oBAAoB;YACpB,OAAO,IAAI;QACf,OAAO;YACH,OAAO;QACX;IACJ;IACA,OAAO;QACH;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;AACJ;AAEA,MAAM,OAAO,IAAI,KAAK;AACtB,SAAS;IACL,MAAM,EAAE,SAAS,EAAE,MAAM,EAAE,qBAAqB,EAAE,yBAAyB,EAAE,YAAY,EAAE,GAAG;IAC9F,MAAM,eAAe,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC3C,MAAM,yBAAyB,CAAA,GAAA,qMAAA,CAAA,UAAO,AAAD,EAAE,IAAI,SAAS,IAAI,CAAC,KAAK,CAAC,eAAe,EAAE,EAAE;IAClF,SAAS;QACL,OAAO,CAAC,OAAO,UAAU,GAAG,iBAAiB,IAAI,OAAO,UAAU;IACtE;IACA,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,IAAI,UAAU,uBAAuB;YACjC,IAAI,aAAa,OAAO,EAAE,aAAa,aAAa,OAAO;YAC3D,MAAM,UAAU,SAAS,aAAa,CAAC,iCAAiC,SAAS,aAAa,CAAC;YAC/F,IAAI,CAAC,SAAS;YACd,MAAM,6BAA6B,CAAC,eAAe,YAAY,SAAS,IAAI,EAAE;gBAC1E,YAAY;YAChB,KAAK,MAAM,YAAY,SAAS;gBAC5B,iBAAiB,WAAW,aAAa,QAAQ;gBACjD,oBAAoB;gBACpB,oBAAoB,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC;gBAC9C,0BAA0B,CAAC,aAAa,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3E;YACA,MAAM,uBAAuB,YAAY,SAAS;gBAC9C,cAAc,GAAG,cAAc,EAAE,CAAC;gBAClC,UAAU;gBACV,GAAG,WAAW,aAAa;oBACvB,WAAW,CAAC,MAAM,EAAE,WAAW,0DAA0D,CAAC;gBAC9F,IAAI;oBACA,WAAW,CAAC,MAAM,EAAE,WAAW,0DAA0D,CAAC;gBAC9F,CAAC;YACL;YACA,OAAO;gBACH;gBACA,aAAa,OAAO,GAAG,OAAO,UAAU,CAAC;oBACrC,IAAI,wBAAwB;wBACxB,SAAS,IAAI,CAAC,KAAK,CAAC,UAAU,GAAG;oBACrC,OAAO;wBACH,SAAS,IAAI,CAAC,KAAK,CAAC,cAAc,CAAC;oBACvC;gBACJ,GAAG,YAAY,QAAQ,GAAG;YAC9B;QACJ;IACJ,GAAG;QACC;QACA;QACA;KACH;AACL;AAEA,IAAI,uBAAuB;AAC3B;;;;;;;CAOC,GAAG,SAAS,iBAAiB,EAAE,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,wBAAwB,EAAE,YAAY,EAAE;IAC1G,MAAM,CAAC,WAAW,aAAa,GAAG,qMAAA,CAAA,UAAc,CAAC,QAAQ,CAAC,IAAI,OAAO,WAAW,cAAc,OAAO,QAAQ,CAAC,IAAI,GAAG;IACrH,MAAM,YAAY,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACxC,MAAM,mBAAmB,qMAAA,CAAA,UAAc,CAAC,WAAW,CAAC;QAChD,6CAA6C;QAC7C,IAAI,CAAC,YAAY;QACjB,8DAA8D;QAC9D,IAAI,yBAAyB,QAAQ,UAAU,CAAC,cAAc;YAC1D,uBAAuB;gBACnB,UAAU,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ;gBACtC,KAAK,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG;gBAC5B,MAAM,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI;gBAC9B,QAAQ,SAAS,IAAI,CAAC,KAAK,CAAC,MAAM;gBAClC,OAAO;YACX;YACA,2CAA2C;YAC3C,MAAM,EAAE,OAAO,EAAE,WAAW,EAAE,GAAG;YACjC,SAAS,IAAI,CAAC,KAAK,CAAC,WAAW,CAAC,YAAY,SAAS;YACrD,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE;gBAC/B,KAAK,GAAG,CAAC,UAAU,OAAO,CAAC,EAAE,CAAC;gBAC9B,MAAM,GAAG,CAAC,QAAQ,EAAE,CAAC;gBACrB,OAAO;gBACP,QAAQ;YACZ;YACA,OAAO,UAAU,CAAC,IAAI,OAAO,qBAAqB,CAAC;oBAC3C,yEAAyE;oBACzE,MAAM,kBAAkB,cAAc,OAAO,WAAW;oBACxD,IAAI,mBAAmB,UAAU,OAAO,IAAI,aAAa;wBACrD,qEAAqE;wBACrE,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,UAAU,OAAO,GAAG,eAAe,EAAE,EAAE,CAAC;oBAC3E;gBACJ,IAAI;QACZ;IACJ,GAAG;QACC;KACH;IACD,MAAM,yBAAyB,qMAAA,CAAA,UAAc,CAAC,WAAW,CAAC;QACtD,6CAA6C;QAC7C,IAAI,CAAC,YAAY;QACjB,IAAI,yBAAyB,QAAQ,CAAC,cAAc;YAChD,wCAAwC;YACxC,MAAM,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK,CAAC,GAAG,EAAE;YAC7C,MAAM,IAAI,CAAC,SAAS,SAAS,IAAI,CAAC,KAAK,CAAC,IAAI,EAAE;YAC9C,iBAAiB;YACjB,OAAO,MAAM,CAAC,SAAS,IAAI,CAAC,KAAK,EAAE;YACnC,OAAO,qBAAqB,CAAC;gBACzB,IAAI,4BAA4B,cAAc,OAAO,QAAQ,CAAC,IAAI,EAAE;oBAChE,aAAa,OAAO,QAAQ,CAAC,IAAI;oBACjC;gBACJ;gBACA,OAAO,QAAQ,CAAC,GAAG;YACvB;YACA,uBAAuB;QAC3B;IACJ,GAAG;QACC;KACH;IACD,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,SAAS;YACL,UAAU,OAAO,GAAG,OAAO,OAAO;QACtC;QACA;QACA,OAAO,gBAAgB,CAAC,UAAU;QAClC,OAAO;YACH,OAAO,mBAAmB,CAAC,UAAU;QACzC;IACJ,GAAG,EAAE;IACL,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,IAAI,UAAU,CAAC,eAAe;QAC9B,iIAAiI;QACjI,IAAI,QAAQ;YACR,kCAAkC;YAClC,MAAM,eAAe,OAAO,UAAU,CAAC,8BAA8B,OAAO;YAC5E,CAAC,gBAAgB;YACjB,IAAI,CAAC,OAAO;gBACR,OAAO,UAAU,CAAC;oBACd;gBACJ,GAAG;YACP;QACJ,OAAO;YACH;QACJ;IACJ,GAAG;QACC;QACA;QACA;QACA;QACA;QACA;QACA;KACH;IACD,OAAO;QACH;IACJ;AACJ;AAEA,SAAS,KAAK,EAAE,MAAM,QAAQ,EAAE,YAAY,EAAE,QAAQ,EAAE,QAAQ,UAAU,EAAE,WAAW,aAAa,EAAE,UAAU,EAAE,wBAAwB,KAAK,EAAE,4BAA4B,IAAI,EAAE,iBAAiB,eAAe,EAAE,oBAAoB,mBAAmB,EAAE,cAAc,IAAI,EAAE,aAAa,KAAK,EAAE,gBAAgB,cAAc,WAAW,MAAM,GAAG,CAAC,EAAE,iBAAiB,mBAAmB,EAAE,oBAAoB,sBAAsB,EAAE,KAAK,EAAE,QAAQ,IAAI,EAAE,OAAO,EAAE,MAAM,EAAE,YAAY,EAAE,YAAY,QAAQ,EAAE,cAAc,KAAK,EAAE,uBAAuB,IAAI,EAAE,wBAAwB,KAAK,EAAE,2BAA2B,KAAK,EAAE,mBAAmB,IAAI,EAAE,cAAc,EAAE,SAAS,EAAE,YAAY,KAAK,EAAE;IAC7qB,IAAI,oBAAoB;IACxB,MAAM,CAAC,SAAS,KAAK,EAAE,UAAU,GAAG,qBAAqB;QACrD,aAAa;QACb,MAAM;QACN,UAAU,CAAC;YACP,gBAAgB,OAAO,KAAK,IAAI,aAAa;YAC7C,IAAI,CAAC,KAAK,CAAC,QAAQ;gBACf;YACJ;YACA,WAAW;gBACP,kBAAkB,OAAO,KAAK,IAAI,eAAe;YACrD,GAAG,YAAY,QAAQ,GAAG;YAC1B,IAAI,KAAK,CAAC,OAAO;gBACb,IAAI,OAAO,WAAW,aAAa;oBAC/B,OAAO,qBAAqB,CAAC;wBACzB,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;oBACxC;gBACJ;YACJ;YACA,IAAI,CAAC,GAAG;gBACJ,8DAA8D;gBAC9D,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;YACxC;QACJ;IACJ;IACA,MAAM,CAAC,eAAe,iBAAiB,GAAG,qMAAA,CAAA,UAAc,CAAC,QAAQ,CAAC;IAClE,MAAM,CAAC,YAAY,cAAc,GAAG,qMAAA,CAAA,UAAc,CAAC,QAAQ,CAAC;IAC5D,MAAM,CAAC,cAAc,gBAAgB,GAAG,qMAAA,CAAA,UAAc,CAAC,QAAQ,CAAC;IAChE,MAAM,aAAa,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACzC,MAAM,WAAW,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACvC,MAAM,gBAAgB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC5C,MAAM,cAAc,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC1C,MAAM,wBAAwB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACpD,MAAM,kBAAkB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC9C,MAAM,wBAAwB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACpD,MAAM,eAAe,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC3C,MAAM,iBAAiB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC7C,MAAM,0BAA0B,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACtD,MAAM,YAAY,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACxC,MAAM,kBAAkB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC,CAAC,CAAC,qBAAqB,UAAU,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,qBAAqB,GAAG,MAAM,KAAK;IACjK,MAAM,iBAAiB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC,CAAC,CAAC,sBAAsB,UAAU,OAAO,KAAK,OAAO,KAAK,IAAI,oBAAoB,qBAAqB,GAAG,KAAK,KAAK;IACjK,MAAM,sBAAsB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAClD,MAAM,oBAAoB,qMAAA,CAAA,UAAc,CAAC,WAAW,CAAC,CAAC;QAClD,8GAA8G;QAC9G,IAAI,cAAc,yBAAyB,iBAAiB,MAAM,GAAG,GAAG,SAAS,OAAO,GAAG,IAAI;IACnG,GAAG,EAAE;IACL,MAAM,EAAE,eAAe,EAAE,oBAAoB,EAAE,kBAAkB,EAAE,WAAW,mBAAmB,EAAE,gBAAgB,EAAE,QAAQ,gBAAgB,EAAE,UAAU,EAAE,sBAAsB,8BAA8B,EAAE,GAAG,cAAc;QAC9N;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;IACJ;IACA,iBAAiB;QACb,YAAY,CAAC,UAAU,cAAc,CAAC,SAAS,gBAAgB,CAAC,iBAAiB,CAAC,oBAAoB,CAAC;IAC3G;IACA,MAAM,EAAE,sBAAsB,EAAE,GAAG,iBAAiB;QAChD;QACA;QACA;QACA;QACA;QACA;IACJ;IACA,SAAS;QACL,OAAO,CAAC,OAAO,UAAU,GAAG,iBAAiB,IAAI,OAAO,UAAU;IACtE;IACA,SAAS,QAAQ,KAAK;QAClB,IAAI,oBAAoB;QACxB,IAAI,CAAC,eAAe,CAAC,YAAY;QACjC,IAAI,UAAU,OAAO,IAAI,CAAC,UAAU,OAAO,CAAC,QAAQ,CAAC,MAAM,MAAM,GAAG;QACpE,gBAAgB,OAAO,GAAG,CAAC,CAAC,qBAAqB,UAAU,OAAO,KAAK,OAAO,KAAK,IAAI,mBAAmB,qBAAqB,GAAG,MAAM,KAAK;QAC7I,eAAe,OAAO,GAAG,CAAC,CAAC,sBAAsB,UAAU,OAAO,KAAK,OAAO,KAAK,IAAI,oBAAoB,qBAAqB,GAAG,KAAK,KAAK;QAC7I,cAAc;QACd,cAAc,OAAO,GAAG,IAAI;QAC5B,4GAA4G;QAC5G,IAAI,SAAS;YACT,OAAO,gBAAgB,CAAC,YAAY,IAAI,gBAAgB,OAAO,GAAG,OAAO;gBACrE,MAAM;YACV;QACJ;QACA,mFAAmF;QACnF,MAAM,MAAM,CAAC,iBAAiB,CAAC,MAAM,SAAS;QAC9C,aAAa,OAAO,GAAG,WAAW,aAAa,MAAM,KAAK,GAAG,MAAM,KAAK;IAC5E;IACA,SAAS,WAAW,EAAE,EAAE,qBAAqB;QACzC,IAAI,sBAAsB;QAC1B,IAAI,UAAU;QACd,MAAM,kBAAkB,CAAC,uBAAuB,OAAO,YAAY,EAAE,KAAK,OAAO,KAAK,IAAI,qBAAqB,QAAQ;QACvH,MAAM,cAAc,UAAU,OAAO,GAAG,aAAa,UAAU,OAAO,EAAE,aAAa;QACrF,MAAM,OAAO,IAAI;QACjB,IAAI,QAAQ,YAAY,CAAC,wBAAwB,QAAQ,OAAO,CAAC,wBAAwB;YACrF,OAAO;QACX;QACA,IAAI,cAAc,WAAW,cAAc,QAAQ;YAC/C,OAAO;QACX;QACA,iCAAiC;QACjC,IAAI,SAAS,OAAO,IAAI,KAAK,OAAO,KAAK,SAAS,OAAO,CAAC,OAAO,KAAK,KAAK;YACvE,OAAO;QACX;QACA,IAAI,gBAAgB,MAAM;YACtB,IAAI,cAAc,WAAW,cAAc,IAAI,cAAc,GAAG;gBAC5D,OAAO;YACX;QACJ;QACA,yCAAyC;QACzC,IAAI,mBAAmB,gBAAgB,MAAM,GAAG,GAAG;YAC/C,OAAO;QACX;QACA,sEAAsE;QACtE,IAAI,KAAK,OAAO,KAAK,CAAC,CAAC,iCAAiC,sBAAsB,OAAO,KAAK,OAAO,KAAK,IAAI,+BAA+B,OAAO,EAAE,IAAI,qBAAqB,gBAAgB,GAAG;YAC1L,sBAAsB,OAAO,GAAG;YAChC,OAAO;QACX;QACA,IAAI,uBAAuB;YACvB,sBAAsB,OAAO,GAAG;YAChC,oDAAoD;YACpD,OAAO;QACX;QACA,4DAA4D;QAC5D,MAAM,QAAQ;YACV,qCAAqC;YACrC,IAAI,QAAQ,YAAY,GAAG,QAAQ,YAAY,EAAE;gBAC7C,IAAI,QAAQ,SAAS,KAAK,GAAG;oBACzB,sBAAsB,OAAO,GAAG,IAAI;oBACpC,uEAAuE;oBACvE,OAAO;gBACX;gBACA,IAAI,QAAQ,YAAY,CAAC,YAAY,UAAU;oBAC3C,OAAO;gBACX;YACJ;YACA,gCAAgC;YAChC,UAAU,QAAQ,UAAU;QAChC;QACA,+DAA+D;QAC/D,OAAO;IACX;IACA,SAAS,OAAO,KAAK;QACjB,IAAI,CAAC,UAAU,OAAO,EAAE;YACpB;QACJ;QACA,yHAAyH;QACzH,IAAI,YAAY;YACZ,MAAM,sBAAsB,cAAc,YAAY,cAAc,UAAU,IAAI,CAAC;YACnF,MAAM,kBAAkB,CAAC,aAAa,OAAO,GAAG,CAAC,WAAW,aAAa,MAAM,KAAK,GAAG,MAAM,KAAK,CAAC,IAAI;YACvG,MAAM,wBAAwB,kBAAkB;YAChD,iEAAiE;YACjE,MAAM,gCAAgC,cAAc,CAAC,eAAe,CAAC;YACrE,gHAAgH;YAChH,IAAI,iCAAiC,yBAAyB,GAAG;YACjE,8FAA8F;YAC9F,MAAM,qBAAqB,KAAK,GAAG,CAAC;YACpC,MAAM,UAAU,SAAS,aAAa,CAAC;YACvC,MAAM,kBAAkB,cAAc,YAAY,cAAc,QAAQ,gBAAgB,OAAO,GAAG,eAAe,OAAO;YACxH,mEAAmE;YACnE,IAAI,oBAAoB,qBAAqB;YAC7C,MAAM,6BAA6B,+BAA+B,oBAAoB;YACtF,IAAI,+BAA+B,MAAM;gBACrC,oBAAoB;YACxB;YACA,0DAA0D;YAC1D,IAAI,iCAAiC,qBAAqB,GAAG;gBACzD;YACJ;YACA,IAAI,CAAC,gBAAgB,OAAO,IAAI,CAAC,WAAW,MAAM,MAAM,EAAE,wBAAwB;YAClF,UAAU,OAAO,CAAC,SAAS,CAAC,GAAG,CAAC;YAChC,6NAA6N;YAC7N,gBAAgB,OAAO,GAAG;YAC1B,IAAI,UAAU,OAAO,EAAE;gBACnB,YAAY;YAChB;YACA,IAAI,WAAW,OAAO,EAAE;gBACpB,YAAY;YAChB;YACA,IAAI,YAAY;gBACZ,iBAAiB;oBACb;gBACJ;YACJ;YACA,gGAAgG;YAChG,IAAI,yBAAyB,CAAC,YAAY;gBACtC,MAAM,0BAA0B,YAAY;gBAC5C,MAAM,iBAAiB,KAAK,GAAG,CAAC,0BAA0B,CAAC,GAAG,KAAK;gBACnE,IAAI,UAAU,OAAO,EAAE;oBACnB,WAAW,WAAW,aAAa,CAAC,eAAe,EAAE,eAAe,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,eAAe,SAAS,CAAC;gBAC1H;gBACA;YACJ;YACA,MAAM,eAAe,IAAI;YACzB,IAAI,cAAc,iBAAiB,yBAAyB,gBAAgB,GAAG;gBAC3E,cAAc,OAAO,KAAK,IAAI,WAAW,OAAO;gBAChD,IAAI,WAAW,OAAO,EAAE;oBACpB,SAAS,GAAG,cAAc;oBAC1B,YAAY;gBAChB,GAAG;YACP;YACA,IAAI,WAAW,WAAW,OAAO,IAAI,uBAAuB;gBACxD,qDAAqD;gBACrD,MAAM,aAAa,KAAK,GAAG,CAAC,aAAa,oBAAoB,CAAC,IAAI,UAAU,GAAG;gBAC/E,MAAM,oBAAoB,IAAI,oBAAoB;gBAClD,MAAM,iBAAiB,KAAK,GAAG,CAAC,GAAG,KAAK,oBAAoB;gBAC5D,IAAI,SAAS;oBACT,cAAc,GAAG,kBAAkB,EAAE,CAAC;oBACtC,WAAW,WAAW,aAAa,CAAC,MAAM,EAAE,WAAW,iBAAiB,EAAE,eAAe,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,WAAW,cAAc,EAAE,eAAe,SAAS,CAAC;oBAChK,YAAY;gBAChB,GAAG;YACP;YACA,IAAI,CAAC,YAAY;gBACb,MAAM,iBAAiB,qBAAqB;gBAC5C,IAAI,UAAU,OAAO,EAAE;oBACnB,WAAW,WAAW,aAAa,CAAC,eAAe,EAAE,eAAe,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,eAAe,SAAS,CAAC;gBAC1H;YACJ;QACJ;IACJ;IACA,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,IAAI;QACJ,SAAS;YACL,IAAI,CAAC,UAAU,OAAO,IAAI,CAAC,kBAAkB;YAC7C,MAAM,iBAAiB,SAAS,aAAa;YAC7C,IAAI,QAAQ,mBAAmB,eAAe,OAAO,EAAE;gBACnD,IAAI;gBACJ,MAAM,uBAAuB,CAAC,CAAC,yBAAyB,OAAO,cAAc,KAAK,OAAO,KAAK,IAAI,uBAAuB,MAAM,KAAK;gBACpI,MAAM,cAAc,OAAO,WAAW;gBACtC,qCAAqC;gBACrC,IAAI,kBAAkB,cAAc;gBACpC,MAAM,eAAe,UAAU,OAAO,CAAC,qBAAqB,GAAG,MAAM,IAAI;gBACzE,gDAAgD;gBAChD,MAAM,eAAe,eAAe,cAAc;gBAClD,IAAI,CAAC,oBAAoB,OAAO,EAAE;oBAC9B,oBAAoB,OAAO,GAAG;gBAClC;gBACA,MAAM,gBAAgB,UAAU,OAAO,CAAC,qBAAqB,GAAG,GAAG;gBACnE,+LAA+L;gBAC/L,IAAI,KAAK,GAAG,CAAC,wBAAwB,OAAO,GAAG,mBAAmB,IAAI;oBAClE,eAAe,OAAO,GAAG,CAAC,eAAe,OAAO;gBACpD;gBACA,IAAI,cAAc,WAAW,MAAM,GAAG,KAAK,oBAAoB,sBAAsB;oBACjF,MAAM,wBAAwB,gBAAgB,CAAC,qBAAqB,IAAI;oBACxE,mBAAmB;gBACvB;gBACA,wBAAwB,OAAO,GAAG;gBAClC,qKAAqK;gBACrK,IAAI,eAAe,wBAAwB,eAAe,OAAO,EAAE;oBAC/D,MAAM,SAAS,UAAU,OAAO,CAAC,qBAAqB,GAAG,MAAM;oBAC/D,IAAI,kBAAkB;oBACtB,IAAI,SAAS,sBAAsB;wBAC/B,kBAAkB,uBAAuB,CAAC,eAAe,gBAAgB,iBAAiB;oBAC9F;oBACA,oJAAoJ;oBACpJ,IAAI,OAAO;wBACP,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,SAAS,KAAK,GAAG,CAAC,iBAAiB,GAAG,EAAE,CAAC;oBACjF,OAAO;wBACH,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,iBAAiB,uBAAuB,eAAe,EAAE,CAAC;oBAC3G;gBACJ,OAAO;oBACH,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,oBAAoB,OAAO,CAAC,EAAE,CAAC;gBACvE;gBACA,IAAI,cAAc,WAAW,MAAM,GAAG,KAAK,CAAC,eAAe,OAAO,EAAE;oBAChE,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,CAAC,GAAG,CAAC;gBAC1C,OAAO;oBACH,+CAA+C;oBAC/C,UAAU,OAAO,CAAC,KAAK,CAAC,MAAM,GAAG,GAAG,KAAK,GAAG,CAAC,iBAAiB,GAAG,EAAE,CAAC;gBACxE;YACJ;QACJ;QACA,CAAC,yBAAyB,OAAO,cAAc,KAAK,OAAO,KAAK,IAAI,uBAAuB,gBAAgB,CAAC,UAAU;QACtH,OAAO;YACH,IAAI;YACJ,OAAO,CAAC,yBAAyB,OAAO,cAAc,KAAK,OAAO,KAAK,IAAI,uBAAuB,mBAAmB,CAAC,UAAU;QACpI;IACJ,GAAG;QACC;QACA;QACA;KACH;IACD,SAAS,YAAY,UAAU;QAC3B;QACA,WAAW,OAAO,KAAK,IAAI;QAC3B,IAAI,CAAC,YAAY;YACb,UAAU;QACd;QACA,WAAW;YACP,IAAI,YAAY;gBACZ,mBAAmB,UAAU,CAAC,EAAE;YACpC;QACJ,GAAG,YAAY,QAAQ,GAAG,OAAO,gBAAgB;IACrD;IACA,SAAS;QACL,IAAI,CAAC,UAAU,OAAO,EAAE;QACxB,MAAM,UAAU,SAAS,aAAa,CAAC;QACvC,MAAM,qBAAqB,aAAa,UAAU,OAAO,EAAE;QAC3D,IAAI,UAAU,OAAO,EAAE;YACnB,WAAW;YACX,YAAY,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;QAChG;QACA,IAAI,WAAW,OAAO,EAAE;YACpB,YAAY,CAAC,QAAQ,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC1F,SAAS;QACb;QACA,2CAA2C;QAC3C,IAAI,yBAAyB,sBAAsB,qBAAqB,KAAK,QAAQ;YACjF,IAAI,SAAS;gBACT,cAAc,GAAG,cAAc,EAAE,CAAC;gBAClC,UAAU;gBACV,GAAG,WAAW,aAAa;oBACvB,WAAW,CAAC,MAAM,EAAE,WAAW,0DAA0D,CAAC;oBAC1F,iBAAiB;gBACrB,IAAI;oBACA,WAAW,CAAC,MAAM,EAAE,WAAW,0DAA0D,CAAC;oBAC1F,iBAAiB;gBACrB,CAAC;gBACD,oBAAoB;gBACpB,oBAAoB,GAAG,YAAY,QAAQ,CAAC,CAAC,CAAC;gBAC9C,0BAA0B,CAAC,aAAa,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC3E,GAAG;QACP;IACJ;IACA,SAAS;QACL,IAAI,CAAC,cAAc,CAAC,UAAU,OAAO,EAAE;QACvC,UAAU,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;QACnC,gBAAgB,OAAO,GAAG;QAC1B,cAAc;QACd,YAAY,OAAO,GAAG,IAAI;IAC9B;IACA,SAAS,UAAU,KAAK;QACpB,IAAI,CAAC,cAAc,CAAC,UAAU,OAAO,EAAE;QACvC,UAAU,OAAO,CAAC,SAAS,CAAC,MAAM,CAAC;QACnC,gBAAgB,OAAO,GAAG;QAC1B,cAAc;QACd,YAAY,OAAO,GAAG,IAAI;QAC1B,MAAM,cAAc,aAAa,UAAU,OAAO,EAAE;QACpD,IAAI,CAAC,WAAW,MAAM,MAAM,EAAE,UAAU,CAAC,eAAe,OAAO,KAAK,CAAC,cAAc;QACnF,IAAI,cAAc,OAAO,KAAK,MAAM;QACpC,MAAM,YAAY,YAAY,OAAO,CAAC,OAAO,KAAK,cAAc,OAAO,CAAC,OAAO;QAC/E,MAAM,YAAY,aAAa,OAAO,GAAG,CAAC,WAAW,aAAa,MAAM,KAAK,GAAG,MAAM,KAAK;QAC3F,MAAM,WAAW,KAAK,GAAG,CAAC,aAAa;QACvC,IAAI,WAAW,MAAM;YACjB,wIAAwI;YACxI,gBAAgB;YAChB,WAAW;gBACP,gBAAgB;YACpB,GAAG;QACP;QACA,IAAI,YAAY;YACZ,MAAM,sBAAsB,cAAc,YAAY,cAAc,UAAU,IAAI,CAAC;YACnF,oBAAoB;gBAChB,iBAAiB,YAAY;gBAC7B;gBACA;gBACA;YACJ;YACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,OAAO;YACtD;QACJ;QACA,mCAAmC;QACnC,IAAI,cAAc,YAAY,cAAc,UAAU,YAAY,IAAI,YAAY,GAAG;YACjF;YACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,OAAO;YACtD;QACJ;QACA,IAAI,WAAW,oBAAoB;YAC/B;YACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,OAAO;YACtD;QACJ;QACA,IAAI;QACJ,MAAM,sBAAsB,KAAK,GAAG,CAAC,CAAC,kDAAkD,UAAU,OAAO,CAAC,qBAAqB,GAAG,MAAM,KAAK,OAAO,kDAAkD,GAAG,OAAO,WAAW;QAC3N,IAAI;QACJ,MAAM,qBAAqB,KAAK,GAAG,CAAC,CAAC,iDAAiD,UAAU,OAAO,CAAC,qBAAqB,GAAG,KAAK,KAAK,OAAO,iDAAiD,GAAG,OAAO,UAAU;QACtN,MAAM,oBAAoB,cAAc,UAAU,cAAc;QAChE,IAAI,KAAK,GAAG,CAAC,gBAAgB,CAAC,oBAAoB,qBAAqB,mBAAmB,IAAI,gBAAgB;YAC1G;YACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,OAAO;YACtD;QACJ;QACA,iBAAiB,OAAO,KAAK,IAAI,cAAc,OAAO;QACtD;IACJ;IACA,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,sDAAsD;QACtD,IAAI,QAAQ;YACR,IAAI,SAAS,eAAe,EAAE;gBAC1B,gBAAgB;YACpB;YACA,SAAS,OAAO,GAAG,IAAI;QAC3B;QACA,OAAO;YACH,MAAM,SAAS,eAAe,EAAE;QACpC;IACJ,GAAG;QACC;KACH;IACD,SAAS,mBAAmB,CAAC;QACzB,MAAM,QAAQ,IAAI,CAAC,OAAO,UAAU,GAAG,mBAAmB,IAAI,OAAO,UAAU,GAAG;QAClF,MAAM,IAAI,IAAI,CAAC,sBAAsB;QACrC,IAAI,sBAAsB,OAAO,EAAE;YAC/B,OAAO,YAAY,CAAC,sBAAsB,OAAO;QACrD;QACA,IAAI,UAAU,OAAO,EAAE;YACnB,YAAY,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;YAC5F,WAAW,CAAC,MAAM,EAAE,MAAM,iBAAiB,EAAE,EAAE,MAAM,CAAC;QAC1D;QACA,IAAI,CAAC,KAAK,UAAU,OAAO,EAAE;YACzB,sBAAsB,OAAO,GAAG,WAAW;gBACvC,MAAM,iBAAiB,aAAa,UAAU,OAAO,EAAE;gBACvD,IAAI,UAAU,OAAO,EAAE;oBACnB,YAAY;oBACZ,WAAW,WAAW,aAAa,CAAC,eAAe,EAAE,eAAe,MAAM,CAAC,GAAG,CAAC,YAAY,EAAE,eAAe,SAAS,CAAC;gBAC1H;YACJ,GAAG;QACP;IACJ;IACA,SAAS,aAAa,MAAM,EAAE,iBAAiB;QAC3C,IAAI,oBAAoB,GAAG;QAC3B,MAAM,eAAe,CAAC,OAAO,UAAU,GAAG,mBAAmB,IAAI,OAAO,UAAU;QAClF,MAAM,WAAW,eAAe,oBAAoB,CAAC,IAAI,YAAY;QACrE,MAAM,eAAe,CAAC,sBAAsB,oBAAoB;QAChE,IAAI,UAAU,OAAO,EAAE;YACnB,WAAW,WAAW,aAAa,CAAC,MAAM,EAAE,SAAS,iBAAiB,EAAE,aAAa,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,SAAS,cAAc,EAAE,aAAa,SAAS,CAAC;YACxJ,YAAY;QAChB;IACJ;IACA,SAAS,gBAAgB,MAAM,EAAE,CAAC;QAC9B,MAAM,MAAM,WAAW,aAAa,OAAO,WAAW,GAAG,OAAO,UAAU;QAC1E,MAAM,QAAQ,IAAI,CAAC,MAAM,mBAAmB,IAAI,MAAM;QACtD,MAAM,YAAY,IAAI,CAAC,sBAAsB;QAC7C,IAAI,GAAG;YACH,IAAI,UAAU,OAAO,EAAE;gBACnB,YAAY,CAAC,UAAU,EAAE,YAAY,QAAQ,CAAC,eAAe,EAAE,YAAY,IAAI,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;gBAC5F,WAAW,WAAW,aAAa,CAAC,MAAM,EAAE,MAAM,iBAAiB,EAAE,UAAU,MAAM,CAAC,GAAG,CAAC,MAAM,EAAE,MAAM,cAAc,EAAE,UAAU,SAAS,CAAC;YAChJ;QACJ;IACJ;IACA,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,kKAAA,CAAA,OAAoB,EAAE;QACpE,aAAa;QACb,cAAc,CAAC;YACX,IAAI,CAAC,eAAe,CAAC,MAAM;YAC3B,IAAI,MAAM;gBACN,iBAAiB;YACrB,OAAO;gBACH,YAAY;YAChB;YACA,UAAU;QACd;QACA,MAAM;IACV,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,cAAc,QAAQ,EAAE;QAClE,OAAO;YACH;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;YACA;QACJ;IACJ,GAAG;AACP;AACA,MAAM,UAAU,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,UAAU,CAAC,SAAS,EAAE,GAAG,MAAM,EAAE,GAAG;IAC7E,MAAM,EAAE,UAAU,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,MAAM,EAAE,KAAK,EAAE,GAAG;IACzE,MAAM,cAAc,gBAAgB,KAAK;IACzC,MAAM,gBAAgB,cAAc,WAAW,MAAM,GAAG;IACxD,wIAAwI;IACxI,IAAI,CAAC,OAAO;QACR,yCAAyC;QACzC,IAAI,OAAO,WAAW,aAAa;YAC/B,OAAO,qBAAqB,CAAC;gBACzB,SAAS,IAAI,CAAC,KAAK,CAAC,aAAa,GAAG;YACxC;QACJ;QACA,OAAO;IACX;IACA,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,kKAAA,CAAA,UAAuB,EAAE;QACvE,WAAW;QACX,KAAK;QACL,qBAAqB;QACrB,yBAAyB,UAAU,gBAAgB,SAAS;QAC5D,iCAAiC,UAAU,aAAa,SAAS;QACjE,GAAG,IAAI;IACX;AACJ;AACA,QAAQ,WAAW,GAAG;AACtB,MAAM,UAAU,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,UAAU,CAAC,SAAS,EAAE,oBAAoB,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,EAAE,GAAG;IAC3H,MAAM,EAAE,SAAS,EAAE,OAAO,EAAE,SAAS,EAAE,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,KAAK,EAAE,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG;IAC5J,iDAAiD;IACjD,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,qMAAA,CAAA,UAAc,CAAC,QAAQ,CAAC;IAC1E,MAAM,cAAc,gBAAgB,KAAK;IACzC,MAAM,kBAAkB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAC9C,MAAM,2BAA2B,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACvD,MAAM,uBAAuB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACnD,MAAM,gBAAgB,cAAc,WAAW,MAAM,GAAG;IACxD;IACA,MAAM,qBAAqB,CAAC,OAAO,WAAW,YAAY,CAAC;QACvD,IAAI,qBAAqB,OAAO,EAAE,OAAO;QACzC,MAAM,SAAS,KAAK,GAAG,CAAC,MAAM,CAAC;QAC/B,MAAM,SAAS,KAAK,GAAG,CAAC,MAAM,CAAC;QAC/B,MAAM,WAAW,SAAS;QAC1B,MAAM,UAAU;YACZ;YACA;SACH,CAAC,QAAQ,CAAC,aAAa,IAAI,CAAC;QAC7B,IAAI,cAAc,UAAU,cAAc,SAAS;YAC/C,MAAM,qBAAqB,MAAM,CAAC,GAAG,UAAU;YAC/C,IAAI,CAAC,sBAAsB,UAAU,KAAK,UAAU,WAAW;gBAC3D,OAAO;YACX;QACJ,OAAO;YACH,MAAM,qBAAqB,MAAM,CAAC,GAAG,UAAU;YAC/C,IAAI,CAAC,sBAAsB,UAAU,KAAK,UAAU,WAAW;gBAC3D,OAAO,CAAC;YACZ;QACJ;QACA,qBAAqB,OAAO,GAAG;QAC/B,OAAO;IACX;IACA,qMAAA,CAAA,UAAc,CAAC,SAAS,CAAC;QACrB,IAAI,eAAe;YACf,OAAO,qBAAqB,CAAC;gBACzB,qBAAqB;YACzB;QACJ;IACJ,GAAG,EAAE;IACL,SAAS,kBAAkB,KAAK;QAC5B,gBAAgB,OAAO,GAAG;QAC1B,qBAAqB,OAAO,GAAG;QAC/B,UAAU;IACd;IACA,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,kKAAA,CAAA,UAAuB,EAAE;QACvE,8BAA8B;QAC9B,oBAAoB;QACpB,iCAAiC,oBAAoB,SAAS;QAC9D,yBAAyB,UAAU,gBAAgB,SAAS;QAC5D,8BAA8B,YAAY,SAAS;QACnD,GAAG,IAAI;QACP,KAAK;QACL,OAAO,oBAAoB,iBAAiB,MAAM,GAAG,IAAI;YACrD,uBAAuB,GAAG,gBAAgB,CAAC,EAAE,CAAC,EAAE,CAAC;YACjD,GAAG,KAAK;QACZ,IAAI;QACJ,eAAe,CAAC;YACZ,IAAI,YAAY;YAChB,KAAK,aAAa,IAAI,OAAO,KAAK,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM;YACpE,gBAAgB,OAAO,GAAG;gBACtB,GAAG,MAAM,KAAK;gBACd,GAAG,MAAM,KAAK;YAClB;YACA,QAAQ;QACZ;QACA,iBAAiB,CAAC;YACd,mBAAmB,OAAO,KAAK,IAAI,gBAAgB;YACnD,IAAI,CAAC,WAAW;gBACZ,EAAE,cAAc;YACpB;QACJ;QACA,sBAAsB,CAAC;YACnB,wBAAwB,OAAO,KAAK,IAAI,qBAAqB;YAC7D,IAAI,CAAC,SAAS,EAAE,gBAAgB,EAAE;gBAC9B,EAAE,cAAc;gBAChB;YACJ;YACA,IAAI,eAAe,OAAO,EAAE;gBACxB,eAAe,OAAO,GAAG;YAC7B;QACJ;QACA,gBAAgB,CAAC;YACb,IAAI,CAAC,OAAO;gBACR,EAAE,cAAc;gBAChB;YACJ;QACJ;QACA,eAAe,CAAC;YACZ,yBAAyB,OAAO,GAAG;YACnC,IAAI,YAAY;YAChB,KAAK,aAAa,IAAI,OAAO,KAAK,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM;YACpE,IAAI,CAAC,gBAAgB,OAAO,EAAE;YAC9B,MAAM,YAAY,MAAM,KAAK,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACzD,MAAM,YAAY,MAAM,KAAK,GAAG,gBAAgB,OAAO,CAAC,CAAC;YACzD,MAAM,sBAAsB,MAAM,WAAW,KAAK,UAAU,KAAK;YACjE,MAAM,QAAQ;gBACV,GAAG;gBACH,GAAG;YACP;YACA,MAAM,mBAAmB,mBAAmB,OAAO,WAAW;YAC9D,IAAI,kBAAkB,OAAO;iBACxB,IAAI,KAAK,GAAG,CAAC,aAAa,uBAAuB,KAAK,GAAG,CAAC,aAAa,qBAAqB;gBAC7F,gBAAgB,OAAO,GAAG;YAC9B;QACJ;QACA,aAAa,CAAC;YACV,KAAK,WAAW,IAAI,OAAO,KAAK,IAAI,KAAK,WAAW,CAAC,IAAI,CAAC,MAAM;YAChE,gBAAgB,OAAO,GAAG;YAC1B,qBAAqB,OAAO,GAAG;YAC/B,UAAU;QACd;QACA,cAAc,CAAC;YACX,KAAK,YAAY,IAAI,OAAO,KAAK,IAAI,KAAK,YAAY,CAAC,IAAI,CAAC,MAAM;YAClE,kBAAkB,yBAAyB,OAAO;QACtD;QACA,eAAe,CAAC;YACZ,KAAK,aAAa,IAAI,OAAO,KAAK,IAAI,KAAK,aAAa,CAAC,IAAI,CAAC,MAAM;YACpE,kBAAkB,yBAAyB,OAAO;QACtD;IACJ;AACJ;AACA,QAAQ,WAAW,GAAG;AACtB,MAAM,4BAA4B;AAClC,MAAM,qBAAqB;AAC3B,MAAM,SAAS,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,UAAU,CAAC,SAAS,EAAE,eAAe,KAAK,EAAE,QAAQ,EAAE,GAAG,MAAM,EAAE,GAAG;IAC5G,MAAM,EAAE,WAAW,EAAE,UAAU,EAAE,UAAU,EAAE,eAAe,EAAE,kBAAkB,EAAE,WAAW,EAAE,UAAU,EAAE,MAAM,EAAE,OAAO,EAAE,MAAM,EAAE,GAAG;IACvI,MAAM,oBAAoB,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IAChD,MAAM,6BAA6B,qMAAA,CAAA,UAAc,CAAC,MAAM,CAAC;IACzD,SAAS;QACL,qDAAqD;QACrD,IAAI,2BAA2B,OAAO,EAAE;YACpC;YACA;QACJ;QACA,OAAO,UAAU,CAAC;YACd;QACJ,GAAG;IACP;IACA,SAAS;QACL,gDAAgD;QAChD,IAAI,cAAc,gBAAgB,2BAA2B,OAAO,EAAE;YAClE;YACA;QACJ;QACA,8FAA8F;QAC9F;QACA,IAAI,CAAC,CAAC,cAAc,WAAW,MAAM,KAAK,CAAC,KAAK,aAAa;YACzD;YACA;QACJ;QACA,MAAM,kBAAkB,oBAAoB,UAAU,CAAC,WAAW,MAAM,GAAG,EAAE;QAC7E,IAAI,mBAAmB,aAAa;YAChC;YACA;QACJ;QACA,MAAM,mBAAmB,WAAW,SAAS,CAAC,CAAC,QAAQ,UAAU;QACjE,IAAI,qBAAqB,CAAC,GAAG,QAAQ,0CAA0C;QAC/E,MAAM,gBAAgB,UAAU,CAAC,mBAAmB,EAAE;QACtD,mBAAmB;IACvB;IACA,SAAS;QACL,kBAAkB,OAAO,GAAG,OAAO,UAAU,CAAC;YAC1C,2CAA2C;YAC3C,2BAA2B,OAAO,GAAG;QACzC,GAAG;IACP;IACA,SAAS;QACL,OAAO,YAAY,CAAC,kBAAkB,OAAO;QAC7C,2BAA2B,OAAO,GAAG;IACzC;IACA,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,OAAO;QACrD,SAAS;QACT,iBAAiB;QACjB,eAAe,CAAC;YACZ,IAAI,YAAY,QAAQ;YACxB;QACJ;QACA,eAAe,CAAC;YACZ,IAAI,YAAY,OAAO;QAC3B;QACA,0DAA0D;QAC1D,KAAK;QACL,4BAA4B,SAAS,SAAS;QAC9C,oBAAoB;QACpB,eAAe;QACf,GAAG,IAAI;IACX,GAAG,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,QAAQ;QAClD,4BAA4B;QAC5B,eAAe;IACnB,GAAG;AACP;AACA,OAAO,WAAW,GAAG;AACrB,SAAS,WAAW,EAAE,MAAM,EAAE,YAAY,EAAE,GAAG,MAAM;IACjD,MAAM,EAAE,YAAY,EAAE,kBAAkB,EAAE,eAAe,EAAE,GAAG;IAC9D,IAAI,CAAC,cAAc;QACf,MAAM,IAAI,MAAM;IACpB;IACA,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,MAAM;QACpD,QAAQ;QACR,SAAS;YACL,mBAAmB;QACvB;QACA,QAAQ,CAAC,GAAG;YACR,aAAa,GAAG;YAChB,UAAU,OAAO,KAAK,IAAI,OAAO,GAAG;QACxC;QACA,cAAc,CAAC;YACX,IAAI,GAAG;gBACH,mBAAmB;YACvB;QACJ;QACA,WAAW;QACX,GAAG,IAAI;IACX;AACJ;AACA,SAAS,OAAO,KAAK;IACjB,MAAM,UAAU;IAChB,MAAM,EAAE,YAAY,QAAQ,SAAS,EAAE,GAAG,aAAa,GAAG;IAC1D,OAAO,WAAW,GAAG,qMAAA,CAAA,UAAc,CAAC,aAAa,CAAC,kKAAA,CAAA,SAAsB,EAAE;QACtE,WAAW;QACX,GAAG,WAAW;IAClB;AACJ;AACA,MAAM,SAAS;IACX;IACA;IACA;IACA;IACA,SAAS,kKAAA,CAAA,UAAuB;IAChC;IACA;IACA,OAAO,kKAAA,CAAA,QAAqB;IAC5B,OAAO,kKAAA,CAAA,QAAqB;IAC5B,aAAa,kKAAA,CAAA,cAA2B;AAC5C", "ignoreList": [0], "debugId": null}}]}