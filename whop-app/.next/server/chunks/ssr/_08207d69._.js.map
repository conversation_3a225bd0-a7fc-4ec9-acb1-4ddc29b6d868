{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Heading, Text, But<PERSON>, Card, Separator } from '@whop/react/components';\n\nexport default function Page() {\n\tconst [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');\n\n\treturn (\n\t\t<div className=\"min-h-screen bg-black\">\n\t\t\t{/* Top Navigation Bar */}\n\t\t\t<nav className=\"border-b border-blue-9/20 bg-black\">\n\t\t\t\t<div className=\"max-w-6xl mx-auto px-6 py-4\">\n\t\t\t\t\t<div className=\"flex items-center justify-center\">\n\t\t\t\t\t\t<div className=\"flex items-center gap-1 bg-blue-9/10 rounded-lg p-1 shadow-lg shadow-blue-9/20\">\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tvariant={activeTab === 'leaderboards' ? 'solid' : 'ghost'}\n\t\t\t\t\t\t\t\tcolor={activeTab === 'leaderboards' ? 'blue' : 'gray'}\n\t\t\t\t\t\t\t\tonClick={() => setActiveTab('leaderboards')}\n\t\t\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t\t\t\tclassName=\"text-sm\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\tLeaderboards\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tvariant={activeTab === 'competitions' ? 'solid' : 'ghost'}\n\t\t\t\t\t\t\t\tcolor={activeTab === 'competitions' ? 'blue' : 'gray'}\n\t\t\t\t\t\t\t\tonClick={() => setActiveTab('competitions')}\n\t\t\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t\t\t\tclassName=\"text-sm\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\tCompetitions\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t</nav>\n\n\t\t\t{/* Content */}\n\t\t\t<div className=\"max-w-6xl mx-auto px-6 py-6\">\n\t\t\t\t{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction LeaderboardsPage() {\n\tconst leaderboardData = [\n\t\t{ rank: 1, name: \"Jack Sharkey\", handle: \"@shark\", earnings: 26800 },\n\t\t{ rank: 2, name: \"Tyler\", handle: \"@methodicalstew\", earnings: 21344 },\n\t\t{ rank: 3, name: \"Shaq\", handle: \"@shaq4257\", earnings: 14565 },\n\t\t{ rank: 4, name: \"Ilya Miskov\", handle: \"@ilyamiskov\", earnings: 13915 },\n\t\t{ rank: 5, name: \"Savnatra\", handle: \"@savnatra\", earnings: 11141 },\n\t\t{ rank: 6, name: \"Travis Williams\", handle: \"@user673237\", earnings: 9820 },\n\t\t{ rank: 7, name: \"Amirah Robinson\", handle: \"@amirahgirl\", earnings: 8760 },\n\t\t{ rank: 8, name: \"AB\", handle: \"@abonsocials\", earnings: 8105 },\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-4\">\n\t\t\t<div className=\"bg-gray-12 border border-gray-8 rounded-lg overflow-hidden\">\n\t\t\t\t<div className=\"divide-y divide-gray-8\">\n\t\t\t\t\t{leaderboardData.map((player) => (\n\t\t\t\t\t\t<div key={player.rank} className=\"px-4 py-3 hover:bg-gray-11 transition-colors\">\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className={`w-6 text-center ${\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 1 ? 'text-yellow-9' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 2 ? 'text-gray-9' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 3 ? 'text-orange-9' :\n\t\t\t\t\t\t\t\t\t\t'text-gray-11'\n\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t{player.rank}\n\t\t\t\t\t\t\t\t\t</Text>\n\n\t\t\t\t\t\t\t\t\t<div className=\"w-8 h-8 rounded-full bg-gray-9 flex items-center justify-center\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-white\">\n\t\t\t\t\t\t\t\t\t\t\t{player.name.split(' ').map(n => n[0]).join('')}\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"medium\" className=\"text-white\">\n\t\t\t\t\t\t\t\t\t\t\t{player.name}\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-11\">\n\t\t\t\t\t\t\t\t\t\t\t{player.handle}\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t<div className=\"w-2 h-2 rounded-full bg-green-9\"></div>\n\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">\n\t\t\t\t\t\t\t\t\t\t${player.earnings.toLocaleString()}\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction CompetitionsPage() {\n\tconst competitions = [\n\t\t{\n\t\t\tid: 1,\n\t\t\ttitle: \"Weekly Challenge\",\n\t\t\ttimeLeft: \"3d 14h\",\n\t\t\tparticipants: 342,\n\t\t\tprize: \"$500\",\n\t\t\tstatus: \"active\"\n\t\t},\n\t\t{\n\t\t\tid: 2,\n\t\t\ttitle: \"Monthly Tournament\",\n\t\t\ttimeLeft: \"5d\",\n\t\t\tparticipants: 89,\n\t\t\tprize: \"$2,000\",\n\t\t\tstatus: \"upcoming\"\n\t\t},\n\t\t{\n\t\t\tid: 3,\n\t\t\ttitle: \"Speed Run Challenge\",\n\t\t\ttimeLeft: \"2h\",\n\t\t\tparticipants: 156,\n\t\t\tprize: \"$250\",\n\t\t\tstatus: \"active\"\n\t\t},\n\t\t{\n\t\t\tid: 4,\n\t\t\ttitle: \"Elite Championship\",\n\t\t\ttimeLeft: \"Closed\",\n\t\t\tparticipants: 50,\n\t\t\tprize: \"$5,000\",\n\t\t\tstatus: \"closed\"\n\t\t}\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-3\">\n\t\t\t{competitions.map((comp) => (\n\t\t\t\t<div key={comp.id} className=\"bg-gray-12 border border-gray-8 rounded-lg p-4\">\n\t\t\t\t\t<div className=\"flex items-center justify-between mb-3\">\n\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t<div className={`w-8 h-8 rounded-lg flex items-center justify-center ${\n\t\t\t\t\t\t\t\tcomp.status === 'active' ? 'bg-blue-9' :\n\t\t\t\t\t\t\t\tcomp.status === 'upcoming' ? 'bg-blue-8' :\n\t\t\t\t\t\t\t\t'bg-gray-8'\n\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t<div className=\"w-3 h-3 bg-white rounded-full\"></div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"medium\" className=\"text-white\">\n\t\t\t\t\t\t\t\t\t{comp.title}\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-11\">\n\t\t\t\t\t\t\t\t\t{comp.participants} participants\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"text-right\">\n\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-blue-9\">\n\t\t\t\t\t\t\t\t{comp.prize}\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-11\">\n\t\t\t\t\t\t\t\t{comp.timeLeft}\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tcolor=\"blue\"\n\t\t\t\t\t\t\tvariant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}\n\t\t\t\t\t\t\tdisabled={comp.status === 'closed'}\n\t\t\t\t\t\t\tclassName=\"flex-1\"\n\t\t\t\t\t\t\tsize=\"1\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t{comp.status === 'active' ? 'Join' :\n\t\t\t\t\t\t\t comp.status === 'upcoming' ? 'Register' :\n\t\t\t\t\t\t\t 'Closed'}\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button variant=\"ghost\" color=\"gray\" size=\"1\">\n\t\t\t\t\t\t\tDetails\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\t\t\t))}\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,qBACC,8OAAC;QAAI,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;0BACd,cAAA,8OAAC;oBAAI,WAAU;8BACd,cAAA,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,8KAAA,CAAA,SAAM;oCACN,SAAS,cAAc,iBAAiB,UAAU;oCAClD,OAAO,cAAc,iBAAiB,SAAS;oCAC/C,SAAS,IAAM,aAAa;oCAC5B,MAAK;oCACL,WAAU;8CACV;;;;;;8CAGD,8OAAC,8KAAA,CAAA,SAAM;oCACN,SAAS,cAAc,iBAAiB,UAAU;oCAClD,OAAO,cAAc,iBAAiB,SAAS;oCAC/C,SAAS,IAAM,aAAa;oCAC5B,MAAK;oCACL,WAAU;8CACV;;;;;;;;;;;;;;;;;;;;;;;;;;;0BASL,8OAAC;gBAAI,WAAU;0BACb,cAAc,+BAAiB,8OAAC;;;;yCAAsB,8OAAC;;;;;;;;;;;;;;;;AAI5D;AAEA,SAAS;IACR,MAAM,kBAAkB;QACvB;YAAE,MAAM;YAAG,MAAM;YAAgB,QAAQ;YAAU,UAAU;QAAM;QACnE;YAAE,MAAM;YAAG,MAAM;YAAS,QAAQ;YAAmB,UAAU;QAAM;QACrE;YAAE,MAAM;YAAG,MAAM;YAAQ,QAAQ;YAAa,UAAU;QAAM;QAC9D;YAAE,MAAM;YAAG,MAAM;YAAe,QAAQ;YAAe,UAAU;QAAM;QACvE;YAAE,MAAM;YAAG,MAAM;YAAY,QAAQ;YAAa,UAAU;QAAM;QAClE;YAAE,MAAM;YAAG,MAAM;YAAmB,QAAQ;YAAe,UAAU;QAAK;QAC1E;YAAE,MAAM;YAAG,MAAM;YAAmB,QAAQ;YAAe,UAAU;QAAK;QAC1E;YAAE,MAAM;YAAG,MAAM;YAAM,QAAQ;YAAgB,UAAU;QAAK;KAC9D;IAED,qBACC,8OAAC;QAAI,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACd,cAAA,8OAAC;gBAAI,WAAU;0BACb,gBAAgB,GAAG,CAAC,CAAC,uBACrB,8OAAC;wBAAsB,WAAU;kCAChC,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC;oCAAI,WAAU;;sDACd,8OAAC,0KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,QAAO;4CAAO,WAAW,CAAC,gBAAgB,EACxD,OAAO,IAAI,KAAK,IAAI,kBACpB,OAAO,IAAI,KAAK,IAAI,gBACpB,OAAO,IAAI,KAAK,IAAI,kBACpB,gBACC;sDACA,OAAO,IAAI;;;;;;sDAGb,8OAAC;4CAAI,WAAU;sDACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DACvC,OAAO,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG,CAAC,CAAA,IAAK,CAAC,CAAC,EAAE,EAAE,IAAI,CAAC;;;;;;;;;;;sDAI9C,8OAAC;;8DACA,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAS,WAAU;8DACvC,OAAO,IAAI;;;;;;8DAEb,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,WAAU;8DACvB,OAAO,MAAM;;;;;;;;;;;;;;;;;;8CAKjB,8OAAC;oCAAI,WAAU;;sDACd,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,0KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,QAAO;4CAAO,WAAU;;gDAAa;gDACjD,OAAO,QAAQ,CAAC,cAAc;;;;;;;;;;;;;;;;;;;uBA/B1B,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;AAyC3B;AAEA,SAAS;IACR,MAAM,eAAe;QACpB;YACC,IAAI;YACJ,OAAO;YACP,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;QACT;QACA;YACC,IAAI;YACJ,OAAO;YACP,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;QACT;QACA;YACC,IAAI;YACJ,OAAO;YACP,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;QACT;QACA;YACC,IAAI;YACJ,OAAO;YACP,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;QACT;KACA;IAED,qBACC,8OAAC;QAAI,WAAU;kBACb,aAAa,GAAG,CAAC,CAAC,qBAClB,8OAAC;gBAAkB,WAAU;;kCAC5B,8OAAC;wBAAI,WAAU;;0CACd,8OAAC;gCAAI,WAAU;;kDACd,8OAAC;wCAAI,WAAW,CAAC,oDAAoD,EACpE,KAAK,MAAM,KAAK,WAAW,cAC3B,KAAK,MAAM,KAAK,aAAa,cAC7B,aACC;kDACD,cAAA,8OAAC;4CAAI,WAAU;;;;;;;;;;;kDAEhB,8OAAC;;0DACA,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DACvC,KAAK,KAAK;;;;;;0DAEZ,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;;oDACvB,KAAK,YAAY;oDAAC;;;;;;;;;;;;;;;;;;;0CAItB,8OAAC;gCAAI,WAAU;;kDACd,8OAAC,0KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,QAAO;wCAAO,WAAU;kDACrC,KAAK,KAAK;;;;;;kDAEZ,8OAAC,0KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,WAAU;kDACvB,KAAK,QAAQ;;;;;;;;;;;;;;;;;;kCAKjB,8OAAC;wBAAI,WAAU;;0CACd,8OAAC,8KAAA,CAAA,SAAM;gCACN,OAAM;gCACN,SAAS,KAAK,MAAM,KAAK,WAAW,UAAU,KAAK,MAAM,KAAK,aAAa,SAAS;gCACpF,UAAU,KAAK,MAAM,KAAK;gCAC1B,WAAU;gCACV,MAAK;0CAEJ,KAAK,MAAM,KAAK,WAAW,SAC3B,KAAK,MAAM,KAAK,aAAa,aAC7B;;;;;;0CAEF,8OAAC,8KAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,OAAM;gCAAO,MAAK;0CAAI;;;;;;;;;;;;;eAzCtC,KAAK,EAAE;;;;;;;;;;AAiDrB", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/next/src/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.ts"], "sourcesContent": ["module.exports = require('../../module.compiled').vendored[\n  'react-ssr'\n].ReactJsxDevRuntime\n"], "names": ["module", "exports", "require", "vendored", "ReactJsxDevRuntime"], "mappings": ";AAAAA,OAAOC,OAAO,GAAGC,QAAQ,4HAAyBC,QAAQ,CACxD,YACD,CAACC,kBAAkB", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 468, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/iframe/use-iframe-sdk.mjs"], "sourcesContent": ["import { use } from \"react\";\nimport { WhopIframeSdkContext } from \"./context.mjs\";\nexport function useIframeSdk() {\n    const sdk = use(WhopIframeSdkContext);\n    if (!sdk) {\n        throw new Error(\"useIframeSdk must be used within a WhopIframeSdkProvider\");\n    }\n    return sdk;\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS;IACZ,MAAM,MAAM,CAAA,GAAA,qMAAA,CAAA,MAAG,AAAD,EAAE,6JAAA,CAAA,uBAAoB;IACpC,IAAI,CAAC,KAAK;QACN,MAAM,IAAI,MAAM;IACpB;IACA,OAAO;AACX", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 488, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/iframe/index.mjs"], "sourcesContent": ["export * from \"./context.mjs\";\nexport * from \"./provider.mjs\";\nexport * from \"./use-iframe-sdk.mjs\";\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 512, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/theme/script.mjs"], "sourcesContent": ["export function script() {\n    const cookie = document.cookie.match(/whop-frosted-theme=appearance:(?<appearance>light|dark)/)?.groups;\n    const el = document.documentElement;\n    const classes = [\n        \"light\",\n        \"dark\"\n    ];\n    const theme = cookie ? cookie.appearance : getSystemTheme();\n    function updateDOM(theme) {\n        el.classList.remove(...classes);\n        el.classList.add(theme);\n        el.style.colorScheme = theme;\n    }\n    function getSystemTheme() {\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    }\n    updateDOM(theme);\n}\n"], "names": [], "mappings": ";;;AAAO,SAAS;IACZ,MAAM,SAAS,SAAS,MAAM,CAAC,KAAK,CAAC,4DAA4D;IACjG,MAAM,KAAK,SAAS,eAAe;IACnC,MAAM,UAAU;QACZ;QACA;KACH;IACD,MAAM,QAAQ,SAAS,OAAO,UAAU,GAAG;IAC3C,SAAS,UAAU,KAAK;QACpB,GAAG,SAAS,CAAC,MAAM,IAAI;QACvB,GAAG,SAAS,CAAC,GAAG,CAAC;QACjB,GAAG,KAAK,CAAC,WAAW,GAAG;IAC3B;IACA,SAAS;QACL,OAAO,OAAO,UAAU,CAAC,gCAAgC,OAAO,GAAG,SAAS;IAChF;IACA,UAAU;AACd", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 539, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/theme/index.mjs"], "sourcesContent": ["import React from \"react\";\nimport { script } from \"./script.mjs\";\nexport function WhopThemeScript() {\n    const scriptString = `(${script.toString()})()`;\n    return React.createElement(React.Fragment, null, React.createElement(\"script\", {\n        dangerouslySetInnerHTML: {\n            __html: scriptString\n        }\n    }));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AACO,SAAS;IACZ,MAAM,eAAe,CAAC,CAAC,EAAE,2JAAA,CAAA,SAAM,CAAC,QAAQ,GAAG,GAAG,CAAC;IAC/C,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,UAAU;QAC3E,yBAAyB;YACrB,QAAQ;QACZ;IACJ;AACJ", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 560, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/components/whop-app.mjs"], "sourcesContent": ["import React from \"react\";\nimport { Theme } from \"./index.mjs\";\nimport { WhopIframeSdkProvider } from \"../iframe/index.mjs\";\nimport { WhopThemeScript } from \"../theme/index.mjs\";\nexport function WhopApp({ children, sdkOptions, ...themeProps }) {\n    return React.createElement(React.Fragment, null, React.createElement(WhopThemeScript, null), React.createElement(WhopIframeSdkProvider, {\n        options: sdkOptions\n    }, React.createElement(Theme, themeProps, children)));\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;AAAA;AACA;;;;;AACO,SAAS,QAAQ,EAAE,QAAQ,EAAE,UAAU,EAAE,GAAG,YAAY;IAC3D,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qMAAA,CAAA,UAAK,CAAC,QAAQ,EAAE,MAAM,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,0JAAA,CAAA,kBAAe,EAAE,OAAO,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,8JAAA,CAAA,wBAAqB,EAAE;QACpI,SAAS;IACb,GAAG,qMAAA,CAAA,UAAK,CAAC,aAAa,CAAC,qJAAA,CAAA,QAAK,EAAE,YAAY;AAC9C", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 584, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/node_modules/%40whop/react/dist/components/index.mjs"], "sourcesContent": ["export * from \"frosted-ui\";\nexport { WhopApp } from \"./whop-app.mjs\";\n"], "names": [], "mappings": ";AACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 603, "column": 0}, "map": {"version": 3, "file": "text-align.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/text-align.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,WAAW,GAAG;IAAC,MAAM;IAAE,QAAQ;IAAE,OAAO;CAAU,CAAC;AAEzD,MAAM,SAAS,GAAG;IAChB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,WAAW;IACnB,OAAO,EAAE,SAAS;CAC6B,CAAC", "debugId": null}}, {"offset": {"line": 624, "column": 0}, "map": {"version": 3, "file": "color.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/color.prop.ts"], "names": [], "mappings": ";;;AACA,OAAO,EAAE,cAAc,EAAE,aAAa,EAAE,MAAM,qBAAqB,CAAC;;AAEpE,MAAM,wBAAwB,GAAG,CAAC;wKAAG,iBAAc,EAAE;wKAAG,gBAAa,CAAC,WAAW,CAAC,MAAM;CAAC,CAAC;AAC1F,MAAM,SAAS,GAAG;IAChB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,wBAAwB;IAChC,OAAO,EAAE,SAAkE;CACf,CAAC", "debugId": null}}, {"offset": {"line": 646, "column": 0}, "map": {"version": 3, "file": "high-contrast.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/high-contrast.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,gBAAgB,GAAG;IACvB,IAAI,EAAE,SAAS;IACf,OAAO,EAAE,SAAS;CACD,CAAC", "debugId": null}}, {"offset": {"line": 661, "column": 0}, "map": {"version": 3, "file": "leading-trim.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/leading-trim.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,UAAU,GAAG;IAAC,QAAQ;IAAE,OAAO;IAAE,KAAK;IAAE,MAAM;CAAU,CAAC;AAE/D,MAAM,QAAQ,GAAG;IACf,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,UAAU;IAClB,OAAO,EAAE,SAAS;CAC4B,CAAC", "debugId": null}}, {"offset": {"line": 683, "column": 0}, "map": {"version": 3, "file": "weight.prop.js", "sourceRoot": "", "sources": ["../../../../src/helpers/props/weight.prop.ts"], "names": [], "mappings": ";;;AAEA,MAAM,OAAO,GAAG;IAAC,OAAO;IAAE,SAAS;IAAE,QAAQ;IAAE,WAAW;IAAE,MAAM;CAAU,CAAC;AAE7E,MAAM,UAAU,GAAG;IACjB,IAAI,EAAE,MAAM;IACZ,MAAM,EAAE,OAAO;IACf,OAAO,EAAE,SAAS;CACyB,CAAC", "debugId": null}}, {"offset": {"line": 706, "column": 0}, "map": {"version": 3, "file": "text.props.js", "sourceRoot": "", "sources": ["../../../../src/components/text/text.props.ts"], "names": [], "mappings": ";;;;;;;AACA,OAAO,EAAE,SAAS,EAAE,SAAS,EAAE,gBAAgB,EAAE,QAAQ,EAAE,UAAU,EAAE,MAAM,eAAe,CAAC;;AAE7F,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AAE1E,MAAM,YAAY,GAAG;IACnB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,SAAS;IAAA,CAAE;IACzD,MAAM,qLAAE,aAAU;IAClB,KAAK,4LAAE,YAAS;IAChB,IAAI,8LAAE,WAAQ;IACd,KAAK,oLAAE,YAAS;IAChB,YAAY,+LAAE,mBAAgB;CAQ/B,CAAC", "debugId": null}}, {"offset": {"line": 747, "column": 0}, "map": {"version": 3, "file": "text.js", "sourceRoot": "", "sources": ["../../../../src/components/text/text.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,YAAY,EAAE,MAAM,cAAc,CAAC;;;;;AAyB5C,MAAM,IAAI,GAAG,CAAC,KAAgB,EAAE,EAAE;IAChC,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,GAAG,KAAK,EACf,EAAE,EAAE,GAAG,GAAG,MAAM,EAChB,IAAI,uLAAG,eAAY,CAAC,IAAI,CAAC,OAAO,EAChC,MAAM,uLAAG,eAAY,CAAC,MAAM,CAAC,OAAO,EACpC,KAAK,uLAAG,eAAY,CAAC,KAAK,CAAC,OAAO,EAClC,IAAI,uLAAG,eAAY,CAAC,IAAI,CAAC,OAAO,EAChC,KAAK,uLAAG,eAAY,CAAC,KAAK,CAAC,OAAO,EAClC,YAAY,sLAAG,gBAAY,CAAC,YAAY,CAAC,OAAO,EAChD,GAAG,SAAS,EACb,GAAG,KAAK,CAAC;IACV,OAAO,0MACL,gBAAA,EAAC,wMAAI,CAAC,IAAI,EAAA;QAAA,qBACW,KAAK;QAAA,GACpB,SAAS;QACb,SAAS,0IAAE,UAAA,AAAU,EACnB,UAAU,EACV,SAAS,EACT,IAAI,CAAC,CAAC,CAAC,CAAA,WAAA,EAAc,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,EACvC,MAAM,CAAC,CAAC,CAAC,CAAA,aAAA,EAAgB,MAAM,EAAE,CAAC,CAAC,CAAC,SAAS,EAC7C,KAAK,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,KAAK,EAAE,CAAC,CAAC,CAAC,SAAS,EACvC,IAAI,CAAC,CAAC,CAAC,CAAA,SAAA,EAAY,IAAI,EAAE,CAAC,CAAC,CAAC,SAAS,EACrC;YAAE,mBAAmB,EAAE,YAAY;QAAA,CAAE,CACtC;IAAA,GAEA,OAAO,CAAC,CAAC,CAAC,QAAQ,CAAC,CAAC,2MAAC,gBAAA,EAAC,GAAG,EAAA,MAAE,QAAQ,CAAO,CACjC,CACb,CAAC;AACJ,CAAC,CAAC;AACF,IAAI,CAAC,WAAW,GAAG,MAAM,CAAC", "debugId": null}}, {"offset": {"line": 777, "column": 0}, "map": {"version": 3, "file": "base-button.props.js", "sourceRoot": "", "sources": ["../../../../src/components/base-button/base-button.props.ts"], "names": [], "mappings": ";;;;AACA,OAAO,EAAE,SAAS,EAAE,gBAAgB,EAAE,MAAM,eAAe,CAAC;;AAE5D,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AAC5C,MAAM,QAAQ,GAAG;IAAC,SAAS;IAAE,OAAO;IAAE,MAAM;IAAE,SAAS;IAAE,OAAO;CAAU,CAAC;AAE3E,MAAM,kBAAkB,GAAG;IACzB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,GAAG;IAAA,CAAE;IACnD,OAAO,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,QAAQ;QAAE,OAAO,EAAE,SAAS;IAAA,CAAE;IAC/D,KAAK,oLAAE,YAAS;IAChB,YAAY,+LAAE,mBAAgB;CAM/B,CAAC", "debugId": null}}, {"offset": {"line": 818, "column": 0}, "map": {"version": 3, "file": "map-prop-values.js", "sourceRoot": "", "sources": ["../../../src/helpers/map-prop-values.ts"], "names": [], "mappings": ";;;;AAIA,SAAS,iBAAiB,CACxB,SAAwC,EACxC,QAAkC;IAElC,IAAI,SAAS,KAAK,SAAS,EAAE,OAAO,SAAS,CAAC;IAC9C,IAAI,OAAO,SAAS,KAAK,QAAQ,EAAE,CAAC;QAClC,OAAO,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC7B,CAAC;IACD,OAAO,MAAM,CAAC,WAAW,CAAC,MAAM,CAAC,OAAO,CAAC,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE,KAAK,CAAC,EAAE,CAAG,CAAD;YAAE,GAAG;YAAE,QAAQ,CAAC,KAAK,CAAC;SAAC,CAAC,CAAC,CAAC;AACrG,CAAC;AAED,SAAS,0BAA0B,CACjC,IAAqD;IAErD,OAAQ,IAAI,EAAE,CAAC;QACb,KAAK,GAAG;YACN,OAAO,GAAG,CAAC;QACb,KAAK,GAAG,CAAC;QACT,KAAK,GAAG;YACN,OAAO,GAAG,CAAC;QACb,KAAK,GAAG;YACN,OAAO,GAAG,CAAC;IACf,CAAC;AACH,CAAC", "debugId": null}}, {"offset": {"line": 851, "column": 0}, "map": {"version": 3, "file": "spinner.props.js", "sourceRoot": "", "sources": ["../../../../src/components/spinner/spinner.props.ts"], "names": [], "mappings": ";;;AAEA,MAAM,KAAK,GAAG;IAAC,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;IAAE,GAAG;CAAU,CAAC;AAEtD,MAAM,eAAe,GAAG;IACtB,IAAI,EAAE;QAAE,IAAI,EAAE,MAAM;QAAE,MAAM,EAAE,KAAK;QAAE,OAAO,EAAE,GAAG;IAAA,CAAE;IACnD,OAAO,EAAE;QAAE,IAAI,EAAE,SAAS;QAAE,OAAO,EAAE,IAAI;IAAA,CAAE;CAI5C,CAAC", "debugId": null}}, {"offset": {"line": 881, "column": 0}, "map": {"version": 3, "file": "spinner.js", "sourceRoot": "", "sources": ["../../../../src/components/spinner/spinner.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,eAAe,EAAE,MAAM,iBAAiB,CAAC;;;;AAOlD,MAAM,OAAO,GAAG,CAAC,KAAmB,EAAE,EAAE;IACtC,MAAM,EACJ,QAAQ,EACR,SAAS,EACT,OAAO,6LAAG,kBAAe,CAAC,OAAO,CAAC,OAAO,EACzC,IAAI,6LAAG,kBAAe,CAAC,IAAI,CAAC,OAAO,EACnC,GAAG,YAAY,EAChB,GAAG,KAAK,CAAC;IAEV,IAAI,CAAC,OAAO,EAAE,OAAO,QAAQ,CAAC;IAE9B,MAAM,OAAO,GAAG,0MACd,gBAAA,EAAA,QAAA;QAAA,GAAU,YAAY;QAAE,SAAS,0IAAE,UAAA,AAAU,EAAC,aAAa,EAAE,CAAA,WAAA,EAAc,IAAI,EAAE,EAAE,SAAS,CAAC;IAAA,IAC3F,yNAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,4MACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,4MACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,4MACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,4MACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,GACpC,yNAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,4MACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,4MACpC,gBAAA,EAAA,QAAA;QAAM,SAAS,EAAC,iBAAiB;IAAA,EAAG,CAC/B,CACR,CAAC;IAEF,IAAI,QAAQ,KAAK,SAAS,EAAE,OAAO,OAAO,CAAC;IAE3C,OAAO,0MACL,gBAAA,EAAA,QAAA;QACE,KAAK,EAAE;YACL,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,QAAQ;YACpB,cAAc,EAAE,QAAQ;SACzB;IAAA,6MAMD,gBAAA,EAAA,QAAA;QAAA,eAAA;QAAkB,KAAK,EAAE;YAAE,OAAO,EAAE,UAAU;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;QAAE,KAAK,EAAA;IAAA,GAC1E,QAAQ,CACJ,4MAEP,gBAAA,EAAA,QAAA;QACE,KAAK,EAAE;YACL,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,QAAQ;YACpB,cAAc,EAAE,QAAQ;YACxB,KAAK,EAAE,GAAG;SACX;IAAA,GAEA,OAAO,CACH,CACF,CACR,CAAC;AACJ,CAAC,CAAC;AACF,OAAO,CAAC,WAAW,GAAG,SAAS,CAAC", "debugId": null}}, {"offset": {"line": 957, "column": 0}, "map": {"version": 3, "file": "base-button.js", "sourceRoot": "", "sources": ["../../../../src/components/base-button/base-button.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,EAAE,IAAI,EAAE,MAAM,UAAU,CAAC;AAChC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAE/B,OAAO,EAAE,kBAAkB,EAAE,MAAM,qBAAqB,CAAC;AAGzD,OAAO,EAAE,0BAA0B,EAAE,MAAM,+BAA+B,CAAC;AAC3E,OAAO,EAAE,OAAO,EAAE,MAAM,YAAY,CAAC;AACrC,OAAO,EAAE,cAAc,EAAE,MAAM,oBAAoB,CAAC;;;;;;;;AAQpD,MAAM,UAAU,GAAG,CAAC,KAAsB,EAAE,EAAE;IAC5C,MAAM,EACJ,QAAQ,EACR,OAAO,EACP,QAAQ,GAAG,KAAK,CAAC,OAAO,EACxB,SAAS,EACT,OAAO,GAAG,KAAK,EACf,IAAI,2MAAG,qBAAkB,CAAC,IAAI,CAAC,OAAO,EACtC,OAAO,2MAAG,qBAAkB,CAAC,OAAO,CAAC,OAAO,EAC5C,KAAK,2MAAG,qBAAkB,CAAC,KAAK,CAAC,OAAO,EACxC,YAAY,2MAAG,qBAAkB,CAAC,YAAY,CAAC,OAAO,EACtD,GAAG,eAAe,EACnB,GAAG,KAAK,CAAC;IACV,MAAM,IAAI,GAAG,OAAO,CAAC,CAAC,kMAAC,OAAI,CAAC,IAAI,CAAC,CAAC,CAAC,QAAQ,CAAC;IAE5C,OAAO,0MACL,gBAAA,EAAC,IAAI,EAAA;QAAA,qBACgB,KAAK,IAAI,CAAC,OAAO,KAAK,SAAS,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC;QAAA,GAChE,eAAe;QACnB,SAAS,EAAE,kJAAA,AAAU,EAAC,WAAW,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAA,WAAA,EAAc,IAAI,EAAE,EAAE,CAAA,YAAA,EAAe,OAAO,EAAE,EAAE;YAC9G,mBAAmB,EAAE,YAAY;SAClC,CAAC;QAAA,aACS,OAAO,IAAI,SAAS;QAAA,iBAEhB,QAAQ,IAAI,SAAS;QACpC,QAAQ,EAAE,QAAQ;IAAA,GAEjB,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,0MACf,gBAAA,EAAA,qMAAA,CAAA,WAAA,EAAA,gNAQE,gBAAA,EAAA,QAAA;QAAM,KAAK,EAAE;YAAE,OAAO,EAAE,UAAU;YAAE,UAAU,EAAE,QAAQ;QAAA,CAAE;QAAA,eAAA;IAAA,GACvD,QAAQ,CACJ,4MACP,gBAAA,2NAAC,iBAAc,CAAC,IAAI,EAAA,MAAE,QAAQ,CAAuB,GAErD,yNAAA,EAAA,QAAA;QACE,KAAK,EAAE;YACL,QAAQ,EAAE,UAAU;YACpB,OAAO,EAAE,MAAM;YACf,UAAU,EAAE,QAAQ;YACpB,cAAc,EAAE,QAAQ;YACxB,KAAK,EAAE,GAAG;SACX;IAAA,6MAED,gBAAA,mLAAC,UAAO,EAAA;QAAC,IAAI,uLAAE,6BAAA,AAA0B,EAAC,IAAI,CAAC;IAAA,EAAI,CAC9C,CACN,CACJ,CAAC,CAAC,AACD,CADE,OACM,CACT,CACI,CACR,CAAC;AACJ,CAAC,CAAC;AACF,UAAU,CAAC,WAAW,GAAG,YAAY,CAAC", "debugId": null}}, {"offset": {"line": 1013, "column": 0}, "map": {"version": 3, "file": "button.js", "sourceRoot": "", "sources": ["../../../../src/components/button/button.tsx"], "names": [], "mappings": ";;;AAAA,OAAO,UAAU,MAAM,YAAY,CAAC;AACpC,OAAO,KAAK,KAAK,MAAM,OAAO,CAAC;AAC/B,OAAO,EAAE,UAAU,EAAE,MAAM,gBAAgB,CAAC;;;;AAI5C,MAAM,MAAM,GAAG,CAAC,KAAkB,EAAE,EAAE,yMAAC,gBAAA,iMAAC,aAAU,EAAA;QAAA,GAAK,KAAK;QAAE,SAAS,0IAAE,UAAA,AAAU,EAAC,YAAY,EAAE,KAAK,CAAC,SAAS,CAAC;IAAA,EAAI,CAAC;AAEvH,MAAM,CAAC,WAAW,GAAG,QAAQ,CAAC", "debugId": null}}]}