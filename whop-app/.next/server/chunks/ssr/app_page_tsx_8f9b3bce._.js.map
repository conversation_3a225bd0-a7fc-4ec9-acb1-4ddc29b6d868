{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Documents/GitHub/Whop-Leaderboard/whop-app/app/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport { Heading, Text, But<PERSON>, Card, Separator } from '@whop/react/components';\n\nexport default function Page() {\n\tconst [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');\n\n\treturn (\n\t\t<div className=\"min-h-screen bg-gray-1 p-3\">\n\t\t\t{/* Navigation */}\n\t\t\t<div className=\"max-w-5xl mx-auto mb-6\">\n\t\t\t\t<Card className=\"p-4 shadow-sm border border-gray-4\">\n\t\t\t\t\t<div className=\"flex items-center justify-between mb-4\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Heading size=\"5\" className=\"text-gray-12 mb-1\">\n\t\t\t\t\t\t\t\tWhop Leaderboards & Competitions\n\t\t\t\t\t\t\t</Heading>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\tCompete, climb, and conquer the leaderboards\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t<div className=\"w-1.5 h-1.5 bg-green-9 rounded-full animate-pulse\"></div>\n\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">Live</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t<div className=\"flex gap-2\">\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tvariant={activeTab === 'leaderboards' ? 'solid' : 'soft'}\n\t\t\t\t\t\t\tcolor={activeTab === 'leaderboards' ? 'green' : 'gray'}\n\t\t\t\t\t\t\tonClick={() => setActiveTab('leaderboards')}\n\t\t\t\t\t\t\tclassName=\"px-4 py-2 text-sm font-medium transition-all duration-200\"\n\t\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t🏆 Leaderboards\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\tvariant={activeTab === 'competitions' ? 'solid' : 'soft'}\n\t\t\t\t\t\t\tcolor={activeTab === 'competitions' ? 'green' : 'gray'}\n\t\t\t\t\t\t\tonClick={() => setActiveTab('competitions')}\n\t\t\t\t\t\t\tclassName=\"px-4 py-2 text-sm font-medium transition-all duration-200\"\n\t\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t\t>\n\t\t\t\t\t\t\t⚔️ Competitions\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\n\t\t\t{/* Content */}\n\t\t\t<div className=\"max-w-5xl mx-auto\">\n\t\t\t\t{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction LeaderboardsPage() {\n\t// Sample leaderboard data\n\tconst leaderboardData = [\n\t\t{ rank: 1, name: \"Alex Thompson\", score: 2847, change: \"+12\", avatar: \"AT\", streak: 7, country: \"🇺🇸\" },\n\t\t{ rank: 2, name: \"Sarah Chen\", score: 2756, change: \"+8\", avatar: \"SC\", streak: 5, country: \"🇨🇦\" },\n\t\t{ rank: 3, name: \"Marcus Johnson\", score: 2698, change: \"-3\", avatar: \"MJ\", streak: 3, country: \"🇬🇧\" },\n\t\t{ rank: 4, name: \"Emma Rodriguez\", score: 2634, change: \"+15\", avatar: \"ER\", streak: 12, country: \"🇪🇸\" },\n\t\t{ rank: 5, name: \"David Kim\", score: 2589, change: \"+5\", avatar: \"DK\", streak: 2, country: \"🇰🇷\" },\n\t\t{ rank: 6, name: \"Lisa Wang\", score: 2543, change: \"-2\", avatar: \"LW\", streak: 8, country: \"🇨🇳\" },\n\t\t{ rank: 7, name: \"James Wilson\", score: 2498, change: \"+7\", avatar: \"JW\", streak: 4, country: \"🇦🇺\" },\n\t\t{ rank: 8, name: \"Maya Patel\", score: 2456, change: \"+3\", avatar: \"MP\", streak: 6, country: \"🇮🇳\" },\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-4\">\n\t\t\t{/* Header */}\n\t\t\t<Card className=\"p-4 bg-gradient-to-r from-green-2 to-green-3 border-green-6\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<Heading size=\"5\" className=\"text-green-12 mb-1\">\n\t\t\t\t\t\tGlobal Leaderboard\n\t\t\t\t\t</Heading>\n\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\tCompete with the best and climb to the top\n\t\t\t\t\t</Text>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Leaderboard */}\n\t\t\t<Card className=\"overflow-hidden shadow-sm\">\n\t\t\t\t<div className=\"p-3 bg-gradient-to-r from-green-1 to-green-2 border-b border-green-4\">\n\t\t\t\t\t<div className=\"flex justify-between items-center\">\n\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t<Heading size=\"4\" className=\"text-green-12\">\n\t\t\t\t\t\t\t\tTop Performers\n\t\t\t\t\t\t\t</Heading>\n\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11 mt-0.5\">\n\t\t\t\t\t\t\t\tUpdated in real-time • Last update: 2 minutes ago\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<Button variant=\"soft\" color=\"green\" size=\"1\">\n\t\t\t\t\t\t\tView All\n\t\t\t\t\t\t</Button>\n\t\t\t\t\t</div>\n\t\t\t\t</div>\n\n\t\t\t\t<div className=\"divide-y divide-gray-4\">\n\t\t\t\t\t{leaderboardData.map((player, index) => (\n\t\t\t\t\t\t<div key={player.rank} className=\"p-3 hover:bg-green-1 transition-all duration-200\">\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t\t\t{/* Rank Badge */}\n\t\t\t\t\t\t\t\t\t<div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-bold ${\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 1 ? 'bg-gradient-to-br from-yellow-9 to-yellow-10 text-yellow-1' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 2 ? 'bg-gradient-to-br from-gray-9 to-gray-10 text-gray-1' :\n\t\t\t\t\t\t\t\t\t\tplayer.rank === 3 ? 'bg-gradient-to-br from-orange-9 to-orange-10 text-orange-1' :\n\t\t\t\t\t\t\t\t\t\t'bg-gradient-to-br from-green-3 to-green-4 text-green-11'\n\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t{player.rank}\n\t\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t\t{/* Player Info */}\n\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t\t<div className=\"w-6 h-6 rounded-full bg-gray-3 flex items-center justify-center text-xs font-semibold text-gray-11\">\n\t\t\t\t\t\t\t\t\t\t\t{player.avatar}\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-1.5\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"medium\" className=\"text-gray-12\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{player.name}\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t\t<span className=\"text-xs\">{player.country}</span>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-1.5 mt-0.5\">\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\t\t\t{player.streak} day streak\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t\t<div className=\"w-0.5 h-0.5 bg-gray-6 rounded-full\"></div>\n\t\t\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\t\t\tRank #{player.rank}\n\t\t\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t\t\t<div className=\"text-right\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-gray-12 block\">\n\t\t\t\t\t\t\t\t\t\t\t{player.score.toLocaleString()}\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t\tpoints\n\t\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<div className={`px-2 py-0.5 rounded-full text-xs font-semibold ${\n\t\t\t\t\t\t\t\t\t\tplayer.change.startsWith('+') ? 'bg-green-3 text-green-11' : 'bg-red-3 text-red-11'\n\t\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t\t{player.change}\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\n\t\t\t\t{/* View More */}\n\t\t\t\t<div className=\"p-3 bg-gray-1 border-t border-gray-4\">\n\t\t\t\t\t<Button variant=\"soft\" color=\"gray\" className=\"w-full\" size=\"1\">\n\t\t\t\t\t\tView Full Leaderboard\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Stats Cards */}\n\t\t\t<div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t1,247\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tTotal Players\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\t+23 this week\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t89.2%\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tActivity Rate\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\t+2.1% from last month\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t2,847\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tTop Score\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\tAlex Thompson\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block mb-0.5\">\n\t\t\t\t\t\t\t156\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"2\" className=\"text-green-11 mb-1\">\n\t\t\t\t\t\t\tAvg Score\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-10\">\n\t\t\t\t\t\t\tDaily average\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n\nfunction CompetitionsPage() {\n\tconst competitions = [\n\t\t{\n\t\t\tid: 1,\n\t\t\ttitle: \"Weekly Challenge\",\n\t\t\tdescription: \"Complete daily tasks to earn points\",\n\t\t\ttimeLeft: \"3d 14h\",\n\t\t\tparticipants: 342,\n\t\t\tprize: \"$500\",\n\t\t\tstatus: \"active\",\n\t\t\tdifficulty: \"Medium\",\n\t\t\ticon: \"✓\",\n\t\t\tprogress: 67\n\t\t},\n\t\t{\n\t\t\tid: 2,\n\t\t\ttitle: \"Monthly Tournament\",\n\t\t\tdescription: \"Ultimate test of skill\",\n\t\t\ttimeLeft: \"5d\",\n\t\t\tparticipants: 89,\n\t\t\tprize: \"$2,000\",\n\t\t\tstatus: \"upcoming\",\n\t\t\tdifficulty: \"Hard\",\n\t\t\ticon: \"🏆\",\n\t\t\tprogress: 0\n\t\t},\n\t\t{\n\t\t\tid: 3,\n\t\t\ttitle: \"Speed Run Challenge\",\n\t\t\tdescription: \"Race against time\",\n\t\t\ttimeLeft: \"2h\",\n\t\t\tparticipants: 156,\n\t\t\tprize: \"$250\",\n\t\t\tstatus: \"active\",\n\t\t\tdifficulty: \"Easy\",\n\t\t\ticon: \"⚡\",\n\t\t\tprogress: 89\n\t\t},\n\t\t{\n\t\t\tid: 4,\n\t\t\ttitle: \"Elite Championship\",\n\t\t\tdescription: \"Invitation-only tournament\",\n\t\t\ttimeLeft: \"Closed\",\n\t\t\tparticipants: 50,\n\t\t\tprize: \"$5,000\",\n\t\t\tstatus: \"closed\",\n\t\t\tdifficulty: \"Expert\",\n\t\t\ticon: \"👑\",\n\t\t\tprogress: 100\n\t\t}\n\t];\n\n\treturn (\n\t\t<div className=\"space-y-4\">\n\t\t\t{/* Header */}\n\t\t\t<Card className=\"p-4 bg-gradient-to-r from-green-2 to-green-3 border-green-6\">\n\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t<Heading size=\"5\" className=\"text-green-12 mb-1\">\n\t\t\t\t\t\tCompetitions\n\t\t\t\t\t</Heading>\n\t\t\t\t\t<Text size=\"2\" className=\"text-green-11\">\n\t\t\t\t\t\tJoin exciting competitions and win amazing prizes\n\t\t\t\t\t</Text>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Featured Competition - Sleek Design */}\n\t\t\t<Card className=\"bg-gray-12 border-gray-11 overflow-hidden relative\">\n\t\t\t\t<div className=\"absolute inset-0 bg-gradient-to-br from-green-9/20 to-green-10/10\"></div>\n\t\t\t\t<div className=\"relative p-4\">\n\t\t\t\t\t{/* Header */}\n\t\t\t\t\t<div className=\"flex items-center justify-between mb-3\">\n\t\t\t\t\t\t<div className=\"flex items-center gap-3\">\n\t\t\t\t\t\t\t<div className=\"w-8 h-8 rounded-lg bg-green-9 flex items-center justify-center shadow-lg shadow-green-9/50\">\n\t\t\t\t\t\t\t\t<span className=\"text-white text-sm font-bold\">✓</span>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div>\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">\n\t\t\t\t\t\t\t\t\tWeekly Challenge\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-3\">\n\t\t\t\t\t\t\t\t\tWhoever makes the most money\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"text-right\">\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"bold\" className=\"text-green-9 block\">\n\t\t\t\t\t\t\t\t$500\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\t\tPrize\n\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Prize Breakdown */}\n\t\t\t\t\t<div className=\"space-y-2 mb-4\">\n\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t<div className=\"w-2 h-2 rounded-full bg-yellow-9 shadow-sm shadow-yellow-9/50\"></div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">1st place</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-white\">$300</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t<div className=\"w-2 h-2 rounded-full bg-gray-9\"></div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">2nd place</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-white\">$150</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t<div className=\"w-2 h-2 rounded-full bg-orange-9\"></div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">3rd place</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-white\">$50</Text>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Timer */}\n\t\t\t\t\t<div className=\"mb-4\">\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11 mb-1\">Time remaining</Text>\n\t\t\t\t\t\t<div className=\"flex items-center gap-1\">\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">0</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">3</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-4\">:</Text>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">1</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">4</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-4\">:</Text>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">5</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"bg-gray-11 rounded px-2 py-1\">\n\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"bold\" className=\"text-white\">0</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Participants */}\n\t\t\t\t\t<div className=\"mb-4\">\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11 mb-2\">Participants (342)</Text>\n\t\t\t\t\t\t<div className=\"space-y-1\">\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t<div className=\"w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-white font-medium\">AS</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">Alex Smith</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-green-9\">$2,847</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t<div className=\"w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-white font-medium\">SC</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">Sarah Chen</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-green-9\">$2,756</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between\">\n\t\t\t\t\t\t\t\t<div className=\"flex items-center gap-2\">\n\t\t\t\t\t\t\t\t\t<div className=\"w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center\">\n\t\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-white font-medium\">MJ</Text>\n\t\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t\t<Text size=\"2\" className=\"text-gray-3\">Marcus Johnson</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"medium\" className=\"text-green-9\">$2,698</Text>\n\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t</div>\n\t\t\t\t\t</div>\n\n\t\t\t\t\t{/* Join Button */}\n\t\t\t\t\t<Button\n\t\t\t\t\t\tclassName=\"w-full bg-green-9 hover:bg-green-10 text-white border-0 shadow-lg shadow-green-9/30\"\n\t\t\t\t\t\tsize=\"2\"\n\t\t\t\t\t>\n\t\t\t\t\t\tJoin Competition\n\t\t\t\t\t</Button>\n\t\t\t\t</div>\n\t\t\t</Card>\n\n\t\t\t{/* Other Competitions */}\n\t\t\t<div>\n\t\t\t\t<Heading size=\"4\" className=\"text-gray-12 mb-3\">\n\t\t\t\t\tOther Competitions\n\t\t\t\t</Heading>\n\t\t\t\t<div className=\"grid grid-cols-1 md:grid-cols-3 gap-3\">\n\t\t\t\t\t{competitions.slice(1).map((comp) => (\n\t\t\t\t\t\t<Card key={comp.id} className=\"p-3 hover:shadow-sm transition-shadow bg-gray-2 border-gray-4\">\n\t\t\t\t\t\t\t<div className=\"flex items-center gap-2 mb-2\">\n\t\t\t\t\t\t\t\t<div className={`w-6 h-6 rounded-lg flex items-center justify-center text-xs ${\n\t\t\t\t\t\t\t\t\tcomp.status === 'active' ? 'bg-green-9 text-white shadow-sm shadow-green-9/50' :\n\t\t\t\t\t\t\t\t\tcomp.status === 'upcoming' ? 'bg-blue-9 text-white' :\n\t\t\t\t\t\t\t\t\t'bg-gray-9 text-white'\n\t\t\t\t\t\t\t\t}`}>\n\t\t\t\t\t\t\t\t\t{comp.icon}\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<div className=\"flex-1\">\n\t\t\t\t\t\t\t\t\t<Text size=\"3\" weight=\"medium\" className=\"text-gray-12\">\n\t\t\t\t\t\t\t\t\t\t{comp.title}\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-10\">\n\t\t\t\t\t\t\t\t\t\t{comp.description}\n\t\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t\t</div>\n\t\t\t\t\t\t\t\t<Text size=\"2\" weight=\"bold\" className=\"text-green-11\">\n\t\t\t\t\t\t\t\t\t{comp.prize}\n\t\t\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<div className=\"flex items-center justify-between mb-2\">\n\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-11\">{comp.timeLeft}</Text>\n\t\t\t\t\t\t\t\t<Text size=\"1\" className=\"text-gray-11\">{comp.participants} players</Text>\n\t\t\t\t\t\t\t</div>\n\n\t\t\t\t\t\t\t<Button\n\t\t\t\t\t\t\t\tcolor=\"green\"\n\t\t\t\t\t\t\t\tvariant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}\n\t\t\t\t\t\t\t\tclassName=\"w-full\"\n\t\t\t\t\t\t\t\tsize=\"1\"\n\t\t\t\t\t\t\t\tdisabled={comp.status === 'closed'}\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{comp.status === 'active' ? 'Join' :\n\t\t\t\t\t\t\t\t comp.status === 'upcoming' ? 'Register' :\n\t\t\t\t\t\t\t\t 'Closed'}\n\t\t\t\t\t\t\t</Button>\n\t\t\t\t\t\t</Card>\n\t\t\t\t\t))}\n\t\t\t\t</div>\n\t\t\t</div>\n\n\t\t\t{/* Competition Stats */}\n\t\t\t<div className=\"grid grid-cols-2 md:grid-cols-4 gap-3\">\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t12\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tActive Competitions\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t$15K\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tTotal Prize Pool\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t847\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tTotal Participants\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\n\t\t\t\t<Card className=\"p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4\">\n\t\t\t\t\t<div className=\"text-center\">\n\t\t\t\t\t\t<Text size=\"4\" weight=\"bold\" className=\"text-green-12 block\">\n\t\t\t\t\t\t\t24h\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t\t<Text size=\"1\" className=\"text-green-11\">\n\t\t\t\t\t\t\tAvg Duration\n\t\t\t\t\t\t</Text>\n\t\t\t\t\t</div>\n\t\t\t\t</Card>\n\t\t\t</div>\n\t\t</div>\n\t);\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAHA;;;;AAKe,SAAS;IACvB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmC;IAE5E,qBACC,8OAAC;QAAI,WAAU;;0BAEd,8OAAC;gBAAI,WAAU;0BACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oBAAC,WAAU;;sCACf,8OAAC;4BAAI,WAAU;;8CACd,8OAAC;;sDACA,8OAAC,gLAAA,CAAA,UAAO;4CAAC,MAAK;4CAAI,WAAU;sDAAoB;;;;;;sDAGhD,8OAAC,0KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAe;;;;;;;;;;;;8CAIzC,8OAAC;oCAAI,WAAU;;sDACd,8OAAC;4CAAI,WAAU;;;;;;sDACf,8OAAC,0KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAgB;;;;;;;;;;;;;;;;;;sCAI3C,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,8KAAA,CAAA,SAAM;oCACN,SAAS,cAAc,iBAAiB,UAAU;oCAClD,OAAO,cAAc,iBAAiB,UAAU;oCAChD,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,MAAK;8CACL;;;;;;8CAGD,8OAAC,8KAAA,CAAA,SAAM;oCACN,SAAS,cAAc,iBAAiB,UAAU;oCAClD,OAAO,cAAc,iBAAiB,UAAU;oCAChD,SAAS,IAAM,aAAa;oCAC5B,WAAU;oCACV,MAAK;8CACL;;;;;;;;;;;;;;;;;;;;;;;0BAQJ,8OAAC;gBAAI,WAAU;0BACb,cAAc,+BAAiB,8OAAC;;;;yCAAsB,8OAAC;;;;;;;;;;;;;;;;AAI5D;AAEA,SAAS;IACR,0BAA0B;IAC1B,MAAM,kBAAkB;QACvB;YAAE,MAAM;YAAG,MAAM;YAAiB,OAAO;YAAM,QAAQ;YAAO,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACvG;YAAE,MAAM;YAAG,MAAM;YAAc,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACnG;YAAE,MAAM;YAAG,MAAM;YAAkB,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACvG;YAAE,MAAM;YAAG,MAAM;YAAkB,OAAO;YAAM,QAAQ;YAAO,QAAQ;YAAM,QAAQ;YAAI,SAAS;QAAO;QACzG;YAAE,MAAM;YAAG,MAAM;YAAa,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QAClG;YAAE,MAAM;YAAG,MAAM;YAAa,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QAClG;YAAE,MAAM;YAAG,MAAM;YAAgB,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;QACrG;YAAE,MAAM;YAAG,MAAM;YAAc,OAAO;YAAM,QAAQ;YAAM,QAAQ;YAAM,QAAQ;YAAG,SAAS;QAAO;KACnG;IAED,qBACC,8OAAC;QAAI,WAAU;;0BAEd,8OAAC,0KAAA,CAAA,OAAI;gBAAC,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACd,8OAAC,gLAAA,CAAA,UAAO;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAGjD,8OAAC,0KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,0KAAA,CAAA,OAAI;gBAAC,WAAU;;kCACf,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC;;sDACA,8OAAC,gLAAA,CAAA,UAAO;4CAAC,MAAK;4CAAI,WAAU;sDAAgB;;;;;;sDAG5C,8OAAC,0KAAA,CAAA,OAAI;4CAAC,MAAK;4CAAI,WAAU;sDAAuB;;;;;;;;;;;;8CAIjD,8OAAC,8KAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAO,OAAM;oCAAQ,MAAK;8CAAI;;;;;;;;;;;;;;;;;kCAMhD,8OAAC;wBAAI,WAAU;kCACb,gBAAgB,GAAG,CAAC,CAAC,QAAQ,sBAC7B,8OAAC;gCAAsB,WAAU;0CAChC,cAAA,8OAAC;oCAAI,WAAU;;sDACd,8OAAC;4CAAI,WAAU;;8DAEd,8OAAC;oDAAI,WAAW,CAAC,wEAAwE,EACxF,OAAO,IAAI,KAAK,IAAI,+DACpB,OAAO,IAAI,KAAK,IAAI,yDACpB,OAAO,IAAI,KAAK,IAAI,+DACpB,2DACC;8DACA,OAAO,IAAI;;;;;;8DAIb,8OAAC;oDAAI,WAAU;;sEACd,8OAAC;4DAAI,WAAU;sEACb,OAAO,MAAM;;;;;;sEAEf,8OAAC;;8EACA,8OAAC;oEAAI,WAAU;;sFACd,8OAAC,0KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,QAAO;4EAAS,WAAU;sFACvC,OAAO,IAAI;;;;;;sFAEb,8OAAC;4EAAK,WAAU;sFAAW,OAAO,OAAO;;;;;;;;;;;;8EAE1C,8OAAC;oEAAI,WAAU;;sFACd,8OAAC,0KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,WAAU;;gFACvB,OAAO,MAAM;gFAAC;;;;;;;sFAEhB,8OAAC;4EAAI,WAAU;;;;;;sFACf,8OAAC,0KAAA,CAAA,OAAI;4EAAC,MAAK;4EAAI,WAAU;;gFAAe;gFAChC,OAAO,IAAI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sDAOvB,8OAAC;4CAAI,WAAU;;8DACd,8OAAC;oDAAI,WAAU;;sEACd,8OAAC,0KAAA,CAAA,OAAI;4DAAC,MAAK;4DAAI,QAAO;4DAAO,WAAU;sEACrC,OAAO,KAAK,CAAC,cAAc;;;;;;sEAE7B,8OAAC,0KAAA,CAAA,OAAI;4DAAC,MAAK;4DAAI,WAAU;sEAAe;;;;;;;;;;;;8DAIzC,8OAAC;oDAAI,WAAW,CAAC,+CAA+C,EAC/D,OAAO,MAAM,CAAC,UAAU,CAAC,OAAO,6BAA6B,wBAC5D;8DACA,OAAO,MAAM;;;;;;;;;;;;;;;;;;+BAlDR,OAAO,IAAI;;;;;;;;;;kCA2DvB,8OAAC;wBAAI,WAAU;kCACd,cAAA,8OAAC,8KAAA,CAAA,SAAM;4BAAC,SAAQ;4BAAO,OAAM;4BAAO,WAAU;4BAAS,MAAK;sCAAI;;;;;;;;;;;;;;;;;0BAOlE,8OAAC;gBAAI,WAAU;;kCACd,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAA6B;;;;;;8CAGpE,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAqB;;;;;;8CAG9C,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C;AAEA,SAAS;IACR,MAAM,eAAe;QACpB;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,UAAU;QACX;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,UAAU;QACX;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,UAAU;QACX;QACA;YACC,IAAI;YACJ,OAAO;YACP,aAAa;YACb,UAAU;YACV,cAAc;YACd,OAAO;YACP,QAAQ;YACR,YAAY;YACZ,MAAM;YACN,UAAU;QACX;KACA;IAED,qBACC,8OAAC;QAAI,WAAU;;0BAEd,8OAAC,0KAAA,CAAA,OAAI;gBAAC,WAAU;0BACf,cAAA,8OAAC;oBAAI,WAAU;;sCACd,8OAAC,gLAAA,CAAA,UAAO;4BAAC,MAAK;4BAAI,WAAU;sCAAqB;;;;;;sCAGjD,8OAAC,0KAAA,CAAA,OAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAgB;;;;;;;;;;;;;;;;;0BAO3C,8OAAC,0KAAA,CAAA,OAAI;gBAAC,WAAU;;kCACf,8OAAC;wBAAI,WAAU;;;;;;kCACf,8OAAC;wBAAI,WAAU;;0CAEd,8OAAC;gCAAI,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAI,WAAU;0DACd,cAAA,8OAAC;oDAAK,WAAU;8DAA+B;;;;;;;;;;;0DAEhD,8OAAC;;kEACA,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAO,WAAU;kEAAa;;;;;;kEAGpD,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAe;;;;;;;;;;;;;;;;;;kDAK1C,8OAAC;wCAAI,WAAU;;0DACd,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAO,WAAU;0DAAqB;;;;;;0DAG5D,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAgB;;;;;;;;;;;;;;;;;;0CAO3C,8OAAC;gCAAI,WAAU;;kDACd,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAc;;;;;;;;;;;;0DAExC,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DAAa;;;;;;;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAc;;;;;;;;;;;;0DAExC,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DAAa;;;;;;;;;;;;kDAEvD,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEAAc;;;;;;;;;;;;0DAExC,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAS,WAAU;0DAAa;;;;;;;;;;;;;;;;;;0CAKxD,8OAAC;gCAAI,WAAU;;kDACd,8OAAC,0KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAqB;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAI,WAAU;0DACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,8OAAC;gDAAI,WAAU;0DACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAc;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,8OAAC;gDAAI,WAAU;0DACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAc;;;;;;0DACvC,8OAAC;gDAAI,WAAU;0DACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;0DAErD,8OAAC;gDAAI,WAAU;0DACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oDAAC,MAAK;oDAAI,QAAO;oDAAO,WAAU;8DAAa;;;;;;;;;;;;;;;;;;;;;;;0CAMvD,8OAAC;gCAAI,WAAU;;kDACd,8OAAC,0KAAA,CAAA,OAAI;wCAAC,MAAK;wCAAI,WAAU;kDAAqB;;;;;;kDAC9C,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;0EACd,8OAAC;gEAAI,WAAU;0EACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oEAAC,MAAK;oEAAI,WAAU;8EAAyB;;;;;;;;;;;0EAEnD,8OAAC,0KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAExC,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;0EACd,8OAAC;gEAAI,WAAU;0EACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oEAAC,MAAK;oEAAI,WAAU;8EAAyB;;;;;;;;;;;0EAEnD,8OAAC,0KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAExC,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEAAe;;;;;;;;;;;;0DAEzD,8OAAC;gDAAI,WAAU;;kEACd,8OAAC;wDAAI,WAAU;;0EACd,8OAAC;gEAAI,WAAU;0EACd,cAAA,8OAAC,0KAAA,CAAA,OAAI;oEAAC,MAAK;oEAAI,WAAU;8EAAyB;;;;;;;;;;;0EAEnD,8OAAC,0KAAA,CAAA,OAAI;gEAAC,MAAK;gEAAI,WAAU;0EAAc;;;;;;;;;;;;kEAExC,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEAAe;;;;;;;;;;;;;;;;;;;;;;;;0CAM3D,8OAAC,8KAAA,CAAA,SAAM;gCACN,WAAU;gCACV,MAAK;0CACL;;;;;;;;;;;;;;;;;;0BAOH,8OAAC;;kCACA,8OAAC,gLAAA,CAAA,UAAO;wBAAC,MAAK;wBAAI,WAAU;kCAAoB;;;;;;kCAGhD,8OAAC;wBAAI,WAAU;kCACb,aAAa,KAAK,CAAC,GAAG,GAAG,CAAC,CAAC,qBAC3B,8OAAC,0KAAA,CAAA,OAAI;gCAAe,WAAU;;kDAC7B,8OAAC;wCAAI,WAAU;;0DACd,8OAAC;gDAAI,WAAW,CAAC,4DAA4D,EAC5E,KAAK,MAAM,KAAK,WAAW,sDAC3B,KAAK,MAAM,KAAK,aAAa,yBAC7B,wBACC;0DACA,KAAK,IAAI;;;;;;0DAEX,8OAAC;gDAAI,WAAU;;kEACd,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,QAAO;wDAAS,WAAU;kEACvC,KAAK,KAAK;;;;;;kEAEZ,8OAAC,0KAAA,CAAA,OAAI;wDAAC,MAAK;wDAAI,WAAU;kEACvB,KAAK,WAAW;;;;;;;;;;;;0DAGnB,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,QAAO;gDAAO,WAAU;0DACrC,KAAK,KAAK;;;;;;;;;;;;kDAIb,8OAAC;wCAAI,WAAU;;0DACd,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;0DAAgB,KAAK,QAAQ;;;;;;0DACtD,8OAAC,0KAAA,CAAA,OAAI;gDAAC,MAAK;gDAAI,WAAU;;oDAAgB,KAAK,YAAY;oDAAC;;;;;;;;;;;;;kDAG5D,8OAAC,8KAAA,CAAA,SAAM;wCACN,OAAM;wCACN,SAAS,KAAK,MAAM,KAAK,WAAW,UAAU,KAAK,MAAM,KAAK,aAAa,SAAS;wCACpF,WAAU;wCACV,MAAK;wCACL,UAAU,KAAK,MAAM,KAAK;kDAEzB,KAAK,MAAM,KAAK,WAAW,SAC3B,KAAK,MAAM,KAAK,aAAa,aAC7B;;;;;;;+BApCQ,KAAK,EAAE;;;;;;;;;;;;;;;;0BA4CrB,8OAAC;gBAAI,WAAU;;kCACd,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;kCAM3C,8OAAC,0KAAA,CAAA,OAAI;wBAAC,WAAU;kCACf,cAAA,8OAAC;4BAAI,WAAU;;8CACd,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,QAAO;oCAAO,WAAU;8CAAsB;;;;;;8CAG7D,8OAAC,0KAAA,CAAA,OAAI;oCAAC,MAAK;oCAAI,WAAU;8CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQ/C", "debugId": null}}]}