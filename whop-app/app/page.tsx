'use client';

import { useState } from 'react';
import { Heading, Text, Button, Card, Separator } from '@whop/react/components';

export default function Page() {
	const [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');

	return (
		<div className="min-h-screen bg-black">
			{/* Top Navigation Bar */}
			<nav className="border-b border-gray-8 bg-black">
				<div className="max-w-6xl mx-auto px-6 py-4">
					<div className="flex items-center justify-between">
						<div>
							<Heading size="5" className="text-white">
								Whop Leaderboards
							</Heading>
							<Text size="2" className="text-gray-11">
								Compete and climb the rankings
							</Text>
						</div>
						<div className="flex items-center gap-1 bg-gray-11 rounded-lg p-1">
							<Button
								variant={activeTab === 'leaderboards' ? 'solid' : 'ghost'}
								color={activeTab === 'leaderboards' ? 'blue' : 'gray'}
								onClick={() => setActiveTab('leaderboards')}
								size="2"
								className="text-sm"
							>
								Leaderboards
							</Button>
							<Button
								variant={activeTab === 'competitions' ? 'solid' : 'ghost'}
								color={activeTab === 'competitions' ? 'blue' : 'gray'}
								onClick={() => setActiveTab('competitions')}
								size="2"
								className="text-sm"
							>
								Competitions
							</Button>
						</div>
					</div>
				</div>
			</nav>

			{/* Content */}
			<div className="max-w-6xl mx-auto px-6 py-6">
				{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}
			</div>
		</div>
	);
}

function LeaderboardsPage() {
	// Sample leaderboard data
	const leaderboardData = [
		{ rank: 1, name: "Jack Sharkey", handle: "@shark", earnings: 26800, avatar: "🏆" },
		{ rank: 2, name: "Tyler", handle: "@methodicalstew", earnings: 21344, avatar: "👤" },
		{ rank: 3, name: "Shaq", handle: "@shaq4257", earnings: 14565, avatar: "🥉" },
		{ rank: 4, name: "Ilya Miskov", handle: "@ilyamiskov", earnings: 13915, avatar: "👤" },
		{ rank: 5, name: "Savnatra", handle: "@savnatra", earnings: 11141, avatar: "👤" },
		{ rank: 6, name: "Travis Williams", handle: "@user673237", earnings: 9820, avatar: "👤" },
		{ rank: 7, name: "Amirah Robinson", handle: "@amirahgirl", earnings: 8760, avatar: "👤" },
		{ rank: 8, name: "AB", handle: "@abonsocials", earnings: 8105, avatar: "👤" },
		{ rank: 9, name: "Savnatra", handle: "@savnatra", earnings: 7677, avatar: "👤" },
	];

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="mb-6">
				<Heading size="6" className="text-white mb-2">
					Global Leaderboard
				</Heading>
				<Text size="3" className="text-gray-11">
					Compete with the best and climb to the top
				</Text>
			</div>

			{/* Leaderboard */}
			<div className="bg-black border border-gray-8 rounded-xl overflow-hidden">
				<div className="px-4 py-3 border-b border-gray-8">
					<Heading size="4" className="text-white">
						Leaderboard
					</Heading>
				</div>

				<div className="divide-y divide-gray-8">
					{leaderboardData.map((player, index) => (
						<div key={player.rank} className="px-4 py-3 hover:bg-gray-12 transition-all duration-200">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									{/* Rank Badge */}
									<div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
										player.rank === 1 ? 'bg-yellow-9 text-black' :
										player.rank === 2 ? 'bg-gray-8 text-white' :
										player.rank === 3 ? 'bg-orange-9 text-white' :
										'bg-gray-9 text-white'
									}`}>
										{player.rank}
									</div>

									{/* Player Info */}
									<div className="flex items-center gap-3">
										<div className="w-8 h-8 rounded-full bg-gray-9 flex items-center justify-center">
											<Text size="2" className="text-white">
												{player.avatar}
											</Text>
										</div>
										<div>
											<Text size="3" weight="medium" className="text-white">
												{player.name}
											</Text>
											<Text size="2" className="text-gray-11">
												{player.handle}
											</Text>
										</div>
									</div>
								</div>

								<div className="flex items-center gap-2">
									<div className={`w-2 h-2 rounded-full ${
										player.rank === 4 ? 'bg-blue-9' : 'bg-green-9'
									}`}></div>
									<Text size="3" weight="bold" className="text-white">
										${player.earnings.toLocaleString()}
									</Text>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Stats */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
				<div className="bg-black border border-gray-8 rounded-xl p-4 text-center">
					<Text size="5" weight="bold" className="text-white">
						$50,000
					</Text>
				</div>

				<div className="bg-black border border-gray-8 rounded-xl p-4 text-center">
					<Text size="5" weight="bold" className="text-white">
						$89,200
					</Text>
				</div>

				<div className="bg-black border border-gray-8 rounded-xl p-4 text-center">
					<Text size="5" weight="bold" className="text-white">
						$26,800
					</Text>
				</div>

				<div className="bg-black border border-gray-8 rounded-xl p-4 text-center">
					<Text size="5" weight="bold" className="text-white">
						$15,600
					</Text>
				</div>
			</div>
		</div>
	);
}

function CompetitionsPage() {
	const competitions = [
		{
			id: 1,
			title: "Weekly Challenge",
			description: "Complete daily tasks to earn money and climb the weekly leaderboard",
			timeLeft: "3d 14h",
			participants: 342,
			prize: "$500",
			status: "active"
		},
		{
			id: 2,
			title: "Monthly Tournament",
			description: "Ultimate test of skill for the top money makers",
			timeLeft: "5d",
			participants: 89,
			prize: "$2,000",
			status: "upcoming"
		},
		{
			id: 3,
			title: "Speed Run Challenge",
			description: "Race against time to earn the most money",
			timeLeft: "2h",
			participants: 156,
			prize: "$250",
			status: "active"
		},
		{
			id: 4,
			title: "Elite Championship",
			description: "Invitation-only tournament for elite earners",
			timeLeft: "Closed",
			participants: 50,
			prize: "$5,000",
			status: "closed"
		}
	];

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="mb-6">
				<Heading size="6" className="text-white mb-2">
					Competitions
				</Heading>
				<Text size="3" className="text-gray-11">
					Join exciting competitions and win amazing prizes
				</Text>
			</div>

			<div className="space-y-4">
				{competitions.map((comp) => (
					<div key={comp.id} className="bg-black border border-gray-8 rounded-xl p-6 relative overflow-hidden">
						{/* Blue accent line */}
						<div className={`absolute left-0 top-0 bottom-0 w-1 ${
							comp.status === 'active' ? 'bg-blue-9' :
							comp.status === 'upcoming' ? 'bg-blue-8' :
							'bg-gray-8'
						}`}></div>

						<div className="flex items-center justify-between mb-4">
							<div className="flex items-center gap-4">
								<div className={`w-10 h-10 rounded-xl flex items-center justify-center ${
									comp.status === 'active' ? 'bg-blue-9' :
									comp.status === 'upcoming' ? 'bg-blue-8' :
									'bg-gray-8'
								}`}>
									<div className="w-5 h-5 bg-white rounded-full flex items-center justify-center">
										<div className={`w-2.5 h-2.5 rounded-full ${
											comp.status === 'active' ? 'bg-blue-9' :
											comp.status === 'upcoming' ? 'bg-blue-8' :
											'bg-gray-8'
										}`}></div>
									</div>
								</div>
								<div>
									<Heading size="4" className="text-white mb-1">
										{comp.title}
									</Heading>
									<Text size="2" className="text-gray-11">
										{comp.description}
									</Text>
								</div>
							</div>
							<div className="text-right">
								<Text size="4" weight="bold" className={`block ${
									comp.status === 'active' ? 'text-blue-9' : 'text-gray-11'
								}`}>
									{comp.prize}
								</Text>
								<Text size="2" className="text-gray-11">
									Prize Pool
								</Text>
							</div>
						</div>

						<div className="grid grid-cols-3 gap-6 mb-4">
							<div>
								<Text size="2" className="text-gray-11 mb-1">
									Time Remaining
								</Text>
								<Text size="3" weight="medium" className="text-white">
									{comp.timeLeft}
								</Text>
							</div>
							<div>
								<Text size="2" className="text-gray-11 mb-1">
									Participants
								</Text>
								<Text size="3" weight="medium" className="text-white">
									{comp.participants}
								</Text>
							</div>
							<div>
								<Text size="2" className="text-gray-11 mb-1">
									Status
								</Text>
								<div className={`inline-flex px-3 py-1 rounded-full text-sm font-medium ${
									comp.status === 'active' ? 'bg-blue-9/20 text-blue-9 border border-blue-9/30' :
									comp.status === 'upcoming' ? 'bg-blue-8/20 text-blue-8 border border-blue-8/30' :
									'bg-gray-8/20 text-gray-8 border border-gray-8/30'
								}`}>
									{comp.status.charAt(0).toUpperCase() + comp.status.slice(1)}
								</div>
							</div>
						</div>

						<div className="flex items-center gap-3">
							<Button
								color="blue"
								variant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}
								disabled={comp.status === 'closed'}
								className="flex-1"
								size="2"
							>
								{comp.status === 'active' ? 'Join Competition' :
								 comp.status === 'upcoming' ? 'Register Now' :
								 'Competition Closed'}
							</Button>
							<Button variant="ghost" color="gray" size="2">
								View Details
							</Button>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
