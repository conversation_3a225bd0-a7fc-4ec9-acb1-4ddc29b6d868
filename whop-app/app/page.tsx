'use client';

import { useState } from 'react';
import { Heading, Text, But<PERSON>, Card, Separator } from '@whop/react/components';

export default function Page() {
	const [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');

	return (
		<div className="min-h-screen bg-black">
			{/* Top Navigation Bar */}
			<nav className="border-b border-blue-9/20 bg-black">
				<div className="max-w-6xl mx-auto px-6 py-4">
					<div className="flex items-center justify-center">
						<div className="flex items-center gap-1 bg-blue-9/10 rounded-lg p-1 shadow-lg shadow-blue-9/20">
							<Button
								variant={activeTab === 'leaderboards' ? 'solid' : 'ghost'}
								color={activeTab === 'leaderboards' ? 'blue' : 'gray'}
								onClick={() => setActiveTab('leaderboards')}
								size="2"
								className="text-sm"
							>
								Leaderboards
							</Button>
							<Button
								variant={activeTab === 'competitions' ? 'solid' : 'ghost'}
								color={activeTab === 'competitions' ? 'blue' : 'gray'}
								onClick={() => setActiveTab('competitions')}
								size="2"
								className="text-sm"
							>
								Competitions
							</Button>
						</div>
					</div>
				</div>
			</nav>

			{/* Content */}
			<div className="max-w-6xl mx-auto px-6 py-6">
				{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}
			</div>
		</div>
	);
}

function LeaderboardsPage() {
	const leaderboardData = [
		{ rank: 1, name: "Jack Sharkey", handle: "@shark", earnings: 26800 },
		{ rank: 2, name: "Tyler", handle: "@methodicalstew", earnings: 21344 },
		{ rank: 3, name: "Shaq", handle: "@shaq4257", earnings: 14565 },
		{ rank: 4, name: "Ilya Miskov", handle: "@ilyamiskov", earnings: 13915 },
		{ rank: 5, name: "Savnatra", handle: "@savnatra", earnings: 11141 },
		{ rank: 6, name: "Travis Williams", handle: "@user673237", earnings: 9820 },
		{ rank: 7, name: "Amirah Robinson", handle: "@amirahgirl", earnings: 8760 },
		{ rank: 8, name: "AB", handle: "@abonsocials", earnings: 8105 },
	];

	return (
		<div className="space-y-3">
			<div className="bg-gradient-to-br from-blue-9/10 to-black border border-blue-9/20 rounded-lg overflow-hidden">
				<div className="divide-y divide-blue-9/10">
					{leaderboardData.map((player) => (
						<div key={player.rank} className="px-3 py-2 hover:bg-blue-9/5 transition-colors">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<div className={`w-5 h-5 rounded-full flex items-center justify-center text-xs font-bold ${
										player.rank === 1 ? 'bg-yellow-9 text-black' :
										player.rank === 2 ? 'bg-gray-8 text-white' :
										player.rank === 3 ? 'bg-orange-9 text-white' :
										'bg-blue-9/20 text-blue-9'
									}`}>
										{player.rank}
									</div>

									<div className="w-6 h-6 rounded-full bg-blue-9/20 flex items-center justify-center">
										<Text size="1" weight="medium" className="text-blue-9">
											{player.name.split(' ').map(n => n[0]).join('')}
										</Text>
									</div>

									<div>
										<Text size="2" weight="medium" className="text-white">
											{player.name}
										</Text>
										<Text size="1" className="text-gray-11">
											{player.handle}
										</Text>
									</div>
								</div>

								<div className="flex items-center gap-1">
									<div className="w-1.5 h-1.5 rounded-full bg-green-9"></div>
									<Text size="2" weight="bold" className="text-white">
										${player.earnings.toLocaleString()}
									</Text>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}

function CompetitionsPage() {
	const competition = {
		title: "$100K views challenge with Iman Gadzhi for a Lambo",
		grandPrize: "Orange Lamborghini Aventador",
		smallerPrizes: "+7 smaller prizes",
		winCondition: "Whoever makes the most money",
		days: "03",
		hours: "14",
		minutes: "15",
		seconds: "03",
		image: "🏎️"
	};

	return (
		<div className="flex justify-start">
			<Card className="w-80 overflow-hidden border border-gray-8">
				{/* Blue top section like navbar */}
				<div className="bg-gradient-to-br from-blue-9/10 to-blue-11/20 p-4 border-b border-blue-9/20">
					{/* Title with glow */}
					<Text size="3" weight="medium" className="text-white leading-tight mb-4" style={{
						textShadow: '0 0 10px rgba(59, 130, 246, 0.5)'
					}}>
						{competition.title}
					</Text>

					{/* Prize section */}
					<div className="flex items-start gap-3">
						<div className="w-16 h-16 rounded-xl bg-gradient-to-br from-blue-9/30 to-blue-11/30 border border-blue-9/40 flex items-center justify-center"
							style={{
								boxShadow: '0 0 20px rgba(59, 130, 246, 0.4)'
							}}>
							<Text size="6">
								{competition.image}
							</Text>
						</div>
						<div className="flex-1">
							<Text size="1" className="text-gray-11 mb-1">
								Grand prize
							</Text>
							<Text size="2" weight="medium" className="text-white mb-1">
								{competition.grandPrize}
							</Text>
							<Text size="1" className="text-gray-11">
								{competition.smallerPrizes}
							</Text>
						</div>
					</div>
				</div>

				{/* Black bottom section */}
				<div className="bg-black p-4 space-y-4">
					{/* Win condition */}
					<div>
						<Text size="1" className="text-gray-11 mb-1">
							Win condition
						</Text>
						<Text size="2" weight="medium" className="text-white">
							{competition.winCondition}
						</Text>
					</div>

					{/* Join button */}
					<Button
						color="blue"
						variant="solid"
						className="w-full"
						size="2"
					>
						Join Competition
					</Button>

					{/* Timer */}
					<div>
						<Text size="1" className="text-gray-11 mb-2">
							Starts in
						</Text>
						<div className="grid grid-cols-4 gap-1">
							<div className="text-center space-y-1">
								<div className="bg-gray-11 rounded-lg p-2 h-12 flex flex-col justify-center">
									<Text size="3" weight="bold" className="text-white">
										{competition.days}
									</Text>
								</div>
								<Text size="1" className="text-gray-11">
									Days
								</Text>
							</div>
							<div className="text-center space-y-1">
								<div className="bg-gray-11 rounded-lg p-2 h-12 flex flex-col justify-center">
									<Text size="3" weight="bold" className="text-white">
										{competition.hours}
									</Text>
								</div>
								<Text size="1" className="text-gray-11">
									Hours
								</Text>
							</div>
							<div className="text-center space-y-1">
								<div className="bg-gray-11 rounded-lg p-2 h-12 flex flex-col justify-center">
									<Text size="3" weight="bold" className="text-white">
										{competition.minutes}
									</Text>
								</div>
								<Text size="1" className="text-gray-11">
									Minutes
								</Text>
							</div>
							<div className="text-center space-y-1">
								<div className="bg-gray-11 rounded-lg p-2 h-12 flex flex-col justify-center">
									<Text size="3" weight="bold" className="text-white">
										{competition.seconds}
									</Text>
								</div>
								<Text size="1" className="text-gray-11">
									Seconds
								</Text>
							</div>
						</div>
					</div>
				</div>
			</Card>
		</div>
	);
}
