'use client';

import { useState } from 'react';
import { Heading, Text, But<PERSON>, Card, Separator } from '@whop/react/components';

export default function Page() {
	const [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');

	return (
		<div className="min-h-screen bg-black">
			{/* Top Navigation Bar */}
			<nav className="border-b border-blue-9/20 bg-black">
				<div className="max-w-6xl mx-auto px-6 py-4">
					<div className="flex items-center justify-center">
						<div className="flex items-center gap-1 bg-blue-9/10 rounded-lg p-1 shadow-lg shadow-blue-9/20">
							<Button
								variant={activeTab === 'leaderboards' ? 'solid' : 'ghost'}
								color={activeTab === 'leaderboards' ? 'blue' : 'gray'}
								onClick={() => setActiveTab('leaderboards')}
								size="2"
								className="text-sm"
							>
								Leaderboards
							</Button>
							<Button
								variant={activeTab === 'competitions' ? 'solid' : 'ghost'}
								color={activeTab === 'competitions' ? 'blue' : 'gray'}
								onClick={() => setActiveTab('competitions')}
								size="2"
								className="text-sm"
							>
								Competitions
							</Button>
						</div>
					</div>
				</div>
			</nav>

			{/* Content */}
			<div className="max-w-6xl mx-auto px-6 py-6">
				{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}
			</div>
		</div>
	);
}

function LeaderboardsPage() {
	const leaderboardData = [
		{ rank: 1, name: "Jack Sharkey", handle: "@shark", earnings: 26800 },
		{ rank: 2, name: "Tyler", handle: "@methodicalstew", earnings: 21344 },
		{ rank: 3, name: "Shaq", handle: "@shaq4257", earnings: 14565 },
		{ rank: 4, name: "Ilya Miskov", handle: "@ilyamiskov", earnings: 13915 },
		{ rank: 5, name: "Savnatra", handle: "@savnatra", earnings: 11141 },
		{ rank: 6, name: "Travis Williams", handle: "@user673237", earnings: 9820 },
		{ rank: 7, name: "Amirah Robinson", handle: "@amirahgirl", earnings: 8760 },
		{ rank: 8, name: "AB", handle: "@abonsocials", earnings: 8105 },
	];

	return (
		<div className="space-y-4">
			<div className="bg-gray-12 border border-gray-8 rounded-lg overflow-hidden">
				<div className="divide-y divide-gray-8">
					{leaderboardData.map((player) => (
						<div key={player.rank} className="px-4 py-3 hover:bg-gray-11 transition-colors">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<Text size="3" weight="bold" className={`w-6 text-center ${
										player.rank === 1 ? 'text-yellow-9' :
										player.rank === 2 ? 'text-gray-9' :
										player.rank === 3 ? 'text-orange-9' :
										'text-gray-11'
									}`}>
										{player.rank}
									</Text>

									<div className="w-8 h-8 rounded-full bg-gray-9 flex items-center justify-center">
										<Text size="2" weight="medium" className="text-white">
											{player.name.split(' ').map(n => n[0]).join('')}
										</Text>
									</div>

									<div>
										<Text size="3" weight="medium" className="text-white">
											{player.name}
										</Text>
										<Text size="2" className="text-gray-11">
											{player.handle}
										</Text>
									</div>
								</div>

								<div className="flex items-center gap-2">
									<div className="w-2 h-2 rounded-full bg-green-9"></div>
									<Text size="3" weight="bold" className="text-white">
										${player.earnings.toLocaleString()}
									</Text>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>
		</div>
	);
}

function CompetitionsPage() {
	const competitions = [
		{
			id: 1,
			title: "Weekly Challenge",
			timeLeft: "3d 14h",
			participants: 342,
			prize: "$500",
			status: "active"
		},
		{
			id: 2,
			title: "Monthly Tournament",
			timeLeft: "5d",
			participants: 89,
			prize: "$2,000",
			status: "upcoming"
		},
		{
			id: 3,
			title: "Speed Run Challenge",
			timeLeft: "2h",
			participants: 156,
			prize: "$250",
			status: "active"
		},
		{
			id: 4,
			title: "Elite Championship",
			timeLeft: "Closed",
			participants: 50,
			prize: "$5,000",
			status: "closed"
		}
	];

	return (
		<div className="space-y-3">
			{competitions.map((comp) => (
				<div key={comp.id} className="bg-gray-12 border border-gray-8 rounded-lg p-4">
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-3">
							<div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
								comp.status === 'active' ? 'bg-blue-9' :
								comp.status === 'upcoming' ? 'bg-blue-8' :
								'bg-gray-8'
							}`}>
								<div className="w-3 h-3 bg-white rounded-full"></div>
							</div>
							<div>
								<Text size="3" weight="medium" className="text-white">
									{comp.title}
								</Text>
								<Text size="2" className="text-gray-11">
									{comp.participants} participants
								</Text>
							</div>
						</div>
						<div className="text-right">
							<Text size="3" weight="bold" className="text-blue-9">
								{comp.prize}
							</Text>
							<Text size="2" className="text-gray-11">
								{comp.timeLeft}
							</Text>
						</div>
					</div>

					<div className="flex items-center gap-2">
						<Button
							color="blue"
							variant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}
							disabled={comp.status === 'closed'}
							className="flex-1"
							size="1"
						>
							{comp.status === 'active' ? 'Join' :
							 comp.status === 'upcoming' ? 'Register' :
							 'Closed'}
						</Button>
						<Button variant="ghost" color="gray" size="1">
							Details
						</Button>
					</div>
				</div>
			))}
		</div>
	);
}
