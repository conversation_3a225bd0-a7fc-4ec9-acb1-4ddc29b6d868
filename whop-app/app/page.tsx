'use client';

import { useState } from 'react';
import { Heading, Text, But<PERSON>, Card, Separator } from '@whop/react/components';

export default function Page() {
	const [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');

	return (
		<div className="min-h-screen bg-gradient-to-br from-gray-1 to-green-1 p-4">
			{/* Navigation */}
			<div className="max-w-6xl mx-auto mb-8">
				<Card className="p-6 shadow-lg bg-white/80 backdrop-blur-sm border border-green-3">
					<div className="flex items-center justify-between mb-6">
						<div>
							<Heading size="7" className="text-gray-12 mb-1">
								Whop Leaderboards & Competitions
							</Heading>
							<Text size="3" className="text-gray-10">
								Compete, climb, and conquer the leaderboards
							</Text>
						</div>
						<div className="flex items-center gap-2">
							<div className="w-2 h-2 bg-green-9 rounded-full animate-pulse"></div>
							<Text size="2" className="text-green-11">Live</Text>
						</div>
					</div>

					<div className="flex gap-2">
						<Button
							variant={activeTab === 'leaderboards' ? 'solid' : 'soft'}
							color={activeTab === 'leaderboards' ? 'green' : 'gray'}
							onClick={() => setActiveTab('leaderboards')}
							className="px-8 py-3 font-medium transition-all duration-200"
							size="3"
						>
							🏆 Leaderboards
						</Button>
						<Button
							variant={activeTab === 'competitions' ? 'solid' : 'soft'}
							color={activeTab === 'competitions' ? 'green' : 'gray'}
							onClick={() => setActiveTab('competitions')}
							className="px-8 py-3 font-medium transition-all duration-200"
							size="3"
						>
							⚔️ Competitions
						</Button>
					</div>
				</Card>
			</div>

			{/* Content */}
			<div className="max-w-6xl mx-auto">
				{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}
			</div>
		</div>
	);
}

function LeaderboardsPage() {
	// Sample leaderboard data
	const leaderboardData = [
		{ rank: 1, name: "Alex Thompson", score: 2847, change: "+12", avatar: "AT", streak: 7, country: "🇺🇸" },
		{ rank: 2, name: "Sarah Chen", score: 2756, change: "+8", avatar: "SC", streak: 5, country: "🇨🇦" },
		{ rank: 3, name: "Marcus Johnson", score: 2698, change: "-3", avatar: "MJ", streak: 3, country: "🇬🇧" },
		{ rank: 4, name: "Emma Rodriguez", score: 2634, change: "+15", avatar: "ER", streak: 12, country: "🇪🇸" },
		{ rank: 5, name: "David Kim", score: 2589, change: "+5", avatar: "DK", streak: 2, country: "🇰🇷" },
		{ rank: 6, name: "Lisa Wang", score: 2543, change: "-2", avatar: "LW", streak: 8, country: "🇨🇳" },
		{ rank: 7, name: "James Wilson", score: 2498, change: "+7", avatar: "JW", streak: 4, country: "🇦🇺" },
		{ rank: 8, name: "Maya Patel", score: 2456, change: "+3", avatar: "MP", streak: 6, country: "🇮🇳" },
	];

	return (
		<div className="space-y-6">
			{/* Header */}
			<Card className="p-6 bg-gradient-to-r from-green-2 to-green-3 border-green-6">
				<div className="text-center">
					<Heading size="8" className="text-green-12 mb-2">
						Global Leaderboard
					</Heading>
					<Text size="4" className="text-green-11">
						Compete with the best and climb to the top
					</Text>
				</div>
			</Card>

			{/* Leaderboard */}
			<Card className="overflow-hidden shadow-lg">
				<div className="p-6 bg-gradient-to-r from-green-1 to-green-2 border-b border-green-4">
					<div className="flex justify-between items-center">
						<div>
							<Heading size="5" className="text-green-12">
								Top Performers
							</Heading>
							<Text size="3" className="text-green-11 mt-1">
								Updated in real-time • Last update: 2 minutes ago
							</Text>
						</div>
						<Button variant="soft" color="green" size="2">
							View All
						</Button>
					</div>
				</div>

				<div className="divide-y divide-gray-4">
					{leaderboardData.map((player, index) => (
						<div key={player.rank} className="p-5 hover:bg-green-1 transition-all duration-200 hover:shadow-sm">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-4">
									{/* Rank Badge */}
									<div className={`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold shadow-sm ${
										player.rank === 1 ? 'bg-gradient-to-br from-yellow-9 to-yellow-10 text-yellow-1' :
										player.rank === 2 ? 'bg-gradient-to-br from-gray-9 to-gray-10 text-gray-1' :
										player.rank === 3 ? 'bg-gradient-to-br from-orange-9 to-orange-10 text-orange-1' :
										'bg-gradient-to-br from-green-3 to-green-4 text-green-11'
									}`}>
										{player.rank}
									</div>

									{/* Player Info */}
									<div className="flex items-center gap-3">
										<div className="w-8 h-8 rounded-full bg-gray-3 flex items-center justify-center text-xs font-semibold text-gray-11">
											{player.avatar}
										</div>
										<div>
											<div className="flex items-center gap-2">
												<Text size="4" weight="medium" className="text-gray-12">
													{player.name}
												</Text>
												<span className="text-sm">{player.country}</span>
											</div>
											<div className="flex items-center gap-2 mt-1">
												<Text size="2" className="text-gray-10">
													{player.streak} day streak
												</Text>
												<div className="w-1 h-1 bg-gray-6 rounded-full"></div>
												<Text size="2" className="text-gray-10">
													Rank #{player.rank}
												</Text>
											</div>
										</div>
									</div>
								</div>

								<div className="flex items-center gap-6">
									<div className="text-right">
										<Text size="4" weight="bold" className="text-gray-12 block">
											{player.score.toLocaleString()}
										</Text>
										<Text size="2" className="text-gray-10">
											points
										</Text>
									</div>
									<div className={`px-3 py-1 rounded-full text-xs font-semibold shadow-sm ${
										player.change.startsWith('+') ? 'bg-green-3 text-green-11 border border-green-6' : 'bg-red-3 text-red-11 border border-red-6'
									}`}>
										{player.change}
									</div>
								</div>
							</div>
						</div>
					))}
				</div>

				{/* View More */}
				<div className="p-4 bg-gray-1 border-t border-gray-4">
					<Button variant="soft" color="gray" className="w-full">
						View Full Leaderboard
					</Button>
				</div>
			</Card>

			{/* Stats Cards */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card className="p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow">
					<div className="text-center">
						<Text size="6" weight="bold" className="text-green-12 block mb-1">
							1,247
						</Text>
						<Text size="3" className="text-green-11 mb-2">
							Total Players
						</Text>
						<Text size="1" className="text-green-10">
							+23 this week
						</Text>
					</div>
				</Card>

				<Card className="p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow">
					<div className="text-center">
						<Text size="6" weight="bold" className="text-green-12 block mb-1">
							89.2%
						</Text>
						<Text size="3" className="text-green-11 mb-2">
							Activity Rate
						</Text>
						<Text size="1" className="text-green-10">
							+2.1% from last month
						</Text>
					</div>
				</Card>

				<Card className="p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow">
					<div className="text-center">
						<Text size="6" weight="bold" className="text-green-12 block mb-1">
							2,847
						</Text>
						<Text size="3" className="text-green-11 mb-2">
							Top Score
						</Text>
						<Text size="1" className="text-green-10">
							Alex Thompson
						</Text>
					</div>
				</Card>

				<Card className="p-6 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-lg transition-shadow">
					<div className="text-center">
						<Text size="6" weight="bold" className="text-green-12 block mb-1">
							156
						</Text>
						<Text size="3" className="text-green-11 mb-2">
							Avg Score
						</Text>
						<Text size="1" className="text-green-10">
							Daily average
						</Text>
					</div>
				</Card>
			</div>
		</div>
	);
}

function CompetitionsPage() {
	const competitions = [
		{
			id: 1,
			title: "Weekly Challenge",
			description: "Complete daily tasks to earn points and climb the weekly leaderboard.",
			timeLeft: "3 days, 14 hours",
			participants: 342,
			prize: "$500",
			status: "active",
			difficulty: "Medium"
		},
		{
			id: 2,
			title: "Monthly Tournament",
			description: "The ultimate test of skill. Top 10 players win exclusive rewards.",
			timeLeft: "Starts in 5 days",
			participants: 89,
			prize: "$2,000",
			status: "upcoming",
			difficulty: "Hard"
		},
		{
			id: 3,
			title: "Speed Run Challenge",
			description: "Race against time in this fast-paced competition.",
			timeLeft: "2 hours left",
			participants: 156,
			prize: "$250",
			status: "active",
			difficulty: "Easy"
		},
		{
			id: 4,
			title: "Elite Championship",
			description: "Invitation-only tournament for top 50 players.",
			timeLeft: "Registration closed",
			participants: 50,
			prize: "$5,000",
			status: "closed",
			difficulty: "Expert"
		}
	];

	return (
		<div className="space-y-6">
			{/* Header */}
			<Card className="p-6 bg-gradient-to-r from-green-2 to-green-3 border-green-6">
				<div className="text-center">
					<Heading size="8" className="text-green-12 mb-2">
						Competitions
					</Heading>
					<Text size="4" className="text-green-11">
						Join exciting competitions and win amazing prizes
					</Text>
				</div>
			</Card>

			{/* Active Competitions */}
			<div>
				<Heading size="5" className="text-gray-12 mb-4">
					Active & Upcoming Competitions
				</Heading>
				<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
					{competitions.map((comp) => (
						<Card key={comp.id} className="p-6 hover:shadow-lg transition-shadow">
							<div className="flex justify-between items-start mb-4">
								<div>
									<Heading size="5" className="text-gray-12 mb-2">
										{comp.title}
									</Heading>
									<div className="flex items-center gap-2 mb-2">
										<div className={`px-2 py-1 rounded-full text-xs font-medium ${
											comp.status === 'active' ? 'bg-green-3 text-green-11' :
											comp.status === 'upcoming' ? 'bg-blue-3 text-blue-11' :
											'bg-gray-3 text-gray-11'
										}`}>
											{comp.status.charAt(0).toUpperCase() + comp.status.slice(1)}
										</div>
										<div className={`px-2 py-1 rounded-full text-xs font-medium ${
											comp.difficulty === 'Easy' ? 'bg-green-3 text-green-11' :
											comp.difficulty === 'Medium' ? 'bg-yellow-3 text-yellow-11' :
											comp.difficulty === 'Hard' ? 'bg-orange-3 text-orange-11' :
											'bg-red-3 text-red-11'
										}`}>
											{comp.difficulty}
										</div>
									</div>
								</div>
								<Text size="4" weight="bold" className="text-green-11">
									{comp.prize}
								</Text>
							</div>

							<Text size="3" className="text-gray-10 mb-4">
								{comp.description}
							</Text>

							<div className="space-y-2 mb-4">
								<div className="flex justify-between">
									<Text size="2" className="text-gray-11">Time:</Text>
									<Text size="2" className="text-gray-12">{comp.timeLeft}</Text>
								</div>
								<div className="flex justify-between">
									<Text size="2" className="text-gray-11">Participants:</Text>
									<Text size="2" className="text-gray-12">{comp.participants}</Text>
								</div>
							</div>

							<Button
								color="green"
								variant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}
								className="w-full"
								disabled={comp.status === 'closed'}
							>
								{comp.status === 'active' ? 'Join Now' :
								 comp.status === 'upcoming' ? 'Register' :
								 'Closed'}
							</Button>
						</Card>
					))}
				</div>
			</div>

			{/* Competition Stats */}
			<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
				<Card className="p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="5" weight="bold" className="text-green-12 block">
							12
						</Text>
						<Text size="2" className="text-green-11">
							Active Competitions
						</Text>
					</div>
				</Card>

				<Card className="p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="5" weight="bold" className="text-green-12 block">
							$15K
						</Text>
						<Text size="2" className="text-green-11">
							Total Prize Pool
						</Text>
					</div>
				</Card>

				<Card className="p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="5" weight="bold" className="text-green-12 block">
							847
						</Text>
						<Text size="2" className="text-green-11">
							Total Participants
						</Text>
					</div>
				</Card>

				<Card className="p-4 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="5" weight="bold" className="text-green-12 block">
							24h
						</Text>
						<Text size="2" className="text-green-11">
							Avg Duration
						</Text>
					</div>
				</Card>
			</div>
		</div>
	);
}
