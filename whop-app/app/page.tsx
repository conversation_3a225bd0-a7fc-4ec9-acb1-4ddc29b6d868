'use client';

import { useState } from 'react';
import { Heading, Text, Button, Card, Separator } from '@whop/react/components';

export default function Page() {
	const [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');

	return (
		<div className="min-h-screen bg-gray-12">
			{/* Top Navigation Bar */}
			<nav className="border-b border-gray-11 bg-gray-12">
				<div className="max-w-5xl mx-auto px-4 py-3">
					<div className="flex items-center justify-between">
						<div>
							<Heading size="4" className="text-white">
								Whop Leaderboards
							</Heading>
							<Text size="1" className="text-gray-9">
								Compete and climb the rankings
							</Text>
						</div>
						<div className="flex items-center gap-2">
							<Button
								variant={activeTab === 'leaderboards' ? 'solid' : 'ghost'}
								color={activeTab === 'leaderboards' ? 'green' : 'gray'}
								onClick={() => setActiveTab('leaderboards')}
								size="1"
							>
								Leaderboards
							</Button>
							<Button
								variant={activeTab === 'competitions' ? 'solid' : 'ghost'}
								color={activeTab === 'competitions' ? 'green' : 'gray'}
								onClick={() => setActiveTab('competitions')}
								size="1"
							>
								Competitions
							</Button>
						</div>
					</div>
				</div>
			</nav>

			{/* Content */}
			<div className="max-w-5xl mx-auto px-4 py-4">
				{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}
			</div>
		</div>
	);
}

function LeaderboardsPage() {
	// Sample leaderboard data
	const leaderboardData = [
		{ rank: 1, name: "Alex Thompson", score: 2847, change: "+12", avatar: "AT", streak: 7 },
		{ rank: 2, name: "Sarah Chen", score: 2756, change: "+8", avatar: "SC", streak: 5 },
		{ rank: 3, name: "Marcus Johnson", score: 2698, change: "-3", avatar: "MJ", streak: 3 },
		{ rank: 4, name: "Emma Rodriguez", score: 2634, change: "+15", avatar: "ER", streak: 12 },
		{ rank: 5, name: "David Kim", score: 2589, change: "+5", avatar: "DK", streak: 2 },
		{ rank: 6, name: "Lisa Wang", score: 2543, change: "-2", avatar: "LW", streak: 8 },
		{ rank: 7, name: "James Wilson", score: 2498, change: "+7", avatar: "JW", streak: 4 },
		{ rank: 8, name: "Maya Patel", score: 2456, change: "+3", avatar: "MP", streak: 6 },
	];

	return (
		<div className="space-y-4">
			{/* Header */}
			<div className="mb-4">
				<Heading size="5" className="text-white mb-1">
					Global Leaderboard
				</Heading>
				<Text size="2" className="text-gray-9">
					Compete with the best and climb to the top
				</Text>
			</div>

			{/* Leaderboard */}
			<div className="bg-gray-11 border border-gray-10 rounded-lg overflow-hidden">
				<div className="px-4 py-3 border-b border-gray-10">
					<div className="flex justify-between items-center">
						<div>
							<Heading size="3" className="text-white">
								Top Performers
							</Heading>
							<Text size="1" className="text-gray-9 mt-0.5">
								Updated in real-time • Last update: 2 minutes ago
							</Text>
						</div>
						<Button variant="ghost" color="green" size="1">
							View All
						</Button>
					</div>
				</div>

				<div className="divide-y divide-gray-10">
					{leaderboardData.map((player, index) => (
						<div key={player.rank} className="px-4 py-3 hover:bg-gray-10 transition-all duration-200">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									{/* Rank Badge */}
									<div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-bold ${
										player.rank === 1 ? 'bg-yellow-9 text-black shadow-sm shadow-yellow-9/30' :
										player.rank === 2 ? 'bg-gray-8 text-white shadow-sm shadow-gray-8/30' :
										player.rank === 3 ? 'bg-orange-9 text-white shadow-sm shadow-orange-9/30' :
										'bg-gray-9 text-white'
									}`}>
										{player.rank}
									</div>

									{/* Player Info */}
									<div className="flex items-center gap-2">
										<div className="w-6 h-6 rounded-full bg-gray-9 flex items-center justify-center text-xs font-semibold text-white">
											{player.avatar}
										</div>
										<div>
											<Text size="3" weight="medium" className="text-white">
												{player.name}
											</Text>
											<Text size="1" className="text-gray-9">
												{player.streak} day streak
											</Text>
										</div>
									</div>
								</div>

								<div className="flex items-center gap-3">
									<div className="text-right">
										<Text size="3" weight="bold" className="text-white block">
											{player.score.toLocaleString()}
										</Text>
										<Text size="1" className="text-gray-9">
											points
										</Text>
									</div>
									<div className={`px-2 py-1 rounded-full text-xs font-semibold ${
										player.change.startsWith('+') ? 'bg-green-9/20 text-green-9 border border-green-9/30' : 'bg-red-9/20 text-red-9 border border-red-9/30'
									}`}>
										{player.change}
									</div>
								</div>
							</div>
						</div>
					))}
				</div>
			</div>

			{/* Stats */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-3">
				<div className="bg-gray-11 border border-gray-10 rounded-lg p-3">
					<Text size="4" weight="bold" className="text-white block mb-0.5">
						1,247
					</Text>
					<Text size="1" className="text-gray-9 mb-0.5">
						Total Players
					</Text>
					<Text size="1" className="text-green-9">
						+23 this week
					</Text>
				</div>

				<div className="bg-gray-11 border border-gray-10 rounded-lg p-3">
					<Text size="4" weight="bold" className="text-white block mb-0.5">
						89.2%
					</Text>
					<Text size="1" className="text-gray-9 mb-0.5">
						Activity Rate
					</Text>
					<Text size="1" className="text-green-9">
						+2.1% from last month
					</Text>
				</div>

				<div className="bg-gray-11 border border-gray-10 rounded-lg p-3">
					<Text size="4" weight="bold" className="text-white block mb-0.5">
						2,847
					</Text>
					<Text size="1" className="text-gray-9 mb-0.5">
						Top Score
					</Text>
					<Text size="1" className="text-green-9">
						Alex Thompson
					</Text>
				</div>

				<div className="bg-gray-11 border border-gray-10 rounded-lg p-3">
					<Text size="4" weight="bold" className="text-white block mb-0.5">
						156
					</Text>
					<Text size="1" className="text-gray-9 mb-0.5">
						Avg Score
					</Text>
					<Text size="1" className="text-green-9">
						Daily average
					</Text>
				</div>
			</div>
		</div>
	);
}

function CompetitionsPage() {
	const competitions = [
		{
			id: 1,
			title: "Weekly Challenge",
			description: "Complete daily tasks to earn points and climb the weekly leaderboard",
			timeLeft: "3d 14h",
			participants: 342,
			prize: "$500",
			status: "active"
		},
		{
			id: 2,
			title: "Monthly Tournament",
			description: "Ultimate test of skill for the top performers",
			timeLeft: "5d",
			participants: 89,
			prize: "$2,000",
			status: "upcoming"
		},
		{
			id: 3,
			title: "Speed Run Challenge",
			description: "Race against time in this fast-paced competition",
			timeLeft: "2h",
			participants: 156,
			prize: "$250",
			status: "active"
		},
		{
			id: 4,
			title: "Elite Championship",
			description: "Invitation-only tournament for elite players",
			timeLeft: "Closed",
			participants: 50,
			prize: "$5,000",
			status: "closed"
		}
	];

	return (
		<div className="space-y-4">
			{/* Header */}
			<div className="mb-4">
				<Heading size="5" className="text-white mb-1">
					Competitions
				</Heading>
				<Text size="2" className="text-gray-9">
					Join exciting competitions and win amazing prizes
				</Text>
			</div>

			<div className="space-y-3">
				{competitions.map((comp) => (
					<div key={comp.id} className="bg-gray-11 border border-gray-10 rounded-lg p-4 relative overflow-hidden">
						{/* Glowing green accent */}
						<div className={`absolute left-0 top-0 bottom-0 w-1 ${
							comp.status === 'active' ? 'bg-green-9 shadow-sm shadow-green-9/50' :
							comp.status === 'upcoming' ? 'bg-blue-9' :
							'bg-gray-8'
						}`}></div>

						<div className="flex items-center justify-between mb-3">
							<div className="flex items-center gap-3">
								<div className={`w-8 h-8 rounded-lg flex items-center justify-center ${
									comp.status === 'active' ? 'bg-green-9 shadow-sm shadow-green-9/30' :
									comp.status === 'upcoming' ? 'bg-blue-9' :
									'bg-gray-8'
								}`}>
									<div className="w-4 h-4 bg-white rounded-full flex items-center justify-center">
										<div className={`w-2 h-2 rounded-full ${
											comp.status === 'active' ? 'bg-green-9' :
											comp.status === 'upcoming' ? 'bg-blue-9' :
											'bg-gray-8'
										}`}></div>
									</div>
								</div>
								<div>
									<Heading size="3" className="text-white mb-0.5">
										{comp.title}
									</Heading>
									<Text size="1" className="text-gray-9">
										{comp.description}
									</Text>
								</div>
							</div>
							<div className="text-right">
								<Text size="3" weight="bold" className={`block ${
									comp.status === 'active' ? 'text-green-9' : 'text-gray-9'
								}`}>
									{comp.prize}
								</Text>
								<Text size="1" className="text-gray-10">
									Prize Pool
								</Text>
							</div>
						</div>

						<div className="grid grid-cols-3 gap-4 mb-3">
							<div>
								<Text size="1" className="text-gray-10 mb-0.5">
									Time Remaining
								</Text>
								<Text size="2" weight="medium" className="text-white">
									{comp.timeLeft}
								</Text>
							</div>
							<div>
								<Text size="1" className="text-gray-10 mb-0.5">
									Participants
								</Text>
								<Text size="2" weight="medium" className="text-white">
									{comp.participants}
								</Text>
							</div>
							<div>
								<Text size="1" className="text-gray-10 mb-0.5">
									Status
								</Text>
								<div className={`inline-flex px-2 py-0.5 rounded-full text-xs font-medium ${
									comp.status === 'active' ? 'bg-green-9/20 text-green-9 border border-green-9/30' :
									comp.status === 'upcoming' ? 'bg-blue-9/20 text-blue-9 border border-blue-9/30' :
									'bg-gray-8/20 text-gray-8 border border-gray-8/30'
								}`}>
									{comp.status.charAt(0).toUpperCase() + comp.status.slice(1)}
								</div>
							</div>
						</div>

						<div className="flex items-center gap-2">
							<Button
								color="green"
								variant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}
								disabled={comp.status === 'closed'}
								className="flex-1"
								size="1"
							>
								{comp.status === 'active' ? 'Join Competition' :
								 comp.status === 'upcoming' ? 'Register Now' :
								 'Competition Closed'}
							</Button>
							<Button variant="ghost" color="gray" size="1">
								View Details
							</Button>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
