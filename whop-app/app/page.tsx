'use client';

import { useState } from 'react';
import { Heading, Text, But<PERSON>, Card, Separator } from '@whop/react/components';

export default function Page() {
	const [activeTab, setActiveTab] = useState<'leaderboards' | 'competitions'>('leaderboards');

	return (
		<div className="min-h-screen bg-gray-1 p-3">
			{/* Navigation */}
			<div className="max-w-5xl mx-auto mb-6">
				<Card className="p-4 shadow-sm border border-gray-4">
					<div className="flex items-center justify-between mb-4">
						<div>
							<Heading size="5" className="text-gray-12 mb-1">
								Whop Leaderboards & Competitions
							</Heading>
							<Text size="2" className="text-gray-10">
								Compete, climb, and conquer the leaderboards
							</Text>
						</div>
						<div className="flex items-center gap-2">
							<div className="w-1.5 h-1.5 bg-green-9 rounded-full animate-pulse"></div>
							<Text size="1" className="text-green-11">Live</Text>
						</div>
					</div>

					<div className="flex gap-2">
						<Button
							variant={activeTab === 'leaderboards' ? 'solid' : 'soft'}
							color={activeTab === 'leaderboards' ? 'green' : 'gray'}
							onClick={() => setActiveTab('leaderboards')}
							className="px-4 py-2 text-sm font-medium transition-all duration-200"
							size="2"
						>
							🏆 Leaderboards
						</Button>
						<Button
							variant={activeTab === 'competitions' ? 'solid' : 'soft'}
							color={activeTab === 'competitions' ? 'green' : 'gray'}
							onClick={() => setActiveTab('competitions')}
							className="px-4 py-2 text-sm font-medium transition-all duration-200"
							size="2"
						>
							⚔️ Competitions
						</Button>
					</div>
				</Card>
			</div>

			{/* Content */}
			<div className="max-w-5xl mx-auto">
				{activeTab === 'leaderboards' ? <LeaderboardsPage /> : <CompetitionsPage />}
			</div>
		</div>
	);
}

function LeaderboardsPage() {
	// Sample leaderboard data
	const leaderboardData = [
		{ rank: 1, name: "Alex Thompson", score: 2847, change: "+12", avatar: "AT", streak: 7, country: "🇺🇸" },
		{ rank: 2, name: "Sarah Chen", score: 2756, change: "+8", avatar: "SC", streak: 5, country: "🇨🇦" },
		{ rank: 3, name: "Marcus Johnson", score: 2698, change: "-3", avatar: "MJ", streak: 3, country: "🇬🇧" },
		{ rank: 4, name: "Emma Rodriguez", score: 2634, change: "+15", avatar: "ER", streak: 12, country: "🇪🇸" },
		{ rank: 5, name: "David Kim", score: 2589, change: "+5", avatar: "DK", streak: 2, country: "🇰🇷" },
		{ rank: 6, name: "Lisa Wang", score: 2543, change: "-2", avatar: "LW", streak: 8, country: "🇨🇳" },
		{ rank: 7, name: "James Wilson", score: 2498, change: "+7", avatar: "JW", streak: 4, country: "🇦🇺" },
		{ rank: 8, name: "Maya Patel", score: 2456, change: "+3", avatar: "MP", streak: 6, country: "🇮🇳" },
	];

	return (
		<div className="space-y-4">
			{/* Header */}
			<Card className="p-4 bg-gradient-to-r from-green-2 to-green-3 border-green-6">
				<div className="text-center">
					<Heading size="5" className="text-green-12 mb-1">
						Global Leaderboard
					</Heading>
					<Text size="2" className="text-green-11">
						Compete with the best and climb to the top
					</Text>
				</div>
			</Card>

			{/* Leaderboard */}
			<Card className="overflow-hidden shadow-sm">
				<div className="p-3 bg-gradient-to-r from-green-1 to-green-2 border-b border-green-4">
					<div className="flex justify-between items-center">
						<div>
							<Heading size="4" className="text-green-12">
								Top Performers
							</Heading>
							<Text size="1" className="text-green-11 mt-0.5">
								Updated in real-time • Last update: 2 minutes ago
							</Text>
						</div>
						<Button variant="soft" color="green" size="1">
							View All
						</Button>
					</div>
				</div>

				<div className="divide-y divide-gray-4">
					{leaderboardData.map((player, index) => (
						<div key={player.rank} className="p-3 hover:bg-green-1 transition-all duration-200">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									{/* Rank Badge */}
									<div className={`w-7 h-7 rounded-full flex items-center justify-center text-xs font-bold ${
										player.rank === 1 ? 'bg-gradient-to-br from-yellow-9 to-yellow-10 text-yellow-1' :
										player.rank === 2 ? 'bg-gradient-to-br from-gray-9 to-gray-10 text-gray-1' :
										player.rank === 3 ? 'bg-gradient-to-br from-orange-9 to-orange-10 text-orange-1' :
										'bg-gradient-to-br from-green-3 to-green-4 text-green-11'
									}`}>
										{player.rank}
									</div>

									{/* Player Info */}
									<div className="flex items-center gap-2">
										<div className="w-6 h-6 rounded-full bg-gray-3 flex items-center justify-center text-xs font-semibold text-gray-11">
											{player.avatar}
										</div>
										<div>
											<div className="flex items-center gap-1.5">
												<Text size="3" weight="medium" className="text-gray-12">
													{player.name}
												</Text>
												<span className="text-xs">{player.country}</span>
											</div>
											<div className="flex items-center gap-1.5 mt-0.5">
												<Text size="1" className="text-gray-10">
													{player.streak} day streak
												</Text>
												<div className="w-0.5 h-0.5 bg-gray-6 rounded-full"></div>
												<Text size="1" className="text-gray-10">
													Rank #{player.rank}
												</Text>
											</div>
										</div>
									</div>
								</div>

								<div className="flex items-center gap-3">
									<div className="text-right">
										<Text size="3" weight="bold" className="text-gray-12 block">
											{player.score.toLocaleString()}
										</Text>
										<Text size="1" className="text-gray-10">
											points
										</Text>
									</div>
									<div className={`px-2 py-0.5 rounded-full text-xs font-semibold ${
										player.change.startsWith('+') ? 'bg-green-3 text-green-11' : 'bg-red-3 text-red-11'
									}`}>
										{player.change}
									</div>
								</div>
							</div>
						</div>
					))}
				</div>

				{/* View More */}
				<div className="p-3 bg-gray-1 border-t border-gray-4">
					<Button variant="soft" color="gray" className="w-full" size="1">
						View Full Leaderboard
					</Button>
				</div>
			</Card>

			{/* Stats Cards */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-3">
				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block mb-0.5">
							1,247
						</Text>
						<Text size="2" className="text-green-11 mb-1">
							Total Players
						</Text>
						<Text size="1" className="text-green-10">
							+23 this week
						</Text>
					</div>
				</Card>

				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block mb-0.5">
							89.2%
						</Text>
						<Text size="2" className="text-green-11 mb-1">
							Activity Rate
						</Text>
						<Text size="1" className="text-green-10">
							+2.1% from last month
						</Text>
					</div>
				</Card>

				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block mb-0.5">
							2,847
						</Text>
						<Text size="2" className="text-green-11 mb-1">
							Top Score
						</Text>
						<Text size="1" className="text-green-10">
							Alex Thompson
						</Text>
					</div>
				</Card>

				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4 hover:shadow-sm transition-shadow">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block mb-0.5">
							156
						</Text>
						<Text size="2" className="text-green-11 mb-1">
							Avg Score
						</Text>
						<Text size="1" className="text-green-10">
							Daily average
						</Text>
					</div>
				</Card>
			</div>
		</div>
	);
}

function CompetitionsPage() {
	const competitions = [
		{
			id: 1,
			title: "Weekly Challenge",
			description: "Complete daily tasks to earn points",
			timeLeft: "3d 14h",
			participants: 342,
			prize: "$500",
			status: "active",
			difficulty: "Medium",
			icon: "✓",
			progress: 67
		},
		{
			id: 2,
			title: "Monthly Tournament",
			description: "Ultimate test of skill",
			timeLeft: "5d",
			participants: 89,
			prize: "$2,000",
			status: "upcoming",
			difficulty: "Hard",
			icon: "🏆",
			progress: 0
		},
		{
			id: 3,
			title: "Speed Run Challenge",
			description: "Race against time",
			timeLeft: "2h",
			participants: 156,
			prize: "$250",
			status: "active",
			difficulty: "Easy",
			icon: "⚡",
			progress: 89
		},
		{
			id: 4,
			title: "Elite Championship",
			description: "Invitation-only tournament",
			timeLeft: "Closed",
			participants: 50,
			prize: "$5,000",
			status: "closed",
			difficulty: "Expert",
			icon: "👑",
			progress: 100
		}
	];

	return (
		<div className="space-y-4">
			{/* Header */}
			<Card className="p-4 bg-gradient-to-r from-green-2 to-green-3 border-green-6">
				<div className="text-center">
					<Heading size="5" className="text-green-12 mb-1">
						Competitions
					</Heading>
					<Text size="2" className="text-green-11">
						Join exciting competitions and win amazing prizes
					</Text>
				</div>
			</Card>

			{/* Featured Competition - Sleek Design */}
			<Card className="bg-gray-12 border-gray-11 overflow-hidden relative">
				<div className="absolute inset-0 bg-gradient-to-br from-green-9/20 to-green-10/10"></div>
				<div className="relative p-4">
					{/* Header */}
					<div className="flex items-center justify-between mb-3">
						<div className="flex items-center gap-3">
							<div className="w-8 h-8 rounded-lg bg-green-9 flex items-center justify-center shadow-lg shadow-green-9/50">
								<span className="text-white text-sm font-bold">✓</span>
							</div>
							<div>
								<Text size="3" weight="bold" className="text-white">
									Weekly Challenge
								</Text>
								<Text size="1" className="text-green-3">
									Whoever makes the most money
								</Text>
							</div>
						</div>
						<div className="text-right">
							<Text size="2" weight="bold" className="text-green-9 block">
								$500
							</Text>
							<Text size="1" className="text-green-11">
								Prize
							</Text>
						</div>
					</div>

					{/* Prize Breakdown */}
					<div className="space-y-2 mb-4">
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-yellow-9 shadow-sm shadow-yellow-9/50"></div>
								<Text size="2" className="text-gray-3">1st place</Text>
							</div>
							<Text size="2" weight="medium" className="text-white">$300</Text>
						</div>
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-gray-9"></div>
								<Text size="2" className="text-gray-3">2nd place</Text>
							</div>
							<Text size="2" weight="medium" className="text-white">$150</Text>
						</div>
						<div className="flex items-center justify-between">
							<div className="flex items-center gap-2">
								<div className="w-2 h-2 rounded-full bg-orange-9"></div>
								<Text size="2" className="text-gray-3">3rd place</Text>
							</div>
							<Text size="2" weight="medium" className="text-white">$50</Text>
						</div>
					</div>

					{/* Timer */}
					<div className="mb-4">
						<Text size="1" className="text-green-11 mb-1">Time remaining</Text>
						<div className="flex items-center gap-1">
							<div className="bg-gray-11 rounded px-2 py-1">
								<Text size="3" weight="bold" className="text-white">0</Text>
							</div>
							<div className="bg-gray-11 rounded px-2 py-1">
								<Text size="3" weight="bold" className="text-white">3</Text>
							</div>
							<Text size="2" className="text-gray-4">:</Text>
							<div className="bg-gray-11 rounded px-2 py-1">
								<Text size="3" weight="bold" className="text-white">1</Text>
							</div>
							<div className="bg-gray-11 rounded px-2 py-1">
								<Text size="3" weight="bold" className="text-white">4</Text>
							</div>
							<Text size="2" className="text-gray-4">:</Text>
							<div className="bg-gray-11 rounded px-2 py-1">
								<Text size="3" weight="bold" className="text-white">5</Text>
							</div>
							<div className="bg-gray-11 rounded px-2 py-1">
								<Text size="3" weight="bold" className="text-white">0</Text>
							</div>
						</div>
					</div>

					{/* Participants */}
					<div className="mb-4">
						<Text size="1" className="text-green-11 mb-2">Participants (342)</Text>
						<div className="space-y-1">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<div className="w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center">
										<Text size="1" className="text-white font-medium">AS</Text>
									</div>
									<Text size="2" className="text-gray-3">Alex Smith</Text>
								</div>
								<Text size="2" weight="medium" className="text-green-9">$2,847</Text>
							</div>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<div className="w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center">
										<Text size="1" className="text-white font-medium">SC</Text>
									</div>
									<Text size="2" className="text-gray-3">Sarah Chen</Text>
								</div>
								<Text size="2" weight="medium" className="text-green-9">$2,756</Text>
							</div>
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-2">
									<div className="w-5 h-5 rounded-full bg-gray-9 flex items-center justify-center">
										<Text size="1" className="text-white font-medium">MJ</Text>
									</div>
									<Text size="2" className="text-gray-3">Marcus Johnson</Text>
								</div>
								<Text size="2" weight="medium" className="text-green-9">$2,698</Text>
							</div>
						</div>
					</div>

					{/* Join Button */}
					<Button
						className="w-full bg-green-9 hover:bg-green-10 text-white border-0 shadow-lg shadow-green-9/30"
						size="2"
					>
						Join Competition
					</Button>
				</div>
			</Card>

			{/* Other Competitions */}
			<div>
				<Heading size="4" className="text-gray-12 mb-3">
					Other Competitions
				</Heading>
				<div className="grid grid-cols-1 md:grid-cols-3 gap-3">
					{competitions.slice(1).map((comp) => (
						<Card key={comp.id} className="p-3 hover:shadow-sm transition-shadow bg-gray-2 border-gray-4">
							<div className="flex items-center gap-2 mb-2">
								<div className={`w-6 h-6 rounded-lg flex items-center justify-center text-xs ${
									comp.status === 'active' ? 'bg-green-9 text-white shadow-sm shadow-green-9/50' :
									comp.status === 'upcoming' ? 'bg-blue-9 text-white' :
									'bg-gray-9 text-white'
								}`}>
									{comp.icon}
								</div>
								<div className="flex-1">
									<Text size="3" weight="medium" className="text-gray-12">
										{comp.title}
									</Text>
									<Text size="1" className="text-gray-10">
										{comp.description}
									</Text>
								</div>
								<Text size="2" weight="bold" className="text-green-11">
									{comp.prize}
								</Text>
							</div>

							<div className="flex items-center justify-between mb-2">
								<Text size="1" className="text-gray-11">{comp.timeLeft}</Text>
								<Text size="1" className="text-gray-11">{comp.participants} players</Text>
							</div>

							<Button
								color="green"
								variant={comp.status === 'active' ? 'solid' : comp.status === 'upcoming' ? 'soft' : 'outline'}
								className="w-full"
								size="1"
								disabled={comp.status === 'closed'}
							>
								{comp.status === 'active' ? 'Join' :
								 comp.status === 'upcoming' ? 'Register' :
								 'Closed'}
							</Button>
						</Card>
					))}
				</div>
			</div>

			{/* Competition Stats */}
			<div className="grid grid-cols-2 md:grid-cols-4 gap-3">
				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block">
							12
						</Text>
						<Text size="1" className="text-green-11">
							Active Competitions
						</Text>
					</div>
				</Card>

				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block">
							$15K
						</Text>
						<Text size="1" className="text-green-11">
							Total Prize Pool
						</Text>
					</div>
				</Card>

				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block">
							847
						</Text>
						<Text size="1" className="text-green-11">
							Total Participants
						</Text>
					</div>
				</Card>

				<Card className="p-3 bg-gradient-to-br from-green-1 to-green-2 border-green-4">
					<div className="text-center">
						<Text size="4" weight="bold" className="text-green-12 block">
							24h
						</Text>
						<Text size="1" className="text-green-11">
							Avg Duration
						</Text>
					</div>
				</Card>
			</div>
		</div>
	);
}
